<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 120" width="300" height="120">
  <defs>
    <!-- 主文字渐变 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#c084fc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f472b6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影渐变 -->
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景装饰线条 -->
  <g stroke="url(#mainGradient)" stroke-width="2" fill="none" opacity="0.3">
    <path d="M20 30 Q50 25 80 30 T140 30">
      <animate attributeName="d" values="M20 30 Q50 25 80 30 T140 30;M20 30 Q50 35 80 30 T140 30;M20 30 Q50 25 80 30 T140 30" dur="3s" repeatCount="indefinite"/>
    </path>
    <path d="M160 90 Q190 85 220 90 T280 90">
      <animate attributeName="d" values="M160 90 Q190 85 220 90 T280 90;M160 90 Q190 95 220 90 T280 90;M160 90 Q190 85 220 90 T280 90" dur="3.5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 主标题 TREX - 阴影层 -->
  <text x="152" y="68" text-anchor="middle" 
        font-family="'SF Pro Display', 'Helvetica Neue', Arial, sans-serif" 
        font-weight="900" 
        font-size="48" 
        fill="url(#shadowGradient)"
        style="letter-spacing: 0.1em;">
    TREX
  </text>
  
  <!-- 主标题 TREX - 主层 -->
  <text x="150" y="66" text-anchor="middle" 
        font-family="'SF Pro Display', 'Helvetica Neue', Arial, sans-serif" 
        font-weight="900" 
        font-size="48" 
        fill="url(#mainGradient)"
        style="letter-spacing: 0.1em;">
    TREX
  </text>
  
  <!-- 副标题 -->
  <text x="150" y="88" text-anchor="middle" 
        font-family="'SF Pro Text', 'Helvetica Neue', Arial, sans-serif" 
        font-weight="500" 
        font-size="12" 
        fill="#94a3b8"
        style="letter-spacing: 0.2em;">
    INTELLIGENT AGENT PLATFORM
  </text>
  
  <!-- 装饰点 -->
  <circle cx="30" cy="60" r="3" fill="url(#mainGradient)" opacity="0.8">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="270" cy="60" r="3" fill="url(#mainGradient)" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 简约的科技线条 -->
  <path d="M40 60 L60 60" stroke="url(#mainGradient)" stroke-width="2" opacity="0.6">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.5s" repeatCount="indefinite"/>
  </path>
  <path d="M240 60 L260 60" stroke="url(#mainGradient)" stroke-width="2" opacity="0.6">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2.5s" repeatCount="indefinite"/>
  </path>
</svg> 