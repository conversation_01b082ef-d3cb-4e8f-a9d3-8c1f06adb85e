# Frontend Application (fe)

This directory contains the frontend implementation for the T-Rex project, designed to provide a web-based interface for project and file management, including a code editor.

## Tech Stack

*   **Framework:** Vue 3 (using Composition API with `<script setup>`)
*   **Build Tool:** Vite
*   **Language:** TypeScript
*   **UI Library:** Element Plus
*   **State Management:** Pinia
*   **Routing:** Vue Router
*   **HTTP Client:** Axios
*   **Styling:** UnoCSS
*   **Code Editor:** `@guolao/vue-monaco-editor`

## Project Structure (`src/`)

The `src/` directory is organized as follows:

*   `main.ts`: Application entry point, initializes Vue and plugins.
*   `App.vue`: Root Vue component.
*   `api/`: Contains modules for interacting with backend APIs (e.g., `fs.ts` for file system operations). Handles data transformation between frontend (camelCase) and backend (snake_case) conventions.
*   `assets/`: Static assets like images or fonts.
*   `components/`: Reusable UI components.
    *   `FileBrowser.vue`: Displays the project file tree and handles file/folder interactions.
    *   `MainColumn.vue`: Hosts the Monaco editor instance and manages editor tabs.
    *   `TopToolbar.vue`: Contains global actions like saving the current file.
*   `constants/`: Application-wide constants.
*   `layout/`: Components defining the overall page structure.
*   `router/`: Vue Router configuration, defining application routes and navigation guards.
*   `store/`: Pinia store modules for state management.
    *   `fs.ts`: Manages file system state, open files, active file, content changes, and interactions with file-related APIs.
*   `styles/`: Global CSS styles and UnoCSS configurations.
*   `types/`: TypeScript type definitions, including those for API payloads.
*   `utils/`: Utility functions.
*   `views/`: Page-level components, typically mapped to routes.

## Core Features

### 1. Project and File System Management
*   Browse project files and directories.
*   Create, rename, and delete files and folders.
*   Open files in the editor.

### 2. File Editing
*   Uses the `@guolao/vue-monaco-editor` component for a rich code editing experience.
*   Supports multiple open files in tabs within `MainColumn.vue`.
*   Changes made in the editor are tracked in the `fsStore`.

### 3. File Saving
*   **Triggering Save:**
    *   Clicking the "Save" button in `TopToolbar.vue`.
    *   Using the `Cmd/Ctrl + S` keyboard shortcut.
*   **State Management (`fsStore`):**
    *   `activeFilePath`: Tracks the currently edited file.
    *   `openFiles`: A record storing the `originalContent` and `currentContent` for each open file.
    *   `isCurrentFileDirty`: A getter that determines if the active file has unsaved changes by comparing `originalContent` and `currentContent`.
    *   `canSaveCurrentFile`: A getter that enables the save button only if a file is active, dirty, and a project is selected.
*   **API Interaction (`api/fs.ts` - `saveFileContent` function):**
    *   The `saveFileContent` function in `api/fs.ts` is called by `fsStore.saveActiveFile()`.
    *   It takes the `projectID`, `filePathInProject`, and `plainTextContent`.
    *   The `plainTextContent` is **Base64 encoded** before sending to the backend.
    *   It makes a `PUT` request to `/api/v1/projects/:projectID/files/content?path=:filePathInProject` with a JSON body: `{ "content": "base64_encoded_string" }`.
    *   A successful save (HTTP 204) updates the `originalContent` of the file in `fsStore` to match the `currentContent`.
*   **Reading Files:**
    *   When a file is opened, `fsStore.fetchFileContent` calls `api/fs.ts` - `readFileContent`.
    *   This function makes a `GET` request to `/api/v1/projects/:projectID/files/content?path=:filePathInProject`.
    *   The backend returns Base64 encoded content, which is then **decoded** by `readFileContent` before being stored in `fsStore`.

### 4. Backend API Interaction Conventions
*   The frontend primarily uses camelCase for its internal data structures and variable names.
*   The backend API expects and returns JSON with snake_case field names.
*   The `api/` modules are responsible for mapping data between these two conventions during requests and responses. For example, a frontend `{ isDir: true }` would be sent as `{ "is_dir": true }` to the backend.

## Setup and Running

1.  **Navigate to the `fe` directory:**
    ```bash
    cd fe
    ```

2.  **Install dependencies:**
    ```bash
    yarn install
    # or npm install if you prefer and the project supports it
    ```

3.  **Run the development server:**
    ```bash
    yarn dev
    # or npm run dev
    ```

This will start the Vite development server, typically available at `http://localhost:5173` (or another port if 5173 is in use). 