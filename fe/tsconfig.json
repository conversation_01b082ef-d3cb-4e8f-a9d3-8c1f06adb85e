{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "NodeNext", "moduleResolution": "NodeNext", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}]}