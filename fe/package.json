{"name": "fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@guolao/vue-monaco-editor": "^1.5.5", "@microsoft/fetch-event-source": "^2.0.1", "@types/dompurify": "^3.2.0", "@xterm/addon-attach": "^0.11.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.7.2", "dompurify": "^3.2.6", "element-plus": "^2.7.7", "marked": "^11.0.0", "monaco-editor": "^0.52.2", "pinia": "^2.1.7", "shiki": "^3.7.0", "vue": "^3.4.31", "vue-router": "^4.4.0", "vue3-click-away": "^1.2.4"}, "devDependencies": {"@iconify-json/ep": "^1.2.2", "@iconify-json/ph": "^1.2.2", "@types/node": "^20.14.10", "@unocss/preset-attributify": "^0.61.3", "@unocss/preset-icons": "^0.61.3", "@unocss/preset-uno": "^0.61.3", "@vitejs/plugin-vue": "^5.0.5", "typescript": "^5.2.2", "unocss": "^0.61.3", "vite": "^5.3.3", "vue-tsc": "^2.0.24"}}