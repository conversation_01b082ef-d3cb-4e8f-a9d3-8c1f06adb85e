/**
 * Maps file extensions to Monaco Editor language IDs.
 * Based on common extensions.
 */
export const getLanguageFromPath = (path: string): string => {
  const extension = path.split('.').pop()?.toLowerCase() || '';

  switch (extension) {
    case 'js':
    case 'jsx':
      return 'javascript';
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'json':
      return 'json';
    case 'css':
      return 'css';
    case 'less':
      return 'less';
    case 'scss':
      return 'scss';
    case 'html':
      return 'html';
    case 'xml':
      return 'xml';
    case 'yaml':
    case 'yml':
      return 'yaml';
    case 'md':
      return 'markdown';
    case 'py':
      return 'python';
    case 'java':
      return 'java';
    case 'go':
      return 'go';
    case 'php':
      return 'php';
    case 'rb':
      return 'ruby';
    case 'sh':
      return 'shell';
    case 'sql':
      return 'sql';
    case 'dockerfile':
      return 'dockerfile';
    case 'vue':
      return 'html'; // Monaco doesn't have built-in Vue, use HTML for template part
    // Add more mappings as needed
    default:
      return 'plaintext';
  }
}; 