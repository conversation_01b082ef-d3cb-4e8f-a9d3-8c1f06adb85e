import axios from 'axios';
import { ElMessage } from 'element-plus';

// Create an Axios instance
const axiosInstance = axios.create({
  baseURL: '/api/v1', // Base URL for backend API endpoints
  timeout: 0, // No timeout - let the request run as long as needed
  headers: {
    'Content-Type': 'application/json',
    // You might add other default headers here, like Authorization if needed
  },
  withCredentials: true, // <-- Add this line to send cookies with cross-origin requests
});

// Optional: Add request interceptors (e.g., for adding auth tokens)
axiosInstance.interceptors.request.use(
  (config) => {
    // const token = localStorage.getItem('token'); // Example: Get token from storage
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptors for handling auth errors globally
axiosInstance.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    
    if (error.response?.status === 401 || error.response?.status === 403) {
      // Authentication failed or expired
      console.warn('Authentication failed:', {
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method
      });
      
      // Only redirect if not an auth profile check and not already on login page
      const isProfileCheck = error.config?.url?.includes('/auth/profile');
      const currentPath = window.location.pathname + window.location.search;
      const isLoginPage = currentPath.includes('/login');
      
      if (!isProfileCheck && !isLoginPage) {
        // Show error message
        ElMessage.error('请重新登录');
        
        // Redirect to login with current path as redirect parameter
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    } else {
      // Handle other error codes
    console.error('API Error:', error.response || error.message);
      
      // Show generic error message for other errors
      const errorMessage = error.response?.data?.message || error.message || '请求失败';
      ElMessage.error(errorMessage);
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance; 