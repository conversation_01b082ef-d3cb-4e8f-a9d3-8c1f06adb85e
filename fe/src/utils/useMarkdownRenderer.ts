import { ref, watchEffect } from 'vue';
import { marked } from 'marked';
import { codeToHtml } from 'shiki';
import DOMPurify from 'dompurify';

export function useMarkdownRenderer() {
  // We don't need a complex ready state, as the new API is more direct.
  // We'll use a simple ref to trigger re-renders when async highlight is done.
  const renderedContent = ref('');

  const formatMarkdown = async (markdown: string) => {
    if (!markdown) {
      renderedContent.value = '';
      return;
    }

    const renderer = new marked.Renderer();
    
    // The new shiki API is async, so marked's renderer needs to be async.
    // We can use `marked.parse` with async option.
    marked.use({
      async: true,
      renderer: renderer,
      gfm: true,
      breaks: false,
      pedantic: false,
      sanitize: false,
    });
    
    renderer.table = (header, body) => {
        return `<div class="table-wrapper"><table class="custom-table"><thead>${header}</thead><tbody>${body}</tbody></table></div>`;
    };

    // Override code block rendering to use shiki async
    renderer.code = (code, lang) => {
      const language = lang || 'plaintext';
      // This is a placeholder that will be replaced by the async highlight.
      // This part is tricky with sync renderer, so we rely on the main async parse.
      return `<pre data-lang="${language}"><code>${code.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</code></pre>`;
    };
    
    // First, parse the markdown into a basic HTML structure
    let html = await marked.parse(markdown) as string;

    // Find all code blocks and highlight them asynchronously
    const codeBlockRegex = /<pre data-lang="(\w+)"><code>([\s\S]*?)<\/code><\/pre>/g;
    const promises = [];
    
    html.replace(codeBlockRegex, (match, lang, code) => {
        const promise = codeToHtml(code.replace(/&lt;/g, "<").replace(/&gt;/g, ">"), {
            lang: lang,
            theme: 'github-dark'
        }).then(highlightedCode => {
            html = html.replace(match, highlightedCode);
        });
        promises.push(promise);
        return match;
    });

    await Promise.all(promises);

    renderedContent.value = DOMPurify.sanitize(html);
  };

  return {
    formatMarkdown,
    renderedContent,
  };
} 