<template>
  <router-view />
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 应用启动时初始化认证状态
onMounted(async () => {
  console.log('[App] Initializing authentication on app startup...')
  await userStore.initializeAuth()
  console.log('[App] Authentication initialized, user:', userStore.user?.username || 'Not logged in')
})
</script> 