import axiosInstance from '@/utils/request.js';
import type {
  AIModel,
  CreateAIModelRequest,
  UpdateAIModelRequest,
  CreateAIModelResponse,
  GetAIModelsResponse,
  GetAIModelResponse,
  AIModelOperationResponse
} from './types.js';

// Re-export types for backward compatibility
export type {
  AIModel,
  CreateAIModelRequest,
  UpdateAIModelRequest,
  CreateAIModelResponse,
  GetAIModelsResponse,
  GetAIModelResponse,
  AIModelOperationResponse
};

// ============== AI Model API Functions ==============

/**
 * Creates a new AI model configuration.
 * @param data - AI model creation data including api_key, api_base, and model_name.
 * @returns Promise resolving with the created AI model.
 */
export const createAIModel = async (data: CreateAIModelRequest): Promise<CreateAIModelResponse> => {
  const response = await axiosInstance.post<CreateAIModelResponse>('/aimodels', data);
  return response.data;
};

/**
 * Gets all AI models with optional filtering.
 * @param activeOnly - Optional flag to filter only active models.
 * @returns Promise resolving with AI models list.
 */
export const getAIModels = async (activeOnly?: boolean): Promise<GetAIModelsResponse> => {
  const params: Record<string, any> = {};
  if (activeOnly !== undefined) {
    params.active_only = activeOnly;
  }
  
  const response = await axiosInstance.get<GetAIModelsResponse>('/aimodels', { params });
  return response.data;
};

/**
 * Gets a specific AI model by ID.
 * @param id - AI model ID.
 * @returns Promise resolving with the AI model data.
 */
export const getAIModel = async (id: string): Promise<GetAIModelResponse> => {
  const response = await axiosInstance.get<GetAIModelResponse>(`/aimodels/${id}`);
  return response.data;
};

/**
 * Updates an existing AI model.
 * @param id - AI model ID.
 * @param data - Partial AI model data to update.
 * @returns Promise resolving with the operation result.
 */
export const updateAIModel = async (id: string, data: UpdateAIModelRequest): Promise<AIModelOperationResponse> => {
  const response = await axiosInstance.put<AIModelOperationResponse>(`/aimodels/${id}`, data);
  return response.data;
};

/**
 * Deletes an AI model.
 * @param id - AI model ID.
 * @returns Promise resolving when deletion is complete.
 */
export const deleteAIModel = async (id: string): Promise<AIModelOperationResponse> => {
  const response = await axiosInstance.delete<AIModelOperationResponse>(`/aimodels/${id}`);
  return response.data;
};

/**
 * Toggles the active status of an AI model.
 * @param id - AI model ID.
 * @returns Promise resolving with the operation result.
 */
export const toggleAIModelStatus = async (id: string): Promise<AIModelOperationResponse> => {
  const response = await axiosInstance.post<AIModelOperationResponse>(`/aimodels/${id}/toggle`);
  return response.data;
};