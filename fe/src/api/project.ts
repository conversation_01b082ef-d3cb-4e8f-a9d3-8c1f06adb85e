import axiosInstance from '@/utils/request.js';

// Define the expected structure for a project list item based on backend json tags
export interface ProjectListItem {
  id: string;           // Corresponds to json:"id,omitempty"
  project_id: string;   // Corresponds to json:"project_id"
  name?: string;        // Corresponds to json:"name,omitempty"
  description?: string; // Corresponds to json:"description,omitempty"
  created_at: string;   // Corresponds to json:"created_at" (expect ISO string)
  // Add other relevant fields based on backend json tags
}

/**
 * Fetches the list of projects for the currently authenticated user.
 * Assumes the backend endpoint is GET /api/v1/projects and requires authentication (session cookie).
 * @returns Promise resolving with an array of project list items.
 */
export const listUserProjects = (): Promise<ProjectListItem[]> => {
  return axiosInstance.get<ProjectListItem[]>('/projects') // Path relative to baseURL /api/v1
    .then(response => response.data);
};

// TODO: Add create project API call if it doesn't exist elsewhere
// Example:
// export interface CreateProjectPayload { projectId: string; }
// export const createProject = (data: CreateProjectPayload): Promise<any> => {
//   return axiosInstance.post('/projects', data).then(res => res.data);
// };

/**
 * Deletes a project by ID.
 * @param projectId - The ID of the project to delete.
 * @returns A promise that resolves when the project is successfully deleted.
 */
export const deleteProject = async (projectId: string): Promise<void> => {
  if (!projectId) {
    throw new Error('Project ID is required');
  }
  
  try {
    const response = await axiosInstance.delete(`/projects/${projectId}`);
    console.log('Project deleted successfully:', response.data);
  } catch (error: any) {
    console.error(`Error deleting project "${projectId}":`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to delete project: ${message}`);
  }
}; 