import axiosInstance from '@/utils/request.js'; // Assuming you have an axios instance setup
import type { User } from './types.js'; // Assuming User type is defined in types.ts

// Define interfaces for request payloads based on backend handler
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  email?: string;
  full_name?: string; // Match backend casing 'full_name'
}

// Define interface for the expected successful login/register response structure
// Based on backend handler's success response: { message: string, user: User }
interface AuthResponse {
  message: string;
  user: User; // Assuming User type matches the relevant fields returned
}

/**
 * Registers a new user.
 * @param data - Registration data including username and password.
 * @returns Promise resolving with the backend response.
 */
export const registerUser = (data: RegisterRequest): Promise<AuthResponse> => {
  return axiosInstance.post<AuthResponse>('/auth/register', data)
    .then(response => response.data);
};

/**
 * Logs in a user.
 * @param data - Login data including username and password.
 * @returns Promise resolving with the backend response.
 */
export const loginUser = (data: LoginRequest): Promise<AuthResponse> => {
  return axiosInstance.post<AuthResponse>('/auth/login', data)
    .then(response => response.data);
};

/**
 * Gets current user profile.
 * @returns Promise resolving with the current user data.
 */
export const getProfile = (): Promise<User> => {
  return axiosInstance.get<User>('/auth/profile')
    .then(response => response.data);
};

/**
 * Logs out the current user.
 * @returns Promise resolving when logout is complete.
 */
export const logoutUser = (): Promise<void> => {
  return axiosInstance.post('/auth/logout')
    .then(response => response.data);
}; 