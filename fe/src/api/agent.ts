import axiosInstance from '@/utils/request.js'
// @ts-ignore 
import { fetchEventSource, type EventSourceMessage } from '@microsoft/fetch-event-source';
import type { AxiosPromise } from 'axios';

export type MsgType = 'user_input' | 'assistant_output' | 'tool_result' | 'continue_content';

// Agent API response types
export interface AgentMessage {
  msg_id: string; // This will be the msg_id from backend
  role: 'user' | 'assistant' | 'system';
  content: string;
  msg_type?: MsgType;
  tool_calls?: ToolCall[];
  tool_results?: ToolResult[];
  timestamp: string;
  status?: 'streaming' | 'complete' | 'error';
}

export interface ToolCall {
  call_id: string
  mcp_service: string
  mcp_method: string
  mcp_params: Record<string, any>
}

export interface ToolResult {
  call_id: string
  content: string
}

export interface AgentConversation {
  conversation_id: string
  project_id: string
  messages: AgentMessage[]
  created_at: string
  updated_at: string
  message_count: number
}

export interface SendMessageRequest {
  message: string
  context?: {
    current_file?: string
    selection?: {
      file_path: string
      start_line: number
      end_line: number
      content: string
    }
    open_files?: string[]
  }
  retry_message_id?: string
}

// SendMessageResponse is no longer needed as POST directly streams SSE
// export interface SendMessageResponse {
//   message_id: string
//   stream_url: string
// }

export interface ExportConversationResponse {
  data: Blob;
  filename: string;
}

// SSE Event types for streaming

// NEW: Domain-level stream event marker type
export type StreamEventMarker = "domain_turn_start" | "domain_turn_end" | "start" | "end";

// NEW: Represents the structure of the 'chunk' coming from the backend's DomainStreamChunk
export interface DomainStreamChunkFromServer {
  Delta?: string | null;
  ConversationID: string;
  MessageID: string; // Domain-level message ID
  Role?: 'user' | 'assistant' | 'system';
  MsgType?: MsgType;
  Marker?: StreamEventMarker | null;
  Error?: { message: string } | string | null; // Match backend error structure more closely
  // ToolCall and ToolResult are not expected as top-level fields in the chunk itself from SSE.
  // They would be parsed from Delta's XML content by the frontend if needed.
}

// NEW: Specific data structures for each SSE event type
export interface SSEConnectedData {
  message: string; // Or any other structure backend sends for 'connected'
  // session_id?: string; // Example if backend sends it
}

export interface SSEKeepAliveData {
  timestamp: number;
}

export interface SSEMessageStartData {
  message_id: string;      // This will be the first Domain-level MessageID of the turn
  conversation_id: string;
}

export interface SSEContentDeltaData {
  chunk: DomainStreamChunkFromServer; // Contains the actual delta and domain-level message_id
}

export interface SSEMessageCompleteData {
  message_id: string;      // Matches the message_id from SSEMessageStartData
}

export interface SSEErrorData {
  message: string;
  code?: string;
  message_id?: string;   // Optional: associates error with a specific message stream
}

// NEW: Central map for SSE event types to their data structures
export interface SSEChatEventDataMap {
  connected: SSEConnectedData;
  keepalive: SSEKeepAliveData;
  message_start: SSEMessageStartData;
  content_delta: SSEContentDeltaData;
  message_complete: SSEMessageCompleteData;
  error: SSEErrorData;
  // conversation_complete is in the old SSEChatEvent, keeping it commented for now
  // unless backend explicitly sends it.
  // conversation_complete: { some_data: string };

  // Removed 'tool_call' and 'tool_result' as top-level event types.
  // These are now expected to be handled via XML parsing within content_delta.
}

// REFACTORED: SSEChatEvent to be more type-safe using the map
export type SSEChatEvent = {
  [K in keyof SSEChatEventDataMap]: {
    type: K;
    data: SSEChatEventDataMap[K];
  }
}[keyof SSEChatEventDataMap];

/**
 * Get or create conversation for a project
 */
export function getProjectConversation(projectId: string): Promise<AgentConversation> {
  return axiosInstance.get(`/projects/${projectId}/conversation`)
    .then(response => response.data)
}

/**
 * (Old) Send a message to the agent - this will be replaced or unused
 */
// export function sendMessage(projectId: string, data: SendMessageRequest): Promise<SendMessageResponse> {
//   return axiosInstance.post(`/projects/${projectId}/conversation/messages`, data)
//     .then(response => response.data)
// }

/**
 * Clear conversation messages
 */
export function clearConversation(projectId: string): Promise<{ success: boolean }> {
  return axiosInstance.delete(`/projects/${projectId}/conversation/messages`)
    .then(response => response.data)
}

/**
 * Export conversation in specified format
 */
export function exportConversation(
  projectId: string, 
  format: 'markdown' | 'json' | 'txt' = 'markdown'
): Promise<ExportConversationResponse> {
  return axiosInstance.get(`/projects/${projectId}/conversation/export?format=${format}`, {
    responseType: 'blob', // Critical: set responseType to blob
  })
    .then(response => {
      const contentDisposition = response.headers['content-disposition'];
      let filename = `conversation_export.${format}`; // Default filename

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;\n]*?)['"]?$/i);
        if (filenameMatch && filenameMatch[1]) {
          try {
            filename = decodeURIComponent(filenameMatch[1].replace(/^utf-8''/i, ''));
          } catch (e) {
            console.warn('Failed to decode filename from Content-Disposition, using raw:', filenameMatch[1]);
            filename = filenameMatch[1];
          }
        }
      }
      return {
        data: response.data, // response.data is now a Blob
        filename: filename,
      };
    });
}

/**
 * (Old) Create EventSource for streaming agent responses - This is removed
 */
// export function createAgentStream(projectId: string): EventSource { ... }


// NEW: Function to send message and handle SSE stream via POST
const baseURL = import.meta.env.VITE_API_BASE_URL || ''; // Ensure your VITE_API_BASE_URL is set, e.g., /api/v1 or http://localhost:8080/api/v1

export interface FetchEventSourceController {
  abort: () => void;
}

export function sendMessageAndStream(
  projectId: string,
  requestData: SendMessageRequest,
  onOpen: (response: Response) => Promise<void> | void, // Explicitly type response
  onMessage: (event: EventSourceMessage) => void, 
  onError: (error: any) => void, 
  onClose?: () => void 
): FetchEventSourceController {
  const ctrl = new AbortController();

  const endpointURL = `${baseURL}/projects/${projectId}/conversation/messages`;
  console.log(`[API] Attempting POST SSE to: ${endpointURL}`);

  fetchEventSource(endpointURL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestData),
    signal: ctrl.signal,
    credentials: 'include',
    
    async onopen(response: Response) { // Explicitly type response
      if (response.ok) {
        console.log('[API] SSE connection opened via POST');
        await onOpen(response); // Pass response to callback
      } else {
        let errorPayload = { message: `Failed to open SSE connection: ${response.status} ${response.statusText}` };
        try {
          errorPayload = await response.json();
        } catch (e) {
          // Ignore if response is not JSON
        }
        onError(new Error(errorPayload.message));
      }
    },
    onmessage(msg: EventSourceMessage) { // Explicitly type msg
      console.log(`[API] Raw SSE message (from POST): type='${msg.event}', data=${msg.data}`);
      onMessage(msg);
    },
    onclose() {
      console.log('[API] SSE connection closed (POST)');
      if (onClose) {
        onClose();
      }
    },
    onerror(err: any) { // Explicitly type err or use a more specific error type if known
      console.error('[API] SSE error (POST fetchEventSource):', err);
      onError(err);
      throw err; 
    }
  });

  return {
    abort: () => {
      console.log('[API] Aborting SSE connection (POST)');
      ctrl.abort();
    }
  };
} 