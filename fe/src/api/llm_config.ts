import axiosInstance from '@/utils/request.js';

// Interface for LLMConfig based on backend model.llm.LLMConfig
export interface LLMConfig {
  id: string;
  name: string;
  type: string;
  api_key: string; // Sensitive, but needed for form submission
  api_base: string;
  model_name: string;
  created_at: string; // ISO string
  updated_at: string; // ISO string
  is_active?: boolean; // Added for frontend display, set by backend's mapLLMConfigToResponse
}

// Request DTOs
export interface CreateLLMConfigRequest {
  name: string;
  type: string;
  api_key: string;
  api_base: string;
  model_name: string;
}

export interface UpdateLLMConfigRequest {
  name?: string;
  type?: string;
  api_key?: string;
  api_base?: string;
  model_name?: string;
}

// Response DTOs
export interface CreateLLMConfigResponse {
  message: string;
  id: string;
}

export interface SetActiveLLMConfigResponse {
  message: string;
}

/**
 * Fetches a list of all LLM configurations. (Admin only)
 */
export const listLLMConfigs = async (): Promise<LLMConfig[]> => {
  const response = await axiosInstance.get<LLMConfig[]>('/admin/llm_configs');
  return response.data;
};

/**
 * Creates a new LLM configuration. (Admin only)
 * @param data - The LLM configuration details.
 */
export const createLLMConfig = async (data: CreateLLMConfigRequest): Promise<CreateLLMConfigResponse> => {
  const response = await axiosInstance.post<CreateLLMConfigResponse>('/admin/llm_configs', data);
  return response.data;
};

/**
 * Fetches a single LLM configuration by ID. (Admin only)
 * @param id - The ID of the LLM configuration.
 */
export const getLLMConfig = async (id: string): Promise<LLMConfig> => {
  const response = await axiosInstance.get<LLMConfig>(`/admin/llm_configs/${id}`);
  return response.data;
};

/**
 * Updates an existing LLM configuration. (Admin only)
 * @param id - The ID of the LLM configuration to update.
 * @param data - The fields to update.
 */
export const updateLLMConfig = async (id: string, data: UpdateLLMConfigRequest): Promise<void> => {
  await axiosInstance.put(`/admin/llm_configs/${id}`, data);
};

/**
 * Deletes an LLM configuration. (Admin only)
 * @param id - The ID of the LLM configuration to delete.
 */
export const deleteLLMConfig = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/admin/llm_configs/${id}`);
};

/**
 * Sets a specific LLM configuration as active. (Admin only)
 * @param id - The ID of the LLM configuration to set as active.
 */
export const setActiveLLMConfig = async (id: string): Promise<SetActiveLLMConfigResponse> => {
  const response = await axiosInstance.post<SetActiveLLMConfigResponse>(`/admin/llm_configs/${id}/set_active`);
  return response.data;
}; 