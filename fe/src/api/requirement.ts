import axiosInstance from '@/utils/request.js';
import type {
  Question,
  Answer,
  ReferenceImage,
  Requirement,
  CreateRequirementRequest,
  CreateRequirementWithImagesRequest,
  UpdateRequirementRequest,
  AnswerSubmission,
  SubmitAnswersRequest,
  CreateRequirementResponse,
  GetRequirementsResponse,
  GetRequirementResponse,
  GenerateQuestionsResponse,
  SubmitAnswersResponse,
  RequirementOperationResponse
} from './types.js';

// Re-export types for backward compatibility
export type {
  Question,
  Answer,
  ReferenceImage,
  Requirement,
  CreateRequirementRequest,
  CreateRequirementWithImagesRequest,
  UpdateRequirementRequest,
  AnswerSubmission,
  SubmitAnswersRequest,
  CreateRequirementResponse,
  GetRequirementsResponse,
  GetRequirementResponse,
  GenerateQuestionsResponse,
  SubmitAnswersResponse,
  RequirementOperationResponse
};

// ============== API Functions ==============

/**
 * Creates a new requirement.
 * @param data - Requirement creation data including title and content.
 * @returns Promise resolving with the created requirement.
 */
export const createRequirement = (data: CreateRequirementRequest): Promise<CreateRequirementResponse> => {
  return axiosInstance.post<CreateRequirementResponse>('/requirements', data)
    .then(response => response.data);
};

/**
 * Gets user requirements with pagination and optional status filtering.
 * @param page - Page number (default: 1).
 * @param limit - Items per page (default: 10, max: 100).
 * @param status - Optional status filter (pending/processing/completed).
 * @returns Promise resolving with requirements list and pagination info.
 */
export const getRequirements = (
  page: number = 1, 
  limit: number = 10, 
  status?: string
): Promise<GetRequirementsResponse> => {
  const params: Record<string, any> = { page, limit };
  if (status) {
    params.status = status;
  }
  
  return axiosInstance.get<GetRequirementsResponse>('/requirements', { params })
    .then(response => response.data);
};

/**
 * Gets a specific requirement by ID.
 * @param id - Requirement ID.
 * @returns Promise resolving with the requirement data.
 */
export const getRequirement = (id: string): Promise<GetRequirementResponse> => {
  return axiosInstance.get<GetRequirementResponse>(`/requirements/${id}`)
    .then(response => response.data);
};

/**
 * Generates AI questions for a specific requirement.
 * @param id - Requirement ID.
 * @returns Promise resolving with the updated requirement containing questions.
 */
export const generateQuestions = (id: string): Promise<GenerateQuestionsResponse> => {
  return axiosInstance.post<GenerateQuestionsResponse>(`/requirements/${id}/questions`)
    .then(response => response.data);
};

/**
 * Submits answers to AI-generated questions.
 * @param id - Requirement ID.
 * @param answers - Array of answers to submit.
 * @returns Promise resolving with the updated requirement.
 */
export const submitAnswers = (id: string, answers: AnswerSubmission[]): Promise<SubmitAnswersResponse> => {
  const data: SubmitAnswersRequest = { answers };
  return axiosInstance.post<SubmitAnswersResponse>(`/requirements/${id}/answers`, data)
    .then(response => response.data);
};

/**
 * Updates an existing requirement.
 * @param id - Requirement ID.
 * @param data - Partial requirement data to update.
 * @returns Promise resolving with the operation result.
 */
export const updateRequirement = (id: string, data: UpdateRequirementRequest): Promise<RequirementOperationResponse> => {
  return axiosInstance.put<RequirementOperationResponse>(`/requirements/${id}`, data)
    .then(response => response.data);
};

/**
 * Deletes a requirement.
 * @param id - Requirement ID.
 * @returns Promise resolving when deletion is complete.
 */
export const deleteRequirement = (id: string): Promise<RequirementOperationResponse> => {
  return axiosInstance.delete<RequirementOperationResponse>(`/requirements/${id}`)
    .then(response => response.data);
};

/**
 * Generates an interactive HTML wireframe for a requirement based on its development plan.
 * @param id - Requirement ID.
 * @returns Promise resolving with the updated requirement containing wireframe HTML.
 */
export const generateWireframe = (id: string): Promise<SubmitAnswersResponse> => {
  return axiosInstance.post<SubmitAnswersResponse>(`/requirements/${id}/wireframe`)
    .then(response => response.data);
};

/**
 * Creates a new requirement with reference images.
 * @param data - Requirement creation data including title, content, model_name, and image files.
 * @returns Promise resolving with the created requirement.
 */
export const createRequirementWithImages = (data: CreateRequirementWithImagesRequest): Promise<CreateRequirementResponse> => {
  console.log('[API] Creating requirement with images:', {
    title: data.title,
    content: data.content?.substring(0, 100) + '...',
    model_name: data.model_name,
    imageCount: data.images.length,
    imageSizes: data.images.map(img => ({ name: img.name, size: img.size, type: img.type }))
  });

  // Validate files before creating FormData
  for (const file of data.images) {
    if (!file || file.size === 0) {
      console.error('[API] Invalid file detected:', file);
      return Promise.reject(new Error('Invalid file detected'));
    }
    if (!file.type.startsWith('image/')) {
      console.error('[API] Non-image file detected:', file);
      return Promise.reject(new Error('Only image files are allowed'));
    }
  }

  // Create FormData for multipart/form-data request
  const formData = new FormData();
  formData.append('title', data.title);
  formData.append('content', data.content);
  
  // Append model_name if provided
  if (data.model_name) {
    formData.append('model_name', data.model_name);
  }
  
  // Append each image file with validation
  data.images.forEach((file: File, index: number) => {
    console.log(`[API] Appending image ${index}: ${file.name} (${file.size} bytes)`);
    formData.append('images', file);
  });

  console.log('[API] FormData created, sending request...');

  return axiosInstance.post<CreateRequirementResponse>('/requirements/with-images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60000, // 增加超时时间到60秒
  }).then(response => {
    console.log('[API] Request successful:', response.data);
    return response.data;
  }).catch(error => {
    console.error('[API] Request failed:', error);
    throw error;
  });
};

/**
 * Creates a new requirement with unified interface supporting images and website URL.
 * @param formData - FormData containing title, content, model_name, optional images and website_url.
 * @returns Promise resolving with the created requirement.
 */
export const createRequirementUnified = (formData: FormData): Promise<CreateRequirementResponse> => {
  console.log('[API] Creating requirement with unified interface');

  return axiosInstance.post<CreateRequirementResponse>('/requirements/unified', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60000, // 60秒超时
  }).then(response => {
    console.log('[API] Unified request successful:', response.data);
    return response.data;
  }).catch(error => {
    console.error('[API] Unified request failed:', error);
    throw error;
  });
};