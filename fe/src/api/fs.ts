import axiosInstance from '@/utils/request.js'; // <-- Use the shared instance
import type { FileSystemObject, CreateProjectPayload, CreateProjectResponse } from './types.js'; // Assuming types moved to types.ts or similar

// TODO: Get base URL from config/env
const API_BASE_URL = 'http://localhost:8080/api/v1'; // Assuming backend runs on 8080

// Define the payload structure expected by the backend for project creation
interface CreateProjectBackendPayload {
  project_id: string; // Matches backend json tag
  name?: string;      // Matches backend json tag, send if applicable
  description?: string; // Optional
}

// --- Exported API Functions ---

/**
 * Lists the content of a directory within a specific project.
 * @param projectID - The ID of the project.
 * @param path - The directory path relative to the project root (e.g., '' for root, 'src', 'src/components').
 * @returns A promise that resolves to an array of file system objects.
 */
export const listDirectory = async (projectID: string, path: string = ""): Promise<FileSystemObject[]> => {
  if (!projectID) throw new Error('Project ID is required for listing directory.');
  try {
    // Pass relative path directly, backend DefaultQuery handles "." for empty string
    const response = await axiosInstance.get(`/projects/${projectID}/files`, {
      params: { path: path }, // Pass path directly ("" for root)
    });
    return response.data || [];
  } catch (error) {
    console.error(`Error listing directory '${path || '<root>'}' in project '${projectID}':`, error);
    throw error;
  }
};

/**
 * Reads the content of a file within a specific project.
 * @param projectID - The ID of the project.
 * @param path - The file path relative to the project root (e.g., 'main.go'). Cannot be empty.
 * @returns A promise that resolves to the decoded file content as a string.
 */
export const readFileContent = async (projectID: string, path: string): Promise<string> => {
  if (!projectID) throw new Error('Project ID is required for reading file.');
  // Path cannot be empty for a file
  if (!path) {
     throw new Error('Invalid file path provided to readFileContent. Path cannot be empty.');
  }
  try {
    console.log(`[API readFileContent] Fetching: Project=${projectID}, Path=${path}`);
    const response = await axiosInstance.get<{ content: string }>(`/projects/${projectID}/files/content`, {
      params: { path: path },
    });
    const base64Content = response.data?.content;
    console.log(`[API readFileContent] Received base64 content (length: ${base64Content?.length}):`, base64Content?.substring(0, 50) + '...'); // Log first 50 chars
    if (typeof base64Content !== 'string') {
        console.error('[API readFileContent] Invalid response format, content is not a string.');
        throw new Error('Invalid response format from server for file content.');
    }
    try {
      // 使用 TextDecoder 来正确解码 UTF-8 内容
      const binaryString = atob(base64Content);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const decoder = new TextDecoder('utf-8');
      const decodedContent = decoder.decode(bytes);
      console.log(`[API readFileContent] Decoded content (length: ${decodedContent.length}):`, decodedContent.substring(0, 100) + '...'); // Log first 100 chars
      return decodedContent;
    } catch (decodeError) {
       console.error('[API readFileContent] Error decoding base64 content:', decodeError);
       throw new Error('Failed to decode file content.');
    }
  } catch (error) {
    console.error(`[API readFileContent] Error fetching/processing file content for '${path}':`, error);
    throw error;
  }
};

/**
 * Creates a new project.
 * @param projectName - The desired name/ID for the new project.
 * @returns A promise that resolves on successful creation (content depends on backend).
 */
export const createProject = async (projectName: string): Promise<CreateProjectResponse | void> => {
  const validPattern = /^[a-zA-Z0-9-_]+$/;
  if (!projectName || !validPattern.test(projectName)) {
      throw new Error('Invalid project name. Use only letters, numbers, hyphens, underscores.');
  }
  // Construct the payload matching the backend struct with json tags
  const payload: CreateProjectBackendPayload = {
     project_id: projectName,
     name: projectName // Assuming name is same as project_id on creation
  };
  try {
    const response = await axiosInstance.post<CreateProjectResponse>(`/projects`, payload);
    return response.data;
  } catch (error: any) {
    console.error(`Error creating project "${projectName}":`, error);
    const message = error.response?.data?.message || error.message || 'Unknown server error';
    throw new Error(`Failed to create project: ${message}`);
  }
};

/**
 * Rebuilds the runtime environment for a project.
 * @param projectId - The ID of the project whose runtime should be rebuilt.
 * @returns A promise that resolves on successful rebuild.
 */
export const rebuildProjectRuntime = async (projectId: string): Promise<void> => {
  if (!projectId) {
    throw new Error('Project ID is required');
  }
  
  try {
    const response = await axiosInstance.post(`/projects/${projectId}/runtime/rebuild`);
    console.log('Runtime rebuilt successfully:', response.data);
  } catch (error: any) {
    console.error(`Error rebuilding runtime for project "${projectId}":`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to rebuild runtime: ${message}`);
  }
};

/**
 * Creates a new file or directory within a project.
 * @param projectID - The ID of the project.
 * @param path - The relative path for the new file/directory (e.g., 'newfile.txt' or 'src'). Cannot be empty.
 * @param isDir - True if creating a directory, false for a file.
 * @param content - Optional base64 encoded content for the new file.
 * @returns A promise that resolves to the FileSystemObject of the created item.
 */
export const createFSObject = async (
  projectID: string,
  path: string, // Expecting relative path now
  isDir: boolean,
  content?: string // Optional base64 content, should be encoded by caller
): Promise<FileSystemObject> => {
  if (!projectID) throw new Error('Project ID is required for creating FS object.');
  // Path cannot be empty
  if (!path) {
    throw new Error('Invalid path provided. Path cannot be empty.');
  }

  const payload = {
    path: path, // Send relative path directly
    is_dir: isDir,
    content: isDir ? undefined : content // Send content only for files
  };

  try {
    const response = await axiosInstance.post<FileSystemObject>(`/projects/${projectID}/files`, payload);
    return response.data;
  } catch (error: any) {
    const itemType = isDir ? 'directory' : 'file';
    console.error(`Error creating ${itemType} at '${path}' in project '${projectID}':`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to create ${itemType}: ${message}`);
  }
};

/**
 * Deletes a file or directory within a project.
 * @param projectID - The ID of the project.
 * @param path - The relative path of the object to delete.
 * @returns A promise that resolves on successful deletion.
 */
export const deleteFSObject = async (projectID: string, path: string): Promise<void> => {
  if (!projectID) throw new Error('Project ID is required for deleting FS object.');
  if (!path) throw new Error('Path is required for deleting FS object.');

  try {
    console.log(`[API deleteFSObject] Deleting: Project=${projectID}, Path=${path}`);
    // Path is sent as a query parameter for DELETE
    await axiosInstance.delete(`/projects/${projectID}/files`, {
      params: { path: path },
    });
    console.log(`[API deleteFSObject] Successfully deleted: ${path}`);
  } catch (error: any) {
    console.error(`Error deleting '${path}' in project '${projectID}':`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to delete: ${message}`);
  }
};

/**
 * Renames or moves a file or directory within a project.
 * @param projectID - The ID of the project.
 * @param oldPath - The current relative path of the object.
 * @param newPath - The desired new relative path of the object.
 * @returns A promise that resolves on successful rename/move.
 */
export const renameFSObject = async (projectID: string, oldPath: string, newPath: string): Promise<void> => {
  if (!projectID) throw new Error('Project ID is required for renaming FS object.');
  if (!oldPath) throw new Error('Old path is required for renaming.');
  if (!newPath) throw new Error('New path is required for renaming.');
  if (oldPath === newPath) throw new Error('Old path and new path cannot be the same.');

  const payload = {
    old_path: oldPath, // Changed from oldPath to old_path
    new_path: newPath, // Changed from newPath to new_path
  };

  try {
    console.log(`[API renameFSObject] Renaming: Project=${projectID}, From=${oldPath}, To=${newPath}, Payload:`, payload);
    // Send paths in the request body for PUT
    await axiosInstance.put(`/projects/${projectID}/files/rename`, payload);
    console.log(`[API renameFSObject] Successfully renamed \'${oldPath}\' to \'${newPath}\'`);
  } catch (error: any) {
    console.error(`Error renaming \'${oldPath}\' to \'${newPath}\' in project \'${projectID}\':`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to rename: ${message}`);
  }
};

// Helper function to encode string to Base64
function encodeToBase64(text: string): string {
  try {
    // 使用 TextEncoder 来正确编码 UTF-8 内容
    const encoder = new TextEncoder();
    const bytes = encoder.encode(text);
    let binaryString = '';
    bytes.forEach((byte) => {
      binaryString += String.fromCharCode(byte);
    });
    return btoa(binaryString);
  } catch (e) {
    console.error("Failed to encode to Base64:", e);
    throw new Error('Failed to encode file content to Base64.');
  }
}

/**
 * Writes content to a file within a specific project.
 * The content will be Base64 encoded before sending to the backend.
 * @param projectID - The ID of the project.
 * @param path - The file path relative to the project root.
 * @param plainTextContent - The raw string content of the file.
 * @returns A promise that resolves on successful write.
 */
export const saveFileContent = async (projectID: string, path: string, plainTextContent: string): Promise<void> => {
  if (!projectID) throw new Error('Project ID is required for saving file.');
  if (!path) throw new Error('File path is required for saving file.');
  if (typeof plainTextContent !== 'string') throw new Error('Content must be a string for saving file.');

  const encodedContent = encodeToBase64(plainTextContent);

  const payload = {
    content: encodedContent, // Backend expects a JSON object with a "content" field
  };

  try {
    console.log(`[API saveFileContent] Saving: Project=${projectID}, Path=${path}, EncodedContentLength=${encodedContent.length}`);
    // Path is sent as a query parameter, content in the body for PUT
    await axiosInstance.put(`/projects/${projectID}/files/content`, payload, {
      params: { path: path },
    });
    console.log(`[API saveFileContent] Successfully saved: ${path}`);
  } catch (error: any) {
    console.error(`Error saving file \'${path}\' in project \'${projectID}\':`, error);
    const message = error.response?.data?.error || error.message || 'Unknown server error';
    throw new Error(`Failed to save file: ${message}`);
  }
};

// TODO: Add functions for writeFile, deleteFSObject, renameFSObject later

// Consider moving interfaces to a separate types file (e.g., src/api/types.ts)
// export interface FileSystemObject { ... }
// export interface CreateProjectPayload { ... }
// export interface CreateProjectResponse { ... } 