/**
 * Represents a file or directory in the project file system.
 */
export interface FileSystemObject {
  name: string;
  path: string; // Relative path from project root (e.g., "main.go" or "src/component.ts")
  is_dir: boolean;
  size?: number; // Optional size in bytes
  modified?: string; // Optional last modified timestamp (ISO 8601 format)
  children?: FileSystemObject[]; // Optional: for eagerly loaded trees, or use lazy loading
}



/**
 * Response structure for the create project API.
 */
export interface CreateProjectResponse {
  message: string;
  // projectID?: string; // Backend currently doesn't explicitly return this on create
}

/**
 * Represents a user object, mirroring the backend `model.User` structure.
 * Fields are based on the actual JSON response from backend (snake_case format).
 */
export interface User {
  id: string;                // Corresponds to _id (primitive.ObjectID)
  username: string;
  email?: string;            // Optional based on omitempty
  // password_hash is sensitive and usually not sent to the frontend
  full_name?: string;        // Optional based on omitempty
  avatar_url?: string;       // Optional based on omitempty
  is_active: boolean;
  is_admin: boolean;
  created_at: string;        // Corresponds to time.Time, expect ISO string
  last_updated_at: string;   // Corresponds to time.Time, expect ISO string
  last_login_at?: string;    // Optional based on omitempty, expect ISO string or null
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  // Add other project-specific fields here if needed
}

export interface RecentProject {
  id: string;
  name: string;
  path: string; 
  lastAccessed: string; 
}

// --- API Request Payloads ---

export interface CreateProjectPayload {
  name: string;
  description?: string;
  // Potentially other fields like git repository, template, etc.
}

export interface CreateFSObjectPayload {
  name: string;
  path: string; // Parent path, name will be appended
  is_dir: boolean;
}

export interface RenameFSObjectPayload {
  newName: string;
}

export interface WriteFilePayload {
  content: string; // Base64 encoded content
}

// --- API Response Payloads (examples, adjust as needed) ---

export interface ListDirectoryResponse extends Array<FileSystemObject> {}

export interface ReadFileResponse {
  content: string; // Base64 encoded content
  name: string;
  path: string;
  size: number;
  modified: string;
} 

// --- File System Watching Types ---

export type FileChangeEventType = 'create' | 'modify' | 'delete' | 'rename';

export interface FileChangeEvent {
  type: FileChangeEventType;
  path: string; // Relative path from project root
  old_path?: string; // For rename events, the original path
  is_dir: boolean;
  timestamp: string; // ISO timestamp
}

export interface FileSystemWatchSSEEvent {
  type: 'file_change' | 'error' | 'connected';
  data: FileChangeEvent | { message: string };
}

// --- Image Analysis Types ---

export interface VisualElementsAnalysis {
  main_subjects: string[];
  composition: string;
  color_palette: string[];
  lighting_style: string;
  lines_shapes: string;
  texture_pattern: string;
}

export interface TechnicalStyleAnalysis {
  medium: string;
  artistic_style: string;
  technique: string;
  quality: string;
}

export interface EmotionalToneAnalysis {
  overall_mood: string;
  emotional_tone: string;
  sensory_impact: string;
  energy_level: string;
}

export interface NarrativeSymbolicAnalysis {
  narrative: string;
  symbols_metaphors: string[];
  thematic_content: string;
  cultural_context: string;
}

export interface BusinessDimensionAnalysis {
  business_direction: string;
  business_function: string;
  business_process: string;
}

export interface ImageAnalysis {
  visual_elements: VisualElementsAnalysis;
  technical_style: TechnicalStyleAnalysis;
  emotional_tone: EmotionalToneAnalysis;
  narrative_symbolic: NarrativeSymbolicAnalysis;
  business_dimension: BusinessDimensionAnalysis;
  summary: string;
}

export interface ReferenceImage {
  id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  analysis: ImageAnalysis;
  uploaded_at: string;
  analyzed_at?: string;
} 

// --- Requirement Related Types ---

export interface Question {
  id: string;
  text: string;
  type: string;
  options: string[];
  category: string;
  created_at: string;
}

export interface Answer {
  question_id: string;
  selected_index: number;
  text: string;
  answered_at: string;
}

export interface Requirement {
  id: string;
  user_id: string;
  title: string;
  content: string;
  model_name: string;
  reference_images: ReferenceImage[];
  questions: Question[];
  answers: Answer[];
  development_plan: string;
  wireframe_html: string; // Legacy
  wireframes: Wireframe[];
  status: RequirementStatus;
  created_at: string;
  updated_at: string;
}

export type RequirementStatus = 
  | 'pending'
  | 'image_analyzing'
  | 'processing'
  | 'plan_generating'
  | 'wireframe_generating'
  | 'completed';

export interface Wireframe {
  id: string;
  title: string;
  description: string;
  html: string;
  generated_at: string;
}

// --- Request Interfaces ---

/**
 * Request payload for creating a new requirement.
 */
export interface CreateRequirementRequest {
  title: string;
  content: string;
  model_name: string;
}

/**
 * Request payload for creating a requirement with reference images.
 */
export interface CreateRequirementWithImagesRequest {
  title: string;
  content: string;
  model_name: string;
  images: File[]; // Array of image files to upload
}

/**
 * Represents the data required to update an existing requirement.
 */
export interface UpdateRequirementRequest {
  title?: string;
  content?: string;
  development_plan?: string;
}

/**
 * Represents a single answer submission in the request.
 */
export interface AnswerSubmission {
  question_id: string;
  selected_index: number; // Index of selected option for choice questions (0-based)
  text: string; // User's answer text (for backward compatibility)
}

/**
 * Request payload for submitting answers to AI-generated questions.
 */
export interface SubmitAnswersRequest {
  answers: AnswerSubmission[];
}

// --- AI Model Related Types ---

/**
 * Represents an AI model configuration.
 */
export interface AIModel {
  id: string;
  api_key: string;
  api_base: string;
  model_name: string;
  is_active: boolean;
  created_at: string; // ISO string timestamp
  updated_at: string; // ISO string timestamp
}

/**
 * Request payload for creating an AI model.
 */
export interface CreateAIModelRequest {
  api_key: string;
  api_base: string;
  model_name: string;
  is_active?: boolean;
}

/**
 * Request payload for updating an AI model.
 */
export interface UpdateAIModelRequest {
  api_key?: string;
  api_base?: string;
  model_name?: string;
  is_active?: boolean;
}

/**
 * Response structure for AI model creation.
 */
export interface CreateAIModelResponse {
  message: string;
  model: AIModel;
}

/**
 * Response structure for getting AI models list.
 */
export interface GetAIModelsResponse {
  models: AIModel[];
  count: number;
}

/**
 * Response structure for getting a single AI model.
 */
export interface GetAIModelResponse {
  model: AIModel;
}

/**
 * Response structure for AI model operations.
 */
export interface AIModelOperationResponse {
  message: string;
}

// --- Response Interfaces ---

/**
 * Response structure for requirement creation.
 */
export interface CreateRequirementResponse {
  message: string;
  requirement: Requirement;
}

/**
 * Response structure for getting requirements list.
 */
export interface GetRequirementsResponse {
  requirements: Requirement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

/**
 * Response structure for getting a single requirement.
 */
export interface GetRequirementResponse {
  requirement: Requirement;
}

/**
 * Response structure for generating questions.
 */
export interface GenerateQuestionsResponse {
  message: string;
  requirement: Requirement;
}

/**
 * Response structure for submitting answers.
 */
export interface SubmitAnswersResponse {
  message: string;
  requirement: Requirement;
}

/**
 * Response structure for requirement operations.
 */
export interface RequirementOperationResponse {
  message: string;
} 