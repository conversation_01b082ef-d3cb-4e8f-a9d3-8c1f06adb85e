<template>
  <div class="file-browser h-full flex flex-col bg-[#29292e] text-white">
    <!-- Toolbar -->
    <div class="toolbar flex items-center p-1.5 border-b border-[#4d4d54] flex-shrink-0">
      <!-- Refresh button -->
       <button 
        @click="refreshRootDirectory"
        :disabled="fsStore.isLoadingTree('') || isRefreshing"
        class="ml-auto px-1 py-0.5 hover:bg-[#6a6a6d] rounded text-gray-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        title="Refresh"
      >
          <span 
            :class="[
              'w-3.5 h-3.5',
              (fsStore.isLoadingTree('') || isRefreshing) ? 'i-ep-loading animate-spin' : 'i-ep-refresh'
            ]"
          ></span>
      </button>
      
      <!-- Auto-refresh indicator -->
      <div v-if="enableAutoRefresh && autoRefreshInterval" class="ml-2 flex items-center space-x-1" title="Auto-refresh enabled (30s)">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-xs text-gray-400">Auto</span>
      </div>
    </div>

    <!-- Tree View - Added contextmenu listener to container -->
    <div 
      class="tree-view flex-grow overflow-auto h-full"
      @contextmenu.prevent="openContextMenuOnContainer($event)"
    >
      <el-tree
        :data="treeData"
        :props="defaultProps"
        node-key="path" 
        :default-expanded-keys="expandedKeys"
        :load="loadNode"
        lazy
        ref="treeRef"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        class="bg-transparent file-tree"
      >
        <template #default="{ node, data }">
          <!-- Context menu listener is on the node span, stop propagation -->
          <span 
            class="custom-tree-node flex items-center w-full text-sm"
            @contextmenu.prevent.stop="openContextMenu($event, data)"
          >
            <span :class="[data.is_dir ? 'i-ep-folder text-[#f2cb61]' : 'i-ep-document text-[#cccccc]', 'w-4 h-4 mr-1.5 flex-shrink-0']"></span>
            <span class="flex-1">{{ node.label }}</span>
            <!-- HTML Preview Icon -->
            <button
              v-if="!data.is_dir && data.name.toLowerCase().endsWith('.html')"
              @click.stop="previewHtmlFile(data)"
              class="ml-2 w-4 h-4 flex items-center justify-center hover:bg-[#3a3a3f] rounded transition-colors duration-200"
              title="Preview HTML"
            >
              <span class="i-ep-view w-3 h-3 text-blue-400 hover:text-blue-300"></span>
            </button>
          </span>
        </template>
      </el-tree>
    </div>
    
    <!-- Context Menu -->
    <div 
      v-if="contextMenu.visible"
      class="context-menu absolute bg-[#2c2c32] border border-[#44444a] rounded-md shadow-xl py-1.5 z-50 min-w-[150px] text-sm"
      :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
      v-click-away="closeContextMenu" 
    >
      <ul>
        <!-- Rename and Delete only show if targetNode is not null -->
        <template v-if="contextMenu.targetNode">
          <li class="flex items-center px-3 py-1.5 hover:bg-[#3e3e44] cursor-pointer text-gray-200 hover:text-white" @click="handleContextMenuClick('rename')">
            <span class="i-mdi-rename-box w-4 h-4 mr-2"></span>
            Rename
          </li>
          <li class="flex items-center px-3 py-1.5 hover:bg-[#3e3e44] cursor-pointer text-red-400 hover:text-red-300" @click="handleContextMenuClick('delete')">
             <span class="i-mdi-delete-outline w-4 h-4 mr-2"></span>
             Delete
          </li>
           <!-- Separator -->
           <hr class="border-t border-[#44444a] my-1 mx-2">
        </template>

        <!-- New File and New Folder always show -->
        <li class="flex items-center px-3 py-1.5 hover:bg-[#3e3e44] cursor-pointer text-gray-200 hover:text-white" @click="handleContextMenuClick('newFile')">
          <span class="i-mdi-file-plus-outline w-4 h-4 mr-2"></span>
          New File
        </li>
        <li class="flex items-center px-3 py-1.5 hover:bg-[#3e3e44] cursor-pointer text-gray-200 hover:text-white" @click="handleContextMenuClick('newFolder')">
           <span class="i-mdi-folder-plus-outline w-4 h-4 mr-2"></span>
           New Folder
        </li>
      </ul>
    </div>

     <!-- Input Dialog for New File/Folder/Rename -->
     <el-dialog 
        v-model="inputDialog.visible"
        :title="inputDialog.title"
        width="300px"
        @closed="inputDialog.inputValue = ''"
        append-to-body 
        class="context-dialog" 
     >
        <el-input 
          v-model="inputDialog.inputValue" 
          :placeholder="inputDialog.placeholder"
          @keyup.enter="handleInputDialogConfirm"
          ref="inputDialogInputRef"
          class="context-input"
        ></el-input>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="inputDialog.visible = false" size="small">Cancel</el-button>
            <el-button type="primary" @click="handleInputDialogConfirm" size="small">Confirm</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- HTML Preview Dialog -->
      <el-dialog
        v-model="htmlPreview.visible"
        :title="`Preview: ${htmlPreview.title}`"
        width="90%"
        :close-on-click-modal="false"
        class="html-preview-dialog"
        top="5vh"
        @closed="closeHtmlPreview"
      >
        <div class="html-preview-content">
          <!-- Loading State -->
          <div 
            v-if="htmlPreview.loading"
            class="html-preview-loading text-center py-16"
          >
            <div class="inline-flex items-center space-x-3 text-blue-600">
              <div class="animate-spin text-2xl">⟳</div>
              <span class="text-lg">Loading HTML content...</span>
            </div>
          </div>

          <!-- Error State -->
          <div 
            v-else-if="htmlPreview.error"
            class="html-preview-error text-center py-16"
          >
            <div class="text-red-500">
              <div class="text-4xl mb-4">⚠️</div>
              <h4 class="text-lg font-medium text-red-600 mb-2">Preview Error</h4>
              <p class="text-red-500 mb-4 max-w-md mx-auto">
                {{ htmlPreview.errorMessage || 'Failed to load or render HTML content.' }}
              </p>
              <button 
                @click="handleHtmlPreviewReload"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200"
              >
                🔄 Retry
              </button>
            </div>
          </div>

          <!-- HTML Content Display -->
          <div 
            v-else-if="htmlPreview.content"
            class="html-preview-iframe-container relative bg-white rounded-lg overflow-hidden"
          >
            <iframe
              :key="htmlPreview.iframeKey"
              :srcdoc="htmlPreview.content"
              class="html-preview-iframe w-full border-0"
              style="height: 75vh; min-height: 500px;"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups-to-escape-sandbox allow-modal"
              @load="handleHtmlPreviewLoad"
              @error="handleHtmlPreviewError"
              ref="htmlPreviewIframe"
              title="HTML Preview"
              loading="lazy"
            ></iframe>
          </div>

          <!-- Empty State -->
          <div 
            v-else
            class="html-preview-empty text-center py-16"
          >
            <div class="text-gray-400">
              <div class="text-4xl mb-4">📄</div>
              <p class="text-gray-500">No content to preview</p>
            </div>
          </div>
        </div>
      </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { ElTree, ElDialog, ElInput, ElButton, ElMessage, ElMessageBox } from 'element-plus';
import type Node from 'element-plus/es/components/tree/src/model/node';
import type { FileSystemObject } from '@/api/types';
import { useFsStore } from '@/store/fs';
import { directive as vClickAway } from 'vue3-click-away';

const emit = defineEmits<{ 
  (e: 'open-file', file: FileSystemObject): void 
}>();

const fsStore = useFsStore();
const treeRef = ref<InstanceType<typeof ElTree> | null>(null);
const expandedKeys = ref<string[]>([]);

// Auto-refresh mechanism
const autoRefreshInterval = ref<NodeJS.Timeout | null>(null)
const enableAutoRefresh = ref(false) // Temporarily disabled for debugging
const refreshInterval = 30000 // 30 seconds
const isRefreshing = ref(false)

// Use store's tree directly
const treeData = computed(() => fsStore.tree[''] || []); // Display root level initially

// Watch for changes in the tree and update El-Tree nodes accordingly
watch(() => fsStore.tree, (newTree, oldTree) => {
  if (!treeRef.value) return;

  // Compare old and new tree to find changed directories
  const changedPaths = new Set<string>();
  
  // Find newly added or modified directories
  for (const path in newTree) {
    if (!oldTree || JSON.stringify(newTree[path]) !== JSON.stringify(oldTree[path])) {
      changedPaths.add(path);
    }
  }
  
  // Find removed directories
  if (oldTree) {
    for (const path in oldTree) {
      if (!(path in newTree)) {
        changedPaths.add(path);
      }
    }
  }

  // Update El-Tree nodes for changed directories
  for (const path of changedPaths) {
    const children = newTree[path] || [];
    console.log(`[FileBrowser] Updating tree node for path: "${path}" with ${children.length} children`);
    
    if (typeof treeRef.value.updateKeyChildren === 'function') {
      try {
        treeRef.value.updateKeyChildren(path, children);
      } catch (error) {
        console.warn(`[FileBrowser] Failed to update tree node for path: "${path}"`, error);
      }
    }
  }
}, { deep: true, immediate: false });

const defaultProps = {
  children: 'children', // This will be dynamically loaded by `loadNode`
  label: 'name',
  // data is of type any because el-tree wraps our FileSystemObject
  // We know it should have is_dir from our FileSystemObject structure
  isLeaf: (data: any, node: Node) => data && typeof data.is_dir === 'boolean' ? !data.is_dir : true, 
};

// Context Menu State
const contextMenu = ref<{ 
  visible: boolean;
  x: number;
  y: number;
  targetNode: FileSystemObject | null; // Use null for root context
}>({ 
  visible: false, 
  x: 0, 
  y: 0, 
  targetNode: null 
});

// Input Dialog State
const inputDialog = ref({
    visible: false,
    title: '',
    placeholder: '',
    inputValue: '',
    action: '' as 'newFile' | 'newFolder' | 'rename',
    targetPath: '', // Path for new file/folder or path of item to rename
    callback: null as Function | null, // Optional callback after confirm
});
const inputDialogInputRef = ref<InstanceType<typeof ElInput> | null>(null);

// HTML Preview State
const htmlPreview = ref({
  visible: false,
  title: '',
  content: '',
  loading: false,
  error: false,
  errorMessage: '',
  iframeKey: 0
});
const htmlPreviewIframe = ref<HTMLIFrameElement | null>(null);

// --- Tree Interaction --- 

const handleNodeClick = (data: FileSystemObject, node: Node) => {
  console.log('Node clicked:', data, node);
  if (!data.is_dir) {
    emit('open-file', data);
  }
  // Do not set targetNode on regular click, only on context menu
};

const handleNodeExpand = (data: FileSystemObject) => {
  console.log('Node expanded:', data.path);
  if (!expandedKeys.value.includes(data.path)) {
    expandedKeys.value.push(data.path);
  }
};

const handleNodeCollapse = (data: FileSystemObject) => {
  console.log('Node collapsed:', data.path);
  expandedKeys.value = expandedKeys.value.filter(key => key !== data.path && !key.startsWith(data.path + '/'));
};

// Lazy load tree nodes
const loadNode = async (node: Node, resolve: (data: FileSystemObject[]) => void) => {
  if (node.level === 0) {
    // Root node: load initial data (handled by computed `treeData`)
    // This part might be redundant if `treeData` handles root correctly
    // await fsStore.fetchDirectory(''); 
    // return resolve(fsStore.tree[''] || []);
    return resolve([]); // Let computed property handle root
  }

  if (node.data?.is_dir) {
    console.log(`Lazy loading children for: ${node.data.path}`);
    await fsStore.fetchDirectory(node.data.path);
    resolve(fsStore.tree[node.data.path] || []);
  } else {
    resolve([]); // Leaf node
  }
};

const refreshRootDirectory = () => {
    fsStore.fetchDirectory(''); // Force refresh root
    // Optionally refresh currently loaded/expanded nodes?
    // Or rely on user expanding again
    ElMessage.info('Refreshing root directory...');
}

// Auto-refresh management functions
const startAutoRefresh = () => {
  if (!enableAutoRefresh.value) return
  
  stopAutoRefresh() // Clear previous timer
  
  autoRefreshInterval.value = setInterval(async () => {
    if (document.visibilityState === 'visible' && fsStore.currentProjectID) {
      try {
        console.log('[FileBrowser] Auto-refreshing file system...')
        isRefreshing.value = true
        await fsStore.fetchDirectory('')
      } catch (err) {
        console.warn('[FileBrowser] Auto-refresh failed:', err)
      } finally {
        isRefreshing.value = false
      }
    }
  }, refreshInterval)
  
  console.log('[FileBrowser] Auto-refresh started')
}

const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
    autoRefreshInterval.value = null
    console.log('[FileBrowser] Auto-refresh stopped')
  }
}

const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // Page becomes visible again - refresh immediately and restart auto-refresh
    if (fsStore.currentProjectID) {
      fsStore.fetchDirectory('')
    }
    startAutoRefresh()
  } else {
    // Page becomes hidden - stop auto-refresh to save resources
    stopAutoRefresh()
  }
}

// --- Context Menu --- 

// Handles right-click on a specific node
const openContextMenu = (event: MouseEvent, data: FileSystemObject) => { 
  console.log('[openContextMenu] Event triggered for node:', data);
  contextMenu.value = { visible: true, x: event.clientX, y: event.clientY, targetNode: data }; 
  // Log the state *after* setting it
  console.log('[openContextMenu] contextMenu.value set to:', JSON.parse(JSON.stringify(contextMenu.value))); 
};

// Handles right-click on the tree container (empty area)
const openContextMenuOnContainer = (event: MouseEvent) => { 
  console.log('[openContextMenuOnContainer] Event triggered for container.');
  contextMenu.value = { visible: true, x: event.clientX, y: event.clientY, targetNode: null }; // Set targetNode to null for root
  // Log the state *after* setting it
  console.log('[openContextMenuOnContainer] contextMenu.value set to:', JSON.parse(JSON.stringify(contextMenu.value))); 
};

const closeContextMenu = () => {
  // Add console log for debugging
  console.log('[FileBrowser] closeContextMenu called by v-click-away?'); 
  contextMenu.value.visible = false;
  contextMenu.value.targetNode = null;
};

const handleContextMenuClick = (action: 'rename' | 'delete' | 'newFile' | 'newFolder') => {
  const targetNode = contextMenu.value.targetNode;
  console.log(`[handleContextMenuClick] Action: ${action}, Target:`, targetNode);
  closeContextMenu(); // Close menu after action selected
  
  // Rename/Delete require a target node
  if ((action === 'rename' || action === 'delete') && !targetNode) {
      console.warn(`Action '${action}' requires a specific file/folder target.`);
      // ElMessage.error(`Cannot perform ${action}: Target context is lost.`); // Optionally show message
      return;
  }

  console.log(`Context menu action '${action}' on target:`, targetNode);

  switch (action) {
    case 'rename':
       openInputDialog('rename', targetNode || undefined); // Pass undefined if targetNode is null
      break;
    case 'delete':
       handleDelete(targetNode as FileSystemObject); // Type assertion safe here due to above check
      break;
    case 'newFile':
        // If targetNode is null (right-click on container), new file in root (target is undefined for openInputDialog)
        // If targetNode is a dir, new file in that dir (target is targetNode)
        // If targetNode is a file, new file in its parent dir (target is parent of targetNode)
        let newFileTargetDir: FileSystemObject | undefined = undefined;
        if (targetNode) {
          if (targetNode.is_dir) {
            newFileTargetDir = targetNode;
          } else {
            // For files, attempt to find parent. If no parent (e.g. root file), then undefined (new file in root)
            const parentPath = targetNode.path.substring(0, targetNode.path.lastIndexOf('/'));
            // This logic to get parent node data might need robust implementation if used often
            // For now, we can assume if it's a file, it's inside a dir structure or root
            // and openInputDialog will correctly place it in targetNode's dir if targetNode is passed
            // Or, pass undefined to create in root if context is on container
            newFileTargetDir = getParentNodeData(targetNode.path) || undefined; 
          }
        }
        openInputDialog('newFile', newFileTargetDir);
      break;
    case 'newFolder':
        let newFolderTargetDir: FileSystemObject | undefined = undefined;
        if (targetNode) {
          if (targetNode.is_dir) {
            newFolderTargetDir = targetNode;
          } else {
            newFolderTargetDir = getParentNodeData(targetNode.path) || undefined;
          }
        }
        openInputDialog('newFolder', newFolderTargetDir);
      break;
  }
};

const getParentNodeData = (path: string): FileSystemObject | null => {
    if (!path || path === '/') return null; // Path is root or invalid
    const parentPath = path.substring(0, path.lastIndexOf('/'));
    if (parentPath === '') return null; // Parent is root
    
    const parentNode = treeRef.value?.getNode(parentPath);
    if (parentNode?.data && typeof parentNode.data === 'object' && 'path' in parentNode.data && 'is_dir' in parentNode.data) {
         return parentNode.data as FileSystemObject;
    }
    console.warn(`Could not reliably get parent node data for path: ${path}. Falling back to null.`);
    return null;
};

// --- CRUD Operations --- 

const handleDelete = async (target: FileSystemObject) => { 
    // target is guaranteed non-null here
    try { 
        await ElMessageBox.confirm(`Are you sure you want to delete '${target.name}'?`, 'Confirm Delete', { confirmButtonText: 'Delete', cancelButtonText: 'Cancel', type: 'warning', appendTo: 'body' }); 
        
        const parentPath = target.path.substring(0, target.path.lastIndexOf('/'));
        // If path doesn't contain '/', parentPath will be empty string, which is correct for root items

        await fsStore.deleteObject(target.path); 
        ElMessage.success(`Successfully deleted '${target.name}'.`);

        await nextTick(); // Ensure store update has propagated

        const updatedChildren = fsStore.tree[parentPath];
        // const parentNode = treeRef.value?.getNode(parentPath); // Not strictly needed for delete unless we want to collapse if empty

        if (treeRef.value && typeof treeRef.value.updateKeyChildren === 'function') {
            console.log(`[FileBrowser] Post-delete: Forcing update for parent key: '${parentPath}'`);
            treeRef.value.updateKeyChildren(parentPath, updatedChildren || []);
        } else {
            console.warn('[FileBrowser] Post-delete: treeRef or updateKeyChildren not available to force update.');
            // Fallback or additional refresh if needed, e.g. force reloading the parent if store changes aren't picked up
            // await fsStore.fetchDirectory(parentPath);
        }

    } catch (error: any) { 
         if (error !== 'cancel') {
             console.error("Delete error in component:", error);
             ElMessage.error(error.message || `Failed to delete '${target.name}'`); 
         } else { 
             ElMessage.info('Delete canceled'); 
         }
    } 
}; 

// --- Input Dialog Logic --- 

const openInputDialog = (
  action: 'newFile' | 'newFolder' | 'rename',
  target?: FileSystemObject
) => {
  inputDialog.value.action = action;
  inputDialog.value.targetPath = target?.path ?? contextMenu.value.targetNode?.path ?? fsStore.activeFilePath ?? '';

  if (action === 'rename' && target) {
    inputDialog.value.title = target.is_dir ? 'Rename Folder' : 'Rename File';
    inputDialog.value.inputValue = target.name;
    inputDialog.value.placeholder = target.is_dir ? 'Enter new folder name' : 'Enter new file name';
  } else if (action === 'newFile') {
    inputDialog.value.title = 'New File';
    inputDialog.value.inputValue = '';
    inputDialog.value.placeholder = 'Enter file name';
    // Default target path for new file is the directory of the right-clicked item, or root
    if (target && target.is_dir) {
      inputDialog.value.targetPath = target.path;
    } else if (target && !target.is_dir) {
      // If target is a file, use its parent directory
      const lastSlash = target.path.lastIndexOf('/');
      inputDialog.value.targetPath = lastSlash === -1 ? '' : target.path.substring(0, lastSlash);
    } else {
       // if contextMenu.value.targetNode is null, it means right click on container, default to root
       inputDialog.value.targetPath = contextMenu.value.targetNode?.path ?? '';
       if (contextMenu.value.targetNode && !contextMenu.value.targetNode.is_dir) {
          const lastSlash = contextMenu.value.targetNode.path.lastIndexOf('/');
          inputDialog.value.targetPath = lastSlash === -1 ? '' : contextMenu.value.targetNode.path.substring(0, lastSlash);
       }
    }

  } else if (action === 'newFolder') {
    inputDialog.value.title = 'New Folder';
    inputDialog.value.inputValue = '';
    inputDialog.value.placeholder = 'Enter folder name';
    // Default target path for new folder is the directory of the right-clicked item, or root
    if (target && target.is_dir) {
      inputDialog.value.targetPath = target.path;
    } else if (target && !target.is_dir) {
      const lastSlash = target.path.lastIndexOf('/');
      inputDialog.value.targetPath = lastSlash === -1 ? '' : target.path.substring(0, lastSlash);
    } else {
       inputDialog.value.targetPath = contextMenu.value.targetNode?.path ?? '';
       if (contextMenu.value.targetNode && !contextMenu.value.targetNode.is_dir) {
          const lastSlash = contextMenu.value.targetNode.path.lastIndexOf('/');
          inputDialog.value.targetPath = lastSlash === -1 ? '' : contextMenu.value.targetNode.path.substring(0, lastSlash);
       }
    }
  }
  inputDialog.value.visible = true;
  nextTick(() => {
    inputDialogInputRef.value?.focus();
  });
};

const handleInputDialogConfirm = async () => { 
    const { action, inputValue, targetPath } = inputDialog.value; 
    if (!inputValue.trim()) { ElMessage.warning('Name cannot be empty.'); return; } 
    inputDialog.value.visible = false; 
    const name = inputValue.trim(); 
    let fullPath = '', op: Promise<any> | null = null, sm = '', fm = '', parentToRefresh = ''; 
    try { 
        if (action === 'newFile' || action === 'newFolder') { 
            parentToRefresh = targetPath; // The parent directory where the new item is created
            fullPath = parentToRefresh ? `${parentToRefresh}/${name}` : name; 
            op = fsStore.createFSObject(fullPath, action === 'newFolder'); 
            sm = `${action === 'newFile' ? 'File' : 'Folder'} '${name}' created successfully.`; 
            fm = `Failed to create ${action === 'newFile' ? 'file' : 'folder'} '${name}'.`; 
        } else if (action === 'rename') { 
            // targetPath is the original item path here
            parentToRefresh = targetPath.substring(0, targetPath.lastIndexOf('/')); 
            if (targetPath.includes('/') && parentToRefresh === '') { // Item was in root, parent is effectively root ''
                 // Handled by parentToRefresh being an empty string
            }
            const newPath = parentToRefresh ? `${parentToRefresh}/${name}` : name; 
            op = fsStore.renameObject(targetPath, newPath); 
            sm = `Renamed to '${name}' successfully.`;
            fm = `Failed to rename to '${name}'.`;
        } 

        if (op) { 
            await op; 
            ElMessage.success(sm); 
            
            await nextTick(); // Ensure store update has propagated and fsStore.tree is updated
            
            const updatedChildren = fsStore.tree[parentToRefresh];
            const parentNode = treeRef.value?.getNode(parentToRefresh);

            if (treeRef.value && typeof treeRef.value.updateKeyChildren === 'function') {
                console.log(`[FileBrowser] Forcing update for parent key: '${parentToRefresh}'`);
                treeRef.value.updateKeyChildren(parentToRefresh, updatedChildren || []);
                // If the parent node was not expanded, expand it to show the new/renamed item
                if (parentNode && !parentNode.expanded) {
                    parentNode.expand();
                }
            } else {
                console.warn('[FileBrowser] treeRef or updateKeyChildren not available to force update.');
                // Fallback: try to refresh the parent directory directly from store if updateKeyChildren is not robust for all cases
                // This might cause a full reload of that node's children if not handled carefully by lazy load
                // await fsStore.fetchDirectory(parentToRefresh);
            }
        } 
    } catch (error: any) { 
        console.error(`${action} error in component:`, error);
        ElMessage.error(`${fm} ${error.message || 'Operation failed'}`); 
    } 
};

// --- HTML Preview Functions ---

const previewHtmlFile = async (file: FileSystemObject) => {
  console.log('[FileBrowser] Previewing HTML file:', file.path);
  
  htmlPreview.value.title = file.name;
  htmlPreview.value.loading = true;
  htmlPreview.value.error = false;
  htmlPreview.value.errorMessage = '';
  htmlPreview.value.visible = true;
  htmlPreview.value.iframeKey += 1; // Force iframe refresh
  
  try {
    // Fetch file content from store
    await fsStore.fetchFileContent(file.path);
    
    // Get the content from store's openFiles map
    const fileState = fsStore.openFiles[file.path];
    if (fileState && fileState.currentContent !== null) {
      htmlPreview.value.content = fileState.currentContent;
    } else {
      throw new Error('File content not available');
    }
    
    htmlPreview.value.loading = false;
  } catch (error: any) {
    console.error('[FileBrowser] Failed to load HTML file:', error);
    htmlPreview.value.loading = false;
    htmlPreview.value.error = true;
    htmlPreview.value.errorMessage = error.message || 'Failed to load HTML file';
    ElMessage.error(`Failed to preview ${file.name}: ${error.message || 'Unknown error'}`);
  }
};

const handleHtmlPreviewLoad = () => {
  console.log('[FileBrowser] HTML preview iframe loaded');
  htmlPreview.value.error = false;
  htmlPreview.value.errorMessage = '';
};

const handleHtmlPreviewError = () => {
  console.error('[FileBrowser] HTML preview iframe error');
  htmlPreview.value.error = true;
  htmlPreview.value.errorMessage = 'Failed to render HTML content';
};

const handleHtmlPreviewReload = () => {
  console.log('[FileBrowser] Reloading HTML preview');
  htmlPreview.value.error = false;
  htmlPreview.value.errorMessage = '';
  htmlPreview.value.iframeKey += 1;
};

const closeHtmlPreview = () => {
  htmlPreview.value.visible = false;
  htmlPreview.value.content = '';
  htmlPreview.value.error = false;
  htmlPreview.value.errorMessage = '';
};


// Watch for project changes to potentially clear the tree (if needed)
watch(() => fsStore.currentProjectID, (newId, oldId) => {
  console.log(`[FileBrowser] Project ID changed from ${oldId} to ${newId}`);
  if (newId && newId !== oldId) {
    console.log('[FileBrowser] Fetching root directory for new project...');
    // Fetch root directory when project ID changes to a valid new ID
    fsStore.fetchDirectory(''); // Removed forceRefresh param
    expandedKeys.value = []; // Collapse nodes on project change
  } else if (!newId) {
      console.log('[FileBrowser] Project ID cleared.');
      // Optional: Clear tree data explicitly if store doesn't handle it
      // fsStore.tree = {}; 
      expandedKeys.value = []; 
  }
}, { immediate: true }); // immediate: true to load on initial mount if projectID is already set

// Lifecycle hooks
onMounted(() => {
  console.log('[FileBrowser] Component mounted, starting auto-refresh...')
  
  // Start auto-refresh
  startAutoRefresh()
  
  // Listen for page visibility changes
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  console.log('[FileBrowser] Component unmounted, cleaning up...')
  
  // Stop auto-refresh
  stopAutoRefresh()
  
  // Remove event listeners
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

</script>

<style>
/* Element Plus Tree Customizations */
.file-tree .el-tree-node__content {
  background-color: transparent !important; /* Ensure background is transparent */
  color: #e0e0e0 !important; /* Brighter default text color for dark background, force it */
  height: 24px;
  line-height: 24px;
}

/* Override background color on the component root itself */
.file-tree.el-tree {
    background-color: transparent !important; 
}

.file-tree .el-tree-node__content:hover {
  background-color: #3a3a3f !important; /* Keep dark hover */
  color: #ffffff !important; /* White text on hover, force it */
}

.file-tree .el-tree-node:focus > .el-tree-node__content {
  background-color: #4a4a4f !important; /* Keep dark focus color */
  color: white !important; /* Force white text on focus */
}

.file-tree .is-current > .el-tree-node__content {
   background-color: #4a4a4f !important; /* Keep dark selected color */
   color: white !important; /* Force white text on select */
}

.file-tree .el-tree-node__expand-icon {
  color: #bdbdbd !important; /* Keep expand icon color or adjust if needed, force it */
}

/* Make icons slightly less bright */
.file-tree .el-tree-node__content .i-ep-folder,
.file-tree .el-tree-node__content .i-ep-document {
    color: #cccccc; /* Adjust icon color */
}

/* Specific color for folder icon if needed */
.file-tree .el-tree-node__content .i-ep-folder {
     color: #f2cb61; /* Example: yellow folder icon */
}


.file-tree .el-tree-node.is-expanded > .el-tree-node__children {
  background-color: transparent !important; /* Ensure children container is also transparent */
}

.custom-tree-node {
  flex-grow: 1; 
}

/* Dialog style override if needed */
.el-dialog {
    background-color: #29292e; 
}
.el-dialog__title {
    color: white;
}
.el-input__inner {
    background-color: #3a3a3f;
    color: white;
    border: 1px solid #4d4d54;
}

/* Context Menu Enhancements */
.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.context-menu li {
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

/* Dialog Styles - Add specific classes if needed */
.context-dialog .el-dialog__header {
  padding-bottom: 10px;
  /* Add other header styles */
}
.context-dialog .el-dialog__body {
   padding-top: 10px;
   padding-bottom: 20px;
  /* Add other body styles */
}
.context-dialog .el-input__inner {
    background-color: #3e3e44; /* Slightly different from tree hover for variety */
    color: white;
    border: 1px solid #55555a;
}

/* Override global dialog background if needed, but prefer specific class */
.el-dialog {
    background-color: #2c2c32 !important; /* Match context menu bg */
}
.el-dialog__title {
    color: #e0e0e0; /* Match tree text */
}

/* HTML Preview Dialog Styles */
.html-preview-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 85vh;
  overflow-y: auto;
}

.html-preview-iframe-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.html-preview-iframe {
  transition: opacity 0.3s ease;
}

.html-preview-loading {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 8px;
}

.html-preview-error {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-radius: 8px;
}

.html-preview-empty {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  border-radius: 8px;
}

</style> 