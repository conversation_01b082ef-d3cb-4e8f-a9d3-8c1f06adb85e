<template>
  <div class="icon-bar flex flex-col items-center pt-2.5 space-y-3.75 bg-[#1a1a1f] w-[50px] h-full box-border">
    <!-- Placeholder Icons -->
    <div class="w-6 h-6 bg-[#b3b3b3] rounded cursor-pointer" title="Files (Placeholder)"></div>
    <div class="w-6 h-6 bg-[#b3b3b3] rounded cursor-pointer" title="Search (Placeholder)"></div>
    
    <!-- New Terminal Icon -->
    <div 
      class="w-6 h-6 rounded cursor-pointer flex items-center justify-center hover:bg-[#333338]"
      :class="{ 'opacity-50 cursor-not-allowed': !canOpenTerminal }"
      title="New Terminal"
      @click="openNewTerminalTab"
    >
      <!-- Using an SVG or a character as a simple terminal icon -->
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-[#b3b3b3]">
        <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z" />
        <path stroke-linecap="round" stroke-linejoin="round" d="m6.75 10.5 3.75 3.75-3.75 3.75M12 10.5h4.5" />
      </svg>
    </div>

    <!-- Agent Icon -->
    <div 
      class="w-6 h-6 rounded cursor-pointer flex items-center justify-center hover:bg-[#333338]"
      :class="{ 
        'opacity-50 cursor-not-allowed': !canOpenAgent,
        'bg-[#4a6cf7]': isAgentTabActive 
      }"
      title="Agent Chat"
      @click="toggleAgentTab"
    >
      <!-- Agent/AI icon -->
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-[#b3b3b3]">
        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.125C8.25 5.004 9.246 6 10.125 6h3.75c.879 0 1.875-.996 1.875-1.875V3m-7.5 0C8.25 1.343 9.593 0 11.25 0h1.5c1.657 0 3 1.343 3 3m-7.5 0v.879c0 .517.271.99.68 1.265.133.089.277.173.427.252.54.284 1.141.519 1.768.656a8.25 8.25 0 002.25 0c.627-.137 1.228-.372 1.768-.656.15-.079.294-.163.427-.252.409-.275.68-.748.68-1.265V3z" />
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5v9m-7.5-9v9a2.25 2.25 0 002.25 2.25h6a2.25 2.25 0 002.25-2.25v-9" />
        <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 12h4.5m-4.5 3h4.5" />
      </svg>
    </div>

    <div class="w-6 h-6 bg-[#b3b3b3] rounded cursor-pointer" title="Settings (Placeholder)"></div>
    <!-- Add more icons as needed -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useTerminalStore } from '@/store/terminal';
import { useFsStore } from '@/store/fs';
import { ElMessage } from 'element-plus';

const props = defineProps<{
  activeRightTabId?: string | null;
}>();

const terminalStore = useTerminalStore();
const fsStore = useFsStore();

const canOpenTerminal = computed(() => !!fsStore.currentProjectID);
const canOpenAgent = computed(() => !!fsStore.currentProjectID);

// Check if Agent tab is currently active by looking at the right column tabs
const isAgentTabActive = computed(() => {
  return props.activeRightTabId === 'agent';
});

const emit = defineEmits<{
  'toggle-agent-tab': [projectId: string];
}>();

const openNewTerminalTab = () => {
  if (!fsStore.currentProjectID) {
    ElMessage.warning('Please select or open a project first to use the terminal.');
    return;
  }
  const newTerminalId = terminalStore.openNewTerminal(fsStore.currentProjectID);
  console.log('New terminal tab requested from IconBar, ID:', newTerminalId);
  // MainColumn should react to terminalStore.activeTerminalId change via its watcher
};

const toggleAgentTab = () => {
  if (!fsStore.currentProjectID) {
    ElMessage.warning('Please select or open a project first to use the Agent.');
    return;
  }
  emit('toggle-agent-tab', fsStore.currentProjectID);
  console.log('Agent tab toggle requested from IconBar for project:', fsStore.currentProjectID);
};

</script>

<style scoped>
/* Scoped styles for the icon bar */
.icon-bar > div {
  transition: background-color 0.2s ease-in-out;
}
</style> 