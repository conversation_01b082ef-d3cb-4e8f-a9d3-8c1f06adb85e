<template>
  <div class="question-list">
    <!-- Header -->
    <div class="question-header flex items-center justify-between mb-4">
      <div class="flex items-center space-x-2">
        <el-icon class="text-primary"><ChatDotRound /></el-icon>
        <h3 class="text-lg font-medium text-white">AI Generated Questions</h3>
        <el-tag v-if="questions.length > 0" size="small" type="info">
          {{ questions.length }} questions
        </el-tag>
      </div>
      
      <div class="flex items-center space-x-2">
        <el-button
          v-if="questions.length > 0 && !isGenerating"
          @click="handleRegenerateQuestions"
          type="primary"
          size="small"
          :loading="isGenerating"
          plain
        >
          <el-icon><RefreshRight /></el-icon>
          Regenerate
        </el-button>
        
        <el-button
          v-if="questions.length === 0 && !isGenerating"
          @click="handleGenerateQuestions"
          type="primary"
          size="small"
          :loading="isGenerating"
        >
          <el-icon><StarFilled /></el-icon>
          Generate Questions
        </el-button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="questions.length === 0 && !isGenerating" class="empty-state text-center py-8">
      <div class="text-[#666666] space-y-3">
        <el-icon class="text-6xl mb-4 text-[#4fc3f7]"><ChatDotRound /></el-icon>
        <h4 class="text-lg font-medium text-[#cccccc]">No Questions Generated Yet</h4>
        <p class="text-sm max-w-md mx-auto">
          Click "Generate Questions" to let AI create targeted questions that will help refine your requirement.
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isGenerating" class="loading-state">
      <div class="bg-[#2d2d30] rounded-lg border border-[#3e3e42] p-6 text-center">
        <div class="flex items-center justify-center space-x-3 mb-3">
          <div class="animate-spin w-5 h-5 border-2 border-[#4fc3f7] border-t-transparent rounded-full"></div>
          <span class="text-[#cccccc] font-medium">Generating Questions...</span>
        </div>
        <p class="text-sm text-[#888888]">
          AI is analyzing your requirement and creating relevant questions.
        </p>
      </div>
    </div>

    <!-- Question Categories -->
    <div v-if="questions.length > 0" class="question-categories space-y-4">
      <div 
        v-for="(categoryQuestions, category) in questionsByCategory" 
        :key="category"
        class="category-section"
      >
        <div class="category-header">
          <el-button
            @click="toggleCategory(category)"
            type="text"
            class="category-toggle w-full justify-between p-3"
            :class="{ 'bg-[#2d2d30]': expandedCategories.has(category) }"
          >
            <div class="flex items-center space-x-2">
              <el-icon class="text-[#4fc3f7]">
                <component :is="getCategoryIcon(category)" />
              </el-icon>
              <span class="font-medium text-white capitalize">{{ category }}</span>
              <el-tag size="small" type="info">{{ categoryQuestions.length }}</el-tag>
            </div>
            <el-icon class="transition-transform duration-200" 
                     :class="{ 'rotate-90': expandedCategories.has(category) }">
              <ArrowRight />
            </el-icon>
          </el-button>
        </div>

        <el-collapse-transition>
          <div v-show="expandedCategories.has(category)" class="category-content">
            <div class="questions-grid space-y-3 p-3 bg-[#1e1e1e] border border-[#3e3e42] rounded-b-lg">
              <div 
                v-for="(question, index) in categoryQuestions" 
                :key="question.id"
                class="question-item"
              >
                <div class="question-card bg-[#2d2d30] rounded-lg p-4 border border-[#3e3e42] hover:border-[#4fc3f7] transition-colors">
                  <div class="flex items-start space-x-3">
                    <div class="question-number">
                      <span class="inline-flex items-center justify-center w-6 h-6 bg-[#4fc3f7] text-black rounded-full text-sm font-medium">
                        {{ getQuestionNumber(category, index) }}
                      </span>
                    </div>
                    <div class="question-content flex-1">
                      <p class="text-[#cccccc] leading-relaxed">{{ question.text }}</p>
                      <div class="question-meta flex items-center justify-between mt-3">
                        <div class="flex items-center space-x-2">
                          <el-tag size="small" type="info" effect="plain">{{ question.category }}</el-tag>
                          <span class="text-xs text-[#666666]">
                            Created {{ formatTime(question.created_at) }}
                          </span>
                        </div>
                        <div class="flex items-center space-x-1">
                          <el-icon 
                            class="w-4 h-4"
                            :class="getAnswerStatusIcon(question.id).class"
                          >
                            <component :is="getAnswerStatusIcon(question.id).icon" />
                          </el-icon>
                          <span 
                            class="text-xs"
                            :class="getAnswerStatusIcon(question.id).class"
                          >
                            {{ getAnswerStatusIcon(question.id).text }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>

    <!-- Summary Stats -->
    <div v-if="questions.length > 0" class="question-stats mt-6 p-4 bg-[#2d2d30] rounded-lg border border-[#3e3e42]">
      <div class="grid grid-cols-3 gap-4 text-center">
        <div class="stat-item">
          <div class="text-2xl font-bold text-[#4fc3f7]">{{ questions.length }}</div>
          <div class="text-sm text-[#888888]">Total Questions</div>
        </div>
        <div class="stat-item">
          <div class="text-2xl font-bold text-[#52c41a]">{{ answeredQuestionsCount }}</div>
          <div class="text-sm text-[#888888]">Answered</div>
        </div>
        <div class="stat-item">
          <div class="text-2xl font-bold text-[#fa8c16]">{{ unansweredQuestionsCount }}</div>
          <div class="text-sm text-[#888888]">Remaining</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
  ElIcon, 
  ElButton, 
  ElTag, 
  ElCollapseTransition,
  ElMessage 
} from 'element-plus';
import { 
  ChatDotRound, 
  RefreshRight, 
  ArrowRight,
  User,
  Setting,
  DataAnalysis,
  Tools,
  StarFilled,
  CircleCheck,
  Clock
} from '@element-plus/icons-vue';
import type { Question, Answer } from '@/api/requirement.js';

// Props interface
interface Props {
  questions: Question[];
  answers: Answer[];
  isGenerating?: boolean;
  requirementId?: string;
}

// Events interface
interface Emits {
  (e: 'generate-questions'): void;
  (e: 'regenerate-questions'): void;
}

const props = withDefaults(defineProps<Props>(), {
  questions: () => [],
  answers: () => [],
  isGenerating: false,
  requirementId: undefined,
});

const emit = defineEmits<Emits>();

// State
const expandedCategories = ref<Set<string>>(new Set());

// Computed properties
const questionsByCategory = computed(() => {
  const categories: Record<string, Question[]> = {};
  props.questions.forEach(question => {
    const category = question.category || 'general';
    if (!categories[category]) {
      categories[category] = [];
    }
    categories[category].push(question);
  });
  return categories;
});

const answeredQuestionsCount = computed(() => {
  const answeredQuestionIds = new Set(props.answers.map(answer => answer.question_id));
  return props.questions.filter(question => answeredQuestionIds.has(question.id)).length;
});

const unansweredQuestionsCount = computed(() => {
  return props.questions.length - answeredQuestionsCount.value;
});

// Methods
const toggleCategory = (category: string) => {
  if (expandedCategories.value.has(category)) {
    expandedCategories.value.delete(category);
  } else {
    expandedCategories.value.add(category);
  }
};

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, any> = {
    'functional': Tools,
    'technical': Setting,
    'business': DataAnalysis,
    'user': User,
    'general': StarFilled,
    'requirements': ChatDotRound,
  };
  return iconMap[category.toLowerCase()] || StarFilled;
};

const getQuestionNumber = (category: string, index: number): number => {
  // Calculate global question number across all categories
  let globalIndex = 0;
  const categories = Object.keys(questionsByCategory.value).sort();
  
  for (const cat of categories) {
    if (cat === category) {
      return globalIndex + index + 1;
    }
    globalIndex += questionsByCategory.value[cat].length;
  }
  
  return globalIndex + index + 1;
};

const getAnswerStatusIcon = (questionId: string) => {
  const hasAnswer = props.answers.some(answer => answer.question_id === questionId);
  
  if (hasAnswer) {
    return {
      icon: CircleCheck,
      class: 'text-[#52c41a]',
      text: 'Answered'
    };
  } else {
    return {
      icon: Clock,
      class: 'text-[#fa8c16]',
      text: 'Pending'
    };
  }
};

const formatTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) {
      return 'just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffMinutes < 1440) {
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  } catch (error) {
    return 'recently';
  }
};

const handleGenerateQuestions = () => {
  console.log('[QuestionList] Generate questions clicked');
  emit('generate-questions');
};

const handleRegenerateQuestions = () => {
  console.log('[QuestionList] Regenerate questions clicked');
  emit('regenerate-questions');
};

// Lifecycle
onMounted(() => {
  // Auto-expand all categories initially
  Object.keys(questionsByCategory.value).forEach(category => {
    expandedCategories.value.add(category);
  });
});
</script>

<style scoped>
.question-list {
  @apply space-y-4;
}

.category-toggle {
  @apply w-full bg-transparent border border-[#3e3e42] rounded-lg hover:bg-[#2d2d30] transition-colors;
}

.category-toggle:hover {
  @apply border-[#4fc3f7];
}

.question-card:hover .question-number span {
  @apply bg-[#1890ff];
}

.stat-item {
  @apply flex flex-col items-center;
}

/* Animation for expand/collapse */
.rotate-90 {
  transform: rotate(90deg);
}

/* Custom scrollbar for long question lists */
.questions-grid {
  max-height: 400px;
  overflow-y: auto;
}

.questions-grid::-webkit-scrollbar {
  width: 4px;
}

.questions-grid::-webkit-scrollbar-track {
  background: #2d2d30;
  border-radius: 4px;
}

.questions-grid::-webkit-scrollbar-thumb {
  background: #4fc3f7;
  border-radius: 4px;
}

.questions-grid::-webkit-scrollbar-thumb:hover {
  background: #1890ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .question-stats {
    @apply grid-cols-1 gap-2;
  }
  
  .question-card {
    @apply p-3;
  }
  
  .question-meta {
    @apply flex-col items-start space-y-2;
  }
}
</style> 