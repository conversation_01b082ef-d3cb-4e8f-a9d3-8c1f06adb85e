<template>
  <div class="terminal-view-wrapper">
    <!-- Reconnect Button Overlay -->
    <div v-if="terminalStore.getTerminalById(terminalId)?.isDisconnected" class="reconnect-overlay">
      <div class="reconnect-content">
        <el-icon class="disconnect-icon" color="#E6A23C" size="32">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path fill="currentColor" d="M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64z"/>
            <path fill="currentColor" d="M832 512a288 288 0 1 0-576 0c0 159.058 128.942 288 288 288s288-128.942 288-288zm-64 0a224 224 0 1 1-448 0c0-123.712 100.288-224 224-224s224 100.288 224 224z"/>
          </svg>
        </el-icon>
        <p class="disconnect-message">连接已断开</p>
        <el-button 
          type="primary" 
          :loading="isReconnecting"
          @click="handleReconnect"
          size="default"
        >
          {{ isReconnecting ? '重连中...' : '重新连接' }}
        </el-button>
      </div>
    </div>
    
    <div ref="xtermContainer" class="terminal-container"></div>
    
    <!-- Runtime Error Dialog -->
    <el-dialog 
      v-model="showRuntimeErrorDialog" 
      title="运行环境错误"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
    <div class="runtime-error-content">
      <el-icon class="error-icon" color="#F56C6C" size="48">
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"/>
        </svg>
      </el-icon>
      <p class="error-message">{{ runtimeErrorMessage }}</p>
      <p class="error-description">
        此项目的运行环境不可用。这通常发生在以下情况：
      </p>
      <ul class="error-reasons">
        <li>Docker 容器被手动删除</li>
        <li>系统重启清除了容器</li>
        <li>运行环境遇到错误</li>
      </ul>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showRuntimeErrorDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="rebuildingRuntime"
          @click="handleRebuildRuntime"
        >
          {{ rebuildingRuntime ? '重建中...' : '重建运行环境' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { AttachAddon } from '@xterm/addon-attach';
// import { WebLinksAddon } from '@xterm/addon-web-links'; // Optional: for automatic link detection
import '@xterm/xterm/css/xterm.css';
import { ElMessage } from 'element-plus';
import { useTerminalStore } from '../store/terminal';
import { rebuildProjectRuntime } from '../api/fs';

const props = defineProps<{
  terminalId: string;
  projectId?: string; // Make projectId optional
}>();

const terminalStore = useTerminalStore();

const xtermContainer = ref<HTMLDivElement | null>(null);
let xterm: Terminal | null = null;
let fitAddon: FitAddon | null = null;
let socket: WebSocket | null = null;

// Runtime error dialog state
const showRuntimeErrorDialog = ref(false);
const runtimeErrorMessage = ref('');
const rebuildingRuntime = ref(false);

// Reconnection state
const isReconnecting = ref(false);

// Construct WebSocket URL
// TODO: Move URL construction to a more centralized place (e.g., api/config.ts or utils)
const getWebSocketURL = (projectId: string, shell?: string, workDir?: string, rows?: number, cols?: number): string => {
  const base = `ws://localhost:8080/api/v1/projects/${projectId}/terminal`; // Assuming backend runs on 8080
  const params = new URLSearchParams();
  if (shell) params.append('shell', shell);
  if (workDir) params.append('workDir', workDir);
  if (rows) params.append('rows', rows.toString());
  if (cols) params.append('cols', cols.toString());
  return params.toString() ? `${base}?${params.toString()}` : base;
};

const initializeTerminal = async () => {
  if (!xtermContainer.value || !props.terminalId) return;

  // Check if projectId is provided before attempting to connect
  if (!props.projectId) {
    console.error(`TerminalView (${props.terminalId}): projectId is missing. Cannot initialize WebSocket.`);
    if (xtermContainer.value) { // If xterm div exists, show message there
        const tempTerm = new Terminal({
            cursorBlink: false,
            fontSize: 14,
            fontFamily: 'Consolas, "Courier New", monospace',
            theme: { background: '#1e1e1e', foreground: '#d4d4d4' },
        });
        tempTerm.open(xtermContainer.value);
        tempTerm.writeln('\x1b[31mError: Project ID is missing. Terminal cannot be started.\x1b[0m');
        // We don't store this tempTerm in the store, just for displaying error
    }
    terminalStore.setTerminalError(props.terminalId, 'Project ID is missing');
    return;
  }

  const terminalInstance = terminalStore.getTerminalById(props.terminalId);
  if (!terminalInstance) {
    console.error(`Terminal instance with ID ${props.terminalId} not found in store.`);
    // Optionally, set an error state in the component or store
    return;
  }

  // Close any existing socket but preserve xterm instance for reconnection
  if (socket && socket.readyState !== WebSocket.CLOSED) {
    socket.close();
  }

  // Only create new xterm instance if one doesn't exist or it was disposed
  if (!xterm || !xterm.element) {
    // Dispose old instance if it exists but not properly attached
    if (xterm) {
      xterm.dispose();
  }

  xterm = new Terminal({
    cursorBlink: true,
    fontSize: 14,
    fontFamily: 'Consolas, "Courier New", monospace',
    theme: {
      background: '#1e1e1e',
      foreground: '#d4d4d4',
      // Add more theme colors as needed
    },
    rows: terminalInstance.xterm?.rows || 24, // Use stored rows or default
    cols: terminalInstance.xterm?.cols || 80, // Use stored cols or default
  });

  fitAddon = new FitAddon();
  // const webLinksAddon = new WebLinksAddon(); // Optional

  xterm.loadAddon(fitAddon);
  // xterm.loadAddon(webLinksAddon); // Optional

  xterm.open(xtermContainer.value);
  terminalStore.setTerminalInstance(props.terminalId, xterm); // Store the xterm instance
  } else {
    // Ensure fitAddon exists even when reusing xterm instance
    if (!fitAddon) {
      fitAddon = new FitAddon();
      xterm.loadAddon(fitAddon);
    }
  }

  try {
    await nextTick(); // Ensure xterm is in the DOM for fitAddon
    fitAddon?.fit();
  } catch (e) {
    console.warn('FitAddon fit failed on initial load:', e);
  }

  const wsURL = getWebSocketURL(
    props.projectId,
    terminalInstance.shell,
    terminalInstance.workDir,
    xterm.rows,
    xterm.cols
  );

  socket = new WebSocket(wsURL);
  terminalStore.setTerminalSocket(props.terminalId, socket); // Store the socket instance

  // Create and load AttachAddon for the new socket
  const attachAddon = new AttachAddon(socket);
  xterm.loadAddon(attachAddon); // This handles data flow between xterm and socket

  socket.onopen = () => {
    console.log(`Terminal ${props.terminalId}: WebSocket connection established.`);
    terminalStore.clearTerminalLoading(props.terminalId);
    
    // Show reconnection success message without clearing previous content
    const terminalInstance = terminalStore.getTerminalById(props.terminalId);
    if (terminalInstance?.isDisconnected && xterm) {
      xterm.writeln('\r\n\x1b[32m[Connection restored successfully!]\x1b[0m');
    }
    
    fitAddon?.fit(); // Fit again once connection is open and terminal might have initial output
  };

  socket.onclose = (event) => {
    console.log(`Terminal ${props.terminalId}: WebSocket connection closed.`, event);
    if (xterm && !event.wasClean) {
      xterm.writeln('\r\n\x1b[33mConnection closed unexpectedly. Use the reconnect button to reconnect.\x1b[0m');
    }
    // Do not mark as error if it was a clean close initiated by client/component unmount
    if (!event.wasClean && terminalStore.getTerminalById(props.terminalId)) { // Check if store still has it
        terminalStore.setTerminalDisconnected(props.terminalId, `WebSocket closed (code: ${event.code})`);
    }
  };

  socket.onerror = (error) => {
    console.error(`Terminal ${props.terminalId}: WebSocket error:`, error);
    if (xterm) {
        xterm.writeln('\r\n\x1b[31mWebSocket connection error. Please try reopening the terminal.\x1b[0m');
    }
    terminalStore.setTerminalError(props.terminalId, 'WebSocket connection error');
  };

  // Listen for error messages from WebSocket
  socket.onmessage = (event) => {
    // Check if this is an error message from the backend
    try {
      const data = JSON.parse(event.data);
      if (data.type === 'error' && data.canRebuild) {
        // Show the rebuild dialog
        runtimeErrorMessage.value = data.error;
        showRuntimeErrorDialog.value = true;
        return; // Don't process this as terminal data
      }
    } catch (e) {
      // Not JSON, process as regular terminal data
    }
    // Let AttachAddon handle regular data
  };

  // Handle terminal resize events
  xterm.onResize(({ cols, rows }) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      const resizeMessage = JSON.stringify({
        type: 'resize',
        payload: { cols, rows },
      });
      socket.send(resizeMessage);
      // console.log(`Terminal ${props.terminalId}: Sent resize event to backend: ${cols}x${rows}`);
    }
  });

  // Optional: Handle data from xterm (user input) if AttachAddon is not used or for custom logic
  // xterm.onData((data) => {
  //   if (socket && socket.readyState === WebSocket.OPEN) {
  //     socket.send(data);
  //   }
  // });

  // Focus the terminal
  xterm.focus();
};

// Handle runtime rebuild
const handleRebuildRuntime = async () => {
  if (!props.projectId) {
    ElMessage.error('项目 ID 缺失');
    return;
  }

  rebuildingRuntime.value = true;
  try {
    await rebuildProjectRuntime(props.projectId);
    ElMessage.success('运行环境重建成功');
    showRuntimeErrorDialog.value = false;
    
    // Reinitialize the terminal after successful rebuild
    await nextTick();
    initializeTerminal();
  } catch (error: any) {
    console.error('Failed to rebuild runtime:', error);
    ElMessage.error(error.message || '重建运行环境失败');
  } finally {
    rebuildingRuntime.value = false;
  }
};

// Handle reconnection
const handleReconnect = async () => {
  if (!props.projectId || !props.terminalId) {
    ElMessage.error('项目 ID 或终端 ID 缺失');
    return;
  }

  isReconnecting.value = true;
  
  try {
    // Reset terminal state for reconnection
    terminalStore.resetTerminalForReconnection(props.terminalId);
    
    // Add a brief delay to ensure state update
    await nextTick();
    
    // Reinitialize the terminal connection
    await initializeTerminal();
    
    ElMessage.success('重连成功');
  } catch (error: any) {
    console.error('Failed to reconnect terminal:', error);
    ElMessage.error('重连失败，请稍后再试');
    // Reset to disconnected state if reconnection fails
    terminalStore.setTerminalDisconnected(props.terminalId, '重连失败');
  } finally {
    isReconnecting.value = false;
  }
};

onMounted(() => {
  initializeTerminal();

  // Watch for changes in terminalId prop, though typically a new component is created for a new tab
  watch(() => props.terminalId, (newId, oldId) => {
    if (newId && newId !== oldId) {
      console.log(`TerminalView prop terminalId changed from ${oldId} to ${newId}. Re-initializing.`);
      initializeTerminal(); // Re-initialize if the ID changes (e.g. component is reused)
    }
  });
});

onUnmounted(() => {
  console.log(`TerminalView ${props.terminalId} unmounting.`);
  if (xterm) {
    xterm.dispose();
    xterm = null;
  }
  if (socket) {
    socket.onopen = null;
    socket.onclose = null; // Prevent onclose logic from running again with stale store refs
    socket.onerror = null;
    if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        socket.close();
    }
    socket = null;
  }
  // Note: The store's closeTerminal action is responsible for removing the terminal metadata from the store list.
  // This component only cleans up its own resources (xterm, socket).
});

// Expose fit method if needed by parent to trigger resize
defineExpose({ 
  fitTerminal: () => {
    if (xtermContainer.value) {
      console.log(`TerminalView (${props.terminalId}): fitTerminal() called. Container offsetWidth: ${xtermContainer.value.offsetWidth}, offsetHeight: ${xtermContainer.value.offsetHeight}, clientWidth: ${xtermContainer.value.clientWidth}, clientHeight: ${xtermContainer.value.clientHeight}`);
    } else {
      console.warn(`TerminalView (${props.terminalId}): fitTerminal() called but xtermContainer.value is null.`);
    }

    if (fitAddon && xterm?.element) { // Check if xterm element is available and attached
      try {
        fitAddon.fit();
        console.log(`TerminalView (${props.terminalId}): fitAddon.fit() executed. Terminal geometry after fit: ${xterm?.cols} cols, ${xterm?.rows} rows.`);
      } catch (e) {
        console.warn(`TerminalView (${props.terminalId}): fitAddon.fit() failed:`, e);
      }
    } else {
      const reasons = [];
      if (!fitAddon) reasons.push("fitAddon is not available");
      if (!xterm?.element) reasons.push("xterm.element is not available (terminal might not be fully opened or attached to DOM)");
      // Add a check for xtermContainer.value as well, as fitAddon needs a rendered container
      if (!xtermContainer.value?.isConnected) reasons.push("xtermContainer is not connected to the DOM");
      
      let message = `TerminalView (${props.terminalId}): fitTerminal() called but prerequisites not met.`;
      if (reasons.length > 0) {
        message += ` Reasons: ${reasons.join('; ')}.`;
      }
      if (xterm) {
        message += ` Terminal state: ${xterm.element ? 'element exists' : 'element DOES NOT exist'}.`;
      } else {
        message += ` Terminal state: xterm instance itself is null.`;
      }
      console.warn(message);
    }
  },
  focusTerminal: () => {
    if (xterm) {
      xterm.focus();
      console.log(`TerminalView (${props.terminalId}): focusTerminal() called and executed.`);
    } else {
      console.warn(`TerminalView (${props.terminalId}): focusTerminal() called but xterm instance not available.`);
    }
  }
});

</script>

<style scoped>
.terminal-view-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.terminal-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e; /* Match Xterm.js theme background */
}

.reconnect-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(30, 30, 30, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.reconnect-content {
  text-align: center;
  color: #ffffff;
}

.disconnect-icon {
  margin-bottom: 16px;
}

.disconnect-message {
  font-size: 16px;
  margin-bottom: 20px;
  color: #E6A23C;
  font-weight: 500;
}

.runtime-error-content {
  text-align: center;
  padding: 20px;
}

.error-icon {
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
}

.error-description {
  font-size: 14px;
  color: #e5e7eb;
  margin-bottom: 12px;
}

.error-reasons {
  text-align: left;
  color: #d1d5db;
  font-size: 13px;
  margin: 0 auto;
  display: inline-block;
}

.error-reasons li {
  margin-bottom: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 