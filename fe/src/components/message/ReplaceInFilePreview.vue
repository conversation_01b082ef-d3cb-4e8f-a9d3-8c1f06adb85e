<template>
  <div class="replace-in-file-preview">
    <div class="header">
      <span class="icon i-ph-arrows-left-right"></span>
      <span class="title">File Change: {{ filePath }}</span>
    </div>
    <DiffViewer
      v-if="originalContent !== null && modifiedContent !== null"
      :file-path="filePath"
      :original-content="originalContent"
      :modified-content="modifiedContent"
      :language="language"
      :height="400"
    />
    <div v-else class="error-message">
      Could not parse diff content.
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import DiffViewer from '../DiffViewer.vue';
import { getLanguageFromPath } from '@/utils/languageMap';

const props = defineProps<{
  filePath: string;
  diffContent: string;
}>();

const language = computed(() => getLanguageFromPath(props.filePath));

const { originalContent, modifiedContent } = computed(() => {
  try {
    let original = '';
    let modified = '';
    
    const diffRegex = /<<<<<<< SEARCH\n([\s\S]*?)=======\n([\s\S]*?)>>>>>>> REPLACE/g;
    
    let match;
    let lastIndex = 0;
    
    while ((match = diffRegex.exec(props.diffContent)) !== null) {
      // Append content between diff blocks
      if (match.index > lastIndex) {
        const between = props.diffContent.substring(lastIndex, match.index);
        original += between;
        modified += between;
      }
      
      original += match[1];
      modified += match[2];
      lastIndex = match.index + match[0].length;
    }

    // Append any remaining content after the last diff block
    if (lastIndex < props.diffContent.length) {
      const remaining = props.diffContent.substring(lastIndex);
      original += remaining;
      modified += remaining;
    }
    
    return { originalContent: original, modifiedContent: modified };
  } catch (e) {
    console.error('Failed to parse diff content for replace_in_file:', e);
    return { originalContent: null, modifiedContent: null };
  }
}).value;

</script>

<style scoped>
.replace-in-file-preview {
  border: 1px solid #4a4a50;
  border-radius: 6px;
  background: #2c2c32;
  margin: 8px 0;
}
.header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #3a3a40;
  border-bottom: 1px solid #4a4a50;
  color: #cccccc;
  font-size: 13px;
  font-weight: 500;
}
.icon {
  font-size: 16px;
  color: #c084fc; /* Purple icon for changes */
}
.error-message {
  padding: 20px;
  color: #f87171;
  text-align: center;
}
</style> 