<template>
  <div
    class="message-content w-full px-4 py-3 rounded-lg break-words"
    :class="[bubbleStyle.class]"
  >
    <!-- Content (always rendered) -->
    <div class="prose prose-invert prose-sm max-w-none">
      <div v-for="(segment, index) in parsedContentSegments" :key="index">
        <WriteFilePreview
          v-if="segment.type === 'write_file'"
          :file-path="segment.filePath"
          :content="segment.content"
        />
        <ReplaceInFilePreview
          v-else-if="segment.type === 'replace_in_file'"
          :file-path="segment.filePath"
          :diff-content="segment.diffContent"
        />
        <ToolCallPreview
          v-else-if="segment.type === 'tool_call'"
          :server-name="segment.serverName"
          :tool-name="segment.toolName"
          :arguments-json="segment.argumentsJson"
        />
        <MessageContent
          v-else-if="segment.type === 'text'"
          :content="segment.content"
        />
        <ReplaceInFileResult
          v-else-if="segment.type === 'tool_result_diff'"
          :segment="segment"
        />
        <ToolResultPreview
          v-else-if="segment.type === 'tool_result'"
          :tool-name="segment.toolName"
          :content="segment.content"
        />
      </div>
    </div>

    <!-- Follow-up Question Content (appended if present) -->
    <div v-if="isFollowUp" class="mt-4">
      <div v-if="followUpData?.question" class="question mb-3 text-white font-medium">
        {{ followUpData.question }}
      </div>
      <div v-if="followUpData?.options && followUpData.options.length > 0" class="options-grid grid grid-cols-1 sm:grid-cols-2 gap-2">
        <el-button
          v-for="(option, index) in followUpData.options"
          :key="index"
          type="primary"
          plain
          size="small"
          class="followup-button"
          @click="handleOptionClick(option)"
        >
          {{ option }}
        </el-button>
      </div>
    </div>

    <!-- Tool calls display -->
    <div v-if="tool_calls && tool_calls.length > 0" class="mt-3 space-y-2">
      <div class="text-xs text-[#888888] font-medium">🔧 Tool Calls:</div>
      <div v-for="toolCall in tool_calls" :key="toolCall.call_id" 
           class="bg-[#1e1e1e] p-2 rounded text-xs font-mono">
        <div class="text-[#4fc3f7]">{{ toolCall.mcp_service }}.{{ toolCall.mcp_method }}</div>
        <div class="text-[#888888] mt-1">{{ JSON.stringify(toolCall.mcp_params, null, 2) }}</div>
      </div>
    </div>

    <!-- Tool results display -->
    <div v-if="tool_results && tool_results.length > 0" class="mt-3 space-y-2">
      <div class="text-xs text-[#888888] font-medium">🔧 Results:</div>
      <div v-for="toolResult in tool_results" :key="toolResult.call_id" 
           class="bg-[#1e1e1e] p-2 rounded text-xs">
        <!-- Special handling for replace_in_file results -->
        <template v-if="isReplaceInFileResult(toolResult)">
          <DiffViewer
            :file-path="getReplaceFileData(toolResult).filePath"
            :original-content="getReplaceFileData(toolResult).originalContent"
            :modified-content="getReplaceFileData(toolResult).modifiedContent"
            :unified-diff="getReplaceFileData(toolResult).unifiedDiff"
            :replacement-count="getReplaceFileData(toolResult).replacementCount"
            :preview-changes="getReplaceFileData(toolResult).previewChanges"
            :language="getLanguageFromPath(getReplaceFileData(toolResult).filePath)"
            :height="300"
          />
        </template>
        
        <!-- Default text display for other tool results -->
        <template v-else>
          <div class="text-[#cccccc]">{{ toolResult.content }}</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElButton } from 'element-plus';
import MessageContent from '../MessageContent.vue';
import DiffViewer from '../DiffViewer.vue';
import WriteFilePreview from './WriteFilePreview.vue';
import ToolCallPreview from './ToolCallPreview.vue';
import ToolResultPreview from './ToolResultPreview.vue';
import ReplaceInFilePreview from './ReplaceInFilePreview.vue';
import ReplaceInFileResult from './ReplaceInFileResult.vue';
import type { AgentMessage } from '@/api/agent';

// Define segment types for better type inference
type ContentSegment =
  | { type: 'text'; content: string }
  | { type: 'write_file'; filePath: string; content: string }
  | { type: 'tool_call'; serverName: string; toolName: string; argumentsJson: string }
  | { type: 'tool_result'; toolName: string; content: string }
  | { type: 'tool_result_diff'; filePath: string; originalContent: string; modifiedContent: string; unifiedDiff: string }
  | { type: 'replace_in_file'; filePath: string; diffContent: string };

// --- Props & Emits ---
const props = defineProps<{
  content: string;
  tool_calls?: AgentMessage['tool_calls'];
  tool_results?: AgentMessage['tool_results'];
}>();

const emit = defineEmits<{
  (e: 'option-selected', option: string): void;
}>();

// --- Computed Properties for Dynamic Rendering ---

const isCompletion = computed(() => props.content.includes('<attempt_completion>'));
const isFollowUp = computed(() => props.content.includes('<ask_followup_question>'));

const bubbleStyle = computed(() => {
  if (isCompletion.value) {
    return { class: 'bg-green-800/50 border border-green-600/50 text-[#cccccc]' };
  }
  if (isFollowUp.value) {
    return { class: 'bg-blue-800/50 border border-blue-600/50 text-[#cccccc]' };
  }
  return { class: 'bg-[#2d2d30] text-[#cccccc] border border-[#3e3e42]' }; // Default
});

// NEW: Function to parse message content into segments
const parseMessageContent = (content: string): ContentSegment[] => {
  const segments: ContentSegment[] = [];
  let lastIndex = 0;

  // Combined regex to find either tool calls or tool results
  const toolRegex = /<(use_mcp_tool|tool_result)[\s\S]*?>[\s\S]*?<\/\1>/g;

  let match;
  while ((match = toolRegex.exec(content)) !== null) {
    // Add text before the tool block
    if (match.index > lastIndex) {
      const textBefore = content.substring(lastIndex, match.index).trim();
      if (textBefore) {
        segments.push({ type: 'text', content: textBefore });
      }
    }

    const toolBlock = match[0];
    const tagName = match[1];

    if (tagName === 'use_mcp_tool') {
      // Regex to parse contents of the tool block
      const serverNameMatch = toolBlock.match(/<server_name>([^<]+)<\/server_name>/);
      const toolNameMatch = toolBlock.match(/<tool_name>([^<]+)<\/tool_name>/);
      const argsMatch = toolBlock.match(/<arguments>\s*({[\s\S]*?})\s*<\/arguments>/);

      const toolName = toolNameMatch ? toolNameMatch[1].trim() : 'unknown_tool';
      const serverName = serverNameMatch ? serverNameMatch[1].trim() : 'unknown_server';
      const argumentsJson = argsMatch ? argsMatch[1].trim() : '{}';
      
      if (toolName === 'mcp.projectfs.replace_in_file' && argsMatch) {
        try {
          const args = JSON.parse(argumentsJson);
          segments.push({
            type: 'replace_in_file',
            filePath: args.filePath || 'unknown.txt',
            diffContent: args.diff || ''
          });
        } catch (error) {
          segments.push({ type: 'text', content: toolBlock });
        }
      } else if (toolName === 'mcp.projectfs.write_file' && argsMatch) {
        try {
          const args = JSON.parse(argumentsJson);
          segments.push({ type: 'write_file', filePath: args.path || args.filePath || 'unknown.txt', content: args.content || '' });
        } catch (error) {
          segments.push({ type: 'text', content: toolBlock });
        }
      } else {
        segments.push({ type: 'tool_call', serverName, toolName, argumentsJson });
      }
    } else if (tagName === 'tool_result') {
      const toolNameMatch = toolBlock.match(/tool_name="([^"]+)"/);
      const toolName = toolNameMatch ? toolNameMatch[1].trim() : 'unknown_tool';
      const resultContentMatch = toolBlock.match(/>([\s\S]*?)<\/tool_result>/);
      const resultContent = resultContentMatch ? resultContentMatch[1].trim() : '';
      
      if (toolName === 'mcp.projectfs.replace_in_file') {
        try {
          const parsedResult = JSON.parse(resultContent);
          // Check for success, diff, and the newly available FilePath
          if (parsedResult.success && parsedResult.unifiedDiff && parsedResult.filePath) {
            segments.push({
              type: 'tool_result_diff',
              filePath: parsedResult.filePath, // Directly use FilePath from the result
              originalContent: parsedResult.originalContent,
              modifiedContent: parsedResult.modifiedContent,
              unifiedDiff: parsedResult.unifiedDiff,
            });
          } else {
            segments.push({ type: 'tool_result', toolName, content: resultContent });
          }
        } catch (e) {
          segments.push({ type: 'tool_result', toolName, content: resultContent });
        }
      } else {
      segments.push({ type: 'tool_result', toolName, content: resultContent });
      }
    }

    lastIndex = match.index + toolBlock.length;
  }

  // Add any remaining text after the last tool call
  if (lastIndex < content.length) {
    const remainingText = content.substring(lastIndex).trim();
    if (remainingText) {
      segments.push({ type: 'text', content: remainingText });
    }
  }

  // If no segments were created (i.e., no tool calls), return the whole content as one text segment
  if (segments.length === 0) {
    segments.push({ type: 'text', content });
  }

  return segments;
};

// NEW: Computed property to get the parsed segments
const parsedContentSegments = computed(() => {
  let contentToParse = props.content;

  // If a follow-up question exists, strip it from the content that will be parsed for other segments.
  if (isFollowUp.value) {
    contentToParse = contentToParse.replace(/<ask_followup_question>[\s\S]*?<\/ask_followup_question>/g, '').trim();
  }
  
  // Also strip completion tags if they exist
  if (isCompletion.value) {
    contentToParse = contentToParse.replace(/<\/?attempt_completion>/g, '').replace(/<\/?result>/g, '').trim();
  }
    
  return parseMessageContent(contentToParse);
});

// Segments for file writes
const fileWriteSegments = computed(() => {
  return parsedContentSegments.value.filter(s => s.type === 'write_file');
});

const followUpData = computed(() => {
  if (!isFollowUp.value) return null;

  const questionMatch = props.content.match(/<question>([\s\S]*?)<\/question>/);
  const optionsMatch = props.content.match(/<options>([\s\S]*?)<\/options>/);

  // 如果有 question 标签，提取问题文本
  if (questionMatch) {
    const question = questionMatch[1].trim();
    console.log('Found question tag:', question);
    
    // 如果也有 options 标签，尝试解析选项
    if (optionsMatch) {
      try {
        const optionsArray = JSON.parse(optionsMatch[1].trim());
        if (Array.isArray(optionsArray)) {
          console.log('Found options:', optionsArray);
          return { question, options: optionsArray };
        }
      } catch (e) {
        console.error("Fallback parsing for followup options.");
        const options = optionsMatch[1].trim().replace(/^\[|\]$/g, '').replace(/"/g, '').split(',').map(s => s.trim()).filter(Boolean);
        return { question, options };
      }
    }
    
    // 只有问题，没有选项，也要正常返回
    console.log('Question without options:', question);
    return { question, options: null };
  }

  const cleanContent = props.content.replace(/<\/?ask_followup_question>/g, '').trim();
  if (cleanContent) {
    return { question: cleanContent, options: null };
  }

  console.log('No followup data found');
  return null;
});

// --- Methods ---
const handleOptionClick = (option: string) => {
  emit('option-selected', option);
};

// Helper function to check if tool result is from replace_in_file
const isReplaceInFileResult = (toolResult: any) => {
  console.log('[DEBUG] Checking tool result:', toolResult);
  try {
    const content = JSON.parse(toolResult.content);
    return content.success !== undefined &&
           content.replacementCount !== undefined &&
           content.unifiedDiff !== undefined;
  } catch (error) {
    return false;
  }
};

const getReplaceFileData = (toolResult: any) => {
  try {
    const content = JSON.parse(toolResult.content);
    const toolCall = props.tool_calls?.find((call: any) => call.call_id === toolResult.call_id);
    const filePath = toolCall?.mcp_params?.filePath || 'unknown';
    return {
      filePath,
      originalContent: content.originalContent || '',
      modifiedContent: content.modifiedContent || '',
      unifiedDiff: content.unifiedDiff || '',
      replacementCount: content.replacementCount || 0,
      previewChanges: content.previewChanges || []
    };
  } catch {
    return {
      filePath: 'unknown',
      originalContent: '',
      modifiedContent: '',
      unifiedDiff: '',
      replacementCount: 0,
      previewChanges: []
    };
  }
};

const getLanguageFromPath = (filePath: string): string => {
  const extension = filePath.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'js': case 'jsx': return 'javascript';
    case 'ts': case 'tsx': return 'typescript';
    case 'json': return 'json';
    case 'html': return 'html';
    case 'css': return 'css';
    case 'scss': case 'sass': return 'scss';
    case 'py': return 'python';
    case 'go': return 'go';
    case 'md': return 'markdown';
    case 'yaml': case 'yml': return 'yaml';
    case 'xml': return 'xml';
    case 'sql': return 'sql';
    case 'sh': case 'bash': return 'shell';
    case 'vue': return 'vue';
    default: return 'plaintext';
  }
};
</script>

<style scoped>
/* Scoped styles remain the same */
:deep(.followup-button) {
  width: 100%;
  justify-content: center;
  background-color: rgba(62, 122, 190, 0.3) !important;
  border-color: rgba(79, 195, 247, 0.5) !important;
  color: #cce7ff !important;
  margin: 0 !important;
}
:deep(.followup-button:hover), :deep(.followup-button:focus) {
  background-color: rgba(62, 122, 190, 0.5) !important;
  border-color: #4fc3f7 !important;
}
</style>
