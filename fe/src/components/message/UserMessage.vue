<template>
  <div class="user-message-wrapper flex items-center justify-end">
    <!-- Message Bubble -->
    <div class="user-message-container">
      <div class="user-message-content">
        {{ message.content }}
      </div>
    </div>
    <!-- Retry Button -->
    <div v-if="shouldShowRetry" class="retry-button-container ml-2 self-center">
      <el-button @click="$emit('retry')" type="danger" circle text>
        <el-icon><component is="RefreshRight" /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElButton, ElIcon } from 'element-plus';
import type { AgentMessage } from '@/api/agent';
import { useAgentStore } from '@/store/agent';

const props = defineProps<{
  message: AgentMessage,
  isLast: boolean,
}>();

defineEmits(['retry']);

const agentStore = useAgentStore();

const shouldShowRetry = computed(() => {
  return props.isLast &&
         agentStore.lastMessageError?.messageId === props.message.msg_id &&
         !agentStore.isStreaming;
});
</script>

<style scoped>
.user-message-wrapper {
  margin-bottom: 20px;
}

.user-message-container {
  max-width: 100%; 
}

.user-message-content {
  display: inline-block; /* Allows the bubble to size to its content */
  background-color: #3b82f6; /* A shade of blue */
  color: white;
  border-radius: 12px;
  padding: 10px 15px;
  word-break: break-word; /* 确保长单词或字符串能正常换行 */
  text-align: left; /* 确保气泡内的文本是左对齐的 */
  max-width: 100%; /* Content takes full width of its container */
}

/* 确保按钮是圆形的并且颜色正确 */
.retry-button-container .el-button.is-text.is-circle {
  color: #f56c6c; /* Element Plus danger color */
}
.retry-button-container .el-button.is-text.is-circle:hover {
  background-color: rgba(245, 108, 108, 0.1); /* Light red background on hover */
}
</style>
