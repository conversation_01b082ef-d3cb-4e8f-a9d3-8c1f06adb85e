<template>
  <div class="write-file-preview">
    <div class="file-header">
      <div class="file-info">
        <span class="file-icon" :class="getFileIconClass(filePath)"></span>
        <span class="file-path">{{ filePath }}</span>
        <span class="file-size">{{ formatFileSize(content.length) }}</span>
      </div>
      <div class="actions">
        <el-button size="small" @click="copyContent" text class="action-button">
          <span class="i-ph-copy text-xs"></span>
          <span class="ml-1">复制</span>
        </el-button>
      </div>
    </div>
    
    <div class="editor-container" :style="{ height: editorHeight + 'px' }">
      <VueMonacoEditor
        :value="content"
        :language="getLanguageFromPath(filePath)"
        :options="editorOptions"
        @editor-did-mount="handleEditorDidMount"
        class="file-editor"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import { ElButton, ElMessage } from 'element-plus';
import { getLanguageFromPath } from '@/utils/languageMap';

const props = defineProps<{
  filePath: string;
  content: string;
}>();

const editorHeight = computed(() => {
  const lines = props.content.split('\n').length;
  return Math.min(Math.max(lines * 18 + 20, 200), 500); // min 200px, max 500px
});

const editorOptions = {
  readOnly: true,
  automaticLayout: true,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  theme: 'vs-dark',
  fontSize: 13,
  lineNumbers: 'on' as const,
  folding: true,
  wordWrap: 'on' as const,
};

const handleEditorDidMount = (editor: any) => {
  // When the component mounts and the editor is ready,
  // it might not have the correct layout dimensions if it was initialized
  // inside a container that just became visible.
  // We use nextTick to wait for the DOM to update, then give it a nudge
  // to recalculate its layout.
  nextTick(() => {
    editor.layout();
  });
};

const copyContent = async () => {
  try {
    await navigator.clipboard.writeText(props.content);
    ElMessage.success('文件内容已复制到剪贴板');
  } catch {
    ElMessage.error('复制失败');
  }
};

const getFileIconClass = (path: string) => {
  const lang = getLanguageFromPath(path);
  switch (lang) {
    case 'html': return 'i-ph-file-html text-orange-500';
    case 'css': return 'i-ph-file-css text-blue-500';
    case 'javascript': return 'i-ph-file-js text-yellow-500';
    case 'typescript': return 'i-ph-file-ts text-blue-600';
    case 'vue': return 'i-ph-file-vue text-green-500';
    case 'json': return 'i-ph-file-json text-yellow-600';
    default: return 'i-ph-file-text text-gray-400';
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};
</script>

<style scoped>
.write-file-preview {
  border: 1px solid #3e3e42;
  border-radius: 6px;
  background: #1e1e1e;
  margin: 8px 0;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  border-radius: 6px 6px 0 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.file-icon {
  font-size: 16px;
}

.file-path {
  color: #cccccc;
  font-family: 'Consolas', monospace;
  font-weight: 500;
}

.file-size {
  color: #888888;
  font-size: 11px;
}

.actions {
  display: flex;
  gap: 4px;
}

.action-button {
  color: #cccccc;
}
.action-button:hover {
  color: #ffffff;
}

.editor-container {
  border-radius: 0 0 6px 6px;
  overflow: hidden;
}

.file-editor {
  height: 100%;
  width: 100%;
}
</style> 