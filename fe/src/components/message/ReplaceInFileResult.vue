<template>
  <div class="diff-wrapper">
    <DiffViewerHeader
      :file-path="segment.filePath"
      @fullscreen="openDiffModal"
    />
    <DiffViewer
      :file-path="segment.filePath"
      :original-content="segment.originalContent"
      :modified-content="segment.modifiedContent"
      :unified-diff="segment.unifiedDiff"
      :language="getLanguageFromPath(segment.filePath)"
      :height="250"
    />
    <DiffModal v-model:visible="isDiffModalVisible" :diff-data="currentDiffData" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DiffViewer from '../DiffViewer.vue';
import DiffViewerHeader from '../DiffViewerHeader.vue';
import DiffModal, { type DiffData } from '../DiffModal.vue';
import { getLanguageFromPath } from '@/utils/languageMap';

const props = defineProps<{
  segment: {
    filePath: string;
    originalContent: string;
    modifiedContent: string;
    unifiedDiff: string;
  };
}>();

const isDiffModalVisible = ref(false);
const currentDiffData = ref<DiffData | null>(null);

const openDiffModal = () => {
  currentDiffData.value = {
    filePath: props.segment.filePath,
    originalContent: props.segment.originalContent,
    modifiedContent: props.segment.modifiedContent,
    unifiedDiff: props.segment.unifiedDiff,
  };
  isDiffModalVisible.value = true;
};
</script>

<style scoped>
.diff-wrapper {
  border: 1px solid #4a4a50;
  border-radius: 6px;
  background: #2c2c32;
  margin: 8px 0; /* Vertical margin only */
  overflow: hidden;
}
</style> 