<template>
  <div class="tool-result-preview">
    <div class="header">
      <span class="icon i-ph-terminal-window"></span>
      <span class="title">Tool Result: {{ toolName }}</span>
    </div>
    <div class="content">
      <!-- Special rendering for bash_exec -->
      <div v-if="isBashExec && parsedContent" class="bash-result">
        <div v-if="parsedContent.stdout" class="output-section">
          <div class="label">STDOUT:</div>
          <pre class="output-content stdout">{{ parsedContent.stdout }}</pre>
        </div>
        <div v-if="parsedContent.stderr" class="output-section">
          <div class="label stderr-label">STDERR:</div>
          <pre class="output-content stderr">{{ parsedContent.stderr }}</pre>
        </div>
        <div class="exit-code" :class="parsedContent.exit_code === 0 ? 'success' : 'error'">
          Exit Code: {{ parsedContent.exit_code }}
        </div>
      </div>
      <!-- Default rendering for other tools -->
      <pre v-else class="default-content">{{ content }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  toolName: string;
  content: string;
}>();

const isBashExec = computed(() => {
  return props.toolName.includes('bash_exec');
});

const parsedContent = computed(() => {
  if (isBashExec.value) {
    try {
      return JSON.parse(props.content);
    } catch (e) {
      console.error('Failed to parse bash_exec result content:', e);
      return null;
    }
  }
  return null;
});
</script>

<style scoped>
.tool-result-preview {
  border: 1px solid #4a4a50;
  border-radius: 6px;
  background: #2c2c32;
  margin: 8px 0;
  font-family: 'Inter', sans-serif;
}
.header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #3a3a40;
  border-bottom: 1px solid #4a4a50;
  color: #cccccc;
  font-size: 13px;
  font-weight: 500;
}
.icon {
  font-size: 16px;
  color: #82c994; /* Greenish icon for results */
}
.content {
  padding: 10px;
  font-size: 12px;
}
.bash-result {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.output-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.label {
  color: #999999;
  font-weight: 500;
}
.stderr-label {
  color: #f87171; /* Red color for stderr label */
}
.output-content {
  background: #1e1e1e;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  color: #e0e0e0;
  font-family: 'Consolas', 'Monaco', monospace;
}
.stderr {
  color: #fca5a5; /* Lighter red for stderr text */
}
.exit-code {
  font-family: 'Consolas', 'Monaco', monospace;
  padding: 4px 8px;
  border-radius: 4px;
  align-self: flex-start;
}
.exit-code.success {
  background-color: rgba(74, 222, 128, 0.1);
  color: #4ade80;
}
.exit-code.error {
  background-color: rgba(248, 113, 113, 0.1);
  color: #f87171;
}
.default-content {
  background: #1e1e1e;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  color: #e0e0e0;
  font-family: 'Consolas', 'Monaco', monospace;
}
</style> 