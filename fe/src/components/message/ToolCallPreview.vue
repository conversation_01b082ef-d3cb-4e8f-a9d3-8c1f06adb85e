<template>
  <div class="tool-call-preview">
    <div class="header">
      <span class="icon i-ph-wrench"></span>
      <span class="title">Tool Call</span>
    </div>
    <div class="content">
      <div class="field">
        <span class="label">Server:</span>
        <span class="value server">{{ serverName }}</span>
      </div>
      <div class="field">
        <span class="label">Tool:</span>
        <span class="value tool">{{ toolName }}</span>
      </div>
      <div class="field">
        <span class="label">Arguments:</span>
        <pre class="value args">{{ formattedArgs }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  serverName: string;
  toolName: string;
  argumentsJson: string;
}>();

const formattedArgs = computed(() => {
  try {
    const args = JSON.parse(props.argumentsJson);
    return JSON.stringify(args, null, 2);
  } catch {
    return props.argumentsJson;
  }
});
</script>

<style scoped>
.tool-call-preview {
  border: 1px solid #4a4a50;
  border-radius: 6px;
  background: #2c2c32;
  margin: 8px 0;
  font-family: 'Inter', sans-serif;
}
.header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #3a3a40;
  border-bottom: 1px solid #4a4a50;
  color: #cccccc;
  font-size: 13px;
  font-weight: 500;
}
.icon {
  font-size: 16px;
  color: #88aadd;
}
.content {
  padding: 10px;
  font-size: 12px;
}
.field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.field + .field {
  margin-top: 8px;
}
.label {
  color: #999999;
}
.value {
  color: #e0e0e0;
  font-family: 'Consolas', 'Monaco', monospace;
}
.args {
  background: #1e1e1e;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}
</style> 