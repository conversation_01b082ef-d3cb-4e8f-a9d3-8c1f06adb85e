<template>
  <div class="agent-chat flex flex-col h-full bg-[#1e1e1e]">
    <!-- Header -->
    <div class="chat-header flex items-center justify-between px-4 py-3 bg-[#2d2d30] border-b border-[#3e3e42]">
      <div class="flex items-center space-x-2">
        <span class="i-ph-robot text-[#4fc3f7] text-lg"></span>
        <h2 class="text-white font-medium">AI Assistant</h2>
        <div v-if="agentStore.isStreaming" class="flex items-center space-x-1">
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span class="text-green-400 text-xs">Thinking...</span>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- Export button -->
        <el-dropdown @command="handleExportCommand">
          <el-button size="small" type="text" class="text-[#cccccc] hover:text-white">
            <span class="i-ph-download text-sm"></span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="markdown">Export as Markdown</el-dropdown-item>
              <el-dropdown-item command="json">Export as JSON</el-dropdown-item>
              <el-dropdown-item command="txt">Export as Text</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <!-- Clear button -->
        <el-button 
          size="small" 
          type="text" 
          class="text-[#cccccc] hover:text-white"
          @click="handleClearChat"
          :disabled="agentStore.isLoading"
        >
          <span class="i-ph-trash text-sm"></span>
        </el-button>
      </div>
    </div>

    <!-- Messages Area -->
    <div 
      ref="messagesContainer"
      class="messages-area flex-1 overflow-y-auto p-4 space-y-4"
      @scroll="handleScroll"
    >
      <!-- Welcome message when no conversation -->
      <div v-if="!agentStore.hasConversation || agentStore.messages.length === 0" class="text-center py-8">
        <div class="text-[#666666] space-y-2">
          <div class="text-6xl mb-4">🤖</div>
          <h3 class="text-lg font-medium text-[#cccccc]">AI Assistant Ready</h3>
          <p class="text-sm">Ask me anything about your project. I can help you with code, debugging, and more!</p>
        </div>
      </div>

      <!-- Message list -->
      <template v-for="(message, index) in agentStore.messages" :key="message.msg_id || `msg-${index}`">
        <div
          v-if="message.content || (message.tool_calls && message.tool_calls.length > 0)"
          :class="[
            'message flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'flex flex-col w-full max-w-[90%]', // Set max-width to 90%
              message.role === 'user' ? 'items-end' : 'items-start'
            ]"
          >
            <!-- The new renderer component handles the entire message bubble -->
            <MessageRenderer 
              :message="message" 
              :is-last="index === agentStore.messages.length - 1"
              @option-selected="handleOptionSelected" 
              @retry="handleRetry"
            />

            <!-- Timestamp -->
            <div class="text-xs text-[#666666] mt-2 px-1">
              {{ formatTimestamp(message.timestamp) }}
            </div>
          </div>
        </div>
      </template>

      <!-- Loading indicator for initial load, not for streaming -->
      <div v-if="agentStore.isLoading && !agentStore.isStreaming" class="flex justify-start">
        <div class="bg-[#2d2d30] px-4 py-3 rounded-lg border border-[#3e3e42]">
          <div class="flex items-center space-x-2">
            <div class="animate-spin w-4 h-4 border-2 border-[#4fc3f7] border-t-transparent rounded-full"></div>
            <span class="text-[#cccccc] text-sm">Loading...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Error display -->
    <div v-if="agentStore.error" class="error-banner bg-red-900/20 border border-red-500/30 mx-4 my-2 p-3 rounded">
      <div class="flex items-center space-x-2">
        <span class="i-ph-warning text-red-400"></span>
        <span class="text-red-400 text-sm">{{ agentStore.error }}</span>
        <el-button 
          size="small" 
          type="text" 
          class="text-red-400 hover:text-red-300 ml-auto"
          @click="agentStore.error = null"
        >
          <span class="i-ph-x text-xs"></span>
        </el-button>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area bg-[#2d2d30] border-t border-[#3e3e42] p-4">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="2"
          placeholder="Ask me anything about your project..."
          class="agent-input"
          :disabled="agentStore.isLoading || agentStore.isStreaming"
          @keydown="handleKeyDown"
          @compositionstart="handleCompositionStart"
          @compositionend="handleCompositionEnd"
          @compositionupdate="handleCompositionUpdate"
        />
        <el-button
          :disabled="!inputMessage.trim() || agentStore.isLoading || agentStore.isStreaming"
          @click="handleSendMessage"
          class="send-button"
        >
          <span v-if="agentStore.isStreaming" class="i-ph-stop text-2xl"></span>
          <span v-else class="i-ph-arrow-circle-up text-2xl"></span>
        </el-button>
      </div>

      <!-- Keyboard shortcuts hint -->
      <div class="mt-2 text-xs text-[#666666]">
        <span class="i-ph-keyboard text-sm mr-1"></span>
        Enter 发送消息，Shift+Enter 换行
      </div>
      
      <!-- Context files selector (future feature) -->
      <div v-if="false" class="mt-3">
        <div class="text-xs text-[#888888] mb-1">Include files for context:</div>
        <div class="flex flex-wrap gap-1">
          <!-- Placeholder for context file tags -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useAgentStore } from '@/store/agent'
import { useFsStore } from '@/store/fs'
import MessageRenderer from './MessageRenderer.vue'
import type { AgentMessage } from '@/api/agent';

interface Props {
  projectId: string
}

const props = defineProps<Props>()

// Stores
const agentStore = useAgentStore()
const fsStore = useFsStore()

// Refs
const messagesContainer = ref<HTMLElement>()
const inputMessage = ref('')
const isUserScrolled = ref(false)
const observer = ref<MutationObserver | null>(null);

// IME (Input Method Editor) state management
const isComposing = ref(false)
const compositionText = ref('')

// Watch for projectId changes to reload conversation
watch(() => props.projectId, async (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    console.log('[AgentChat] Project changed from', oldProjectId, 'to', newProjectId)
    // Stop any ongoing streaming from the previous project
    agentStore.stopStreaming()
    // Reset the agent store to clear previous project's data
    agentStore.reset()
    // Load the new project's conversation
    await agentStore.loadConversation(newProjectId)
    await nextTick()
    scrollToBottom()
    // Reset scroll state
    isUserScrolled.value = false
  }
}, { immediate: false })

// Lifecycle
onMounted(async () => {
  if (props.projectId) {
    await agentStore.loadConversation(props.projectId)
    await nextTick()
    scrollToBottom()
  }

  // Use MutationObserver to detect changes in the message container DOM
  if (messagesContainer.value) {
    observer.value = new MutationObserver(() => {
      // Any DOM change in the container should trigger a scroll check.
      if (!isUserScrolled.value) {
        // By using requestAnimationFrame, we ensure that the scroll happens
        // right before the next browser repaint, which smooths out the
        // experience during rapid DOM changes from streaming.
        window.requestAnimationFrame(() => {
            scrollToBottom();
        });
      }
    });

    observer.value.observe(messagesContainer.value, {
      childList: true, // observes direct children additions or removals
      subtree: true,   // observes all descendants
      characterData: true, // observes changes to text nodes, crucial for streaming
    });
  }
})

onUnmounted(() => {
  agentStore.stopStreaming()
  if (observer.value) {
    observer.value.disconnect();
  }
})

// IME (Input Method Editor) event handlers
const handleCompositionStart = () => {
  isComposing.value = true
  console.log('[AgentChat] Composition started')
}

const handleCompositionEnd = () => {
  isComposing.value = false
  compositionText.value = ''
  console.log('[AgentChat] Composition ended')
}

const handleCompositionUpdate = (event: CompositionEvent) => {
  compositionText.value = event.data || ''
  console.log('[AgentChat] Composition update:', event.data)
}

// Enhanced keyboard event handler that respects IME state
const handleKeyDown = (event: KeyboardEvent) => {
  // If IME is active (composing), don't intercept any keyboard events
  if (isComposing.value) {
    console.log('[AgentChat] IME is active, ignoring keyboard event:', event.key)
    return
  }
  
  // Handle Enter key
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      // Shift + Enter: Allow default behavior (newline)
      console.log('[AgentChat] Shift+Enter: New line')
      return
    } else {
      // Single Enter: Send message
      event.preventDefault()
      console.log('[AgentChat] Enter: Sending message')
      handleSendMessage()
    }
  }
}

// Methods

const handleSendMessage = async () => {
  if (!inputMessage.value.trim() || agentStore.isLoading || agentStore.isStreaming) return

  const content = inputMessage.value.trim()
  inputMessage.value = ''

  try {
    // When the user sends a message, we want to re-engage auto-scrolling.
    isUserScrolled.value = false

    await agentStore.sendUserMessage(props.projectId, {
      message: content,
      context: {
        // TODO: Add current file context from fsStore
        open_files: [] // TODO: Get from fsStore.openFiles
      }
    })
    
    // The deep watcher on agentStore.messages will handle scrolling now.
    
  } catch (error) {
    console.error('[AgentChat] Failed to send message:', error)
  }
}

const handleOptionSelected = async (option: string) => {
  if (!option.trim() || agentStore.isLoading || agentStore.isStreaming) return

  // This function is very similar to handleSendMessage, but uses the provided option as content
  try {
    // Re-engage auto-scrolling when an option is selected.
    isUserScrolled.value = false;
    
    await agentStore.sendUserMessage(props.projectId, {
      message: option,
      context: {
        // TODO: Add current file context from fsStore
        open_files: [] // TODO: Get from fsStore.openFiles
      }
    })
    
    // The deep watcher will handle scrolling.
    
  } catch (error) {
    console.error('[AgentChat] Failed to send selected option:', error)
  }
}

const handleClearChat = async () => {
  if (agentStore.isLoading) return

  try {
    // Show confirmation dialog
    await ElMessageBox.confirm(
      '确定要删除所有对话历史吗？此操作无法撤销。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // If user confirmed, proceed with clearing
    await agentStore.clearMessages(props.projectId)
    isUserScrolled.value = false
  } catch (error) {
    // ElMessageBox.confirm throws an error when cancelled, so we need to distinguish
    if (error === 'cancel') {
      console.log('[AgentChat] Clear chat cancelled by user')
      return
    }
    console.error('[AgentChat] Failed to clear chat:', error)
  }
}

const handleExportCommand = (command: string) => {
  const format = command as 'markdown' | 'json' | 'txt'
  agentStore.exportChat(props.projectId, format)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const handleScroll = () => {
  if (!messagesContainer.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value
  const isAtBottom = scrollHeight - scrollTop <= clientHeight + 10
  
  isUserScrolled.value = !isAtBottom
}

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const handleRetry = () => {
  agentStore.retryLastMessage();
};
</script>

<style scoped>
.agent-chat {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.messages-area {
  scrollbar-width: thin;
  scrollbar-color: #404040 #1e1e1e;
}

.messages-area::-webkit-scrollbar {
  width: 8px;
}

.messages-area::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.messages-area::-webkit-scrollbar-thumb {
  background-color: #404040;
  border-radius: 4px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
  background-color: #555555;
}

/* Custom input styling */
:deep(.agent-input .el-textarea__inner) {
  background-color: #1e1e1e !important;
  border-color: #3e3e42 !important;
  color: #cccccc !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
  /* Add this to ensure the textarea resizes correctly */
  resize: none !important;
  overflow-y: auto !important; /* In case content exceeds max-rows */
}

:deep(.agent-input .el-textarea__inner:focus) {
  border-color: #4fc3f7 !important;
  box-shadow: 0 0 0 1px #4fc3f7 !important;
}

:deep(.agent-input .el-textarea__inner::placeholder) {
  color: #666666 !important;
}

/* Message content styling */
.prose {
  color: inherit;
}

.prose pre {
  background-color: #1e1e1e;
  border: 1px solid #3e3e42;
}

.prose code {
  background-color: #2d2d30;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  /* Ensure code blocks don't get their own background color from prose spec */
  color: inherit;
}

.prose blockquote {
  border-left-color: #4fc3f7;
}

/* Custom styling for user message bubbles, handled here for alignment */
.message-content.user-message {
  background-color: #0e4f88;
  color: white;
}

/* General component styling */
.agent-chat {
  /* You can add global styles for the chat component here if needed */
}

/* Custom styles for the input textarea */
.agent-input :deep(.el-textarea__inner) {
  background-color: #1e1e1e;
  border-color: #3e3e42;
  color: #cccccc;
  border-radius: 8px;
  box-shadow: none;
  padding: 10px;
  line-height: 1.5;
  resize: none; /* Disable native resize handle */
}

.agent-input :deep(.el-textarea__inner:focus) {
  border-color: #4fc3f7;
  box-shadow: 0 0 0 1px #4fc3f7;
}

.agent-input :deep(.el-textarea__inner::placeholder) {
  color: #666666;
}

/* New wrapper for integrated input and button */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-end; /* Align items to the bottom */
  background-color: #1e1e1e;
  border: 1px solid #3e3e42;
  border-radius: 8px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.input-wrapper:focus-within {
  border-color: #4fc3f7;
  box-shadow: 0 0 0 1px #4fc3f7;
}

/* Update textarea to be transparent within the new wrapper */
.agent-input :deep(.el-textarea__inner) {
  background-color: transparent;
  border-color: transparent;
  color: #cccccc;
  border-radius: 8px;
  box-shadow: none !important; /* Override focus shadow on textarea itself */
  padding: 10px 48px 10px 10px; /* Right padding for the button */
  line-height: 1.5;
  resize: none;
  height: auto; /* Allow it to grow based on rows */
  min-height: calc(2 * 1.5em + 22px); /* 2 lines + padding */
}

/* Update send button to be positioned absolutely inside the wrapper */
.send-button {
  position: absolute;
  right: 5px;
  bottom: 5px;
  width: 36px;
  height: 36px;
  border: 1px solid transparent;
  background-color: transparent !important;
  color: #4fc3f7;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: scale(1);
}

.send-button:hover,
.send-button:focus {
  background-color: transparent !important; /* Force override */
  color: #80deea; /* Brighter, electric cyan on hover */
  transform: scale(1.15); /* Pop effect */
  box-shadow: 0 0 14px rgba(79, 195, 247, 0.5), 0 0 5px rgba(128, 222, 234, 0.7); /* Sci-fi glow - enhanced */
}

.send-button:disabled {
  background-color: transparent !important;
  border-color: transparent;
  color: #555555;
  cursor: not-allowed;
  transform: scale(1); /* Reset transform */
  box-shadow: none; /* Reset glow */
}

.send-button.is-loading {
  /* Add styles for loading state if needed */
}

/* Scroll to bottom button styling */
.scroll-to-bottom {
  position: absolute;
  bottom: 1rem;
  right: 1.5rem;
  z-index: 10;
}
</style> 