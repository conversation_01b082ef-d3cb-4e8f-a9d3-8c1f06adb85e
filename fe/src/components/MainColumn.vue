<template>
  <div class="main-column flex flex-col h-full">
    <!-- Tab Bar -->
    <div class="tab-bar flex items-center h-[30px] bg-[#29292e] flex-shrink-0 overflow-x-auto">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        :class="[
          'tab flex items-center px-2.5 h-full cursor-pointer relative flex-shrink-0',
          { 'active-tab': activeTabId === tab.id },
          { 'modified-tab': tab.isModified }
        ]"
        @click="setActiveTab(tab.id)"
      >
        <span :class="[
          'text-sm whitespace-nowrap',
          activeTabId === tab.id ? 'text-white' : 'text-[#bdbdbd]',
          { 'text-yellow-400': tab.isModified },
          { 'text-red-400': getFileConflictStatus(tab.id) }
        ]">{{ tab.title }}</span>
        
        <!-- File conflict indicator -->
        <span 
          v-if="getFileConflictStatus(tab.id)"
          class="i-ep-warning-filled w-3 h-3 ml-1 text-red-400"
          :title="getFileConflictMessage(tab.id)"
        ></span>
        

        
        <span 
          class="i-ep-close w-3 h-3 ml-1.5 text-[#999999] hover:text-white cursor-pointer"
          @click.stop="handleCloseTab(tab.id, $event)"
        ></span>
        <!-- Separator for inactive tabs -->
        <div
          v-if="activeTabId !== tab.id"
          class="separator absolute right-0 top-[5px] bottom-[5px] w-[1px] bg-[#4d4d54]"
        ></div>
         <!-- Indicator for active tab -->
        <div
          v-if="activeTabId === tab.id"
          class="indicator absolute bottom-0 left-0 right-0 h-[2px] bg-[#4d4d54]"
        ></div>
      </div>
      <div class="new-tab-icon i-ep-plus ml-2.5 text-[#999999] hover:text-white cursor-pointer" @click="openNewEditorTabPlaceholder"></div>
    </div>

    <!-- Content Area -->
    <div class="content-area flex-grow relative" :style="{ backgroundColor: columnBgColor }">
      <!-- Iterate over all tabs to create their content containers -->
      <template v-for="tabInLoop in props.tabs" :key="tabInLoop.id">
        <!-- Editor Content Area for a specific tab -->
        <div v-if="tabInLoop.type === 'editor'"
             v-show="activeTabId === tabInLoop.id"
             class="w-full h-full editor-content-wrapper">
          <!-- Loading State -->
          <div v-if="fsStore.isFileLoading(tabInLoop.id)"
               class="p-4 w-full h-full flex items-center justify-center text-white">Loading {{ tabInLoop.title }}...
          </div>
          <!-- Error State -->
          <div v-else-if="fsStore.getFileError(tabInLoop.id)"
               class="p-4 text-red-500">Error: {{ fsStore.getFileError(tabInLoop.id) }}
          </div>
          <!-- Monaco Editor -->
          <MonacoEditor
            v-else
            :path="tabInLoop.id"
            :language="tabInLoop.language || 'plaintext'"
            :options="editorOptions"
            :value="fsStore.openFiles[tabInLoop.id]?.currentContent ?? ''"
            @update:value="(newContent) => handleEditorContentChange(newContent, tabInLoop.id)"
            @editor-did-mount="(editor: any) => handleEditorDidMount(editor, tabInLoop.id)"
            class="editor-instance w-full h-full"
          />
        </div>

        <!-- TerminalView for a specific tab -->
        <TerminalView
          v-else-if="tabInLoop.type === 'terminal'"
          v-show="activeTabId === tabInLoop.id"
          :terminalId="tabInLoop.id"
          :projectId="tabInLoop.projectId"
          class="w-full h-full"
          :ref="(el) => setTerminalViewRef(el, tabInLoop.id)"
        />

        <!-- Other Tab Types for a specific tab -->
        <div v-else-if="tabInLoop.type === 'other'"
             v-show="activeTabId === tabInLoop.id"
             class="w-full h-full">
           <!-- Agent Chat Component -->
           <AgentChat
             v-if="tabInLoop.id === 'agent'"
             :project-id="tabInLoop.projectId || ''"
             class="w-full h-full"
           />
           <!-- Other content types -->
           <div v-else class="p-4 w-full h-full">
             <p class="text-white">Content for {{ tabInLoop.title }} (type: {{ tabInLoop.type }})</p>
           </div>
        </div>
      </template>

      <!-- Fallback if no active tab (or active tab has no matching content renderer) -->
      <div v-show="!activeTab"
           class="p-4 w-full h-full flex items-center justify-center text-gray-500">
        Select a file or open a terminal.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, shallowRef, nextTick } from 'vue'
import MonacoEditor from '@guolao/vue-monaco-editor'
import { useFsStore } from '@/store/fs';
import { useTerminalStore, type TerminalInstance } from '@/store/terminal';
import TerminalView from './TerminalView.vue';
import AgentChat from './AgentChat.vue';

// --- Define Component Interfaces ---
interface Tab {
  id: string; 
  title: string;
  type: 'editor' | 'terminal' | 'other';
  language?: string;
  projectId?: string; 
  isModified?: boolean;
}

interface Props {
  tabs?: Tab[];
  initialActiveTabId?: string | null; 
  columnBgColor: string;
}

// --- Props and Emits ---
const props = defineProps<Props>();
const emit = defineEmits<{ 
  (e: 'update:activeTabId', id: string | null): void;
  (e: 'close-tab', id: string): void
}>();

// --- Store and State ---
const fsStore = useFsStore();
const terminalStore = useTerminalStore();
const activeTabId = ref<string | null>(props.initialActiveTabId === undefined ? null : props.initialActiveTabId);
const editorInstances = shallowRef<Record<string, any>>({}); // Stores editor instances mapped by tabId
const terminalViewRefs = shallowRef<Record<string, any>>({}); // Stores TerminalView component instances mapped by tabId

// Function to assign refs in v-for
const setTerminalViewRef = (el: any, tabId: string) => {
  if (el) {
    terminalViewRefs.value[tabId] = el;
  } else {
    // Clean up if element is unmounted (though with v-show, it might not be strictly necessary to delete)
    delete terminalViewRefs.value[tabId];
  }
};

// --- Editor Options ---
const editorOptions = ref({
  automaticLayout: true,
  fontSize: 14,
  minimap: { enabled: false },
  theme: 'vs-dark'
});

// --- Computed Properties ---
const activeTab = computed<Tab | undefined>(() => {
  return props.tabs?.find(tab => tab.id === activeTabId.value);
});

// --- Helper Functions ---
// getFilenameFromPath and getLanguageFromPath are not directly used in script setup after refactor,
// but they might be useful for tab display logic if it were part of this component.
// Keeping them in case they are used by parent or for future tab title generation here.
/*
function getFilenameFromPath(filePath: string): string {
  if (!filePath) return '';
  return filePath.split('/').pop() || filePath;
}

function getLanguageFromPath(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'js': case 'jsx': return 'javascript';
    case 'ts': case 'tsx': return 'typescript';
    case 'json': return 'json';
    case 'html': return 'html';
    case 'css': return 'css';
    case 'py': return 'python';
    case 'go': return 'go';
    case 'md': return 'markdown';
    case 'yaml': case 'yml': return 'yaml';
    default: return 'plaintext';
  }
}
*/

// --- Editor Lifecycle and Content Management ---
const handleEditorDidMount = (editor: any, tabId: string) => {
  console.log(`[MainColumn handleEditorDidMount] Editor mounted for tab: ${tabId}`);
  editorInstances.value[tabId] = editor;
  if (activeTabId.value === tabId) {
    nextTick(() => { // Ensure editor is fully rendered before focusing
        editor.focus();
    });
  }
};

const handleEditorContentChange = (newContent: string, tabId: string) => {
  // Update store only if the content has actually changed from what store has.
  // This check helps prevent feedback loops if store updates also trigger editor updates.
  if (fsStore.openFiles[tabId]?.currentContent !== newContent) {
    console.log(`[MainColumn handleEditorContentChange] Editor for tab ${tabId} changed. Updating store.`);
    fsStore.updateOpenFileContent({ path: tabId, newContent: newContent });
  }
};

// --- Tab Click Handler (New or Modified) ---
const setActiveTab = (id: string) => {
  if (activeTabId.value === id) return; // Do nothing if already active

  console.log(`[MainColumn setActiveTab] Setting internal activeTabId to: ${id}`);
  activeTabId.value = id;
  emit('update:activeTabId', id); // Notify parent about the change
};

// --- Watchers ---

// 1. Sync with props.initialActiveTabId
watch(() => props.initialActiveTabId, (newId) => {
  const newActiveId = newId === undefined ? null : newId;
  if (newActiveId !== activeTabId.value) {
    console.log(`[MainColumn Watcher initialActiveTabId] Prop changed. Old: ${activeTabId.value}, New: ${newActiveId}`);
    activeTabId.value = newActiveId;
  }
});

// 2. Watcher: When this column's internal activeTabId changes,
//    ensure necessary content is loaded for editor tabs and manage focus.
watch(activeTabId, (newPathId, oldPathId) => {
  console.log(`[MainColumn Watcher activeTabId] Internal activeTabId changed from ${oldPathId} to ${newPathId}`);
  
  const currentActiveTabDetails = activeTab.value; // Use the computed activeTab

  if (newPathId && currentActiveTabDetails?.type === 'editor') {
    const filePath = newPathId;
    const fileState = fsStore.openFiles[filePath];

    console.log(`[MainColumn Watcher activeTabId] New active tab is editor: ${filePath}. File state in store:`, fileState);

    if (!fileState || (fileState.currentContent === null && !fileState.loading && !fileState.error)) {
      // File not in store, or in store but content is null (placeholder) and not loading/error -> fetch it
      console.log(`[MainColumn Watcher activeTabId] File ${filePath} needs fetching. Fetching.`);
      fsStore.fetchFileContent(filePath);
    }
    
    // Attempt to focus the editor for the new active tab
    nextTick(() => {
        const editorInstance = editorInstances.value[newPathId];
        if (editorInstance && typeof editorInstance.focus === 'function') {
            console.log(`[MainColumn Watcher activeTabId] Focusing editor for tab ${newPathId}`);
            editorInstance.focus();
        } else {
            console.log(`[MainColumn Watcher activeTabId] Editor instance for tab ${newPathId} not found or not focusable yet.`);
        }
    });

  } else if (newPathId && currentActiveTabDetails?.type === 'terminal') {
    // If needed, add logic here to focus the terminal or interact with terminalStore
    // For now, TerminalView itself might handle focus based on its own props or visibility.
    console.log(`[MainColumn Watcher activeTabId] New active tab is terminal: ${newPathId}`);
    nextTick(() => {
      const terminalInstanceRef = terminalViewRefs.value[newPathId];
      if (terminalInstanceRef && typeof terminalInstanceRef.fitTerminal === 'function') {
        console.log(`[MainColumn Watcher activeTabId] Calling fitTerminal() for terminal tab ${newPathId}`);
        terminalInstanceRef.fitTerminal();
      } else {
        console.warn(`[MainColumn Watcher activeTabId] TerminalView instance or fitTerminal method for tab ${newPathId} not found.`);
      }
      if (terminalInstanceRef && typeof terminalInstanceRef.focusTerminal === 'function') {
        console.log(`[MainColumn Watcher activeTabId] Calling focusTerminal() for terminal tab ${newPathId}`);
        terminalInstanceRef.focusTerminal();
      } else {
        console.warn(`[MainColumn Watcher activeTabId] TerminalView instance or focusTerminal method for tab ${newPathId} not found.`);
      }
    });
  }
}, { immediate: true }); // Immediate to handle initial activeTabId

// 3. Sync activeTabId with terminalStore.activeTerminalId (for terminal tabs)
watch(() => terminalStore.activeTerminalId, (newTermId) => {
    if (newTermId && activeTabId.value !== newTermId) {
        const terminalTabExists = props.tabs?.some(tab => tab.id === newTermId && tab.type === 'terminal');
        if (terminalTabExists) {
            console.log(`[MainColumn Watcher terminalStore.activeTerminalId] Store changed active terminal to ${newTermId}. No local activeTabId sync.`);
            // The problematic assignment `activeTabId.value = newTermId;` should be removed.
            // AppLayout should be the source of truth for syncing activeTabId with global stores.
        }
    }
});

// --- Tab Management Methods ---
const handleCloseTab = (tabId: string, event?: MouseEvent) => {
  if (event) event.stopPropagation();
  console.log(`[MainColumn handleCloseTab] Emitting close-tab event for: ${tabId}`);
  emit('close-tab', tabId);
};

const openNewEditorTabPlaceholder = () => {
  console.log("Placeholder: Open new editor tab clicked.");
  // This would typically emit an event to parent (AppLayout) to create a new tab.
};

// --- File Conflict Status Helpers ---
const getFileConflictStatus = (tabId: string): boolean => {
  const fileState = fsStore.getRawFileState(tabId);
  return !!(fileState?.isDeleted || fileState?.isRenamed || fileState?.externallyModified);
};

const getFileConflictMessage = (tabId: string): string => {
  const fileState = fsStore.getRawFileState(tabId);
  if (!fileState) return '';
  
  if (fileState.isDeleted) {
    return 'File has been deleted externally';
  }
  if (fileState.isRenamed) {
    return 'File has been renamed externally';
  }
  if (fileState.externallyModified) {
    return 'File has been modified externally - may have conflicts';
  }
  return '';
};



// --- Lifecycle Hooks ---
onMounted(() => {
  // Initial focus for the active tab if any
  if (activeTabId.value) {
    const currentActiveTabDetails = activeTab.value;
    if (currentActiveTabDetails?.type === 'editor') {
      nextTick(() => {
        const editorInstance = editorInstances.value[activeTabId.value!];
        if (editorInstance && typeof editorInstance.focus === 'function') {
          console.log(`[MainColumn onMounted] Focusing initial editor for tab ${activeTabId.value}`);
          editorInstance.focus();
        }
      });
    }
  }
  console.log(`[MainColumn ${props.columnBgColor}] Mounted. Initial active tab ID: ${activeTabId.value}. Tabs count: ${props.tabs?.length || 0}`);
});

onUnmounted(() => {
  // Clean up editor instances if necessary, though Vue's unmount should handle components.
  // If Monaco creates global resources not tied to component instance, manual cleanup might be needed.
  // For @guolao/vue-monaco-editor, this is generally not required.
  editorInstances.value = {}; // Clear the map
  console.log(`[MainColumn ${props.columnBgColor}] Unmounted.`);
});

</script>

<style scoped>
.main-column {
}

.tab-bar {
  user-select: none; 
}

.tab {
  border-right: 1px solid #3a3a40; 
  transition: background-color 0.2s ease;
}
.tab:last-child {
}

.tab:hover {
  background-color: #38383e;
}

.active-tab {
  background-color: v-bind(columnBgColor); 
  position: relative;
  border-right: none; 
}
.active-tab + .tab .separator {
    display: none; 
}

.modified-tab span:first-child { 
  font-style: italic;
}

.content-area {
  overflow: hidden; 
}

.editor-instance {
}

.tab-bar::-webkit-scrollbar {
  height: 4px;
}
.tab-bar::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 2px;
}
.tab-bar::-webkit-scrollbar-track {
  background-color: #29292e;
}
</style>