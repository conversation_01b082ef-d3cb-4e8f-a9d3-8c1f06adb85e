<template>
  <div class="answer-form">
    <!-- Header -->
    <div class="answer-header flex items-center justify-between mb-6">
      <div class="flex items-center space-x-2">
        <el-icon class="text-primary"><EditPen /></el-icon>
        <h3 class="text-lg font-medium text-white">Answer Questions</h3>
        <el-tag v-if="questions.length > 0" size="small" :type="getProgressTagType()">
          {{ answeredCount }}/{{ questions.length }} completed
        </el-tag>
      </div>
      
      <div class="flex items-center space-x-2">
        <el-button
          v-if="hasUnansweredQuestions && !isSubmitting"
          @click="handleSaveDraft"
          type="info"
          size="small"
          :loading="isSavingDraft"
          plain
        >
          <el-icon><DocumentCopy /></el-icon>
          Save Draft
        </el-button>
        
        <el-button
          v-if="canSubmit"
          @click="handleSubmitAnswers"
          type="primary"
          size="small"
          :loading="isSubmitting"
        >
          <el-icon><Check /></el-icon>
          Submit Answers
        </el-button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="questions.length === 0" class="empty-state text-center py-8">
      <div class="text-[#666666] space-y-3">
        <el-icon class="text-6xl mb-4 text-[#4fc3f7]"><ChatDotRound /></el-icon>
        <h4 class="text-lg font-medium text-[#cccccc]">No Questions Available</h4>
        <p class="text-sm max-w-md mx-auto">
          Generate questions first to start providing answers that will help refine your requirement.
        </p>
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="questions.length > 0" class="progress-section mb-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-[#cccccc]">Progress</span>
        <span class="text-sm text-[#888888]">{{ Math.round(progressPercentage) }}%</span>
      </div>
      <el-progress 
        :percentage="progressPercentage" 
        :color="getProgressColor()"
        :stroke-width="6"
        :show-text="false"
      />
    </div>

    <!-- Answer Form -->
    <div v-if="questions.length > 0" class="answer-form-container">
      <el-form
        ref="answerFormRef"
        :model="answerForm"
        :rules="answerRules"
        label-position="top"
        @submit.prevent="handleSubmitAnswers"
      >
        <div class="space-y-6">
          <div 
            v-for="(question, index) in questions" 
            :key="question.id"
            class="answer-section"
          >
            <div class="answer-card bg-[#2d2d30] rounded-lg p-6 border border-[#3e3e42]"
                 :class="{ 'border-[#4fc3f7]': hasAnswer(question.id), 'border-[#fa8c16]': !hasAnswer(question.id) }">
              
              <!-- Question Display -->
              <div class="question-display mb-4">
                <div class="flex items-start space-x-3 mb-3">
                  <div class="question-number">
                    <span class="inline-flex items-center justify-center w-8 h-8 bg-[#4fc3f7] text-black rounded-full text-sm font-bold">
                      {{ index + 1 }}
                    </span>
                  </div>
                  <div class="question-content flex-1">
                    <p class="text-[#cccccc] leading-relaxed text-base">{{ question.text }}</p>
                    <div class="flex items-center space-x-2 mt-2">
                      <el-tag size="small" type="info" effect="plain">{{ question.category }}</el-tag>
                      <div class="flex items-center space-x-1">
                        <el-icon 
                          class="w-4 h-4"
                          :class="hasAnswer(question.id) ? 'text-[#52c41a]' : 'text-[#fa8c16]'"
                        >
                          <component :is="hasAnswer(question.id) ? CircleCheck : Clock" />
                        </el-icon>
                        <span 
                          class="text-xs"
                          :class="hasAnswer(question.id) ? 'text-[#52c41a]' : 'text-[#fa8c16]'"
                        >
                          {{ hasAnswer(question.id) ? 'Answered' : 'Pending' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Answer Input -->
              <el-form-item 
                :prop="`answers.${question.id}`"
                :label="`Your Answer for Question ${index + 1}`"
                class="answer-input-section"
              >
                <!-- Choice Question -->
                <div v-if="question.type === 'choice' && question.options" class="choice-question">
                  <div class="choice-grid grid grid-cols-2 gap-3">
                    <div
                      v-for="(option, optionIndex) in question.options"
                      :key="optionIndex"
                      @click="selectOption(question.id, optionIndex)"
                      :class="[
                        'choice-card p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
                        'bg-[#1e1e1e] hover:bg-[#2d2d30]',
                        isOptionSelected(question.id, optionIndex)
                          ? 'border-[#4fc3f7] bg-[#2d2d30] shadow-lg' 
                          : 'border-[#3e3e42] hover:border-[#4fc3f7]',
                        isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                      ]"
                      :disabled="isSubmitting"
                    >
                      <div class="choice-content text-center">
                        <div class="choice-text text-[#cccccc] font-medium leading-tight">
                          {{ option }}
                        </div>
                        <div v-if="isOptionSelected(question.id, optionIndex)" class="choice-check mt-2">
                          <el-icon class="text-[#4fc3f7] text-lg"><CircleCheck /></el-icon>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Choice Tips -->
                  <div class="answer-tips mt-3 text-xs text-[#888888]">
                    💡 Click on the option that best matches your requirements.
                  </div>
                  
                  <!-- \"其他\" option input box -->
                  <div 
                    v-if="isOptionSelected(question.id, 3)" 
                    class="mt-4 other-input"
                  >
                    <div class="mb-2 text-sm text-[#cccccc] font-medium">
                      Please specify your "其他" requirement:
                    </div>
                    <el-input
                      v-model="answerForm.otherTexts[question.id]"
                      type="textarea"
                      placeholder="请输入您的具体需求..."
                      :rows="3"
                      :maxlength="500"
                      show-word-limit
                      :disabled="isSubmitting"
                      @input="handleOtherTextInput(question.id)"
                      class="other-textarea"
                    />
                  </div>
                </div>

                <!-- Text Question (fallback) -->
                <div v-else class="text-question">
                  <el-input
                    v-model="answerForm.answers[question.id]"
                    type="textarea"
                    :placeholder="`Please provide a detailed answer to help us understand your requirements better...`"
                    :rows="4"
                    :maxlength="1000"
                    show-word-limit
                    :disabled="isSubmitting"
                    @input="handleAnswerInput(question.id)"
                    class="answer-textarea"
                  />
                  
                  <!-- Text Tips -->
                  <div class="answer-tips mt-2 text-xs text-[#888888]">
                    💡 Tip: Be specific and include examples when possible. This helps AI better understand your needs.
                  </div>
                </div>
              </el-form-item>

              <!-- Existing Answer Display -->
              <div v-if="getExistingAnswer(question.id)" class="existing-answer mt-4 p-3 bg-[#1e1e1e] rounded border border-[#52c41a]">
                <div class="flex items-center space-x-2 mb-2">
                  <el-icon class="text-[#52c41a]"><CircleCheck /></el-icon>
                  <span class="text-sm font-medium text-[#52c41a]">Previously Answered</span>
                  <span class="text-xs text-[#666666]">
                    {{ formatTime(getExistingAnswer(question.id)?.answered_at) }}
                  </span>
                </div>
                <p class="text-sm text-[#cccccc] leading-relaxed">
                  "{{ getExistingAnswer(question.id)?.text }}"
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions mt-8 flex items-center justify-between p-4 bg-[#2d2d30] rounded-lg border border-[#3e3e42]">
          <div class="answer-summary">
            <div class="grid grid-cols-3 gap-4 text-center">
              <div class="stat-item">
                <div class="text-lg font-bold text-[#52c41a]">{{ answeredCount }}</div>
                <div class="text-xs text-[#888888]">Answered</div>
              </div>
              <div class="stat-item">
                <div class="text-lg font-bold text-[#fa8c16]">{{ unansweredCount }}</div>
                <div class="text-xs text-[#888888]">Remaining</div>
              </div>
              <div class="stat-item">
                <div class="text-lg font-bold text-[#4fc3f7]">{{ draftCount }}</div>
                <div class="text-xs text-[#888888]">Drafts</div>
              </div>
            </div>
          </div>
          
          <div class="action-buttons flex items-center space-x-3">
            <el-button
              @click="handleClearDrafts"
              type="danger"
              size="small"
              :disabled="isSubmitting || Object.keys(answerForm.answers).length === 0"
              plain
            >
              <el-icon><Delete /></el-icon>
              Clear All
            </el-button>
            
            <el-button
              @click="handleSaveDraft"
              type="info"
              size="small"
              :loading="isSavingDraft"
              :disabled="isSubmitting || Object.keys(answerForm.answers).length === 0"
            >
              <el-icon><DocumentCopy /></el-icon>
              Save Draft
            </el-button>
            
            <el-button
              type="primary"
              native-type="submit"
              size="small"
              :loading="isSubmitting"
              :disabled="!canSubmit"
            >
              <el-icon><Check /></el-icon>
              Submit All Answers
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { 
  ElIcon, 
  ElButton, 
  ElTag, 
  ElForm,
  ElFormItem,
  ElInput,
  ElProgress,
  ElMessage,
  ElMessageBox
} from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { 
  EditPen,
  DocumentCopy,
  Check,
  ChatDotRound,
  CircleCheck,
  Clock,
  Delete
} from '@element-plus/icons-vue';
import type { Question, Answer, AnswerSubmission } from '@/api/requirement.js';

// Props interface
interface Props {
  questions: Question[];
  answers: Answer[];
  isSubmitting?: boolean;
  isSavingDraft?: boolean;
  requirementId?: string;
}

// Events interface
interface Emits {
  (e: 'submit-answers', answers: AnswerSubmission[]): void;
  (e: 'save-draft', answers: AnswerSubmission[]): void;
  (e: 'clear-drafts'): void;
}

const props = withDefaults(defineProps<Props>(), {
  questions: () => [],
  answers: () => [],
  isSubmitting: false,
  isSavingDraft: false,
  requirementId: undefined,
});

const emit = defineEmits<Emits>();

// Form refs
const answerFormRef = ref<FormInstance>();

// Form data
const answerForm = reactive<{ 
  answers: Record<string, string | number>;
  otherTexts: Record<string, string>;
}>({
  answers: {},
  otherTexts: {}
});

// Form validation rules
const answerRules = reactive<FormRules>({});

// Computed properties
const answeredCount = computed(() => {
  return Object.keys(answerForm.answers).filter(questionId => {
    return hasAnswer(questionId);
  }).length;
});

const unansweredCount = computed(() => {
  return props.questions.length - answeredCount.value;
});

const draftCount = computed(() => {
  return Object.keys(answerForm.answers).filter(questionId => {
    const currentAnswer = answerForm.answers[questionId];
    const existingAnswer = getExistingAnswer(questionId);
    
    // Has current answer but no existing answer = draft
    const hasCurrentAnswer = (typeof currentAnswer === 'number' && currentAnswer >= 0) || 
                             (typeof currentAnswer === 'string' && currentAnswer.trim().length > 0);
    
    return hasCurrentAnswer && !existingAnswer;
  }).length;
});

const progressPercentage = computed(() => {
  if (props.questions.length === 0) return 0;
  return (answeredCount.value / props.questions.length) * 100;
});

const hasUnansweredQuestions = computed(() => {
  return unansweredCount.value > 0;
});

const canSubmit = computed(() => {
  return props.questions.length > 0 && answeredCount.value > 0 && !props.isSubmitting;
});

// Methods
const hasAnswer = (questionId: string): boolean => {
  const currentAnswer = answerForm.answers[questionId];
  const existingAnswer = getExistingAnswer(questionId);
  
  // For choice questions, check if a valid index is selected
  if (typeof currentAnswer === 'number' && currentAnswer >= 0) {
    // If it's "其他" option (index 3), also check if other text is provided
    if (currentAnswer === 3) {
      const otherText = answerForm.otherTexts[questionId];
      return typeof otherText === 'string' && otherText.trim().length > 0;
    }
    return true;
  }
  
  // For text questions, check if text is not empty
  if (typeof currentAnswer === 'string' && currentAnswer.trim().length > 0) {
    return true;
  }
  
  return !!existingAnswer;
};

// Helper to check if an option is currently selected (for visual styling)
const isOptionSelected = (questionId: string, optionIndex: number): boolean => {
  const currentAnswer = answerForm.answers[questionId];
  
  // Check current form state first
  if (typeof currentAnswer === 'number' && currentAnswer === optionIndex) {
    return true;
  }
  
  // If no current selection but there's an existing answer, check existing answer
  if (currentAnswer === undefined) {
    const existingAnswer = getExistingAnswer(questionId);
    return existingAnswer?.selected_index === optionIndex;
  }
  
  return false;
};

const getExistingAnswer = (questionId: string): Answer | undefined => {
  return props.answers.find(answer => answer.question_id === questionId);
};

const getProgressTagType = () => {
  const percentage = progressPercentage.value;
  if (percentage === 100) return 'success';
  if (percentage >= 50) return 'warning';
  return 'info';
};

const getProgressColor = () => {
  const percentage = progressPercentage.value;
  if (percentage === 100) return '#52c41a';
  if (percentage >= 50) return '#fa8c16';
  return '#4fc3f7';
};

const formatTime = (timestamp?: string): string => {
  if (!timestamp) return '';
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) {
      return 'just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffMinutes < 1440) {
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  } catch (error) {
    return 'recently';
  }
};

const handleAnswerInput = (questionId: string) => {
  console.log('[AnswerForm] Answer updated for question:', questionId);
  // Auto-save functionality could be implemented here
};

const selectOption = (questionId: string, optionIndex: number) => {
  if (props.isSubmitting) return;
  
  console.log('[AnswerForm] Option selected:', questionId, optionIndex);
  answerForm.answers[questionId] = optionIndex;
  
  // Clear other text if not selecting "其他" option
  if (optionIndex !== 3) {
    delete answerForm.otherTexts[questionId];
  }
  
  handleAnswerInput(questionId);
};

const handleOtherTextInput = (questionId: string) => {
  console.log('[AnswerForm] Other text updated for question:', questionId);
  // Auto-save functionality could be implemented here
};

const handleSaveDraft = () => {
  console.log('[AnswerForm] Save draft clicked');
  const draftAnswers = getDraftAnswers();
  if (draftAnswers.length > 0) {
    emit('save-draft', draftAnswers);
  } else {
    ElMessage.info('No draft answers to save');
  }
};

const handleSubmitAnswers = () => {
  answerFormRef.value?.validate((valid) => {
    if (valid) {
      console.log('[AnswerForm] Submitting answers');
      const submissionAnswers = getSubmissionAnswers();
      
      if (submissionAnswers.length === 0) {
        ElMessage.warning('Please provide at least one answer before submitting');
        return;
      }
      
      emit('submit-answers', submissionAnswers);
    } else {
      console.log('[AnswerForm] Form validation failed');
      ElMessage.error('Please check your answers and try again');
    }
  });
};

const handleClearDrafts = () => {
  ElMessageBox.confirm(
    'This will clear all draft answers. Continue?',
    'Clear Drafts',
    {
      confirmButtonText: 'Clear',
      cancelButtonText: 'Cancel',
      type: 'warning',
    }
  ).then(() => {
    answerForm.answers = {};
    answerForm.otherTexts = {};
    ElMessage.success('All drafts cleared');
    emit('clear-drafts');
  }).catch(() => {
    // User cancelled
  });
};

const getDraftAnswers = (): AnswerSubmission[] => {
  return Object.entries(answerForm.answers)
    .filter(([questionId, answer]) => {
      const existingAnswer = getExistingAnswer(questionId);
      let hasCurrentAnswer = false;
      
      if (typeof answer === 'number' && answer >= 0) {
        // For "其他" option, check if other text is provided
        if (answer === 3) {
          const otherText = answerForm.otherTexts[questionId];
          hasCurrentAnswer = typeof otherText === 'string' && otherText.trim().length > 0;
        } else {
          hasCurrentAnswer = true;
        }
      } else if (typeof answer === 'string' && answer.trim().length > 0) {
        hasCurrentAnswer = true;
      }
      
      return hasCurrentAnswer && !existingAnswer;
    })
    .map(([questionId, answer]) => {
      const question = props.questions.find(q => q.id === questionId);
      
      if (question?.type === 'choice' && typeof answer === 'number') {
        if (answer === 3) {
          // For "其他" option, use the custom text
          const otherText = answerForm.otherTexts[questionId] || '';
          return {
            question_id: questionId,
            selected_index: answer,
            text: otherText.trim()
          };
        } else {
          return {
            question_id: questionId,
            selected_index: answer,
            text: question.options[answer] || ''
          };
        }
      } else {
        return {
          question_id: questionId,
          selected_index: -1, // Default for text questions
          text: typeof answer === 'string' ? answer.trim() : ''
        };
      }
    });
};

const getSubmissionAnswers = (): AnswerSubmission[] => {
  return Object.entries(answerForm.answers)
    .filter(([questionId, answer]) => {
      if (typeof answer === 'number' && answer >= 0) {
        // For "其他" option, check if other text is provided
        if (answer === 3) {
          const otherText = answerForm.otherTexts[questionId];
          return typeof otherText === 'string' && otherText.trim().length > 0;
        }
        return true;
      }
      return (typeof answer === 'string' && answer.trim().length > 0);
    })
    .map(([questionId, answer]) => {
      const question = props.questions.find(q => q.id === questionId);
      
      if (question?.type === 'choice' && typeof answer === 'number') {
        if (answer === 3) {
          // For "其他" option, use the custom text
          const otherText = answerForm.otherTexts[questionId] || '';
          return {
            question_id: questionId,
            selected_index: answer,
            text: otherText.trim()
          };
        } else {
          return {
            question_id: questionId,
            selected_index: answer,
            text: question.options[answer] || ''
          };
        }
      } else {
        return {
          question_id: questionId,
          selected_index: -1, // Default for text questions
          text: typeof answer === 'string' ? answer.trim() : ''
        };
      }
    });
};

const initializeFormValidation = () => {
  // Dynamic validation rules based on questions
  const rules: FormRules = {};
  props.questions.forEach(question => {
    if (question.type === 'choice') {
      // For choice questions, just require a selection
      rules[`answers.${question.id}`] = [
        { 
          required: true, 
          message: 'Please select an option', 
          trigger: 'change',
          validator: (rule: any, value: any, callback: any) => {
            if (typeof value !== 'number' || value < 0) {
              callback(new Error('Please select an option'));
              return;
            }
            
            // If "其他" option is selected, check if other text is provided
            if (value === 3) {
              const otherText = answerForm.otherTexts[question.id];
              if (!otherText || otherText.trim().length === 0) {
                callback(new Error('Please specify your "其他" requirement'));
                return;
              }
              if (otherText.trim().length < 5) {
                callback(new Error('Please provide at least 5 characters for "其他" option'));
                return;
              }
            }
            
            callback();
          }
        },
      ];
    } else {
      // For text questions, require minimum characters
      rules[`answers.${question.id}`] = [
        { min: 10, message: 'Answer must be at least 10 characters', trigger: 'blur' },
        { max: 1000, message: 'Answer cannot exceed 1000 characters', trigger: 'blur' },
      ];
    }
  });
  Object.assign(answerRules, rules);
};

const initializeExistingAnswers = () => {
  // Clear current form state first
  answerForm.answers = {};
  answerForm.otherTexts = {};
  
  // Pre-populate form with existing answers
  props.answers.forEach(answer => {
    const question = props.questions.find(q => q.id === answer.question_id);
    
    if (question?.type === 'choice' && answer.selected_index >= 0) {
      // For choice questions, use the selected index
      answerForm.answers[answer.question_id] = answer.selected_index;
      
      // If it's "其他" option, also populate the other text
      if (answer.selected_index === 3 && answer.text) {
        answerForm.otherTexts[answer.question_id] = answer.text;
      }
    } else {
      // For text questions, use the text
      answerForm.answers[answer.question_id] = answer.text;
    }
  });

};

// Watch for changes in requirementId to clear form when switching requirements
watch(
  () => props.requirementId,
  (newRequirementId, oldRequirementId) => {
    if (newRequirementId !== oldRequirementId && oldRequirementId !== undefined) {
      // Clear form state when switching requirements
      answerForm.answers = {};
      answerForm.otherTexts = {};
    }
  },
  { immediate: false }
);

// Watch for changes in questions or answers
watch(
  () => [props.questions, props.answers],
  () => {
    initializeFormValidation();
    initializeExistingAnswers();
  },
  { immediate: true, deep: true }
);

// Lifecycle
onMounted(() => {
  initializeFormValidation();
  initializeExistingAnswers();
});
</script>

<style scoped>
.answer-form {
  @apply space-y-6;
}

.answer-card {
  transition: border-color 0.3s ease;
}

.answer-card:hover {
  @apply border-[#4fc3f7];
}

.answer-textarea :deep(.el-textarea__inner) {
  @apply bg-[#1e1e1e] border-[#3e3e42] text-[#cccccc];
  resize: vertical;
  min-height: 100px;
}

.answer-textarea :deep(.el-textarea__inner):focus {
  @apply border-[#4fc3f7];
}

.stat-item {
  @apply flex flex-col items-center;
}

.form-actions {
  @apply sticky bottom-0 backdrop-blur-sm;
}

.answer-tips {
  line-height: 1.4;
}

/* Choice question styles */
.choice-grid {
  min-height: 200px;
}

.choice-card {
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transform: scale(1);
}

.choice-card:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
}

.choice-card:active {
  transform: scale(0.98);
}

.choice-card.selected {
  border-color: #4fc3f7 !important;
  background-color: #2d2d30 !important;
  box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.2);
}

.choice-text {
  line-height: 1.3;
  word-wrap: break-word;
  hyphens: auto;
}

.choice-check {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* Other input styles */
.other-input {
  background: rgba(45, 45, 48, 0.6);
  border-radius: 8px;
  padding: 16px;
  border: 1px dashed #4fc3f7;
}

.other-textarea :deep(.el-textarea__inner) {
  @apply bg-[#1e1e1e] border-[#3e3e42] text-[#cccccc];
  resize: vertical;
  min-height: 80px;
}

.other-textarea :deep(.el-textarea__inner):focus {
  @apply border-[#4fc3f7];
  box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.2);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .choice-grid {
    grid-template-columns: 1fr;
  }
  
  .choice-card {
    min-height: 60px;
  }
}

/* Custom scrollbar */
.answer-form-container {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.answer-form-container::-webkit-scrollbar {
  width: 6px;
}

.answer-form-container::-webkit-scrollbar-track {
  background: #2d2d30;
  border-radius: 4px;
}

.answer-form-container::-webkit-scrollbar-thumb {
  background: #4fc3f7;
  border-radius: 4px;
}

.answer-form-container::-webkit-scrollbar-thumb:hover {
  background: #1890ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-actions {
    @apply flex-col space-y-4;
  }
  
  .action-buttons {
    @apply flex-col w-full;
  }
  
  .action-buttons .el-button {
    @apply w-full;
  }
  
  .answer-summary {
    @apply w-full;
  }
  
  .answer-summary .grid {
    @apply grid-cols-3 gap-2;
  }
}
</style> 