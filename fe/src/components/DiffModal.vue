<template>
  <el-dialog
    v-model="internalVisible"
    :title="diffData ? `Diff: ${diffData.filePath}` : 'Diff Viewer'"
    width="90vw"
    top="5vh"
    append-to-body
    custom-class="diff-modal"
  >
    <div class="diff-container" v-if="diffData">
      <DiffViewer
        :file-path="diffData.filePath"
        :original-content="diffData.originalContent"
        :modified-content="diffData.modifiedContent"
        :unified-diff="diffData.unifiedDiff"
        :language="language"
        :height="modalHeight"
        theme="vs-dark"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElDialog } from 'element-plus';
import DiffViewer from './DiffViewer.vue';
import { getLanguageFromPath } from '@/utils/languageMap';

export interface DiffData {
  filePath: string;
  originalContent: string;
  modifiedContent: string;
  unifiedDiff: string;
}

const props = defineProps<{
  visible: boolean;
  diffData: DiffData | null;
}>();

const emit = defineEmits(['update:visible']);

const internalVisible = ref(props.visible);

watch(() => props.visible, (newVal) => {
  internalVisible.value = newVal;
});

watch(internalVisible, (newVal) => {
  emit('update:visible', newVal);
});

const language = computed(() => {
  return props.diffData ? getLanguageFromPath(props.diffData.filePath) : 'plaintext';
});

const modalHeight = computed(() => {
  return window.innerHeight * 0.8; // 80% of viewport height
});
</script>

<style>
.diff-modal {
  background-color: #2e3440; /* Nord dark background */
  border-radius: 8px;
}
.diff-modal .el-dialog__header {
  background-color: #3b4252;
  color: #d8dee9;
  border-bottom: 1px solid #4c566a;
  border-radius: 8px 8px 0 0;
}
.diff-modal .el-dialog__title {
  color: #eceff4;
  font-weight: 600;
}
.diff-modal .el-dialog__body {
  padding: 0;
  height: calc(80vh + 40px); /* Adjust based on your needs */
}
.diff-container {
  height: 100%;
}
</style> 