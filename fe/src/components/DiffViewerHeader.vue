<template>
  <div class="diff-viewer-header">
    <div class="file-info">
      <span class="icon i-ph-file-code"></span>
      <span class="file-path">{{ filePath }}</span>
    </div>
    <div class="actions">
      <el-tooltip content="View Fullscreen" placement="top">
        <el-button
          type="text"
          class="action-btn"
          @click="$emit('fullscreen')"
        >
          <span class="icon i-ph-arrows-out-simple"></span>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  filePath: string;
}>();

defineEmits(['fullscreen']);
</script>

<style scoped>
.diff-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: #3a3a40;
  border-bottom: 1px solid #4a4a50;
  color: #cccccc;
  font-size: 13px;
}
.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}
.icon {
  font-size: 16px;
  color: #81a1c1; /* Bluish icon for code file */
}
.action-btn {
  color: #d8dee9;
  padding: 4px;
  height: auto;
  min-height: auto;
}
.action-btn:hover {
  color: #eceff4;
  background-color: #4c566a;
}
</style> 