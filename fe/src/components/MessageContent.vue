<template>
  <div class="prose prose-invert prose-sm max-w-none" v-html="renderedHtml"></div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { Marked } from 'marked';
import DOMPurify from 'dompurify';
import { codeToHtml } from 'shiki';

const props = defineProps<{
  content: string;
}>();

const renderedHtml = ref('');

const markedInstance = new Marked();

markedInstance.use({
  async: true,
  walkTokens: async (token) => {
    if (token.type === 'code') {
      try {
        const lang = token.lang || 'plaintext';
        const highlightedCode = await codeToHtml(token.text, {
          lang: lang,
          theme: 'dark-plus',
        });
        // Replace the token's raw text with the highlighted HTML
        token.text = highlightedCode;
        // The 'raw' property also needs to be updated. This is crucial.
        token.raw = highlightedCode;
      } catch (e) {
        console.error(`Shiki highlighting failed for lang ${token.lang}:`, e);
      }
    }
  },
  renderer: {
    // Override the code renderer to output the pre-rendered HTML from shiki
    code(code) {
      return code; // 'code' is now the HTML string from shiki
    }
  }
});

watchEffect(async () => {
  const rawHtml = await markedInstance.parse(props.content ?? '');
  renderedHtml.value = DOMPurify.sanitize(String(rawHtml));
});

</script>

<style>
/* Shiki generates styled <pre> tags, remove default prose background/padding */
.prose :where(pre) {
  background-color: transparent;
  padding: 0;
}
/* Ensure the code inside has a matching background from shiki's theme */
.prose :where(pre code) {
  background-color: transparent;
  color: inherit;
  font-size: inherit;
  padding: 0;
}
</style> 