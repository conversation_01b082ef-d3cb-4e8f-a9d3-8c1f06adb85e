<template>
  <div class="top-toolbar flex items-center justify-between px-2.5 py-1.25 bg-[#29292e] h-10 box-border">
    <!-- Left side: Run Button and Save Button -->
    <div class="flex items-center space-x-2">
      <button class="run-button bg-[#33b24d] text-white text-sm font-medium rounded-[6px] px-3 py-1.5 flex items-center focus:outline-none">
        <span>Run</span>
        <span class="i-ep-arrow-down ml-1 w-3 h-3"></span> <!-- UnoCSS icon -->
      </button>
      
      <button
        @click="handleSave"
        :disabled="!canSave"
        title="Save active file (Ctrl+S / Cmd+S)"
        class="save-button text-white text-sm font-medium rounded-[6px] px-3 py-1.5 flex items-center focus:outline-none"
        :class="[canSave ? 'bg-[#007acc] hover:bg-[#008ae6]' : 'bg-gray-500 cursor-not-allowed']"
      >
        <span class="i-material-symbols-save-outline w-4 h-4 mr-1.5"></span> <!-- Save Icon -->
        <span>Save</span>
      </button>
      
      <button
        @click="handleRebuildRuntime"
        :disabled="!fsStore.currentProjectID || rebuildingRuntime"
        title="Rebuild Runtime Environment"
        class="rebuild-button text-white text-sm font-medium rounded-[6px] px-3 py-1.5 flex items-center focus:outline-none"
        :class="[fsStore.currentProjectID && !rebuildingRuntime ? 'bg-[#f56c6c] hover:bg-[#f78989]' : 'bg-gray-500 cursor-not-allowed']"
      >
        <span class="i-ep-refresh w-4 h-4 mr-1.5" :class="{ 'animate-spin': rebuildingRuntime }"></span>
        <span>{{ rebuildingRuntime ? '重建中...' : '重建Runtime' }}</span>
      </button>
      
      <!-- File Watch Status Indicator -->
      <div 
        v-if="fsStore.currentProjectID"
        class="file-watch-status flex items-center px-2 py-1 rounded text-xs"
        :class="fileWatchStatusClass"
        :title="fileWatchStatusTitle"
      >
        <span 
          class="w-2 h-2 rounded-full mr-1.5"
          :class="fileWatchDotClass"
        ></span>
        <span>{{ fileWatchStatusText }}</span>
      </div>
    </div>

    <!-- Center: Project Switcher -->
    <div class="center-section">
      <ProjectSwitcher />
    </div>

    <!-- Right side: Buttons & User Icon -->
    <div class="flex items-center space-x-3">
      <button class="text-[#bdbdbd] text-sm hover:text-white focus:outline-none">Invite</button>
      <button class="text-[#bdbdbd] text-sm hover:text-white focus:outline-none">Deploy</button>
      <span class="i-ep-bell text-xl text-[#bdbdbd] hover:text-white"></span>
      
      <!-- User Menu Dropdown -->
      <el-dropdown @command="handleUserAction" placement="bottom-end">
        <div class="w-7 h-7 rounded-full bg-purple-500 flex items-center justify-center text-white text-sm font-bold cursor-pointer hover:bg-purple-600 transition-colors">
          {{ userInitials }}
      </div>
        <template #dropdown>
          <el-dropdown-menu class="!bg-[#262626] !border-[#555555]">
            <el-dropdown-item disabled class="!text-gray-300">
              {{ userStore.user?.username }}
            </el-dropdown-item>
            <el-dropdown-item
              v-if="userStore.isAdmin"
              divided
              command="globalSettings"
              class="!text-gray-300 hover:!bg-[#3a3a40]"
            >
              <span class="i-ph-gear-six w-4 h-4 mr-2"></span>
              Global LLM Settings
            </el-dropdown-item>
            <el-dropdown-item divided command="logout" class="!text-red-400 hover:!bg-red-800/20">
              <span class="i-material-symbols-logout w-4 h-4 mr-2"></span>
              Logout
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, watchEffect, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useFsStore } from '@/store/fs.js'; // Adjusted path to include .js extension
import { useUserStore } from '@/store/user.js';
import { ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';
import ProjectSwitcher from './ProjectSwitcher.vue';
import { rebuildProjectRuntime } from '@/api/fs';

const fsStore = useFsStore();
const userStore = useUserStore();
const router = useRouter();

const canSave = computed(() => fsStore.canSaveCurrentFile);
const rebuildingRuntime = ref(false);

// File watch status computed properties
const fileWatchStatusClass = computed(() => {
  if (fsStore.isFileWatchConnected) {
    return 'bg-green-900/20 text-green-400';
  } else if (fsStore.fileWatchError) {
    return 'bg-red-900/20 text-red-400';
  } else {
    return 'bg-yellow-900/20 text-yellow-400';
  }
});

const fileWatchDotClass = computed(() => {
  if (fsStore.isFileWatchConnected) {
    return 'bg-green-400 animate-pulse';
  } else if (fsStore.fileWatchError) {
    return 'bg-red-400';
  } else {
    return 'bg-yellow-400 animate-pulse';
  }
});

const fileWatchStatusText = computed(() => {
  if (fsStore.isFileWatchConnected) {
    return 'Live';
  } else if (fsStore.fileWatchError) {
    return 'Error';
  } else {
    return 'Connecting';
  }
});

const fileWatchStatusTitle = computed(() => {
  if (fsStore.isFileWatchConnected) {
    return 'File monitoring is active - changes will be detected automatically';
  } else if (fsStore.fileWatchError) {
    return `File monitoring error: ${fsStore.fileWatchError}`;
  } else {
    return 'Connecting to file monitoring service...';
  }
});

// Compute user initials for avatar
const userInitials = computed(() => {
  const username = userStore.user?.username || 'U';
  return username.slice(0, 2).toUpperCase();
});

// Watch effect for debugging canSave state
watchEffect(() => {
  console.log('[TopToolbar canSave Debug] projectID:', fsStore.currentProjectID);
  console.log('[TopToolbar canSave Debug] activeFilePath:', fsStore.activeFilePath);
  if (fsStore.activeFilePath && fsStore.openFiles[fsStore.activeFilePath]) {
    const details = fsStore.openFiles[fsStore.activeFilePath];
    console.log(`[TopToolbar canSave Debug] File: ${fsStore.activeFilePath}, Original (len): ${details.originalContent?.length}, Current (len): ${details.currentContent?.length}`);
  } else if (fsStore.activeFilePath) {
    console.log(`[TopToolbar canSave Debug] File: ${fsStore.activeFilePath}, but no details in openFiles.`);
  }
  console.log('[TopToolbar canSave Debug] isCurrentFileDirty:', fsStore.isCurrentFileDirty);
  console.log('[TopToolbar canSave Debug] canSaveCurrentFile (final):', fsStore.canSaveCurrentFile);
});

const handleSave = () => {
  if (canSave.value) {
    fsStore.saveActiveFile();
  } else {
    console.warn('[TopToolbar] Save action triggered when canSave is false.');
    ElMessage.warning('No changes to save or no active file.');
  }
};

const handleRebuildRuntime = async () => {
  if (!fsStore.currentProjectID) {
    ElMessage.error('No active project');
    return;
  }

  rebuildingRuntime.value = true;
  try {
    await rebuildProjectRuntime(fsStore.currentProjectID);
    ElMessage.success('Runtime environment rebuilt successfully');
  } catch (error: any) {
    console.error('Failed to rebuild runtime:', error);
    ElMessage.error(error.message || 'Failed to rebuild runtime environment');
  } finally {
    rebuildingRuntime.value = false;
  }
};

// Handle user dropdown actions
const handleUserAction = async (command: string) => {
  switch (command) {
    case 'logout':
      try {
        await userStore.logout();
        ElMessage.success('Logged out successfully');
        router.push('/login');
      } catch (error) {
        console.error('Logout failed:', error);
        ElMessage.error('Failed to logout');
      }
      break;
    case 'globalSettings':
      router.push('/settings/global');
      break;
    default:
      console.warn('Unknown user action:', command);
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  console.log('[TopToolbar handleKeyDown] Event triggered:', event.key, 'Ctrl:', event.ctrlKey, 'Meta:', event.metaKey);
  if ((event.ctrlKey || event.metaKey) && (event.key === 's' || event.key === 'S')) { // Also check for 'S' just in case
    console.log('[TopToolbar handleKeyDown] Cmd/Ctrl+S detected. Calling preventDefault().');
    event.preventDefault(); 
    if (canSave.value) {
      console.log('[TopToolbar handleKeyDown] Conditions met, calling handleSave().');
      handleSave();
    } else {
      console.log('[TopToolbar handleKeyDown] Conditions NOT met for saving (canSave is false).');
    }
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
/* Scoped styles if needed, but primarily using UnoCSS */
.run-button {
  /* Example: Add transition if desired */
  transition: background-color 0.2s;
}
.run-button:hover {
  background-color: #40c057; /* Slightly lighter green on hover */
}

.center-section {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 