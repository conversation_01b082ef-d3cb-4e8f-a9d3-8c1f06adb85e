<template>
  <div class="image-upload-area">
    <div class="upload-section">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :file-list="fileList"
        :on-change="handleFileChange"
        :on-remove="handleRemove"
        :before-upload="beforeUpload"
        :show-file-list="false"
        accept="image/*"
        multiple
        drag
        class="upload-dragger"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">
            <p>Drop images here or click to upload</p>
            <p class="upload-hint">Supports: JPG, PNG, GIF, WebP (Max 10MB each)</p>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- Image Preview List -->
    <div v-if="fileList.length > 0" class="preview-section">
      <div class="preview-header">
        <h4>Reference Images ({{ fileList.length }})</h4>
        <el-button 
          size="small" 
          type="danger" 
          :icon="Delete"
          @click="clearAll"
          class="clear-all-btn"
        >
          Clear All
        </el-button>
      </div>
      
      <div class="preview-grid">
        <div 
          v-for="(file, index) in fileList" 
          :key="file.uid" 
          class="preview-item"
        >
          <div class="preview-image">
            <img :src="file.url" :alt="file.name" />
            <div class="preview-overlay">
              <el-button 
                size="small" 
                type="danger" 
                :icon="Delete"
                circle
                @click="handleRemove(file)"
              />
            </div>
          </div>
          <div class="preview-info">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <div class="file-size">{{ formatFileSize(file.size || 0) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type UploadFile, type UploadFiles } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: File[]
  maxCount?: number
  maxSize?: number // Max size in bytes
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: 5,
  maxSize: 10 * 1024 * 1024 // 10MB default
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [files: File[]]
}>()

// State
const uploadRef = ref()
const fileList = ref<UploadFiles>([])

// Computed
const selectedFiles = computed({
  get: () => props.modelValue,
  set: (files: File[]) => emit('update:modelValue', files)
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newFiles) => {
  // Update fileList to match modelValue
  fileList.value = newFiles.map((file, index) => ({
    uid: Date.now() + index,
    name: file.name,
    size: file.size,
    status: 'ready',
    url: URL.createObjectURL(file),
    raw: file
  } as UploadFile))
}, { immediate: true })

// Methods
const beforeUpload = (file: File): boolean => {
  // Check file type
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('Please upload image files only!')
    return false
  }

  // Check file size
  if (file.size > props.maxSize) {
    ElMessage.error(`File size should not exceed ${formatFileSize(props.maxSize)}!`)
    return false
  }

  // Check count limit
  if (fileList.value.length >= props.maxCount) {
    ElMessage.error(`Maximum ${props.maxCount} images allowed!`)
    return false
  }

  return true
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  // Update fileList
  fileList.value = files.filter(f => f.status !== 'fail')
  
  // Extract raw files and update modelValue
  const rawFiles = fileList.value
    .map(f => f.raw)
    .filter(f => f != null && f instanceof File) as File[]
  
  selectedFiles.value = rawFiles
}

const handleRemove = (file: UploadFile) => {
  // Remove from fileList
  fileList.value = fileList.value.filter(f => f.uid !== file.uid)
  
  // Update modelValue
  const rawFiles = fileList.value
    .map(f => f.raw)
    .filter(f => f != null && f instanceof File) as File[]
  
  selectedFiles.value = rawFiles
  
  // Clean up object URL to prevent memory leaks
  if (file.url?.startsWith('blob:')) {
    URL.revokeObjectURL(file.url)
  }
}

const clearAll = () => {
  // Clean up all object URLs
  fileList.value.forEach(file => {
    if (file.url?.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
  })
  
  fileList.value = []
  selectedFiles.value = []
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i]
}

// Cleanup on unmount
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(() => {
  // Clean up object URLs to prevent memory leaks
  fileList.value.forEach(file => {
    if (file.url?.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
  })
})
</script>

<style scoped>
.image-upload-area {
  width: 100%;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload) {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 140px;
  border: 2px dashed rgba(79, 195, 247, 0.4) !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.6) 0%, rgba(55, 65, 81, 0.4) 100%) !important;
  backdrop-filter: blur(10px) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: rgba(79, 195, 247, 0.8) !important;
  background: linear-gradient(135deg, rgba(55, 65, 81, 0.7) 0%, rgba(79, 195, 247, 0.1) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(79, 195, 247, 0.2) !important;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.upload-icon {
  font-size: 40px;
  color: rgba(79, 195, 247, 0.8) !important;
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(79, 195, 247, 0.3));
}

.upload-text p {
  margin: 0;
  color: #e5e7eb !important;
  font-weight: 500;
}

.upload-hint {
  font-size: 12px;
  color: rgba(156, 163, 175, 0.9) !important;
  margin-top: 4px;
}

.preview-section {
  border: 1px solid rgba(75, 85, 99, 0.6) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.7) 0%, rgba(55, 65, 81, 0.5) 100%) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: #f3f4f6 !important;
  font-weight: 600;
  font-size: 16px;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 16px;
}

.preview-item {
  display: flex;
  flex-direction: column;
}

.preview-image {
  position: relative;
  width: 130px;
  height: 130px;
  border: 1px solid rgba(75, 85, 99, 0.6) !important;
  border-radius: 12px !important;
  overflow: hidden;
  background: rgba(17, 24, 39, 0.8) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease;
}

.preview-image:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(79, 195, 247, 0.6) !important;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
}

.preview-image:hover .preview-overlay {
  opacity: 1;
}

.preview-overlay .el-button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  color: #dc2626 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.preview-info {
  margin-top: 10px;
}

.file-name {
  font-size: 13px;
  color: #f3f4f6 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.file-size {
  font-size: 11px;
  color: rgba(156, 163, 175, 0.9) !important;
  margin-top: 3px;
}

.clear-all-btn {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.9) 100%) !important;
  border: 1px solid rgba(239, 68, 68, 0.6) !important;
  color: white !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3) !important;
}

.clear-all-btn:hover {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.9) 0%, rgba(239, 68, 68, 1) 100%) !important;
  border-color: rgba(220, 38, 38, 0.8) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4) !important;
}
</style> 