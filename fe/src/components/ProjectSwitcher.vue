<template>
  <div class="project-switcher">
    <!-- 项目切换按钮 -->
    <el-button
      @click="toggleDropdown"
      type="text"
      class="project-button"
      :icon="ProjectIcon"
      size="small"
    >
      <span class="project-name">{{ currentProjectName }}</span>
      <el-icon class="dropdown-icon" :class="{ 'rotate-180': dropdownVisible }">
        <ArrowDown />
      </el-icon>
    </el-button>

    <!-- 项目列表弹窗 -->
    <el-dialog
      v-model="dropdownVisible"
      title="切换项目"
      width="400px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      class="project-switcher-dialog"
    >
      <div class="project-list">
        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索项目..."
          :prefix-icon="Search"
          size="default"
          class="search-input mb-4"
        />

        <!-- 项目列表 -->
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading text-2xl">
            <Loading />
          </el-icon>
          <p class="text-sm text-gray-500 mt-2">加载项目中...</p>
        </div>

        <div v-else-if="error" class="error-container">
          <p class="text-red-500 text-sm">{{ error }}</p>
          <el-button @click="loadProjects" type="primary" size="small" class="mt-2">
            重试
          </el-button>
        </div>

        <div v-else class="projects-container">
          <div
            v-for="project in filteredProjects"
            :key="project.project_id"
            @click="switchToProject(project)"
            class="project-item"
            :class="{ 'current-project': project.project_id === currentProjectId }"
          >
            <div class="project-info">
              <h4 class="project-title">{{ project.name || project.project_id }}</h4>
              <p class="project-id">ID: {{ project.project_id }}</p>
            </div>
            <el-icon v-if="project.project_id === currentProjectId" class="current-icon">
              <Check />
            </el-icon>
          </div>

          <div v-if="filteredProjects.length === 0" class="no-projects">
            <p class="text-gray-500 text-sm">没有找到匹配的项目</p>
          </div>
        </div>

        <!-- 创建新项目按钮 -->
        <div class="footer-actions">
          <el-button
            @click="openCreateDialog"
            type="primary"
            size="default"
            :icon="Plus"
            class="w-full"
          >
            创建新项目
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 创建项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新项目"
      width="450px"
      :close-on-click-modal="false"
      class="create-project-dialog"
    >
      <form @submit.prevent="handleCreateProject" class="create-form">
        <div class="form-group">
          <label for="newProjectName" class="form-label">项目名称</label>
          <el-input
            id="newProjectName"
            v-model="newProjectName"
            placeholder="输入项目名称 (例如: my-cool-app)"
            size="default"
            :disabled="isCreating"
            required
          />
          <p v-if="projectNameError" class="error-text">{{ projectNameError }}</p>
        </div>

        <p v-if="createError" class="error-text">{{ createError }}</p>

        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false" :disabled="isCreating">
            取消
          </el-button>
          <el-button
            type="primary"
            native-type="submit"
            :loading="isCreating"
            :disabled="isCreating"
          >
            创建项目
          </el-button>
        </div>
      </form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  ElButton,
  ElDialog,
  ElInput,
  ElIcon,
  ElMessage,
} from 'element-plus';
import {
  ArrowDown,
  Search,
  Loading,
  Check,
  Plus,
  Folder as ProjectIcon,
} from '@element-plus/icons-vue';
import { listUserProjects, type ProjectListItem } from '@/api/project';
import { createProject as createProjectApi } from '@/api/fs';

const router = useRouter();
const route = useRoute();

// State
const dropdownVisible = ref(false);
const projects = ref<ProjectListItem[]>([]);
const isLoading = ref(false);
const error = ref<string | null>(null);
const searchQuery = ref('');

// Create project dialog state
const showCreateDialog = ref(false);
const newProjectName = ref('');
const isCreating = ref(false);
const createError = ref<string | null>(null);
const projectNameError = ref<string | null>(null);

// Current project info
const currentProjectId = computed(() => route.params.projectID as string);
const currentProjectName = computed(() => {
  if (!currentProjectId.value) return '选择项目';
  const currentProject = projects.value.find(p => p.project_id === currentProjectId.value);
  return currentProject?.name || currentProjectId.value;
});

// Filtered projects for search
const filteredProjects = computed(() => {
  if (!searchQuery.value) return projects.value;
  const query = searchQuery.value.toLowerCase();
  return projects.value.filter(project => 
    (project.name?.toLowerCase().includes(query)) ||
    project.project_id.toLowerCase().includes(query)
  );
});

// Methods
const toggleDropdown = async () => {
  if (!dropdownVisible.value) {
    await loadProjects();
  }
  dropdownVisible.value = !dropdownVisible.value;
};

const loadProjects = async () => {
  isLoading.value = true;
  error.value = null;
  try {
    projects.value = await listUserProjects();
  } catch (err: any) {
    console.error('Failed to load projects:', err);
    error.value = `加载项目失败: ${err.message || '未知错误'}`;
  } finally {
    isLoading.value = false;
  }
};

const switchToProject = (project: ProjectListItem) => {
  if (project.project_id === currentProjectId.value) {
    dropdownVisible.value = false;
    return;
  }

  router.push({
    name: 'ide',
    params: { projectID: project.project_id }
  });
  dropdownVisible.value = false;
  ElMessage.success(`已切换到项目: ${project.name || project.project_id}`);
};

const openCreateDialog = () => {
  dropdownVisible.value = false;
  showCreateDialog.value = true;
  newProjectName.value = '';
  projectNameError.value = null;
  createError.value = null;
};

const validateProjectName = (): boolean => {
  const validPattern = /^[a-zA-Z0-9-_]+$/;
  if (!newProjectName.value) {
    projectNameError.value = '项目名称不能为空';
    return false;
  }
  if (!validPattern.test(newProjectName.value)) {
    projectNameError.value = '项目名称只能包含字母、数字、连字符(-) 和下划线(_)';
    return false;
  }
  projectNameError.value = null;
  return true;
};

const handleCreateProject = async () => {
  createError.value = null;
  if (!validateProjectName()) {
    return;
  }

  isCreating.value = true;
  try {
    await createProjectApi(newProjectName.value);
    showCreateDialog.value = false;
    ElMessage.success(`项目 "${newProjectName.value}" 创建成功！`);
    
    // Navigate to the new project
    router.push({
      name: 'ide',
      params: { projectID: newProjectName.value }
    });
  } catch (err: any) {
    console.error('Failed to create project:', err);
    createError.value = `创建项目失败: ${err.message || '未知错误'}`;
  } finally {
    isCreating.value = false;
  }
};

// Load projects on mount
onMounted(() => {
  loadProjects();
});
</script>

<style scoped>
.project-switcher {
  position: relative;
}

.project-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  color: #e0e0e0;
  background: transparent;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.2s;
}

.project-button:hover {
  background: #3a3a3f;
  border-color: #555;
}

.project-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  transition: transform 0.2s;
  font-size: 12px;
}

.rotate-180 {
  transform: rotate(180deg);
}

.project-list {
  max-height: 400px;
  overflow-y: auto;
}

.search-input {
  margin-bottom: 16px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}

.projects-container {
  max-height: 250px;
  overflow-y: auto;
}

.project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.project-item:hover {
  background: #f5f5f5;
  border-color: #007acc;
}

.current-project {
  background: #e8f4fd;
  border-color: #007acc;
}

.project-info {
  flex: 1;
}

.project-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #333;
}

.project-id {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.current-icon {
  color: #007acc;
  font-size: 16px;
}

.no-projects {
  text-align: center;
  padding: 24px;
}

.footer-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.create-form {
  padding: 4px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.error-text {
  color: #f56565;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 20px;
}
</style>

<style>
/* Global styles for dark theme dialog */
.project-switcher-dialog .el-dialog {
  background-color: #262626 !important;
  border-radius: 8px !important;
}

.project-switcher-dialog .el-dialog__title {
  color: #e0e0e0 !important;
}

.project-switcher-dialog .el-dialog__headerbtn .el-icon {
  color: #e0e0e0 !important;
}

.project-switcher-dialog .el-dialog__body {
  color: #cccccc !important;
}

.project-switcher-dialog .project-item {
  background: #333 !important;
  border-color: #555 !important;
}

.project-switcher-dialog .project-item:hover {
  background: #3a3a3f !important;
  border-color: #007acc !important;
}

.project-switcher-dialog .current-project {
  background: #1e3a5f !important;
  border-color: #007acc !important;
}

.project-switcher-dialog .project-title {
  color: #e0e0e0 !important;
}

.project-switcher-dialog .project-id {
  color: #999 !important;
}

.create-project-dialog .el-dialog {
  background-color: #262626 !important;
  border-radius: 8px !important;
}

.create-project-dialog .el-dialog__title {
  color: #e0e0e0 !important;
}

.create-project-dialog .el-dialog__headerbtn .el-icon {
  color: #e0e0e0 !important;
}

.create-project-dialog .el-dialog__body {
  color: #cccccc !important;
}

.create-project-dialog .form-label {
  color: #e0e0e0 !important;
}
</style> 