<template>
  <div class="diff-viewer w-full">
    <!-- Header with file path and action buttons -->
    <div class="diff-header flex items-center justify-between p-3 bg-[#1e1e1e] border border-[#3e3e42] rounded-t">
      <div class="flex items-center space-x-2">
        <span class="i-ph-file-text text-[#4fc3f7]"></span>
        <span class="text-[#cccccc] font-medium">{{ filePath }}</span>
        <span v-if="replacementCount > 0" class="text-xs bg-green-600 text-white px-2 py-1 rounded">
          {{ replacementCount }} change{{ replacementCount > 1 ? 's' : '' }}
        </span>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- Copy diff button -->
        <el-button 
          size="small" 
          type="text"
          class="text-[#cccccc] hover:text-white"
          @click="copyDiffToClipboard"
          title="Copy unified diff"
        >
          <span class="i-ph-copy text-sm"></span>
        </el-button>
        
        <!-- Toggle view mode -->
        <el-button 
          size="small" 
          type="text"
          class="text-[#cccccc] hover:text-white"
          @click="toggleViewMode"
          :title="viewMode === 'side' ? 'Switch to inline view' : 'Switch to side-by-side view'"
        >
          <span :class="viewMode === 'side' ? 'i-ph-columns' : 'i-ph-list'"></span>
        </el-button>
      </div>
    </div>

    <!-- Monaco Diff Editor -->
    <div class="diff-container" :style="{ height: diffHeight + 'px' }">
      <VueMonacoDiffEditor
        :original="originalContent"
        :modified="modifiedContent"
        :language="language"
        :options="diffOptions"
        @editor-did-mount="handleEditorMount"
        class="w-full h-full"
      />
    </div>

    <!-- Change summary -->
    <div v-if="previewChanges && previewChanges.length > 0" class="change-summary p-3 bg-[#1e1e1e] border border-[#3e3e42] border-t-0 rounded-b">
      <div class="text-xs text-[#888888] font-medium mb-2">Changes Preview:</div>
      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div 
          v-for="(change, index) in previewChanges" 
          :key="index"
          class="bg-[#2d2d30] p-2 rounded text-xs"
        >
          <div class="text-[#4fc3f7] font-medium mb-1">
            Lines {{ change.startLine }}-{{ change.endLine }}:
          </div>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <div class="text-red-400 font-medium">- Removed:</div>
              <pre class="text-[#cccccc] bg-red-900/20 p-1 rounded overflow-x-auto">{{ change.searchContent }}</pre>
            </div>
            <div>
              <div class="text-green-400 font-medium">+ Added:</div>
              <pre class="text-[#cccccc] bg-green-900/20 p-1 rounded overflow-x-auto">{{ change.replaceContent }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import { ElButton, ElMessage } from 'element-plus'

// Import Monaco Diff Editor from the package
import { VueMonacoDiffEditor } from '@guolao/vue-monaco-editor'

interface ChangePreview {
  searchContent: string
  replaceContent: string
  startLine: number
  endLine: number
  contextBefore: string
  contextAfter: string
}

interface Props {
  filePath: string
  originalContent: string
  modifiedContent: string
  unifiedDiff?: string
  replacementCount?: number
  previewChanges?: ChangePreview[]
  language?: string
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  language: 'plaintext',
  height: 400,
  replacementCount: 0
})

// State
const viewMode = ref<'side' | 'inline'>('side')
const diffEditor = ref<any>(null)

// Computed
const diffHeight = computed(() => {
  // Calculate height based on content or use provided height
  const minHeight = 200
  const maxHeight = 600
  const calculatedHeight = Math.max(
    minHeight, 
    Math.min(maxHeight, props.height)
  )
  return calculatedHeight
})

const diffOptions = computed(() => ({
  theme: 'vs-dark',
  fontSize: 14,
  automaticLayout: true,
  readOnly: true,
  renderSideBySide: viewMode.value === 'side',
  renderWhitespace: 'boundary' as const,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  contextmenu: true,
  folding: false,
  lineNumbers: 'on' as const,
  glyphMargin: false,
  wordWrap: 'on' as const,
  diffWordWrap: 'on' as const,
  enableSplitViewResizing: false,
  renderIndicators: true,
  ignoreTrimWhitespace: false,
  renderOverviewRuler: true
}))

// Methods
const handleEditorMount = (editor: any) => {
  console.log('[DiffViewer] Monaco Diff Editor mounted')
  diffEditor.value = editor
  
  // Auto-resize to fit content
  setTimeout(() => {
    if (editor && typeof editor.layout === 'function') {
      editor.layout()
    }
  }, 100)
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'side' ? 'inline' : 'side'
  
  // Update editor options
  if (diffEditor.value && typeof diffEditor.value.updateOptions === 'function') {
    diffEditor.value.updateOptions({
      renderSideBySide: viewMode.value === 'side'
    })
  }
}

const copyDiffToClipboard = async () => {
  try {
    const textToCopy = props.unifiedDiff || generateSimpleDiff()
    await navigator.clipboard.writeText(textToCopy)
    ElMessage.success('Diff copied to clipboard')
  } catch (error) {
    console.error('Failed to copy diff:', error)
    ElMessage.error('Failed to copy diff to clipboard')
  }
}

const generateSimpleDiff = () => {
  if (!props.previewChanges || props.previewChanges.length === 0) {
    return `--- ${props.filePath}\n+++ ${props.filePath}\n@@ -1,1 +1,1 @@\nNo changes detected`
  }
  
  let diff = `--- a/${props.filePath}\n+++ b/${props.filePath}\n`
  
  props.previewChanges.forEach((change, index) => {
    diff += `@@ -${change.startLine},${change.endLine - change.startLine + 1} +${change.startLine},${change.endLine - change.startLine + 1} @@\n`
    
    // Add removed lines
    change.searchContent.split('\n').forEach(line => {
      diff += `-${line}\n`
    })
    
    // Add added lines  
    change.replaceContent.split('\n').forEach(line => {
      diff += `+${line}\n`
    })
    
    if (index < props.previewChanges!.length - 1) {
      diff += '\n'
    }
  })
  
  return diff
}

// Lifecycle
onMounted(() => {
  console.log('[DiffViewer] Component mounted with file:', props.filePath)
})
</script>

<style scoped>
.diff-viewer {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.diff-container {
  border-left: 1px solid #3e3e42;
  border-right: 1px solid #3e3e42;
}

.change-summary {
  scrollbar-width: thin;
  scrollbar-color: #404040 #1e1e1e;
}

.change-summary::-webkit-scrollbar {
  width: 6px;
}

.change-summary::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.change-summary::-webkit-scrollbar-thumb {
  background-color: #404040;
  border-radius: 3px;
}

/* Monaco diff editor custom styles */
:deep(.monaco-editor) {
  background-color: #1e1e1e !important;
}

:deep(.monaco-diff-editor) {
  background-color: #1e1e1e !important;
}

:deep(.monaco-editor .margin) {
  background-color: #1e1e1e !important;
}

:deep(.monaco-editor .monaco-editor-background) {
  background-color: #1e1e1e !important;
}
</style> 