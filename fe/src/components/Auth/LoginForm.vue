<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" label-position="top" @submit.prevent="handleSubmit">
    <el-form-item label="Username" prop="username">
      <el-input v-model="loginForm.username" placeholder="Enter your username" />
    </el-form-item>
    <el-form-item label="Password" prop="password">
      <el-input v-model="loginForm.password" type="password" placeholder="Enter your password" show-password />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" native-type="submit" :loading="loading" class="w-full">
        Login
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElForm, ElFormItem, ElInput, ElButton } from 'element-plus';
import type { LoginRequest } from '@/api/user'; // Ensure correct path

const emit = defineEmits<{
  (e: 'submit', formData: LoginRequest, Cb: () => void): void;
}>();

const loginFormRef = ref<FormInstance>();
const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
});
const loading = ref(false);

const loginRules = reactive<FormRules<LoginRequest>>({
  username: [{ required: true, message: 'Please input username', trigger: 'blur' }],
  password: [{ required: true, message: 'Please input password', trigger: 'blur' }],
});

const handleSubmit = () => {
  loginFormRef.value?.validate((valid) => {
    if (valid) {
      loading.value = true;
      // Emit the submit event with form data and a callback to stop loading
      emit('submit', { ...loginForm }, () => {
        loading.value = false;
      });
    } else {
      console.log('error submit!');
    }
  });
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style> 