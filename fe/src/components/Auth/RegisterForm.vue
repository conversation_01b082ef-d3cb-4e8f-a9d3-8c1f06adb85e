<template>
  <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" label-position="top" @submit.prevent="handleSubmit">
    <el-form-item label="Username" prop="username">
      <el-input v-model="registerForm.username" placeholder="Choose a username" />
    </el-form-item>
    <el-form-item label="Password" prop="password">
      <el-input v-model="registerForm.password" type="password" placeholder="Create a password" show-password />
    </el-form-item>
    <el-form-item label="Confirm Password" prop="confirmPassword">
      <el-input v-model="registerForm.confirmPassword" type="password" placeholder="Confirm your password" show-password />
    </el-form-item>
    <el-form-item label="Email (Optional)" prop="email">
      <el-input v-model="registerForm.email" type="email" placeholder="Enter your email (optional)" />
    </el-form-item>
    <el-form-item label="Full Name (Optional)" prop="full_name">
      <el-input v-model="registerForm.full_name" placeholder="Enter your full name (optional)" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" native-type="submit" :loading="loading" class="w-full">
        Register
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElForm, ElFormItem, ElInput, ElButton } from 'element-plus';
import type { RegisterRequest } from '@/api/user'; // Ensure correct path

// Define the shape of the form data including confirmPassword
interface RegisterFormData extends RegisterRequest {
  confirmPassword?: string;
}

const emit = defineEmits<{
  (e: 'submit', formData: RegisterRequest, Cb: () => void): void;
}>();

const registerFormRef = ref<FormInstance>();
const registerForm = reactive<RegisterFormData>({
  username: '',
  password: '',
  confirmPassword: '', // Add confirm password field
  email: '',
  full_name: '',
});
const loading = ref(false);

// Custom validator for confirm password
const validatePassConfirm = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('Please input the password again'));
  } else if (value !== registerForm.password) {
    callback(new Error("Passwords don't match!"));
  } else {
    callback();
  }
};

const registerRules = reactive<FormRules<RegisterFormData>>({
  username: [
    { required: true, message: 'Please input username', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters', trigger: 'blur' },
  ],
  password: [
    { required: true, message: 'Please input password', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: 'Please confirm password', trigger: 'blur' },
    { validator: validatePassConfirm, trigger: 'blur' },
  ],
  email: [
    { type: 'email', message: 'Please input correct email address', trigger: ['blur', 'change'] },
  ],
  // No specific rules for full_name as it's optional
});

const handleSubmit = () => {
  registerFormRef.value?.validate((valid) => {
    if (valid) {
      loading.value = true;
      // Prepare data for emission (exclude confirmPassword)
      const { confirmPassword, ...submitData } = registerForm;
      emit('submit', submitData, () => {
        loading.value = false;
      });
    } else {
      console.log('error submit!');
    }
  });
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style> 