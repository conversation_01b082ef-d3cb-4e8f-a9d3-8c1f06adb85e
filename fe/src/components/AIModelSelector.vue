<template>
    <div class="ai-model-selector">
        <select v-model="selectedModel" @change="handleModelChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            :disabled="loading">
            <option value="">请选择AI模型...</option>
            <option v-for="model in availableModels" :key="model.id" :value="model.model_name">
                {{ model.model_name }}
            </option>
        </select>

        <div v-if="loading" class="mt-1 text-xs text-gray-500">
            加载模型中...
        </div>

        <div v-if="error" class="mt-1 text-xs text-red-600">
            {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getAIModels } from '@/api/aimodel'
import type { AIModel } from '@/api/types'

// Props
interface Props {
    modelValue?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: ''
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: string]
    'change': [model: AIModel | null]
}>()

// State
const selectedModel = ref(props.modelValue)
const availableModels = ref<AIModel[]>([])
const loading = ref(false)
const error = ref('')

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
    selectedModel.value = newValue
})

// Watch for internal changes
watch(selectedModel, (newValue) => {
    emit('update:modelValue', newValue)
})

// Methods
const loadModels = async () => {
    try {
        loading.value = true
        error.value = ''

        const response = await getAIModels(true) // Only get active models
        availableModels.value = response.models

        // If no model is selected and there are available models, select the first one
        if (!selectedModel.value && response.models.length > 0) {
            selectedModel.value = response.models[0].model_name
        }
    } catch (err) {
        console.error('Failed to load AI models:', err)
        error.value = '加载AI模型失败'
    } finally {
        loading.value = false
    }
}

const handleModelChange = () => {
    const selectedModelData = availableModels.value.find(
        model => model.model_name === selectedModel.value
    )
    emit('change', selectedModelData || null)
}

// Lifecycle
onMounted(() => {
    loadModels()
})
</script>

<style scoped>
/* 组件样式由父组件通过class传递 */
</style>