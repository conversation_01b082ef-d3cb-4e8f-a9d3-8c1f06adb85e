<script setup lang="ts">
import { computed } from 'vue'
import type { AgentMessage } from '@/api/agent'
import UserMessage from './message/UserMessage.vue'
import AssistantMessage from './message/AssistantMessage.vue'

const props = defineProps<{
  message: AgentMessage,
  isLast: boolean,
}>()

const emit = defineEmits(['option-selected', 'retry'])

const renderInfo = computed(() => {
  const { role, content, tool_calls, tool_results, msg_type } = props.message

  // Primary logic: Use msg_type for rendering decision
  if (msg_type) {
    if (msg_type === 'user_input') {
      if (!content) return { component: null, props: {} }
      return { component: UserMessage, props: { message: props.message, isLast: props.isLast } };
    }
    // All other message types ('assistant_output', 'tool_result', etc.) are handled by AssistantMessage
    if (!content && !tool_calls?.length && !tool_results?.length) {
      return { component: null, props: {} };
    }
    return { component: AssistantMessage, props: { content, tool_calls, tool_results } };
  }

  // Fallback logic for old data without msg_type
  console.warn(`[MessageRenderer] Message lacks msg_type, falling back to role-based rendering. ID: ${props.message.msg_id}`);
  if (role === 'user') {
    // Legacy check: if a user-role message has tool results, it's a tool result message
    if (content && content.includes('<tool_result')) {
      return { component: AssistantMessage, props: { content, tool_calls, tool_results } };
    }
    if (!content) return { component: null, props: {} };
    return { component: UserMessage, props: { message: props.message, isLast: props.isLast } };
  } else { // 'assistant' role
    if (!content && !tool_calls?.length && !tool_results?.length) {
      return { component: null, props: {} };
    }
    return {
      component: AssistantMessage,
      props: { content, tool_calls, tool_results },
    }
  }
})
</script>

<template>
  <component
    :is="renderInfo.component"
    v-if="renderInfo.component"
    v-bind="renderInfo.props"
    @option-selected="(option: string) => emit('option-selected', option)"
    @retry="emit('retry')"
  />
</template> 