<template>
  <el-form 
    ref="requirementFormRef" 
    :model="requirementForm" 
    :rules="requirementRules" 
    label-position="top" 
    @submit.prevent="handleSubmit"
    class="requirement-form"
  >
    <el-form-item label="Title" prop="title">
      <el-input 
        v-model="requirementForm.title" 
        placeholder="Enter a clear and concise requirement title"
        maxlength="200"
        show-word-limit
        :disabled="loading"
      />
    </el-form-item>

    <el-form-item label="AI Model" prop="model_name">
      <el-select 
        v-model="requirementForm.model_name" 
        placeholder="Select AI model for processing"
        :disabled="loading"
        style="width: 100%"
      >
        <el-option 
          value="gemini-2.5-pro" 
          label="Gemini 2.5 Pro (Recommended)"
        >
          <div>
            <div style="font-weight: 500;">Gemini 2.5 Pro</div>
            <div style="font-size: 12px; color: var(--el-text-color-secondary);">
              Most capable model for complex requirements and detailed analysis
            </div>
          </div>
        </el-option>
        <el-option 
          value="gemini-2.5-flash" 
          label="Gemini 2.5 Flash"
        >
          <div>
            <div style="font-weight: 500;">Gemini 2.5 Flash</div>
            <div style="font-size: 12px; color: var(--el-text-color-secondary);">
              Faster response for simpler requirements
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="Description" prop="content">
      <el-input 
        v-model="requirementForm.content" 
        type="textarea"
        placeholder="Describe your requirement in detail. What do you want to build or achieve? Include any specific features, constraints, or preferences you have."
        :rows="8"
        maxlength="5000"
        show-word-limit
        :disabled="loading"
      />
    </el-form-item>

    <el-form-item label="Reference Images (Optional)">
      <div class="image-upload-section">
        <p class="image-hint">
          Upload reference images to help AI better understand your requirements. 
          The AI will analyze visual elements, style, and context to generate more accurate questions and plans.
        </p>
        <ImageUploadArea 
          v-model="selectedImages" 
          :max-count="5"
          :max-size="10 * 1024 * 1024"
          :disabled="loading"
        />
      </div>
    </el-form-item>

    <el-form-item label="Reference Website (Optional)" prop="website_url">
      <div class="website-url-section">
        <p class="website-hint">
          Provide a reference website URL to help AI understand your design preferences and functional requirements.
          The AI will analyze the website's structure, style, and features.
        </p>
        <el-input 
          v-model="requirementForm.website_url" 
          placeholder="https://example.com - Enter a website URL for reference"
          :disabled="loading"
          clearable
        >
          <template #prefix>
            <span style="color: var(--el-color-primary);">🌐</span>
          </template>
        </el-input>
      </div>
    </el-form-item>
    
    <el-form-item>
      <div class="form-actions">
        <el-button 
          v-if="!isEditMode"
          type="primary" 
          native-type="submit" 
          :loading="loading" 
          size="large"
          class="submit-btn"
        >
          {{ loading ? 'Creating...' : 'Create Requirement' }}
        </el-button>
        
        <el-button 
          v-if="isEditMode"
          type="primary" 
          native-type="submit" 
          :loading="loading" 
          size="large"
          class="submit-btn"
        >
          {{ loading ? 'Updating...' : 'Update Requirement' }}
        </el-button>
        
        <el-button 
          v-if="showCancel"
          @click="handleCancel" 
          :disabled="loading"
          size="large"
        >
          Cancel
        </el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElForm, ElFormItem, ElInput, ElButton, ElSelect, ElOption } from 'element-plus';
import ImageUploadArea from './ImageUploadArea.vue';
import type { 
  CreateRequirementRequest, 
  CreateRequirementWithImagesRequest,
  UpdateRequirementRequest, 
  Requirement 
} from '@/api/requirement.js';

// Props interface
interface Props {
  // For edit mode: pass existing requirement
  requirement?: Requirement | null;
  // Loading state (can be controlled by parent)
  loading?: boolean;
  // Show cancel button
  showCancel?: boolean;
}

// Events interface
interface Emits {
  (e: 'submit', formData: CreateRequirementRequest | CreateRequirementWithImagesRequest | UpdateRequirementRequest): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  requirement: null,
  loading: false,
  showCancel: true,
});

const emit = defineEmits<Emits>();

// Form ref
const requirementFormRef = ref<FormInstance>();

// Check if in edit mode
const isEditMode = computed(() => !!props.requirement);

// Form data
const requirementForm = reactive<CreateRequirementRequest>({
  title: '',
  content: '',
  model_name: 'gemini-2.5-pro', // Default to recommended model
  website_url: '', // Add website URL field
});

// Image upload state
const selectedImages = ref<File[]>([]);

// Form validation rules
const requirementRules = reactive<FormRules<CreateRequirementRequest>>({
  title: [
    { required: true, message: 'Please input requirement title', trigger: 'blur' },
    { min: 1, max: 200, message: 'Title must be between 1 and 200 characters', trigger: 'blur' },
  ],
  content: [
    { required: true, message: 'Please input requirement description', trigger: 'blur' },
    { min: 10, max: 5000, message: 'Description must be between 10 and 5000 characters', trigger: 'blur' },
  ],
  website_url: [
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value || value.trim() === '') {
          callback(); // Optional field, allow empty
          return;
        }
        try {
          new URL(value);
          callback();
        } catch {
          callback(new Error('Please enter a valid URL (e.g., https://example.com)'));
        }
      },
      trigger: 'blur'
    }
  ],
});

// Internal loading state
const loading = computed(() => props.loading);

// Watch for requirement prop changes (for edit mode)
watch(
  () => props.requirement,
  (newRequirement) => {
    if (newRequirement) {
      requirementForm.title = newRequirement.title;
      requirementForm.content = newRequirement.content;
      requirementForm.model_name = newRequirement.model_name || 'gemini-2.5-pro'; // Ensure model_name is set
      requirementForm.website_url = newRequirement.website_url || ''; // Load website URL
    } else {
      // Reset form for create mode
      resetForm();
    }
  },
  { immediate: true }
);

// Methods
const handleSubmit = () => {
  requirementFormRef.value?.validate((valid) => {
    if (valid) {
      console.log('[RequirementForm] Form validation passed, submitting:', {
        form: requirementForm,
        imageCount: selectedImages.value.length
      });
      
      if (isEditMode.value) {
        // In edit mode, only emit changed fields (images not supported in edit mode for now)
        const updateData: UpdateRequirementRequest = {};
        if (requirementForm.title !== props.requirement?.title) {
          updateData.title = requirementForm.title;
        }
        if (requirementForm.content !== props.requirement?.content) {
          updateData.content = requirementForm.content;
        }
        if (requirementForm.model_name !== props.requirement?.model_name) {
          updateData.model_name = requirementForm.model_name;
        }
        if (requirementForm.website_url !== props.requirement?.website_url) {
          updateData.website_url = requirementForm.website_url;
        }
        emit('submit', updateData);
      } else {
        // In create mode, use unified FormData format
        const formData = new FormData();
        formData.append('title', requirementForm.title);
        formData.append('content', requirementForm.content);
        formData.append('model_name', requirementForm.model_name);
        
        if (requirementForm.website_url) {
          formData.append('website_url', requirementForm.website_url);
        }
        
        // Add image files
        selectedImages.value.forEach((file: File) => {
          formData.append('images', file);
        });
        
        emit('submit', formData);
      }
    } else {
      console.log('[RequirementForm] Form validation failed');
    }
  });
};

const handleCancel = () => {
  console.log('[RequirementForm] Cancel clicked');
  emit('cancel');
};

const resetForm = () => {
  requirementForm.title = '';
  requirementForm.content = '';
  requirementForm.model_name = 'gemini-2.5-pro'; // Reset model_name
  selectedImages.value = [];
  requirementFormRef.value?.clearValidate();
  console.log('[RequirementForm] Form reset');
};

// Expose methods for parent component
defineExpose({
  resetForm,
  validate: () => requirementFormRef.value?.validate(),
});
</script>

<style scoped>
.requirement-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  align-items: center;
}

.submit-btn {
  min-width: 140px;
}

:deep(.el-textarea__inner) {
  resize: vertical;
  min-height: 120px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-textarea__inner) {
  font-size: 14px;
  line-height: 1.5;
}

.image-upload-section {
  margin-top: 8px;
}

.image-hint {
  margin: 0 0 16px 0;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .requirement-form {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .submit-btn {
    width: 100%;
  }
}
</style>