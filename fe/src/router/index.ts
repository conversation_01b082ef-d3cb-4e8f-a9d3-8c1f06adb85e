import { createRouter, createWebHistory } from 'vue-router'
// Remove import for NewProjectView if no longer used directly
// import NewProjectView from '@/views/NewProjectView.vue'
import AppLayout from '@/layout/AppLayout.vue'
import LoginView from '@/views/LoginView.vue'
import RegisterView from '@/views/RegisterView.vue'
// Import the new ProjectsView
import ProjectsView from '@/views/ProjectsView.vue'
import GlobalSettingsView from '@/views/GlobalSettingsView.vue' // NEW: Import GlobalSettingsView
import { useUserStore } from '@/store/user.js' // NEW: Import user store with .js extension
// Import HomeView
import HomeView from '@/views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home', // Changed to home
      component: HomeView, // Point root to HomeView
    },
    {
      path: '/projects',
      name: 'projects',
      component: ProjectsView,
    },
    {
      path: '/ide/:projectID',
      name: 'ide',
      component: AppLayout,
      props: true,
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView,
    },
    {
      path: '/settings/global', // NEW: Global Settings route
      name: 'global_settings',
      component: GlobalSettingsView,
      meta: { requiresAuth: true, requiresAdmin: true }, // Mark as requiring auth and admin
    },
    // Catch-all route - redirects to project list if logged in, otherwise login (handled by guard)
    { 
      path: '/:pathMatch(.*)*', 
      redirect: '/' 
    }
  ]
})

// Simple navigation guard - let backend handle auth
router.beforeEach(async (to, from, next) => { // Made async
  console.log('[Router] Navigation to:', to.fullPath);
  
  const userStore = useUserStore(); // Get store instance

  // Ensure user authentication status is initialized before checking roles
  if (!userStore.isInitialized) {
    await userStore.initializeAuth();
  }

  // For login/register pages, always allow access
  if (to.name === 'login' || to.name === 'register') {
    next();
    return;
  }
  
  // Check if route requires authentication
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    console.warn('[Router] Navigation blocked: requires authentication');
    next({ name: 'login', query: { redirect: to.fullPath } });
    return;
  }

  // Check if route requires admin privileges
  if (to.meta.requiresAdmin && !userStore.isAdmin) {
    console.warn('[Router] Navigation blocked: requires admin privileges');
    // Redirect to home or a permission denied page
    next({ path: '/', replace: true }); // Or a dedicated /permission-denied page
    return;
  }

  // For all other routes, just proceed - let backend APIs handle auth
  next();
});

export default router 