/* Import UnoCSS entry points (removed - imported in main.ts) */

/* Import Element Plus styles */
@import 'element-plus/dist/index.css';

/* Global styles if needed */
body {
  margin: 0;
  font-family: sans-serif;
  background-color: #1a1a1f; /* Match base background */
  color: #e6e6e6; /* Default text color */
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Dark theme for Element Plus MessageBox */
.el-message-box {
  background-color: #2d2d30 !important;
  border: 1px solid #3e3e42 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
}

.el-message-box__header {
  background-color: transparent !important;
  border-bottom: 1px solid #3e3e42 !important;
  padding: 20px 20px 10px !important;
}

.el-message-box__title {
  color: #cccccc !important;
  font-weight: 600 !important;
}

.el-message-box__content {
  background-color: transparent !important;
  padding: 10px 20px 20px !important;
}

.el-message-box__message {
  color: #e6e6e6 !important;
}

.el-message-box__btns {
  background-color: transparent !important;
  border-top: 1px solid #3e3e42 !important;
  padding: 15px 20px 20px !important;
}

.el-message-box__btns .el-button {
  border: 1px solid #3e3e42 !important;
  background-color: #1e1e1e !important;
  color: #cccccc !important;
}

.el-message-box__btns .el-button:hover {
  border-color: #4fc3f7 !important;
  background-color: #2d2d30 !important;
  color: #ffffff !important;
}

.el-message-box__btns .el-button--danger {
  background-color: #d32f2f !important;
  border-color: #d32f2f !important;
  color: #ffffff !important;
}

.el-message-box__btns .el-button--danger:hover {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
}

/* Dark theme for the overlay */
.el-overlay {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

/* Warning icon color */
.el-message-box__status.el-icon {
  color: #ff9800 !important;
} 