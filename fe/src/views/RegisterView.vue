<template>
  <div class="flex justify-center items-center min-h-screen bg-[#1a1a1f] py-8">
    <el-card class="register-card w-full max-w-md p-8 !bg-[#262626] border-none rounded-lg">
      <template #header>
        <div class="text-center text-2xl font-bold text-gray-100">Register</div>
      </template>
      <el-divider class="!my-4 border-[#4d4d54]" />
      <RegisterForm @submit="handleRegister" />
       <div class="mt-6 text-center text-gray-300">
        Already have an account? 
        <router-link to="/login" class="text-[#5a8dee] hover:text-[#7aa5f5] hover:underline">
          Login here
        </router-link>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ElCard, ElMessage, ElDivider } from 'element-plus';
import RegisterForm from '@/components/Auth/RegisterForm.vue';
import type { RegisterRequest } from '@/api/user.js';
import { useUserStore } from '@/store/user.js';

const router = useRouter();
const userStore = useUserStore();

const handleRegister = async (formData: RegisterRequest, stopLoading: () => void) => {
  try {
    const response = await userStore.register(formData);
    console.log('Registration successful:', response);
    
    ElMessage.success(response.message || 'Registration successful! You are now logged in.');
    
    // After successful registration, redirect to projects page
    router.push('/');
  } catch (error: any) {
    console.error('Registration failed:', error);
    const errorMessage = error.response?.data?.message || error.message || 'Registration failed. Please try again.';
    ElMessage.error(errorMessage);
  } finally {
    stopLoading(); // Ensure loading state is always turned off
  }
};
</script>

<style>
/* Global styles to override Element Plus */
.el-card {
  border: none !important;
  border-radius: 8px !important;
  background-color: #262626 !important;
  box-shadow: none !important;
}
</style>

<style scoped>
/* Remove the no longer needed deep rules for el-card border/shadow */
/*
.register-card :deep(.el-card) {
  border: none !important;
  box-shadow: none !important;
}
*/

/* Keep other styles for contrast and appearance */
.register-card :deep(.el-card__header) {
  border-bottom: none !important;
  padding-bottom: 0 !important;
}

.register-card :deep(.el-form-item__label) {
  color: #e0e0e0 !important;
  margin-bottom: 4px !important;
}

.register-card :deep(.el-input__wrapper) {
  background-color: #333338 !important;
  box-shadow: none !important;
  border: 1px solid #555555 !important;
  border-radius: 4px !important;
}
.register-card :deep(.el-input__wrapper.is-focus) {
  border-color: #5a8dee !important;
}

.register-card :deep(.el-input__inner) {
  color: #f0f0f0 !important;
}

.register-card :deep(.el-input__inner::placeholder) {
    color: #888888 !important;
    opacity: 1;
}

.register-card :deep(.el-button--primary) {
  --el-button-bg-color: #5a8dee !important;
  --el-button-border-color: #5a8dee !important;
  --el-button-hover-bg-color: #7aa5f5 !important;
  --el-button-hover-border-color: #7aa5f5 !important;
  border-radius: 6px !important;
  padding: 18px 20px !important;
  font-weight: 500 !important;
  color: #ffffff !important;
}

.register-card :deep(.el-form-item__content .el-button) {
    width: 100% !important;
}

.min-h-screen {
  min-height: 100vh;
}
</style> 