<template>
  <div class="flex justify-center items-center min-h-screen bg-[#1a1a1f]">
    <el-card class="login-card w-full max-w-md p-8 !bg-[#262626] border-none rounded-lg">
      <template #header>
        <div class="text-center text-2xl font-bold text-gray-100">Login</div>
      </template>
      <el-divider class="!my-4 border-[#4d4d54]" />
      <LoginForm @submit="handleLogin" />
      <div class="mt-6 text-center text-gray-300">
        Don't have an account? 
        <router-link to="/register" class="text-[#5a8dee] hover:text-[#7aa5f5] hover:underline">
          Register here
        </router-link>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { ElCard, ElMessage, ElDivider } from 'element-plus';
import LoginForm from '@/components/Auth/LoginForm.vue';
import type { LoginRequest } from '@/api/user.js';
import { useUserStore } from '@/store/user.js';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const handleLogin = async (formData: LoginRequest, stopLoading: () => void) => {
  try {
    const response = await userStore.login(formData);
    console.log('Login successful:', response);

    ElMessage.success(response.message || 'Login successful!');
    
    // Redirect to the original intended route or projects page
    const redirectPath = (route.query.redirect as string) || '/';
    router.push(redirectPath);
  } catch (error: any) {
    console.error('Login failed:', error);
    const errorMessage = error.response?.data?.message || error.message || 'Login failed. Please try again.';
    ElMessage.error(errorMessage);
  } finally {
    stopLoading(); // Ensure loading state is always turned off
  }
};
</script>

<style>
/* Global styles to override Element Plus */
.el-card {
  border: none !important;
  border-radius: 8px !important;
  background-color: #262626 !important;
  box-shadow: none !important;
}
</style>

<style scoped>
/* Remove the no longer needed deep rules for el-card border/shadow */
/*
.login-card :deep(.el-card) {
  border: none !important;
  box-shadow: none !important;
}
*/

/* Keep other styles for contrast and appearance */
.login-card :deep(.el-card__header) {
  border-bottom: none !important;
  padding-bottom: 0 !important;
}

.login-card :deep(.el-form-item__label) {
  color: #e0e0e0 !important; /* Brighter label */
  margin-bottom: 4px !important;
}

.login-card :deep(.el-input__wrapper) {
  background-color: #333338 !important; /* Force dark input bg */
  box-shadow: none !important;
  border: 1px solid #555555 !important; /* Visible border */
  border-radius: 4px !important;
}
.login-card :deep(.el-input__wrapper.is-focus) {
  border-color: #5a8dee !important; /* Match link color */
}

.login-card :deep(.el-input__inner) {
  color: #f0f0f0 !important; /* Bright input text */
}

/* Style placeholder */
.login-card :deep(.el-input__inner::placeholder) {
    color: #888888 !important; /* Visible placeholder */
    opacity: 1; /* Override potential opacity issues */
}

.login-card :deep(.el-button--primary) {
  --el-button-bg-color: #5a8dee !important;
  --el-button-border-color: #5a8dee !important;
  --el-button-hover-bg-color: #7aa5f5 !important;
  --el-button-hover-border-color: #7aa5f5 !important;
  border-radius: 6px !important;
  padding: 18px 20px !important;
  font-weight: 500 !important;
  color: #ffffff !important;
}

.login-card :deep(.el-form-item__content .el-button) {
    width: 100% !important;
}

.min-h-screen {
  min-height: 100vh;
}
</style> 