<template>
  <div class="home-view bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen">
    <!-- 星空背景效果 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div 
        v-for="i in 50" 
        :key="i"
        class="absolute bg-white rounded-full opacity-70 animate-pulse"
        :style="{
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          width: Math.random() * 3 + 1 + 'px',
          height: Math.random() * 3 + 1 + 'px',
          animationDelay: Math.random() * 3 + 's'
        }"
      ></div>
    </div>

        <!-- 顶部导航 -->
    <div class="fixed top-0 right-0 z-50 p-6">
      <div class="flex items-center space-x-4">
        <!-- 用户信息显示 -->
        <div 
          v-if="isLoggedIn" 
          class="px-4 py-2 bg-gray-800/90 border border-gray-600 text-gray-200 rounded-lg backdrop-blur-sm"
        >
          <div class="flex items-center space-x-2">
            <span class="text-sm">👋</span>
            <span class="text-sm">您好，<span class="font-medium text-white">{{ 
              userStore.user?.full_name || 
              userStore.user?.username || 
              '用户' 
            }}</span></span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <button 
          v-if="isLoggedIn"
          @click="$router.push('/projects')"
          class="px-4 py-2 bg-gradient-to-r from-cyan-600/80 to-purple-600/80 hover:from-cyan-500/90 hover:to-purple-500/90 text-white border border-cyan-500/50 hover:border-cyan-400/70 rounded-lg backdrop-blur-sm transition-all duration-200 shadow-lg"
        >
          查看我的项目
        </button>
        <button 
          v-if="isLoggedIn"
          @click="handleLogout"
          class="px-3 py-2 bg-gray-700/80 hover:bg-gray-600/80 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 rounded-lg backdrop-blur-sm transition-all duration-200"
        >
          退出
        </button>
        <button 
          v-if="!isLoggedIn"
          @click="$router.push('/login')"
          class="px-4 py-2 bg-blue-600/80 hover:bg-blue-500/80 text-white border border-blue-500 hover:border-blue-400 rounded-lg backdrop-blur-sm transition-all duration-200"
        >
          登录
        </button>
        <button 
          v-if="!isLoggedIn"
          @click="$router.push('/register')"
          class="px-4 py-2 bg-purple-600/80 hover:bg-purple-500/80 text-white border border-purple-500 hover:border-purple-400 rounded-lg backdrop-blur-sm transition-all duration-200"
        >
          注册
        </button>
      </div>
    </div>

    <div class="relative z-10 container mx-auto px-6 py-8">
      <div class="flex flex-col items-center space-y-12 pt-6">
        
        <!-- Logo区域 -->
        <div class="text-center animate-fade-in">
        <div class="logo-container mb-6 animate-float">
          <img 
            src="/trex-logo.svg" 
            alt="Trex Logo" 
            class="w-72 h-24 mx-auto drop-shadow-2xl hover:scale-110 transition-all duration-500 filter hover:brightness-110"
          />
        </div>
        
        <h1 class="text-4xl font-bold text-white mb-3 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
          欢迎来到 Trex
        </h1>
        
        <p class="text-lg text-gray-300 mb-2">
          智能Agent平台 - 让AI为您工作
        </p>
        
        <p class="text-base text-gray-400 mx-auto">
          为您构建应用、游戏提供智能化解决方案
        </p>
      </div>

        <!-- 主内容区域 - 已登录时显示需求管理，未登录时显示快速创建 -->
        <div class="w-full max-w-6xl mx-auto">
          <!-- 未登录用户 - 快速创建区域 -->
          <div v-if="!isLoggedIn" class="quick-create-section">
        <div class="bg-gradient-to-br from-slate-800/70 via-slate-700/60 to-slate-800/70 border border-slate-500/40 backdrop-blur-xl shadow-2xl rounded-3xl p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-white mb-3 bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent">
              🤖 告诉我您的想法
            </h2>
            <p class="text-slate-300 text-base leading-relaxed">
              描述您想要创建的项目，我们的AI助手将为您提供最佳方案
            </p>
          </div>

          <div class="space-y-6">
            <!-- 简单描述 -->
            <div class="w-full">
              <h3 class="text-lg font-semibold text-cyan-200 mb-4">💭 简单描述</h3>
              <textarea
                    v-model="quickRequirement"
                rows="5"
                placeholder="请简单描述您想要的项目，比如：一个任务管理应用、一个小游戏、一个聊天机器人等..."
                class="w-full p-4 rounded-xl bg-gradient-to-br from-slate-900/90 to-slate-800/90 border border-slate-600/40 text-white placeholder-slate-400 shadow-lg backdrop-blur-sm focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/30 focus:outline-none transition-all duration-300 hover:border-slate-500 resize-none box-border font-medium"
              />
            </div>

            <!-- 开始创建按钮 -->
            <div class="text-center pb-2">
              <button
                    :disabled="!quickRequirement.trim()"
                    @click="handleQuickStart"
                class="px-8 py-3.5 text-lg font-semibold rounded-xl bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 hover:from-cyan-400 hover:via-blue-400 hover:to-purple-400 disabled:from-slate-600 disabled:via-slate-700 disabled:to-slate-800 disabled:cursor-not-allowed text-white border border-cyan-400/30 hover:border-cyan-300/50 disabled:border-slate-600 shadow-xl hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 disabled:scale-100 disabled:translate-y-0 disabled:shadow-lg backdrop-blur-sm"
              >
                <span class="mr-3">🚀</span>
                开始创建我的项目
              </button>
            </div>
          </div>
        </div>
      </div>

          <!-- 已登录用户 - 完整需求管理界面 -->
          <div v-if="isLoggedIn" class="requirement-management-section space-y-12">
            
            <!-- 快速创建需求区域 -->
            <div class="quick-requirement-section">
              <div class="bg-gradient-to-br from-slate-800/70 via-slate-700/60 to-slate-800/70 border border-slate-500/40 backdrop-blur-xl shadow-2xl rounded-3xl p-8">
                <div class="text-center mb-8">
                  <h2 class="text-2xl font-bold text-white mb-3 bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent">
                    ✨ 创建新需求
                  </h2>
                  <p class="text-slate-300 text-base leading-relaxed">
                    描述您想要创建的项目，让AI帮助您细化和完善想法
                  </p>
                </div>

                <div class="space-y-6">
                  <!-- AI模型选择器 -->
                  <div class="w-full">
                    <label class="block text-sm font-medium text-cyan-200 mb-3">🤖 AI 模型</label>
                    <AIModelSelector
                      v-model="selectedModel"
                      class="[&>select]:w-full [&>select]:p-4 [&>select]:rounded-xl [&>select]:bg-gradient-to-r [&>select]:from-slate-900/90 [&>select]:to-slate-800/90 [&>select]:border [&>select]:border-slate-600/40 [&>select]:text-white [&>select]:shadow-lg [&>select]:backdrop-blur-sm [&>select]:focus:border-cyan-400 [&>select]:focus:ring-2 [&>select]:focus:ring-cyan-400/30 [&>select]:focus:outline-none [&>select]:transition-all [&>select]:duration-300 [&>select]:hover:border-slate-500 [&>select]:font-medium"
                    />
                  </div>

                  <!-- 需求描述输入框 -->
                  <div class="w-full">
                    <label class="block text-sm font-medium text-cyan-200 mb-3">📝 需求描述</label>
                    <textarea
                      v-model="newRequirementContent"
                      rows="6"
                      placeholder="请描述您的需求，比如：我想要一个任务管理应用、开发一个小游戏、创建聊天机器人等..."
                      class="w-full p-4 rounded-xl bg-gradient-to-br from-slate-900/90 to-slate-800/90 border border-slate-600/40 text-white placeholder-slate-400 shadow-lg backdrop-blur-sm focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/30 focus:outline-none transition-all duration-300 hover:border-slate-500 resize-none box-border font-medium"
                    />
                    
                    <!-- 功能按钮区域 -->
                    <div class="flex items-center space-x-3 mt-3">
                      <button
                        @click="showImageUploadDialog = true"
                        class="flex items-center space-x-2 px-3 py-2 text-sm bg-slate-700/60 hover:bg-slate-600/60 text-gray-300 hover:text-white rounded-lg border border-slate-600/40 hover:border-slate-500/60 transition-all duration-200"
                      >
                        <span>📷</span>
                        <span>添加图片</span>
                      </button>
                      
                      <button
                        @click="showWebsiteUrlDialog = true"
                        class="flex items-center space-x-2 px-3 py-2 text-sm bg-slate-700/60 hover:bg-slate-600/60 text-gray-300 hover:text-white rounded-lg border border-slate-600/40 hover:border-slate-500/60 transition-all duration-200"
                      >
                        <span>🌐</span>
                        <span>添加网站</span>
                      </button>
                      
                      <!-- 已添加的图片数量提示 -->
                      <div v-if="selectedImages.length > 0" class="flex items-center space-x-1 text-xs text-cyan-400">
                        <span>📷</span>
                        <span>{{ selectedImages.length }} 张图片</span>
                      </div>
                      
                      <!-- 已添加的网站提示 -->
                      <div v-if="websiteUrl && websiteUrl.trim()" class="flex items-center space-x-1 text-xs text-cyan-400">
                        <span>🌐</span>
                        <span>已添加网站</span>
                      </div>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="text-center pt-4">
                    <button
                      :disabled="!newRequirementContent.trim() || requirementStore.isCreating"
                      @click="handleCreateRequirement"
                      class="px-12 py-4 text-lg font-semibold rounded-xl bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 hover:from-cyan-400 hover:via-blue-400 hover:to-purple-400 disabled:from-slate-600 disabled:via-slate-700 disabled:to-slate-800 disabled:cursor-not-allowed text-white border border-cyan-400/30 hover:border-cyan-300/50 disabled:border-slate-600 shadow-xl hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 disabled:scale-100 disabled:translate-y-0 disabled:shadow-lg backdrop-blur-sm"
                    >
                      <span v-if="requirementStore.isCreating" class="flex items-center justify-center space-x-2">
                        <span class="animate-spin">⏳</span>
                        <span>创建中...</span>
                      </span>
                      <span v-else class="flex items-center justify-center space-x-2">
                        <span>🚀</span>
                        <span>创建需求</span>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 需求列表区域 -->
            <div class="requirements-list-section">
              <div class="bg-gradient-to-br from-slate-800/70 via-slate-700/60 to-slate-800/70 border border-slate-500/40 backdrop-blur-xl shadow-2xl rounded-3xl p-8">
                
                <!-- 列表头部 -->
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <h2 class="text-xl font-semibold text-white">📋 我的需求</h2>
                    <el-tag v-if="requirementStore.totalRequirements > 0" size="small" type="info">
                      {{ requirementStore.totalRequirements }} 个需求
                    </el-tag>
                  </div>
                  
                  <!-- 状态筛选 -->
                  <div class="flex items-center space-x-2">
                    <el-select 
                      v-model="statusFilter" 
                      placeholder="筛选状态" 
                      size="small"
                      style="width: 120px"
                      @change="handleStatusFilterChange"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="待处理" value="pending" />
                      <el-option label="处理中" value="processing" />
                      <el-option label="已完成" value="completed" />
                    </el-select>
                  </div>
                </div>

                <!-- 加载状态 -->
                <div v-if="requirementStore.isLoading" class="text-center py-8">
                  <div class="animate-spin w-8 h-8 border-2 border-[#4fc3f7] border-t-transparent rounded-full mx-auto mb-3"></div>
                  <p class="text-gray-400">加载需求列表...</p>
                </div>

                <!-- 空状态 -->
                <div v-else-if="!requirementStore.hasRequirements" class="empty-state text-center py-12">
                  <div class="text-[#666666] space-y-3">
                    <div class="text-6xl mb-4">📝</div>
                    <h4 class="text-lg font-medium text-[#cccccc]">还没有任何需求</h4>
                    <p class="text-sm max-w-md mx-auto">
                      点击上方"创建新需求"按钮，开始您的第一个项目需求规划
                    </p>
                    <button 
                      @click="showCreateRequirementDialog = true"
                      class="mt-4 px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white rounded-lg transition-all duration-200"
                    >
                      立即创建
                    </button>
                  </div>
                </div>

                <!-- 需求卡片列表 -->
                <div v-else class="requirements-grid grid gap-4">
                  <div 
                    v-for="requirement in requirementStore.requirements" 
                    :key="requirement.id"
                    class="requirement-card bg-gradient-to-br from-slate-700/60 to-slate-600/40 rounded-xl border border-slate-500/40 hover:border-cyan-400/60 hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.03] hover:-translate-y-1 backdrop-blur-sm"
                    @click="openRequirementDetail(requirement)"
                  >
                    <div class="p-6">
                      <!-- 卡片头部 -->
                      <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                          <h3 class="text-lg font-medium text-white mb-2 line-clamp-2">{{ requirement.title }}</h3>
                          <p class="text-gray-400 text-sm line-clamp-3">{{ requirement.content }}</p>
                        </div>
                        <div class="ml-4 flex flex-col items-end space-y-2">
                          <el-tag 
                            :type="getRequirementStatusType(requirement.status)"
                            size="small"
                          >
                            {{ getRequirementStatusText(requirement.status) }}
                          </el-tag>
                        </div>
                      </div>

                      <!-- 卡片统计 -->
                      <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                        <div class="flex items-center space-x-4">
                          <div class="flex items-center space-x-1">
                            <span>❓</span>
                            <span>{{ requirement.questions.length }} 问题</span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <span>✅</span>
                            <span>{{ requirement.answers.length }} 回答</span>
                          </div>
                        </div>
                        <div class="text-xs">
                          {{ formatDate(requirement.created_at) }}
                        </div>
                      </div>

                      <!-- 操作按钮 -->
                      <div class="flex items-center justify-between pt-4 border-t border-[#3e3e42]">
                        <div class="flex items-center space-x-2">
                          <button 
                            v-if="requirement.questions.length === 0"
                            @click.stop="generateQuestions(requirement.id)"
                            :disabled="requirementStore.isGeneratingQuestions || requirement.status === 'image_analyzing'"
                            class="px-3 py-1 bg-blue-600/80 hover:bg-blue-500/80 text-white text-xs rounded transition-all duration-200 disabled:opacity-50"
                            :title="requirement.status === 'image_analyzing' ? '请等待图片分析完成' : ''"
                          >
                            <span v-if="requirementStore.isGeneratingQuestions">生成中...</span>
                            <span v-else-if="requirement.status === 'image_analyzing'">⏳ 分析图片中</span>
                            <span v-else>🤖 生成问题</span>
                          </button>
                          
                          <button 
                            v-if="requirement.questions.length > 0 && requirement.answers.length < requirement.questions.length"
                            @click.stop="openRequirementDetail(requirement, 'answer')"
                            class="px-3 py-1 bg-orange-600/80 hover:bg-orange-500/80 text-white text-xs rounded transition-all duration-200"
                          >
                            ✏️ 回答问题
                          </button>
                          
                          <button 
                            v-if="requirement.answers.length > 0"
                            @click.stop="openRequirementDetail(requirement, 'view')"
                            class="px-3 py-1 bg-green-600/80 hover:bg-green-500/80 text-white text-xs rounded transition-all duration-200"
                          >
                            👁️ 查看详情
                          </button>
                          
                          <button 
                            v-if="requirement.development_plan && requirement.development_plan.trim().length > 0"
                            @click.stop="openWireframeViewer(requirement)"
                            class="px-3 py-1 bg-purple-600/80 hover:bg-purple-500/80 text-white text-xs rounded transition-all duration-200"
                          >
                            <span v-if="requirement.wireframe_html && requirement.wireframe_html.trim().length > 0">🖼️ 线框图</span>
                            <span v-else>🎨 生成线框图</span>
                          </button>
                        </div>
                        
                        <div class="flex items-center space-x-1">
                          <button 
                            @click.stop="editRequirement(requirement)"
                            class="px-2 py-1 bg-gray-600/80 hover:bg-gray-500/80 text-gray-300 hover:text-white text-xs rounded transition-all duration-200"
                          >
                            ✏️
                          </button>
                          <button 
                            @click.stop="confirmDeleteRequirement(requirement)"
                            class="px-2 py-1 bg-red-600/80 hover:bg-red-500/80 text-white text-xs rounded transition-all duration-200"
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 分页控制 -->
                <div v-if="requirementStore.hasRequirements && requirementStore.pagination.total_pages > 1" class="pagination-section mt-6 flex justify-center">
                  <el-pagination
                    v-model:current-page="currentPage"
                    :page-size="requirementStore.pagination.limit"
                    :total="requirementStore.pagination.total"
                    layout="prev, pager, next"
                    @current-change="handlePageChange"
                    small
                    background
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建需求对话框 -->
    <el-dialog
      v-model="showCreateRequirementDialog"
      title="创建新需求"
      width="600px"
      :close-on-click-modal="false"
      class="requirement-dialog"
    >
      <RequirementForm
        @submit="handleCreateRequirementFromDialog"
        @cancel="showCreateRequirementDialog = false"
        :loading="requirementStore.isCreating"
        :show-cancel="true"
      />
    </el-dialog>

    <!-- 编辑需求对话框 -->
    <el-dialog
      v-model="showEditRequirementDialog"
      title="编辑需求"
      width="600px"
      :close-on-click-modal="false"
      class="requirement-dialog"
    >
      <RequirementForm
        v-if="editingRequirement"
        :requirement="editingRequirement"
        @submit="handleUpdateRequirement"
        @cancel="showEditRequirementDialog = false"
        :loading="requirementStore.isUpdating"
        :show-cancel="true"
      />
    </el-dialog>

    <!-- 需求详情对话框 -->
    <el-dialog
      v-model="showRequirementDetailDialog"
      :title="selectedRequirement?.title"
      width="90%"
      :close-on-click-modal="false"
      class="requirement-detail-dialog"
      top="5vh"
    >
      <div v-if="selectedRequirement" class="requirement-detail-content space-y-6">
        
        <!-- 需求基本信息 -->
        <div class="requirement-info bg-[#2d2d30] rounded-lg p-4 border border-[#3e3e42]">
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-white mb-2">需求信息</h3>
              <p class="text-gray-300 leading-relaxed">{{ selectedRequirement.content }}</p>
            </div>
            <el-tag 
              :type="getRequirementStatusType(selectedRequirement.status)"
              size="small"
            >
              {{ getRequirementStatusText(selectedRequirement.status) }}
            </el-tag>
          </div>

          <!-- 参考图片展示 -->
          <div 
            v-if="selectedRequirement.reference_images && selectedRequirement.reference_images.length > 0" 
            class="reference-images mt-4"
          >
            <h4 class="text-sm font-medium text-gray-300 mb-3 flex items-center">
              <span class="mr-2">🖼️</span>
              参考图片 ({{ selectedRequirement.reference_images.length }})
            </h4>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              <div 
                v-for="(image, index) in selectedRequirement.reference_images"
                :key="image.id"
                class="reference-image-item group"
              >
                <div class="relative bg-gray-700 rounded-lg overflow-hidden aspect-square border border-gray-600">
                  <img 
                    :src="'/api/v1/uploads/' + image.file_path"
                    :alt="image.file_name"
                    class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                    @error="handleImageError"
                  />
                  <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <span class="text-white text-xs">{{ image.file_name }}</span>
                  </div>
                  
                  <!-- AI分析状态指示器 -->
                  <div class="absolute top-2 right-2">
                    <div 
                      v-if="image.analyzed_at"
                      class="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center"
                      title="AI已分析"
                    >
                      <span class="text-white text-xs">✓</span>
                    </div>
                    <div 
                      v-else
                      class="w-3 h-3 bg-orange-500 rounded-full animate-pulse"
                      title="AI分析中"
                    ></div>
                  </div>
                </div>
                
                <!-- 图片信息 -->
                <div class="mt-2 text-xs text-gray-400">
                  <div class="truncate">{{ image.file_name }}</div>
                  <div>{{ formatFileSize(image.file_size) }}</div>
                </div>
              </div>
            </div>

            <!-- AI分析结果展示 -->
            <div 
              v-if="requirementStore.currentRequirementAnalyzedImages.length > 0"
              class="mt-4 space-y-3"
            >
              <h4 class="text-sm font-medium text-gray-300 mb-2 flex items-center">
                <span class="mr-2">🤖</span>
                AI图片分析结果
              </h4>
              <div 
                v-for="(image, index) in requirementStore.currentRequirementAnalyzedImages"
                :key="image.id"
                class="analysis-result bg-gray-700/50 rounded-lg p-3 border border-gray-600"
              >
                <div class="flex items-start space-x-3">
                  <img 
                    :src="'/api/v1/uploads/' + image.file_path"
                    :alt="image.file_name"
                    class="w-16 h-16 object-cover rounded"
                  />
                  <div class="flex-1">
                    <div class="text-sm font-medium text-white mb-2">{{ image.file_name }}</div>
                    <div class="text-xs text-gray-300 space-y-1">
                      <div v-if="image.analysis.visual_elements.main_subjects">
                        <strong>主要元素：</strong>{{ image.analysis.visual_elements.main_subjects }}
                      </div>
                      <div v-if="image.analysis.emotional_tone.overall_mood">
                        <strong>整体氛围：</strong>{{ image.analysis.emotional_tone.overall_mood }}
                      </div>
                      <div v-if="image.analysis.technical_style.artistic_style">
                        <strong>风格特征：</strong>{{ image.analysis.technical_style.artistic_style }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <div class="text-sm text-gray-400 mt-3">
            创建时间：{{ formatDate(selectedRequirement.created_at) }} | 
            更新时间：{{ formatDate(selectedRequirement.updated_at) }}
          </div>
        </div>

        <!-- 问题列表 -->
        <div class="questions-section">
          <QuestionList
            :questions="selectedRequirement.questions"
            :answers="selectedRequirement.answers"
            :is-generating="requirementStore.isGeneratingQuestions"
            :requirement-id="selectedRequirement.id"
            @generate-questions="generateQuestions(selectedRequirement.id)"
            @regenerate-questions="regenerateQuestions(selectedRequirement.id)"
          />
        </div>

        <!-- 答案表单 -->
        <div v-if="selectedRequirement.questions.length > 0 && (detailMode === 'answer' || detailMode === 'view')" class="answers-section">
          <AnswerForm
            :questions="selectedRequirement.questions"
            :answers="selectedRequirement.answers"
            :is-submitting="requirementStore.isSubmittingAnswers"
            :requirement-id="selectedRequirement.id"
            @submit-answers="submitAnswers"
            @save-draft="saveDraft"
          />
        </div>

        <!-- 需求开发计划 -->
        <div 
          v-if="selectedRequirement.answers.length > 0 || selectedRequirement.status === 'plan_generating' || selectedRequirement.development_plan"
          class="development-plan-section"
        >
          <div class="bg-[#2d2d30] rounded-lg p-4 border border-[#3e3e42]">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-white flex items-center">
                <span class="mr-2">📋</span>
                需求开发计划
              </h3>
              <div v-if="selectedRequirement.development_plan && selectedRequirement.development_plan.trim() && !isEditingPlan">
                <button
                  @click="handleEditPlan"
                  class="px-3 py-1 bg-blue-600/80 hover:bg-blue-500/80 text-white text-xs rounded transition-all duration-200"
                >
                  ✏️ 编辑
                </button>
              </div>
            </div>
            
            <!-- 计划生成中状态 -->
            <div 
              v-if="selectedRequirement.status === 'plan_generating'"
              class="text-center py-8"
            >
              <div class="inline-flex items-center space-x-3 text-blue-400">
                <div class="animate-spin text-2xl">⟳</div>
                <span class="text-lg">生成中...</span>
              </div>
              <p class="text-gray-400 text-sm mt-2">
                AI正在根据您的需求和答案生成详细的开发计划，请稍等...
              </p>
            </div>
            
            <!-- 已生成的开发计划 -->
            <div 
              v-else-if="selectedRequirement.development_plan && selectedRequirement.development_plan.trim()"
              class="development-plan-content"
            >
              <!-- 编辑器 -->
              <div v-if="isEditingPlan" class="plan-editor-container space-y-4">
                <div class="border border-gray-600 rounded-lg overflow-hidden">
                  <MonacoEditor
                    v-model:value="editedPlanContent"
                    language="markdown"
                    theme="vs-dark"
                    :options="{
                      automaticLayout: true,
                      wordWrap: 'on',
                      minimap: { enabled: false }
                    }"
                    style="height: 500px;"
                  />
                </div>
                <div class="flex justify-end space-x-2">
                  <button @click="handleCancelEditPlan" class="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white text-sm rounded transition-all duration-200">
                    取消
                  </button>
                  <button @click="handleSavePlan" :disabled="requirementStore.isUpdating" class="px-4 py-2 bg-green-600 hover:bg-green-500 text-white text-sm rounded transition-all duration-200 disabled:opacity-50">
                    <span v-if="requirementStore.isUpdating">保存中...</span>
                    <span v-else>保存</span>
                  </button>
                </div>
              </div>
              <!-- 只读视图 -->
              <div
                v-else
                v-html="renderedContent"
                class="prose-styles-placeholder"
              ></div>
            </div>
            
            <!-- 等待生成状态（已回答问题但计划还未开始生成）-->
            <div 
              v-else-if="selectedRequirement.answers.length > 0 && selectedRequirement.status === 'completed' && !selectedRequirement.development_plan"
              class="text-center py-6"
            >
              <div class="text-gray-400">
                <div class="text-4xl mb-2">📝</div>
                <p class="text-base">开发计划准备中</p>
                <p class="text-sm mt-1">系统正在处理您的回答...</p>
              </div>
            </div>
            
            <!-- 暂无计划状态 -->
            <div 
              v-else
              class="text-center py-6 text-gray-500"
            >
              <div class="text-3xl mb-2">⏳</div>
              <p class="text-sm">请先完成问题回答以生成开发计划</p>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 线框图查看器对话框 -->
    <el-dialog
      v-model="showWireframeDialog"
      :title="`${selectedWireframeRequirement?.title} - 线框图`"
      width="95%"
      :close-on-click-modal="false"
      class="wireframe-dialog"
      top="2vh"
    >
      <div v-if="selectedWireframeRequirement" class="wireframe-content">
        
        <!-- 操作栏 -->
        <div class="flex items-center justify-between mb-4 p-3 bg-[#2d2d30] rounded-lg border border-[#3e3e42]">
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-400">
              基于开发计划生成的交互式线框图
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button 
              v-if="!selectedWireframeRequirement.wireframe_html || !selectedWireframeRequirement.wireframe_html.trim()"
              @click="generateWireframe(selectedWireframeRequirement.id)"
              :disabled="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'"
              class="px-4 py-2 bg-purple-600/80 hover:bg-purple-500/80 text-white text-sm rounded transition-all duration-200 disabled:opacity-50"
            >
              <span v-if="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'">🎨 生成中...</span>
              <span v-else>🎨 生成线框图</span>
            </button>
            <button 
              v-if="selectedWireframeRequirement.wireframe_html && selectedWireframeRequirement.wireframe_html.trim()"
              @click="regenerateWireframe(selectedWireframeRequirement.id)"
              :disabled="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'"
              class="px-4 py-2 bg-blue-600/80 hover:bg-blue-500/80 text-white text-sm rounded transition-all duration-200 disabled:opacity-50"
            >
              <span v-if="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'">🔄 重新生成中...</span>
              <span v-else>🔄 重新生成</span>
            </button>
          </div>
        </div>

        <!-- 线框图显示区域 -->
        <div class="wireframe-display bg-white rounded-lg">
          
          <!-- 生成中状态 -->
          <div 
            v-if="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'"
            class="wireframe-loading text-center py-16"
          >
            <div class="inline-flex items-center space-x-3 text-purple-600">
              <div class="animate-spin text-4xl">🎨</div>
              <div>
                <div class="text-xl font-medium">正在生成5套线框图方案...</div>
                <div class="text-sm text-gray-600 mt-1">
                  AI正在根据开发计划创建5种不同风格的交互式线框图，这可能需要2-3分钟
                </div>
              </div>
            </div>
          </div>

          <!-- 线框图错误状态 -->
          <div 
            v-else-if="wireframeError"
            class="wireframe-error text-center py-16"
          >
            <div class="text-red-500">
              <div class="text-6xl mb-4">⚠️</div>
              <h4 class="text-xl font-medium text-red-600 mb-2">线框图显示错误</h4>
              <p class="text-red-500 mb-4 max-w-md mx-auto">
                {{ wireframeErrorMessage || '线框图在渲染时遇到了问题，这可能是由于内容包含不安全的脚本或导航操作。' }}
              </p>
              <div class="space-x-3">
                <button 
                  @click="handleWireframeReload"
                  class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200"
                >
                  🔄 重新加载
                </button>
                <button 
                  @click="regenerateWireframe(selectedWireframeRequirement.id)"
                  :disabled="requirementStore.isGeneratingWireframe"
                  class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 disabled:opacity-50"
                >
                  🎨 重新生成
                </button>
              </div>
            </div>
          </div>
          
          <!-- 多个线框图展示 -->
          <div 
            v-else-if="hasWireframes"
            class="wireframes-container"
          >
            <!-- 线框图选择器 -->
            <div class="wireframe-selector bg-gray-50 p-4 border-b">
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="(wireframe, index) in availableWireframes"
                  :key="wireframe.id"
                  @click="selectedWireframeIndex = index"
                  :class="[
                    'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                    selectedWireframeIndex === index 
                      ? 'bg-purple-600 text-white shadow-md' 
                      : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                  ]"
                >
                  <div class="text-left">
                    <div class="font-medium">{{ wireframe.title }}</div>
                    <div class="text-xs opacity-75 mt-1">{{ wireframe.description }}</div>
                  </div>
                </button>
              </div>
            </div>

            <!-- 当前选中的线框图展示 -->
            <div class="wireframe-viewer relative">
              <!-- 错误遮罩层 -->
              <div 
                v-if="wireframeError"
                class="absolute inset-0 bg-red-50 flex items-center justify-center z-10"
              >
                <div class="text-center">
                  <div class="text-4xl mb-2">⚠️</div>
                  <p class="text-red-600 mb-4">线框图渲染出错</p>
                  <button 
                    @click="handleWireframeReload"
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    重新加载
                  </button>
                </div>
              </div>

              <iframe
                v-if="currentWireframe"
                :key="`${wireframeIframeKey}-${selectedWireframeIndex}`"
                :srcdoc="currentWireframe.html"
                class="wireframe-iframe w-full border-0"
                style="height: 60vh; min-height: 400px;"
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups-to-escape-sandbox allow-modal"
                @load="handleWireframeLoad"
                @error="handleWireframeError"
                ref="wireframeIframe"
                title="线框图预览"
                loading="lazy"
              ></iframe>

              <!-- 操作按钮区域 -->
              <div v-if="currentWireframe" class="wireframe-action-bar bg-gray-800 border-t border-gray-600 p-4">
                <div class="flex justify-center">
                  <button
                    @click="selectWireframeForProject(currentWireframe)"
                    class="px-6 py-3 bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                  >
                    <span class="text-base">✨</span>
                    <span>就这个了</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 向后兼容：单个线框图展示（如果没有多个线框图但有单个线框图） -->
          <div 
            v-else-if="selectedWireframeRequirement.wireframe_html && selectedWireframeRequirement.wireframe_html.trim()"
            class="wireframe-iframe-container relative"
          >
            <!-- 错误遮罩层 -->
            <div 
              v-if="wireframeError"
              class="absolute inset-0 bg-red-50 flex items-center justify-center z-10"
            >
              <div class="text-center">
                <div class="text-4xl mb-2">⚠️</div>
                <p class="text-red-600 mb-4">线框图渲染出错</p>
                <button 
                  @click="handleWireframeReload"
                  class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  重新加载
                </button>
              </div>
            </div>

            <iframe
              :key="wireframeIframeKey"
              :srcdoc="selectedWireframeRequirement.wireframe_html"
              class="wireframe-iframe w-full border-0"
              style="height: 70vh; min-height: 500px;"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups-to-escape-sandbox allow-modal"
              @load="handleWireframeLoad"
              @error="handleWireframeError"
              ref="wireframeIframe"
              title="线框图预览"
              loading="lazy"
            ></iframe>
          </div>
          
          <!-- 暂无线框图状态 -->
          <div 
            v-else
            class="wireframe-empty text-center py-16"
          >
            <div class="text-gray-400">
              <div class="text-6xl mb-4">🎨</div>
              <h4 class="text-xl font-medium text-gray-600 mb-2">暂无线框图</h4>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">
                点击"生成线框图"按钮，AI将根据您的开发计划创建5种不同风格的交互式UI线框图
              </p>
              <button 
                @click="generateWireframe(selectedWireframeRequirement.id)"
                :disabled="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'"
                class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 disabled:opacity-50"
              >
                <span v-if="requirementStore.isGeneratingWireframe || (selectedWireframeRequirement.status as any) === 'wireframe_generating'">🎨 生成中...</span>
                <span v-else>🎨 生成5套线框图方案</span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 说明文字 -->
        <div class="mt-4 text-xs text-gray-500 text-center">
          线框图为AI生成的交互式原型，展示了项目的基本界面和功能布局
        </div>
      </div>
    </el-dialog>

    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="showImageUploadDialog"
      title="添加参考图片"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="space-y-4">
        <p class="text-gray-400 text-sm">上传参考图片来帮助AI更好地理解您的需求</p>
        <ImageUploadArea
          v-model="selectedImages"
          :max-files="5"
          accept="image/*"
        />
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <button
            @click="showImageUploadDialog = false"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-all duration-200"
          >
            取消
          </button>
          <button
            @click="handleConfirmImages"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-all duration-200"
          >
            确认添加
          </button>
        </div>
      </template>
    </el-dialog>

    <!-- 网站URL输入对话框 -->
    <el-dialog
      v-model="showWebsiteUrlDialog"
      title="添加参考网站"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-4">
        <p class="text-gray-400 text-sm">添加参考网站URL来帮助AI理解您想要的功能或设计风格</p>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">网站URL</label>
          <input
            v-model="tempWebsiteUrl"
            type="url"
            placeholder="https://example.com"
            class="w-full p-3 rounded-lg bg-slate-700 border border-slate-600 text-white placeholder-slate-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/30 focus:outline-none transition-all duration-200"
          />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <button
            @click="handleCancelWebsiteUrl"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-all duration-200"
          >
            取消
          </button>
          <button
            @click="handleConfirmWebsiteUrl"
            :disabled="!tempWebsiteUrl.trim()"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-all duration-200 disabled:opacity-50"
          >
            确认添加
          </button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目创建对话框 -->
    <el-dialog
      v-model="showCreateProjectDialog"
      title="从线框图创建项目"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWireframeForProject" class="space-y-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center space-x-2 text-blue-600 mb-2">
            <span>✨</span>
            <span class="font-medium">选中的线框图</span>
          </div>
          <h4 class="font-medium text-gray-900">{{ selectedWireframeForProject.title }}</h4>
          <p class="text-sm text-gray-600 mt-1">{{ selectedWireframeForProject.description }}</p>
        </div>
        
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">项目ID</label>
            <input
              v-model="newProjectId"
              type="text"
              placeholder="例如: my-awesome-project"
              class="w-full p-3 rounded-lg border border-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/30 focus:outline-none transition-all duration-200"
            />
            <p class="text-xs text-gray-500 mt-1">项目ID将用作文件夹名，只能包含字母、数字、连字符</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
            <input
              v-model="newProjectName"
              type="text"
              placeholder="例如: 我的超棒项目"
              class="w-full p-3 rounded-lg border border-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/30 focus:outline-none transition-all duration-200"
            />
          </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h5 class="font-medium text-gray-900 mb-2">创建后将包含：</h5>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• 📋 .development_plan.md - 详细的开发计划</li>
            <li>• 🎨 index.html - 交互式线框图原型</li>
            <li>• 🐳 Docker 运行时环境</li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <button
            @click="showCreateProjectDialog = false"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-all duration-200"
          >
            取消
          </button>
          <button
            @click="handleCreateProjectFromRequirement"
            :disabled="!newProjectId.trim() || !newProjectName.trim() || isCreatingProject"
            class="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded transition-all duration-200 disabled:opacity-50 flex items-center space-x-2"
          >
            <span v-if="isCreatingProject">创建中...</span>
            <span v-else>✨ 创建项目</span>
          </button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, watchEffect } from 'vue';
import { useRouter } from 'vue-router';
import { 
  ElMessage, 
  ElDialog, 
  ElTag, 
  ElSelect, 
  ElOption, 
  ElPagination,
  ElMessageBox 
} from 'element-plus';
import { useUserStore } from '@/store/user';
import { useRequirementStore } from '@/store/requirement';
import RequirementForm from '@/components/RequirementForm.vue';
import QuestionList from '@/components/QuestionList.vue';
import AnswerForm from '@/components/AnswerForm.vue';
import ImageUploadArea from '@/components/ImageUploadArea.vue';
import AIModelSelector from '@/components/AIModelSelector.vue';
import MonacoEditor from '@guolao/vue-monaco-editor';
import type { 
  Requirement, 
  CreateRequirementRequest, 
  CreateRequirementWithImagesRequest,
  UpdateRequirementRequest,
  AnswerSubmission,
  RequirementStatus
} from '@/api/types';
import { useMarkdownRenderer } from '@/utils/useMarkdownRenderer';

const router = useRouter();
const userStore = useUserStore();
const requirementStore = useRequirementStore();

// 响应式数据
const quickRequirement = ref('');
const newRequirementContent = ref('');
const selectedModel = ref('gemini-2.5-pro'); // AI模型选择，默认为推荐模型
const selectedImages = ref([]);
const websiteUrl = ref('');
const tempWebsiteUrl = ref('');
// 移除了selectedImages和websiteUrl的引用
const statusFilter = ref('');
const currentPage = ref(1);

// 项目创建相关
const newProjectId = ref('');
const newProjectName = ref('');
const isCreatingProject = ref(false);

// 对话框状态
const showCreateRequirementDialog = ref(false);
const showEditRequirementDialog = ref(false);
const showRequirementDetailDialog = ref(false);
const showWireframeDialog = ref(false);
const showImageUploadDialog = ref(false);
const showWebsiteUrlDialog = ref(false);
const showCreateProjectDialog = ref(false);

// 选中的需求和模式
const selectedRequirement = ref<Requirement | null>(null);
const editingRequirement = ref<Requirement | null>(null);
const selectedWireframeRequirement = ref<Requirement | null>(null);
const selectedWireframeForProject = ref<any>(null);
const detailMode = ref<'view' | 'answer'>('view');

// 开发计划编辑相关
const isEditingPlan = ref(false);
const editedPlanContent = ref('');

// 开发计划轮询相关
const planPollingTimer = ref<NodeJS.Timeout | null>(null);

// 移除了图片和网站分析相关的定时器变量

// 需求列表轮询相关
const requirementListPollingTimer = ref<NodeJS.Timeout | null>(null);
const isPollingActive = ref(false);

// 线框图错误处理相关
const wireframeError = ref(false);
const wireframeErrorMessage = ref('');
const wireframeIframeKey = ref(0); // 用于强制重新渲染 iframe
const wireframeIframe = ref<HTMLIFrameElement | null>(null);

// 多线框图选择相关
const selectedWireframeIndex = ref(0);

// 计算属性
const isLoggedIn = computed(() => userStore.isAuthenticated);

// 多线框图相关计算属性
const hasWireframes = computed(() => {
  return selectedWireframeRequirement.value?.wireframes && 
         selectedWireframeRequirement.value.wireframes.length > 0;
});

const availableWireframes = computed(() => {
  return selectedWireframeRequirement.value?.wireframes || [];
});

const currentWireframe = computed(() => {
  if (!hasWireframes.value || selectedWireframeIndex.value < 0) return null;
  return availableWireframes.value[selectedWireframeIndex.value] || null;
});

// 方法
const handleQuickStart = () => {
  if (!quickRequirement.value.trim()) {
    ElMessage.warning('请先描述您的需求');
    return;
  }
  
  // 引导用户注册登录
  ElMessage.info('请先登录或注册以使用完整功能');
  router.push('/login');
};

const resetForm = () => {
  newRequirementContent.value = '';
  selectedImages.value = [];
  selectedModel.value = 'gemini-2.5-pro'; // Or your default model
};

const refreshRequirements = async () => {
  try {
    await requirementStore.fetchRequirements(currentPage.value, 10, statusFilter.value);
  } catch (error) {
    console.error('Failed to refresh requirements:', error);
    ElMessage.error('刷新需求列表失败');
  }
};

const handleStatusFilterChange = () => {
  currentPage.value = 1;
  refreshRequirements();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  refreshRequirements();
};

const handleCreateRequirement = async () => {
  if (!newRequirementContent.value.trim()) {
    ElMessage.warning('请先描述您的需求');
    return;
  }

  try {
    // 生成标题（取前20个字符作为标题）
    const title = newRequirementContent.value.trim().substring(0, 20) + 
                  (newRequirementContent.value.trim().length > 20 ? '...' : '');
    
    // 创建需求数据
    const requirementData = {
      title,
      content: newRequirementContent.value.trim(),
      model_name: selectedModel.value,
      ...(websiteUrl.value && { website_url: websiteUrl.value })
    };

    // 使用统一的FormData格式
    const formData = new FormData();
    formData.append('title', title);
    formData.append('content', newRequirementContent.value.trim());
    formData.append('model_name', selectedModel.value);
    
    if (websiteUrl.value) {
      formData.append('website_url', websiteUrl.value);
    }
    
    // 添加图片文件
    selectedImages.value.forEach((file: File) => {
      formData.append('images', file);
    });
    
    const createdRequirement = await requirementStore.createRequirementUnified(formData);
    
    ElMessage.success('需求创建成功');
    
    // 清空表单
    newRequirementContent.value = '';
    selectedModel.value = 'gemini-2.5-pro';
    selectedImages.value = [];
    websiteUrl.value = '';
    await refreshRequirements();
  } catch (error: any) {
    console.error('Failed to create requirement:', error);
    
    // 更具体的错误处理
    if (error?.response?.status === 400) {
      ElMessage.error('请求参数有误，请检查填写内容');
    } else if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('EOF')) {
      ElMessage.error('网络连接异常，请检查网络后重试');
    } else {
      ElMessage.error('创建需求失败，请稍后重试');
    }
  }
};

const handleCreateRequirementFromDialog = async (data: CreateRequirementRequest | CreateRequirementWithImagesRequest | UpdateRequirementRequest | FormData) => {
  try {
    let createdRequirement: Requirement | null = null;
    
    // 检查是否是FormData格式（统一API）
    if (data instanceof FormData) {
      const response = await requirementStore.createRequirementUnified(data);
      createdRequirement = response || null;
      
      // 检查是否有图片
      const hasImages = data.getAll('images').length > 0;
      const message = hasImages ? '需求创建成功，AI正在分析图片...' : '需求创建成功';
      ElMessage.success(message);
    } else if ('images' in data) {
      // 兼容旧的带图片创建请求
      const response = await requirementStore.createRequirementWithImages(data as CreateRequirementWithImagesRequest);
      createdRequirement = response || null;
      ElMessage.success('需求创建成功，AI正在分析图片...');
    } else if ('title' in data && typeof data.title === 'string') {
      // 兼容旧的创建请求
      const response = await requirementStore.createRequirement(data as CreateRequirementRequest);
      createdRequirement = response || null;
      ElMessage.success('需求创建成功');
    }
    
    showCreateRequirementDialog.value = false;
    await refreshRequirements();
    
    // 移除了图片分析轮询相关逻辑
  } catch (error) {
    console.error('Failed to create requirement:', error);
    ElMessage.error('创建需求失败');
  }
};

const handleUpdateRequirement = async (data: UpdateRequirementRequest) => {
  if (!editingRequirement.value) return;
  
  try {
    await requirementStore.updateRequirement(editingRequirement.value.id, data);
    showEditRequirementDialog.value = false;
    editingRequirement.value = null;
    ElMessage.success('需求更新成功');
    await refreshRequirements();
  } catch (error) {
    console.error('Failed to update requirement:', error);
    ElMessage.error('更新需求失败');
  }
};

const handleEditPlan = () => {
  if (selectedRequirement.value) {
    editedPlanContent.value = selectedRequirement.value.development_plan;
    isEditingPlan.value = true;
  }
};

const handleCancelEditPlan = () => {
  isEditingPlan.value = false;
  editedPlanContent.value = '';
};

const handleSavePlan = async () => {
  if (!selectedRequirement.value) return;
  
  const success = await requirementStore.updateRequirement(selectedRequirement.value.id, {
    development_plan: editedPlanContent.value
  });
  
  if (success) {
    ElMessage.success('开发计划更新成功');
    isEditingPlan.value = false;
    // Manually update the local data to avoid a full refresh
    if (selectedRequirement.value) {
      selectedRequirement.value.development_plan = editedPlanContent.value;
    }
  } else {
    ElMessage.error('开发计划更新失败');
  }
};

const generateQuestions = async (requirementId: string) => {
  try {
    const success = await requirementStore.generateQuestions(requirementId);
    if (success) {
      ElMessage.success('问题生成成功');
      
      // 刷新列表
      await refreshRequirements();
      
      // 如果详情对话框已打开，重新获取最新的需求数据
      if (selectedRequirement.value?.id === requirementId) {
        const updatedRequirement = await requirementStore.fetchRequirement(requirementId);
        if (updatedRequirement) {
          selectedRequirement.value = updatedRequirement;
        }
      }
    }
  } catch (error) {
    console.error('Failed to generate questions:', error);
    ElMessage.error('生成问题失败');
  }
};

const regenerateQuestions = async (requirementId: string) => {
  try {
    await ElMessageBox.confirm(
      '重新生成将替换现有问题，是否继续？',
      '确认重新生成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await generateQuestions(requirementId);
  } catch (error) {
    // 用户取消操作
  }
};

const submitAnswers = async (answers: AnswerSubmission[]) => {
  if (!selectedRequirement.value) return;
  
  try {
    const success = await requirementStore.submitAnswers(selectedRequirement.value.id, answers);
    if (success) {
      ElMessage.success('答案提交成功，正在生成开发计划...');
      
      // 重新获取最新的需求数据
      const updatedRequirement = await requirementStore.fetchRequirement(selectedRequirement.value.id);
      if (updatedRequirement) {
        selectedRequirement.value = updatedRequirement;
        
        // 如果状态是plan_generating，开始轮询检查开发计划生成状态
        if (updatedRequirement.status === 'plan_generating') {
          startDevelopmentPlanPolling(updatedRequirement.id);
        }
      }
      
      // 刷新列表
      await refreshRequirements();
    }
  } catch (error) {
    console.error('Failed to submit answers:', error);
    ElMessage.error('提交答案失败');
  }
};

const saveDraft = async (answers: AnswerSubmission[]) => {
  // 这里可以实现草稿保存逻辑
  ElMessage.success('草稿已保存');
};

const openRequirementDetail = (requirement: Requirement, mode: 'view' | 'answer' = 'view') => {
  selectedRequirement.value = requirement;
  detailMode.value = mode;
  showRequirementDetailDialog.value = true;
  
  // 根据需求状态开始相应的轮询
  if (requirement.status === 'plan_generating') {
    startDevelopmentPlanPolling(requirement.id);
  }
  // 移除了图片和网站分析轮询相关逻辑
};

const editRequirement = (requirement: Requirement) => {
  editingRequirement.value = requirement;
  showEditRequirementDialog.value = true;
};

const confirmDeleteRequirement = async (requirement: Requirement) => {
  try {
    const hasImages = requirement.reference_images && requirement.reference_images.length > 0;
    const confirmMessage = hasImages 
      ? `确定要删除需求"${requirement.title}"吗？\n\n此操作将同时删除 ${requirement.reference_images.length} 个关联的图片文件，此操作不可逆。`
      : `确定要删除需求"${requirement.title}"吗？此操作不可逆。`;
    
    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: hasImages, // 允许换行符
      }
    );
    
    await requirementStore.deleteRequirement(requirement.id);
    
    const successMessage = hasImages 
      ? `需求及其 ${requirement.reference_images.length} 个关联图片删除成功`
      : '需求删除成功';
    ElMessage.success(successMessage);
    
    await refreshRequirements();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete requirement:', error);
      ElMessage.error('删除需求失败');
    }
  }
};

const getRequirementStatusType = (status: RequirementStatus): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<RequirementStatus, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'info',
    image_analyzing: 'warning',
    processing: 'warning',
    plan_generating: 'primary',
    wireframe_generating: 'primary',
    completed: 'success'
  };
  return statusMap[status] || 'info';
};

const getRequirementStatusText = (status: RequirementStatus) => {
  const statusMap: Record<RequirementStatus, string> = {
    pending: '待处理',
    image_analyzing: '图片分析中',
    processing: '处理中',
    plan_generating: '生成计划中',
    wireframe_generating: '生成线框中',
    completed: '已完成'
  };
  return statusMap[status] || '未知';
};

// 图片上传相关方法
const handleConfirmImages = () => {
  showImageUploadDialog.value = false;
  ElMessage.success(`已添加 ${selectedImages.value.length} 张图片`);
};

// 网站URL相关方法
const handleConfirmWebsiteUrl = () => {
  if (tempWebsiteUrl.value.trim()) {
    websiteUrl.value = tempWebsiteUrl.value.trim();
    showWebsiteUrlDialog.value = false;
    tempWebsiteUrl.value = '';
    ElMessage.success('网站URL已添加');
  }
};

const handleCancelWebsiteUrl = () => {
  tempWebsiteUrl.value = '';
  showWebsiteUrlDialog.value = false;
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjNzQ3NDc0Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Zu+54mH5oCg6L275aSx6LSlPC90ZXh0Pgo8L3N2Zz4=';
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const { formatMarkdown, renderedContent } = useMarkdownRenderer();

watchEffect(() => {
  if (selectedRequirement.value?.development_plan) {
    formatMarkdown(selectedRequirement.value.development_plan);
  }
});

// 开始轮询开发计划生成状态
const startDevelopmentPlanPolling = (requirementId: string) => {
  // 清除之前的定时器
  stopDevelopmentPlanPolling();
  
  
  
  planPollingTimer.value = setInterval(async () => {
    try {
      // 使用静默获取，避免触发loading状态导致UI闪烁
      const updatedRequirement = await silentFetchSingleRequirement(requirementId);
      
      if (updatedRequirement) {
        // 如果当前显示的需求是正在轮询的需求，且数据有变化，才更新它
        if (selectedRequirement.value?.id === requirementId) {
          if (!requirementsEqual(selectedRequirement.value, updatedRequirement)) {
            selectedRequirement.value = updatedRequirement;
  
          }
        }
        
        // 静默更新需求列表中的数据
        requirementStore.updateRequirementInList(updatedRequirement);
        
        // 检查状态变化
        if (updatedRequirement.status === 'completed') {
          ElMessage.success('开发计划生成完成！');
          stopDevelopmentPlanPolling();
        } else if (updatedRequirement.status !== 'plan_generating') {
          // 如果状态不是plan_generating也不是completed，停止轮询
          stopDevelopmentPlanPolling();
        }
      }
    } catch (error) {
      console.error('[HomeView] Error during development plan polling:', error);
      // 出错时也停止轮询
      stopDevelopmentPlanPolling();
    }
  }, 3000); // 每3秒检查一次
};

// 停止轮询
const stopDevelopmentPlanPolling = () => {
  if (planPollingTimer.value) {
    clearInterval(planPollingTimer.value);
    planPollingTimer.value = null;
  
  }
};

// 移除了图片和网站分析相关的轮询函数

// 工具函数：比较两个需求是否相等（用于避免不必要的更新）
const requirementsEqual = (req1: Requirement, req2: Requirement): boolean => {
  return (
    req1.id === req2.id &&
    req1.status === req2.status &&
    req1.updated_at === req2.updated_at &&
    req1.title === req2.title &&
    req1.development_plan === req2.development_plan &&
    req1.wireframe_html === req2.wireframe_html &&
    req1.questions.length === req2.questions.length &&
    req1.answers.length === req2.answers.length &&
    req1.reference_images.length === req2.reference_images.length
  );
};

// 静默获取单个需求（不显示错误消息，不触发loading状态）
const silentFetchSingleRequirement = async (id: string): Promise<Requirement | null> => {
  try {
    // 直接调用API，避免触发store的loading状态
    const { getRequirement } = await import('@/api/requirement.js');
    const response = await getRequirement(id);
    return response.requirement;
  } catch (error) {
    // 静默失败，不显示错误消息
    console.error('[HomeView] Silent fetch single requirement error:', error);
    return null;
  }
};

// 开始需求列表轮询
const startRequirementListPolling = () => {
  if (isPollingActive.value) return; // 避免重复启动
  
  
  isPollingActive.value = true;
  
  requirementListPollingTimer.value = setInterval(async () => {
    try {

      
      // 使用静默轮询，避免UI闪烁
      await requirementStore.silentFetchRequirements(currentPage.value, 10, statusFilter.value);
      
      // 批量静默更新打开的对话框中的需求
      const updatePromises: Promise<void>[] = [];
      
      if (selectedRequirement.value) {
        updatePromises.push(
          silentFetchSingleRequirement(selectedRequirement.value.id).then(updated => {
            if (updated && !requirementsEqual(selectedRequirement.value!, updated)) {
              selectedRequirement.value = updated;
  
            }
          })
        );
      }
      
      if (selectedWireframeRequirement.value) {
        updatePromises.push(
          silentFetchSingleRequirement(selectedWireframeRequirement.value.id).then(updated => {
            if (updated && !requirementsEqual(selectedWireframeRequirement.value!, updated)) {
              selectedWireframeRequirement.value = updated;
  
            }
          })
        );
      }
      
      // 并行执行所有更新
      if (updatePromises.length > 0) {
        await Promise.allSettled(updatePromises);
      }
      
    } catch (error) {
      console.error('[HomeView] Error during requirement list polling:', error);
      // 轮询出错时不显示错误消息，避免干扰用户
    }
  }, 5000); // 改为每5秒轮询一次，减少服务器压力
};

// 停止需求列表轮询
const stopRequirementListPolling = () => {
  if (requirementListPollingTimer.value) {
    clearInterval(requirementListPollingTimer.value);
    requirementListPollingTimer.value = null;
    isPollingActive.value = false;

  }
};

// 线框图相关方法
const openWireframeViewer = (requirement: Requirement) => {
  selectedWireframeRequirement.value = requirement;
  selectedWireframeIndex.value = 0; // 重置为第一个线框图
  showWireframeDialog.value = true;
  
  // 重置错误状态
  wireframeError.value = false;
  wireframeErrorMessage.value = '';
  wireframeIframeKey.value += 1; // 强制重新渲染
};

// 处理线框图 iframe 加载完成
const handleWireframeLoad = () => {
  
  wireframeError.value = false;
  wireframeErrorMessage.value = '';
  
  // 监听 iframe 内的错误事件
  try {
    const iframe = wireframeIframe.value;
    if (iframe && iframe.contentWindow) {
      // 监听 iframe 内的错误
      iframe.contentWindow.addEventListener('error', (event) => {
        console.error('[HomeView] Wireframe iframe content error:', event);
        wireframeError.value = true;
        wireframeErrorMessage.value = '线框图内容执行时发生错误，可能包含不兼容的脚本。';
      });
      
      // 监听未处理的 Promise 错误
      iframe.contentWindow.addEventListener('unhandledrejection', (event) => {
        console.error('[HomeView] Wireframe iframe unhandled rejection:', event);
        wireframeError.value = true;
        wireframeErrorMessage.value = '线框图内容执行时发生异步错误。';
      });
      
      // 监听导航事件，防止页面跳转
      iframe.contentWindow.addEventListener('beforeunload', (event) => {
        console.warn('[HomeView] Wireframe attempting navigation, preventing...');
        event.preventDefault();
        event.returnValue = '';
        wireframeError.value = true;
        wireframeErrorMessage.value = '线框图尝试进行页面导航，已被阻止。';
        return '';
      });
      
      // 设置定时器检查 iframe 状态
      setTimeout(() => {
        try {
          if (iframe.contentWindow && iframe.contentDocument) {
            // 检查页面是否加载了意外内容
            const location = iframe.contentWindow.location;
            if (location.href !== 'about:srcdoc' && location.href !== 'about:blank') {
              console.warn('[HomeView] Wireframe iframe navigated to unexpected location:', location.href);
              wireframeError.value = true;
              wireframeErrorMessage.value = '线框图发生了意外的页面跳转。';
            }
          }
        } catch (error) {
          console.error('[HomeView] Error checking iframe status:', error);
        }
      }, 1000);
    }
  } catch (error) {
    console.error('[HomeView] Error setting up iframe monitoring:', error);
  }
};

// 处理线框图 iframe 加载错误
const handleWireframeError = (event: Event) => {
  console.error('[HomeView] Wireframe iframe load error:', event);
  wireframeError.value = true;
  wireframeErrorMessage.value = '线框图加载失败，内容可能包含无效的HTML。';
};

// 重新加载线框图
const handleWireframeReload = () => {
  
  wireframeError.value = false;
  wireframeErrorMessage.value = '';
  wireframeIframeKey.value += 1; // 强制重新渲染 iframe
  
  ElMessage.info('正在重新加载线框图...');
};

const generateWireframe = async (requirementId: string) => {
  try {

    const success = await requirementStore.generateWireframe(requirementId);
    
    if (success) {
      // 更新选中的线框图需求
      if (selectedWireframeRequirement.value?.id === requirementId) {
        const updatedRequirement = await requirementStore.fetchRequirement(requirementId);
        if (updatedRequirement) {
          selectedWireframeRequirement.value = updatedRequirement;
        }
      }
      
      // 刷新列表
      await refreshRequirements();
    }
  } catch (error) {
    console.error('Failed to generate wireframe:', error);
    ElMessage.error('生成线框图失败');
  }
};

const regenerateWireframe = async (requirementId: string) => {
  try {
    await ElMessageBox.confirm(
      '重新生成将覆盖现有的线框图，确定要继续吗？',
      '确认重新生成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await generateWireframe(requirementId);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to regenerate wireframe:', error);
    }
  }
};

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  } catch (error) {
    return '未知时间';
  }
};

const handleLogout = async () => {
  try {
    await userStore.logout();
    ElMessage.success('已成功退出登录');
  } catch (error) {
    console.error('Logout failed:', error);
    ElMessage.error('退出登录失败');
  }
};

// 监听认证状态变化
watch(isLoggedIn, async (newValue, oldValue) => {
  if (newValue !== oldValue) {

    
    if (newValue) {
      // 用户登录后，加载需求列表并启动轮询
      await refreshRequirements();
      startRequirementListPolling();
    } else {
      // 用户登出后，停止轮询并清理状态
      stopRequirementListPolling();
      requirementStore.$reset();
    }
  }
});

// 监听对话框关闭，清理轮询
watch(showRequirementDetailDialog, (newValue) => {
  if (!newValue) {
    // 对话框关闭时停止所有轮询
    stopDevelopmentPlanPolling();
    // 同时重置编辑状态
    isEditingPlan.value = false;
    editedPlanContent.value = '';
  }
});

// 组件挂载时检查登录状态并加载数据
onMounted(async () => {
  
  
  if (isLoggedIn.value) {
    await refreshRequirements();
    // 启动需求列表轮询
    startRequirementListPolling();
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopDevelopmentPlanPolling();
  stopRequirementListPolling();
  
  // 清理线框图相关状态
  wireframeError.value = false;
  wireframeErrorMessage.value = '';
  
  // 清理iframe事件监听器
  if (wireframeIframe.value && wireframeIframe.value.contentWindow) {
    try {
      wireframeIframe.value.contentWindow.removeEventListener('error', () => {});
      wireframeIframe.value.contentWindow.removeEventListener('unhandledrejection', () => {});
      wireframeIframe.value.contentWindow.removeEventListener('beforeunload', () => {});
    } catch (error) {
      
    }
  }
});

const selectWireframeForProject = (wireframe: any) => {
  // 显示项目创建确认对话框
  showCreateProjectDialog.value = true;
  selectedWireframeForProject.value = wireframe;
  
  // 清空之前的输入
  newProjectId.value = '';
  newProjectName.value = '';
};

const handleCreateProjectFromRequirement = async () => {
  if (!selectedWireframeRequirement.value || !selectedWireframeForProject.value) {
    ElMessage.error('请先选择线框图');
    return;
  }

  if (!newProjectId.value.trim() || !newProjectName.value.trim()) {
    ElMessage.error('请填写项目ID和名称');
    return;
  }

  try {
    isCreatingProject.value = true;
    
    const response = await fetch(`/api/v1/requirements/${selectedWireframeRequirement.value.id}/create-project`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: newProjectId.value.trim(),
        project_name: newProjectName.value.trim(),
        wireframe_id: selectedWireframeForProject.value.id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '创建项目失败');
    }

    const result = await response.json();
    
    ElMessage.success('项目创建成功！即将跳转到项目页面...');
    
    // 关闭对话框
    showCreateProjectDialog.value = false;
    showWireframeDialog.value = false;
    
    // 跳转到项目IDE页面
    setTimeout(() => {
      router.push(`/ide/${newProjectId.value}`);
    }, 1500);
    
  } catch (error: any) {
    console.error('Failed to create project from requirement:', error);
    ElMessage.error(error.message || '创建项目失败，请稍后重试');
  } finally {
    isCreatingProject.value = false;
  }
};
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 确保页面可以正常滚动 */
.home-view {
  overflow-y: auto;
  height: 100vh;
}

/* 滚动条样式 */
.home-view::-webkit-scrollbar {
  width: 12px;
}

.home-view::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.8);
  border-radius: 6px;
}

.home-view::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.8);
  border-radius: 6px;
  border: 2px solid rgba(31, 41, 55, 0.8);
}

.home-view::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 1);
}

/* 需求卡片网格 */
.requirements-grid {
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 对话框样式优化 */
:deep(.requirement-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.requirement-detail-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .requirements-grid {
    grid-template-columns: 1fr;
  }
  
  .requirement-card {
    margin: 0 -1rem;
  }
  
  .requirement-detail-dialog {
    width: 95% !important;
  }
}

/* 确保所有输入元素都可见且布局正确 */
textarea, input {
  box-sizing: border-box;
}

/* Element Plus组件样式覆盖 */
:deep(.el-select .el-input__inner) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%) !important;
  border-color: rgba(71, 85, 105, 0.4) !important;
  color: #f1f5f9 !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-select .el-input__inner:hover) {
  border-color: rgba(56, 189, 248, 0.6) !important;
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.1), inset 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-select .el-input__inner:focus) {
  border-color: rgba(56, 189, 248, 0.8) !important;
  box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.2), inset 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* 选择框下拉选项样式 */
:deep(.el-select-dropdown) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(20px) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  background: transparent !important;
  color: #f1f5f9 !important;
  border-radius: 6px !important;
  margin: 2px 4px !important;
  transition: all 0.2s ease !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.2) 0%, rgba(168, 85, 247, 0.2) 100%) !important;
  color: #ffffff !important;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: rgba(45, 45, 48, 0.8);
  --el-pagination-text-color: #cccccc;
  --el-pagination-border-radius: 6px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination .el-pager li) {
  background-color: rgba(45, 45, 48, 0.8) !important;
  border: 1px solid #3e3e42 !important;
  color: #cccccc !important;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .el-pager li:hover) {
  color: #4fc3f7 !important;
  border-color: #4fc3f7 !important;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #4fc3f7 !important;
  border-color: #4fc3f7 !important;
  color: #000 !important;
}

/* 线框图对话框样式 */
:deep(.wireframe-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 90vh;
  overflow-y: auto;
}

/* 线框图iframe样式 */
.wireframe-iframe {
  transition: opacity 0.3s ease;
}

.wireframe-iframe-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 线框图错误状态样式 */
.wireframe-error {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-radius: 8px;
}

.wireframe-loading {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  border-radius: 8px;
}

.wireframe-empty {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 8px;
}

.development-plan-content {
  @apply text-gray-300 leading-relaxed;
}

.development-plan-content :deep(h1),
.development-plan-content :deep(h2),
.development-plan-content :deep(h3) {
  @apply font-semibold text-white border-b border-gray-600 pb-2 mb-4 mt-6;
}
.development-plan-content :deep(h1) { @apply text-2xl; }
.development-plan-content :deep(h2) { @apply text-xl; }
.development-plan-content :deep(h3) { @apply text-lg; }

.development-plan-content :deep(ul),
.development-plan-content :deep(ol) {
  @apply list-disc list-inside pl-5 mb-4 space-y-1;
}

.development-plan-content :deep(p) {
  @apply mb-4;
}

.development-plan-content :deep(strong) {
  @apply font-semibold text-white;
}

/* Shiki code block container styling */
.development-plan-content :deep(pre.shiki) {
  @apply bg-[#1E1E1E] rounded-lg p-4 my-4 overflow-x-auto;
}

/* Custom table styling */
.development-plan-content :deep(.table-wrapper) {
  @apply my-4 overflow-x-auto border border-gray-600 rounded-lg;
}
.development-plan-content :deep(table.custom-table) {
  @apply w-full border-collapse;
}
.development-plan-content :deep(table.custom-table th),
.development-plan-content :deep(table.custom-table td) {
  @apply border border-gray-600 px-4 py-2 text-left;
}
.development-plan-content :deep(table.custom-table th) {
  @apply bg-gray-700 font-semibold text-white;
}
.development-plan-content :deep(table.custom-table tr:nth-child(even)) {
  @apply bg-gray-800/50;
}

.development-plan-content .prose-styles-placeholder {
  @apply text-gray-300 leading-relaxed;
}
.development-plan-content .prose-styles-placeholder :deep(h1),
.development-plan-content .prose-styles-placeholder :deep(h2),
.development-plan-content .prose-styles-placeholder :deep(h3) {
  @apply font-semibold text-white border-b border-gray-600 pb-2 mb-4 mt-6;
}
.development-plan-content .prose-styles-placeholder :deep(h1) { @apply text-2xl; }
.development-plan-content .prose-styles-placeholder :deep(h2) { @apply text-xl; }
.development-plan-content .prose-styles-placeholder :deep(h3) { @apply text-lg; }

.development-plan-content .prose-styles-placeholder :deep(ul),
.development-plan-content .prose-styles-placeholder :deep(ol) {
  @apply list-disc list-inside pl-5 mb-4 space-y-1;
}

.development-plan-content .prose-styles-placeholder :deep(p) {
  @apply mb-4;
}

.development-plan-content .prose-styles-placeholder :deep(strong) {
  @apply font-semibold text-white;
}

/* Shiki code block container styling */
.development-plan-content .prose-styles-placeholder :deep(pre.shiki) {
  @apply bg-[#1E1E1E] rounded-lg p-4 my-4 overflow-x-auto;
}

/* Ensure default text color inside code blocks is light */
.development-plan-content .prose-styles-placeholder :deep(pre.shiki code) {
  color: #e5e7eb; /* Light gray for good contrast on dark background */
}

/* Custom table styling */
.development-plan-content .prose-styles-placeholder :deep(.table-wrapper) {
  @apply my-4 overflow-x-auto border border-gray-600 rounded-lg;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table) {
  @apply w-full border-collapse;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table th),
.development-plan-content .prose-styles-placeholder :deep(table.custom-table td) {
  @apply border border-gray-600 px-4 py-2 text-left;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table th) {
  @apply bg-gray-700 font-semibold text-white;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table tr:nth-child(even)) {
  @apply bg-gray-800/50;
}

.development-plan-content .prose-styles-placeholder {
  color: #d1d5db; /* Default light gray text */
  line-height: 1.7;
}

.development-plan-content .prose-styles-placeholder :deep(h1),
.development-plan-content .prose-styles-placeholder :deep(h2),
.development-plan-content .prose-styles-placeholder :deep(h3) {
  color: #ffffff;
  font-weight: 600;
  border-bottom: 1px solid #4a5568;
  padding-bottom: 0.5rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}
.development-plan-content .prose-styles-placeholder :deep(h1) { font-size: 1.5em; }
.development-plan-content .prose-styles-placeholder :deep(h2) { font-size: 1.25em; }
.development-plan-content .prose-styles-placeholder :deep(h3) { font-size: 1.1em; }

.development-plan-content .prose-styles-placeholder :deep(ul),
.development-plan-content .prose-styles-placeholder :deep(ol) {
  list-style-position: inside;
  padding-left: 1rem;
  margin-bottom: 1rem;
}
.development-plan-content .prose-styles-placeholder :deep(li) {
  margin-bottom: 0.5rem;
}

.development-plan-content .prose-styles-placeholder :deep(p) {
  margin-bottom: 1rem;
}

.development-plan-content .prose-styles-placeholder :deep(strong) {
  color: #ffffff;
  font-weight: 600;
}

/* Shiki code block container styling */
.development-plan-content .prose-styles-placeholder :deep(pre.shiki) {
  background-color: #161a22;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
}

/* Ensure default text color inside code blocks is light */
.development-plan-content .prose-styles-placeholder :deep(pre.shiki code) {
  color: #e5e7eb;
  font-family: "Sarasa Gothic SC", SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* Custom table styling */
.development-plan-content .prose-styles-placeholder :deep(.table-wrapper) {
  margin-top: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
  border: 1px solid #4a5568;
  border-radius: 0.5rem;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table) {
  width: 100%;
  border-collapse: collapse;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table th),
.development-plan-content .prose-styles-placeholder :deep(table.custom-table td) {
  border: 1px solid #4a5568;
  padding: 0.75rem 1rem;
  text-align: left;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table th) {
  background-color: #2d3748;
  color: #ffffff;
  font-weight: 600;
}
.development-plan-content .prose-styles-placeholder :deep(table.custom-table tr:nth-child(even)) {
  background-color: #272f3d;
}

.development-plan-content .prose-styles-placeholder :deep(code) {
    background-color: #2d3748;
    color: #90cdf4;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    border-radius: 3px;
}

.development-plan-content .prose-styles-placeholder :deep(pre code) {
    padding: 0;
    margin: 0;
    font-size: inherit;
    color: inherit;
    background-color: transparent;
    border-radius: 0;
}
</style>