<template>
  <div class="new-project-view flex items-center justify-center min-h-screen bg-[#1a1a1f]">
    <div class="form-container bg-[#29292e] p-8 rounded-lg shadow-lg w-full max-w-md">
      <h1 class="text-2xl font-semibold text-white mb-6 text-center">Create New Project</h1>
      <form @submit.prevent="handleCreateProject">
        <div class="mb-4">
          <label for="projectName" class="block text-sm font-medium text-[#bdbdbd] mb-1">Project Name</label>
          <el-input
            id="projectName"
            v-model="projectName"
            placeholder="Enter project name (e.g., my-cool-app)"
            size="large"
            :disabled="isLoading"
            required
          />
          <!-- Basic validation feedback -->
           <p v-if="projectNameError" class="text-red-500 text-xs mt-1">{{ projectNameError }}</p>
        </div>

        <el-button
          type="primary"
          native-type="submit"
          size="large"
          class="w-full"
          :loading="isLoading"
          :disabled="isLoading"
        >
          Create Project
        </el-button>

        <p v-if="apiError" class="text-red-500 text-sm mt-4 text-center">{{ apiError }}</p>

      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElInput, ElButton } from 'element-plus'; // Using Element Plus components
import { createProject as createProjectApi } from '@/api/fs'; // Assuming createProject exists in fsApi

const router = useRouter();
const projectName = ref('');
const isLoading = ref(false);
const apiError = ref<string | null>(null);
const projectNameError = ref<string | null>(null);

const validateProjectName = (): boolean => {
  // Basic validation: non-empty and maybe prevent special chars if backend requires
  // Example: only allow letters, numbers, hyphens, underscores
  const validPattern = /^[a-zA-Z0-9-_]+$/;
  if (!projectName.value) {
    projectNameError.value = 'Project name cannot be empty.';
    return false;
  }
  if (!validPattern.test(projectName.value)) {
     projectNameError.value = 'Project name can only contain letters, numbers, hyphens (-), and underscores (_).';
     return false;
  }
  projectNameError.value = null; // Clear error if valid
  return true;
};


const handleCreateProject = async () => {
  apiError.value = null; // Clear previous API errors
  if (!validateProjectName()) {
    return;
  }

  isLoading.value = true;
  try {
    // Call the backend API
    // We assume the backend uses the provided name as the project ID
    // and returns success or error.
    await createProjectApi(projectName.value);

    // Navigate to the IDE view for the new project
    router.push({ name: 'ide', params: { projectID: projectName.value } });

  } catch (error: any) {
    console.error('Failed to create project:', error);
    apiError.value = `Failed to create project: ${error.message || 'Unknown error'}`;
    // Handle specific errors from backend if possible
    // if (error.response && error.response.data) {
    //   apiError.value = `Failed to create project: ${error.response.data.message || 'Server error'}`;
    // }
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* Add specific styles if needed */
.el-input__inner {
  /* Example to ensure dark theme compatibility if needed */
  /* color: #e6e6e6; */
  /* background-color: #3a3a40; */
  /* border-color: #555; */
}
</style> 