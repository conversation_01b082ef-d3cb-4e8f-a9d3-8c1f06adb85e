<template>
  <div class="global-settings-view flex flex-col items-center min-h-screen bg-[#1a1a1f] py-12 px-4">
    <h1 class="text-3xl font-bold text-white mb-8">Global LLM Settings</h1>

    <el-card class="settings-card w-full max-w-5xl !bg-[#262626] border-none shadow-lg rounded-lg">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold text-gray-200">LLM Configurations</span>
          <el-button type="primary" @click="openCreateDialog">
            Create New LLM Config
          </el-button>
        </div>
      </template>

      <el-alert
        v-if="llmSettingStore.error"
        :title="llmSettingStore.error"
        type="error"
        show-icon
        class="mb-4"
        closable
        @close="llmSettingStore.error = null"
      />

      <el-table
        v-if="llmSettingStore.getLLMConfigs.length > 0 && !llmSettingStore.isLoading"
        :data="llmSettingStore.getLLMConfigs"
        style="width: 100%"
        class="llm-config-table"
        row-key="id"
      >
        <el-table-column prop="name" label="Name" width="180" />
        <el-table-column prop="type" label="Type" width="120" />
        <el-table-column prop="model_name" label="Model Name" width="200" />
        <el-table-column label="API Base" min-width="250">
          <template #default="scope">
            <span class="font-mono text-sm">{{ scope.row.api_base }}</span>
          </template>
        </el-table-column>
        <el-table-column label="API Key" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.api_key" class="font-mono text-sm">
              {{ scope.row.api_key.substring(0, 5) }}...{{ scope.row.api_key.substring(scope.row.api_key.length - 4) }}
            </span>
            <span v-else class="text-gray-500">Not set</span>
          </template>
        </el-table-column>
        <el-table-column label="Status" width="120" align="center">
          <template #default="scope">
            <el-tag
              :type="scope.row.is_active ? 'success' : 'info'"
              effect="dark"
            >
              {{ scope.row.is_active ? 'Active' : 'Inactive' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="240" align="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="openEditDialog(scope.row)"
            >
              Edit
            </el-button>
            <el-button
              link
              :type="scope.row.is_active ? 'info' : 'success'"
              size="small"
              @click="handleSetActive(scope.row.id)"
              :disabled="scope.row.is_active"
            >
              {{ scope.row.is_active ? 'Active' : 'Set Active' }}
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="confirmDelete(scope.row)"
              :disabled="scope.row.is_active"
            >
              Delete
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty
        v-else-if="!llmSettingStore.isLoading && llmSettingStore.getLLMConfigs.length === 0"
        description="No LLM configurations found. Create one to get started."
      />
      <div v-if="llmSettingStore.isLoading" class="flex justify-center p-6">
        <el-icon class="is-loading text-4xl text-gray-400">
          <Loading />
        </el-icon>
      </div>
    </el-card>

    <!-- Create/Edit Dialog -->
    <el-dialog
      v-model="showEditDialog"
      :title="isEditMode ? 'Edit LLM Configuration' : 'Create New LLM Configuration'"
      width="600px"
      :close-on-click-modal="false"
      class="llm-config-dialog"
    >
      <el-form
        :model="currentConfigForm"
        :rules="configFormRules"
        ref="configFormRef"
        label-position="top"
      >
        <el-form-item label="Name" prop="name">
          <el-input v-model="currentConfigForm.name" placeholder="e.g., OpenAI GPT-4, My Kimi AI" />
        </el-form-item>
        <el-form-item label="Type" prop="type">
          <el-select v-model="currentConfigForm.type" placeholder="Select LLM Type" class="w-full">
            <el-option label="OpenAI Compatible" value="openai_compatible" />
            <el-option label="Google Gemini" value="gemini" />
            <!-- Add other types as needed -->
          </el-select>
        </el-form-item>
        <el-form-item label="API Key" prop="api_key">
          <el-input v-model="currentConfigForm.api_key" placeholder="Enter API Key" show-password />
        </el-form-item>
        <el-form-item label="API Base URL" prop="api_base">
          <el-input v-model="currentConfigForm.api_base" placeholder="e.g., https://api.openai.com/v1" />
        </el-form-item>
        <el-form-item label="Model Name" prop="model_name">
          <el-input v-model="currentConfigForm.model_name" placeholder="e.g., gpt-4o, kimi-k2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">Cancel</el-button>
          <el-button type="primary" @click="handleSubmitConfig">
            {{ isEditMode ? 'Update' : 'Create' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import {
  ElCard,
  ElButton,
  ElTable,
  ElTableColumn,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTag,
  ElIcon,
  ElMessage,
  ElMessageBox,
  ElAlert,
} from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import { useLLMSettingStore } from '@/store/llm_settings'; // Import the new LLM settings store
import type { LLMConfig, CreateLLMConfigRequest, UpdateLLMConfigRequest } from '@/api/llm_config'; // Import types
import type { FormInstance, FormRules } from 'element-plus';

const llmSettingStore = useLLMSettingStore();

// State for Create/Edit Dialog
const showEditDialog = ref(false);
const isEditMode = ref(false); // True for edit, false for create
const currentConfigForm = reactive<CreateLLMConfigRequest & { id?: string }>({ // Combined type for form
  id: undefined, // Only present in edit mode
  name: '',
  type: '',
  api_key: '',
  api_base: '',
  model_name: '',
});
const configFormRef = ref<FormInstance | null>(null);

// Form Validation Rules
const configFormRules = reactive<FormRules<typeof currentConfigForm>>({
  name: [{ required: true, message: 'Please enter a name', trigger: 'blur' }],
  type: [{ required: true, message: 'Please select a type', trigger: 'change' }],
  api_key: [{ required: true, message: 'Please enter an API Key', trigger: 'blur' }],
  api_base: [{ required: true, message: 'Please enter an API Base URL', trigger: 'blur' }],
  model_name: [{ required: true, message: 'Please enter a Model Name', trigger: 'blur' }],
});

// Lifecycle
onMounted(() => {
  llmSettingStore.fetchLLMConfigs();
});

// Methods
const openCreateDialog = () => {
  isEditMode.value = false;
  resetConfigForm();
  showEditDialog.value = true;
};

const openEditDialog = (config: LLMConfig) => {
  isEditMode.value = true;
  // Populate form with existing config data
  currentConfigForm.id = config.id;
  currentConfigForm.name = config.name;
  currentConfigForm.type = config.type;
  currentConfigForm.api_key = config.api_key; // API Key will be pre-filled
  currentConfigForm.api_base = config.api_base;
  currentConfigForm.model_name = config.model_name;
  showEditDialog.value = true;
};

const resetConfigForm = () => {
  currentConfigForm.id = undefined;
  currentConfigForm.name = '';
  currentConfigForm.type = '';
  currentConfigForm.api_key = '';
  currentConfigForm.api_base = '';
  currentConfigForm.model_name = '';
  if (configFormRef.value) {
    configFormRef.value.resetFields();
  }
};

const handleSubmitConfig = async () => {
  if (!configFormRef.value) return;

  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      if (isEditMode.value && currentConfigForm.id) {
        // Update existing config
        const updateData: UpdateLLMConfigRequest = {
          name: currentConfigForm.name,
          type: currentConfigForm.type,
          api_key: currentConfigForm.api_key,
          api_base: currentConfigForm.api_base,
          model_name: currentConfigForm.model_name,
        };
        const success = await llmSettingStore.updateExistingLLMConfig(currentConfigForm.id, updateData);
        if (success) {
          showEditDialog.value = false;
        }
      } else {
        // Create new config
        const createData: CreateLLMConfigRequest = {
          name: currentConfigForm.name,
          type: currentConfigForm.type,
          api_key: currentConfigForm.api_key,
          api_base: currentConfigForm.api_base,
          model_name: currentConfigForm.model_name,
        };
        const success = await llmSettingStore.createNewLLMConfig(createData);
        if (success) {
          showEditDialog.value = false;
        }
      }
    } else {
      ElMessage.error('Please check the form for errors.');
    }
  });
};

const handleSetActive = async (id: string) => {
  try {
    await ElMessageBox.confirm('Are you sure you want to set this LLM configuration as active?', 'Confirm Activation', {
      confirmButtonText: 'Activate',
      cancelButtonText: 'Cancel',
      type: 'warning',
      customClass: 'llm-config-dialog', // Apply dark theme
    });
    const success = await llmSettingStore.setAsActiveLLMConfig(id);
    if (success) {
      // Nothing extra needed, store updates will re-render
    }
  } catch (error) {
    if (error !== 'cancel') { // Ignore user cancellation
      console.error('Failed to set active LLM config:', error);
      ElMessage.error(`Failed to set active LLM config: ${error}`);
    }
  }
};

const confirmDelete = async (config: LLMConfig) => {
  try {
    await ElMessageBox.confirm(`Are you sure you want to delete LLM configuration "${config.name}"?`, 'Confirm Deletion', {
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      type: 'warning',
      customClass: 'llm-config-dialog', // Apply dark theme
    });
    const success = await llmSettingStore.deleteExistingLLMConfig(config.id);
    if (success) {
      // Nothing extra needed, store updates will re-render
    }
  } catch (error) {
    if (error !== 'cancel') { // Ignore user cancellation
      console.error('Failed to delete LLM config:', error);
      ElMessage.error(`Failed to delete LLM config: ${error}`);
    }
  }
};
</script>

<style scoped>
.global-settings-view {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.settings-card {
  /* Inherits global dark card styles from main.css */
}

.llm-config-table {
  background-color: transparent !important;
  color: #e0e0e0 !important;
}

.llm-config-table :deep(th),
.llm-config-table :deep(td) {
  background-color: #262626 !important;
  color: #e0e0e0 !important;
  border-bottom: 1px solid #4d4d54 !important;
}

.llm-config-table :deep(th) {
  font-weight: 600;
  color: #f0f0f0 !important;
}

.llm-config-table :deep(.el-table__empty-text) {
  color: #a0a0a0;
}

.llm-config-table :deep(.el-table__inner-wrapper::before) {
  content: none; /* Remove default bottom border on header */
}

.llm-config-table :deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.llm-config-table :deep(.el-table__row:hover) {
  background-color: #333338 !important;
}

/* Dialog and Form element overrides */
.llm-config-dialog :deep(.el-dialog) {
  background-color: #2c2c32 !important;
  border-radius: 8px;
}
.llm-config-dialog :deep(.el-dialog__title) {
  color: #e0e0e0 !important;
}
.llm-config-dialog :deep(.el-dialog__headerbtn .el-icon) {
  color: #e0e0e0 !important;
}
.llm-config-dialog :deep(.el-dialog__body) {
  color: #cccccc !important;
}
.llm-config-dialog :deep(.el-form-item__label) {
  color: #e0e0e0 !important;
}
.llm-config-dialog :deep(.el-input__wrapper),
.llm-config-dialog :deep(.el-select__wrapper) {
  background-color: #3a3a40 !important;
  box-shadow: none !important;
  border: 1px solid #555555 !important;
}
.llm-config-dialog :deep(.el-input__inner),
.llm-config-dialog :deep(.el-select__placeholder) {
  color: #f0f0f0 !important;
}
.llm-config-dialog :deep(.el-input__wrapper.is-focus),
.llm-config-dialog :deep(.el-select__wrapper.is-focused) {
  border-color: #4fc3f7 !important;
  box-shadow: 0 0 0 1px #4fc3f7 !important;
}
.llm-config-dialog :deep(.el-button--primary) {
  background-color: #4fc3f7 !important;
  border-color: #4fc3f7 !important;
  color: #1e1e1e !important;
}
.llm-config-dialog :deep(.el-button--primary:hover) {
  background-color: #6ed1f9 !important;
  border-color: #6ed1f9 !important;
}
.llm-config-dialog :deep(.el-button) {
  background-color: #424242 !important;
  border-color: #555555 !important;
  color: #e0e0e0 !important;
}
.llm-config-dialog :deep(.el-button:hover) {
  background-color: #555555 !important;
  border-color: #666666 !important;
}
</style> 