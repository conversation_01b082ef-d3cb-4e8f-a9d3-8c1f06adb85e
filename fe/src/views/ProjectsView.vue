<template>
  <div class="projects-view flex flex-col items-center min-h-screen bg-[#1a1a1f] py-12 px-4">
    <h1 class="text-3xl font-bold text-white mb-8">Your Projects</h1>

    <!-- Project List -->
    <div class="project-list w-full max-w-3xl mb-8">
      <el-card class="box-card !bg-[#262626] border-none shadow-lg rounded-lg">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-lg font-semibold text-gray-200">Existing Projects</span>
            <el-button type="primary" @click="openCreateProjectDialog">
              Create New Project
            </el-button>
          </div>
        </template>
        <el-table
          v-if="projects.length > 0 && !isLoadingProjects"
          :data="projects"
          style="width: 100%"
          :row-class-name="tableRowClassName"
          @row-click="navigateToProject"
          empty-text="No projects found."
          class="project-table"
        >
          <el-table-column prop="name" label="Project Name">
            <template #default="scope">
              <!-- Display name, fallback to project_id if name is empty -->
              {{ scope.row.name || scope.row.project_id }}
            </template>
          </el-table-column>
          <el-table-column prop="project_id" label="Project ID" />
          <!-- Add other columns like 'created_at' if desired -->
          <el-table-column label="Actions" width="180" align="right">
             <template #default="scope">
                <el-button type="primary" link size="small" @click.stop="navigateToProject(scope.row)">
                  Open
                </el-button>
                <el-button 
                  type="danger" 
                  link 
                  size="small" 
                  @click.stop="confirmDeleteProject(scope.row)"
                  :disabled="isDeletingProject === scope.row.project_id"
                >
                  {{ isDeletingProject === scope.row.project_id ? 'Deleting...' : 'Delete' }}
                </el-button>
             </template>
          </el-table-column>
        </el-table>
        <el-empty v-else-if="!isLoadingProjects && projects.length === 0" description="You don't have any projects yet." />
        <div v-if="isLoadingProjects" class="flex justify-center p-6">
          <el-icon class="is-loading text-4xl text-gray-400">
            <span>Loading...</span>
          </el-icon>
        </div>
         <p v-if="loadError" class="text-red-500 text-sm mt-4 text-center">{{ loadError }}</p>
      </el-card>
    </div>

    <!-- Create Project Dialog -->
    <el-dialog
      v-model="showCreateDialog"
      title="Create New Project"
      width="500px"
      :close-on-click-modal="false"
      class="create-dialog"
    >
      <form @submit.prevent="handleCreateProject" class="p-4">
        <div class="mb-4">
          <label for="newProjectName" class="block text-sm font-medium text-gray-300 mb-1">Project Name</label>
          <el-input
            id="newProjectName"
            v-model="newProjectName"
            placeholder="Enter project name (e.g., my-cool-app)"
            size="large"
            :disabled="isCreatingProject"
            required
            class="mb-1"
          />
          <p v-if="projectNameError" class="text-red-500 text-xs">{{ projectNameError }}</p>
        </div>
        <p v-if="createApiError" class="text-red-500 text-sm mb-4 text-center">{{ createApiError }}</p>
        <div class="dialog-footer flex justify-end space-x-2">
            <el-button @click="showCreateDialog = false">Cancel</el-button>
            <el-button
              type="primary"
              native-type="submit"
              :loading="isCreatingProject"
              :disabled="isCreatingProject"
            >
              Create Project
            </el-button>
        </div>
      </form>
    </el-dialog>

    <!-- Delete Confirmation Dialog -->
    <el-dialog
      v-model="showDeleteDialog"
      title="Confirm Delete Project"
      width="450px"
      :close-on-click-modal="false"
      class="delete-dialog"
    >
      <div class="p-4">
        <p class="text-gray-300 mb-4">
          Are you sure you want to delete the project 
          <strong class="text-red-400">"{{ projectToDelete?.name || projectToDelete?.project_id }}"</strong>?
        </p>
        <p class="text-red-400 text-sm mb-4">
          <strong>Warning:</strong> This will permanently delete all project files, runtime environment, and metadata. This action cannot be undone.
        </p>
        <p v-if="deleteApiError" class="text-red-500 text-sm mb-4 text-center">{{ deleteApiError }}</p>
                 <div class="dialog-footer flex justify-end space-x-2">
           <el-button @click="showDeleteDialog = false" :disabled="!!isDeletingProject">
             Cancel
           </el-button>
           <el-button
             type="danger"
             @click="handleDeleteProject"
             :loading="!!isDeletingProject"
             :disabled="!!isDeletingProject"
           >
             Delete Project
           </el-button>
         </div>
      </div>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElCard, ElTable, ElTableColumn, ElButton, ElDialog, ElInput, ElMessage, ElEmpty, ElIcon, ElMessageBox } from 'element-plus';
import { listUserProjects, ProjectListItem, deleteProject } from '@/api/project';
import { createProject as createProjectApi } from '@/api/fs';

const router = useRouter();
const projects = ref<ProjectListItem[]>([]);
const isLoadingProjects = ref(false);
const loadError = ref<string | null>(null);

// State for Create Project Dialog
const showCreateDialog = ref(false);
const newProjectName = ref('');
const isCreatingProject = ref(false);
const createApiError = ref<string | null>(null);
const projectNameError = ref<string | null>(null);

// State for Delete Project Dialog
const showDeleteDialog = ref(false);
const projectToDelete = ref<ProjectListItem | null>(null);
const isDeletingProject = ref<string | null>(null); // Store project ID being deleted
const deleteApiError = ref<string | null>(null);

// Fetch projects on mount
onMounted(async () => {
  isLoadingProjects.value = true;
  loadError.value = null;
  try {
    projects.value = await listUserProjects();
  } catch (error: any) {
    console.error('Failed to fetch projects:', error);
    loadError.value = `Failed to load projects: ${error.message || 'Unknown error'}`;
    ElMessage.error(loadError.value);
  } finally {
    isLoadingProjects.value = false;
  }
});

const openCreateProjectDialog = () => {
  newProjectName.value = '';
  projectNameError.value = null;
  createApiError.value = null;
  showCreateDialog.value = true;
};

const validateProjectName = (): boolean => {
  const validPattern = /^[a-zA-Z0-9-_]+$/;
  if (!newProjectName.value) {
    projectNameError.value = 'Project name cannot be empty.';
    return false;
  }
  if (!validPattern.test(newProjectName.value)) {
     projectNameError.value = 'Project name can only contain letters, numbers, hyphens (-), and underscores (_).';
     return false;
  }
  projectNameError.value = null;
  return true;
};

const handleCreateProject = async () => {
  createApiError.value = null;
  if (!validateProjectName()) {
    return;
  }

  isCreatingProject.value = true;
  try {
    await createProjectApi(newProjectName.value);
    showCreateDialog.value = false; // Close dialog on success
    ElMessage.success(`Project "${newProjectName.value}" created successfully!`);
    // Navigate immediately to the new project
    router.push({ name: 'ide', params: { projectID: newProjectName.value } });
    // Optionally, refresh the project list (though immediate navigation might make it unnecessary)
    // await fetchProjects(); // You'd need to extract fetching into a separate function

  } catch (error: any) {
    console.error('Failed to create project:', error);
    createApiError.value = `Failed to create project: ${error.message || 'Unknown error'}`;
    // Handle specific errors, e.g., project already exists
  } finally {
    isCreatingProject.value = false;
  }
};

const navigateToProject = (project: ProjectListItem) => {
  // Use project_id for navigation parameter
  router.push({ name: 'ide', params: { projectID: project.project_id } }); 
};

// Custom row class name for hover effect (optional)
const tableRowClassName = ({ row, rowIndex }: { row: ProjectListItem; rowIndex: number }) => {
  return 'project-table-row cursor-pointer';
};

// Delete Project Methods
const confirmDeleteProject = (project: ProjectListItem) => {
  projectToDelete.value = project;
  deleteApiError.value = null;
  showDeleteDialog.value = true;
};

const handleDeleteProject = async () => {
  if (!projectToDelete.value) return;
  
  deleteApiError.value = null;
  isDeletingProject.value = projectToDelete.value.project_id;
  
  try {
    await deleteProject(projectToDelete.value.project_id);
    
    // Remove from local projects list
    projects.value = projects.value.filter(p => p.project_id !== projectToDelete.value!.project_id);
    
    showDeleteDialog.value = false;
    ElMessage.success(`Project "${projectToDelete.value.name || projectToDelete.value.project_id}" deleted successfully!`);
    
  } catch (error: any) {
    console.error('Failed to delete project:', error);
    deleteApiError.value = `Failed to delete project: ${error.message || 'Unknown error'}`;
  } finally {
    isDeletingProject.value = null;
  }
};

</script>

<style>
/* Global overrides for dialog in dark mode */
.el-dialog.create-dialog, .el-dialog.delete-dialog {
  --el-dialog-bg-color: #262626 !important;
  border-radius: 8px !important;
}
.el-dialog.create-dialog .el-dialog__title, .el-dialog.delete-dialog .el-dialog__title {
  color: #e0e0e0 !important;
}
.el-dialog.create-dialog .el-dialog__headerbtn .el-icon, .el-dialog.delete-dialog .el-dialog__headerbtn .el-icon {
   color: #e0e0e0 !important;
}
.el-dialog.create-dialog .el-dialog__body, .el-dialog.delete-dialog .el-dialog__body {
    color: #cccccc !important;
    padding-top: 10px; /* Adjust padding */
    padding-bottom: 20px;
}
</style>

<style scoped>
/* Ensure consistent dark theme for table */
.project-table :deep(th),
.project-table :deep(td) {
  background-color: #262626 !important;
  color: #e0e0e0 !important;
  border-bottom: 1px solid #4d4d54 !important; /* Match divider color */
}

.project-table :deep(th) {
   font-weight: 600;
   color: #f0f0f0 !important; /* Header text slightly brighter */
}

/* Remove default table border */
.project-table :deep(.el-table__border-left-patch),
.project-table :deep(th.el-table__cell.is-leaf),
.project-table :deep(td.el-table__cell) {
    border-left: none;
    border-right: none;
}
.project-table :deep(.el-table__inner-wrapper::before) {
    content: none; /* Remove top border pseudo element */
}

/* Hover effect for rows */
.project-table :deep(.project-table-row:hover > td) {
  background-color: #333338 !important;
}

.el-empty :deep(.el-empty__description p) {
   color: #a0a0a0; /* Adjust empty state text color */
}

/* Input field styles within dialog (scoped) */
:deep(.el-input__wrapper) {
  background-color: #333338 !important;
  box-shadow: none !important;
  border: 1px solid #555555 !important;
  border-radius: 4px !important;
}
:deep(.el-input__wrapper.is-focus) {
  border-color: #5a8dee !important;
}

:deep(.el-input__inner) {
  color: #f0f0f0 !important;
}

:deep(.el-input__inner::placeholder) {
    color: #888888 !important;
    opacity: 1;
}

/* Label style within dialog */
:deep(label[for="newProjectName"]) {
   color: #e0e0e0 !important;
}
</style> 