import { defineStore } from 'pinia';
import { ref, computed, nextTick } from 'vue';
import * as fsApi from '@/api/fs.js';
import type { FileSystemObject, FileChangeEvent, FileSystemWatchSSEEvent } from '@/api/types.js';
import { ElMessage, ElMessageBox } from 'element-plus';
// @ts-ignore
import { fetchEventSource, type EventSourceMessage } from '@microsoft/fetch-event-source';

// Define state structure for an open file
interface OpenFileState {
  originalContent: string | null;
  currentContent: string | null;
  loading: boolean;
  error: string | null;
  // File status for conflict detection
  isDeleted?: boolean; // File has been deleted externally
  isRenamed?: boolean; // File has been renamed externally
  externallyModified?: boolean; // File has been modified externally
  // isDirty can be a getter
}

// Define FsState structure
interface FsState {
  currentProjectID: string | null;
  activeFilePath: string | null; // Path of the currently active/focused file in editor
  tree: Record<string, FileSystemObject[]>;
  openFiles: Record<string, OpenFileState>; // Use the new OpenFileState
  loadingState: Record<string, boolean>;
  errorState: Record<string, string | null>;
  // File system watching
  fileWatchController: AbortController | null;
  isFileWatchConnected: boolean;
  fileWatchError: string | null;
}

export const useFsStore = defineStore('filesystem', () => {
  // State
  const currentProjectID = ref<FsState['currentProjectID']>(null);
  const activeFilePath = ref<FsState['activeFilePath']>(null);
  const tree = ref<FsState['tree']>({});
  const openFiles = ref<FsState['openFiles']>({});
  const loadingState = ref<FsState['loadingState']>({});
  const errorState = ref<FsState['errorState']>({});
  
  // File system watching state
  const fileWatchController = ref<FsState['fileWatchController']>(null);
  const isFileWatchConnected = ref<FsState['isFileWatchConnected']>(false);
  const fileWatchError = ref<FsState['fileWatchError']>(null);

  // Getters
  const isLoadingTree = computed(() => (path: string = "") => loadingState.value[path] ?? false);
  const treeError = computed(() => (path: string = "") => errorState.value[path]);

  // Getters for active file
  const activeFileDetails = computed((): OpenFileState | null => {
    if (activeFilePath.value && openFiles.value[activeFilePath.value]) {
      return openFiles.value[activeFilePath.value];
    }
    return null;
  });

  const getCurrentFileContent = computed((): string | null => activeFileDetails.value?.currentContent ?? null);
  const isFileLoading = computed(() => (path: string) => openFiles.value[path]?.loading ?? false); // Keep for general file loading
  const getFileError = computed(() => (path: string) => openFiles.value[path]?.error); // Keep for general file errors

  const isCurrentFileDirty = computed((): boolean => {
    if (!activeFileDetails.value) return false;
    // Ensure originalContent is not null to consider it a modification from a loaded state
    return activeFileDetails.value.originalContent !== null && activeFileDetails.value.currentContent !== activeFileDetails.value.originalContent;
  });

  const canSaveCurrentFile = computed((): boolean => {
    return !!currentProjectID.value && !!activeFilePath.value && isCurrentFileDirty.value;
  });

  // Actions
  const setCurrentProjectID = (projectID: string | null) => {
    console.log('Setting current project ID:', projectID);
    if (currentProjectID.value !== projectID) {
      // Stop any existing file watching
      stopFileWatching();
      
      currentProjectID.value = projectID;
      tree.value = {};
      openFiles.value = {};
      loadingState.value = {};
      errorState.value = {};
      activeFilePath.value = null; // Reset active file when project changes
      
      if (projectID) {
        console.log('Project ID set, starting file watching...');
        // Start file watching for the new project
        setTimeout(() => {
          startFileWatching();
        }, 1000); // Small delay to ensure project is properly set up
      }
    }
  };

  const setActiveFile = (filePath: string | null) => {
    console.log('[Store setActiveFile] Setting active file path to:', filePath);
    activeFilePath.value = filePath;
    // Optionally, if the file isn't in openFiles yet and filePath is not null,
    // you might want to trigger a fetchFileContent here.
    // For now, we assume FileBrowser.vue handles opening/fetching.
  };

  const fetchDirectory = async (path: string = "") => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      errorState.value = { ...errorState.value, [path]: 'No project selected.' };
      return;
    }
    const relativePath = path;
    console.log(`Store: Fetching directory for project ${projectID}, relative path: '${relativePath}'`);
    loadingState.value = { ...loadingState.value, [relativePath]: true };
    errorState.value = { ...errorState.value, [relativePath]: null };

    try {
      const items = await fsApi.listDirectory(projectID, relativePath);
      items.sort((a: FileSystemObject, b: FileSystemObject) => {
        if (a.is_dir && !b.is_dir) return -1;
        if (!a.is_dir && b.is_dir) return 1;
        return a.name.localeCompare(b.name);
      });
      tree.value = { ...tree.value, [relativePath]: items };
    } catch (err: any) {
      console.error(`Store: Error fetching directory '${relativePath}':`, err);
      errorState.value = { ...errorState.value, [relativePath]: err.message || 'Failed to fetch directory.' };
    } finally {
      loadingState.value = { ...loadingState.value, [relativePath]: false };
    }
  };

  const fetchFileContent = async (filePath: string) => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      openFiles.value = { ...openFiles.value, [filePath]: { originalContent: null, currentContent: null, loading: false, error: 'No project selected.' } };
      return;
    }
    if (!filePath) {
      openFiles.value = { ...openFiles.value, [filePath]: { originalContent: null, currentContent: null, loading: false, error: 'Invalid file path.' } };
      return;
    }
    console.log(`[Store fetchFileContent] Start: Fetching content for project ${projectID}, relative path: '${filePath}'`);
    // Ensure the entry exists with loading state
    openFiles.value[filePath] = {
        ...(openFiles.value[filePath] || { originalContent: null, currentContent: null }),
        loading: true,
        error: null
    };

    try {
      // fsApi.readFileContent is expected to return decoded content
      const decodedContent = await fsApi.readFileContent(projectID, filePath);
      console.log(`[Store fetchFileContent] Received decoded content from API for '${filePath}'`);
      openFiles.value[filePath] = {
        originalContent: decodedContent,
        currentContent: decodedContent,
        loading: false,
        error: null
      };
      // If this fetched file is meant to be the active one, set it.
      // setActiveFile(filePath); // Or ensure FileBrowser does this after successful fetch.
    } catch (err: any) {
      console.error(`[Store fetchFileContent] Error fetching file content for '${filePath}':`, err);
      openFiles.value[filePath] = {
        ...(openFiles.value[filePath] || { originalContent: null, currentContent: null }),
        loading: false,
        error: err.message || 'Failed to fetch file.'
      };
    }
  };

  /**
   * Updates the current content of an already opened file.
   * Typically called when the user types in the editor.
   * @param payload - Object containing path and newContent.
   */
  const updateOpenFileContent = (payload: { path: string; newContent: string }) => {
    const { path, newContent } = payload;
    if (openFiles.value[path]) {
      openFiles.value[path].currentContent = newContent;
      console.log(`[Store updateOpenFileContent] Content updated for ${path}. New isDirty: ${openFiles.value[path].currentContent !== openFiles.value[path].originalContent}`);
    } else {
      console.warn(`[Store updateOpenFileContent] Attempted to update content for a file not in openFiles: ${path}`);
    }
  };

  /**
   * Saves the content of the currently active file to the backend.
   */
  const saveActiveFile = async () => {
    if (!currentProjectID.value || !activeFilePath.value) {
      ElMessage.error('No active file or project selected to save.');
      console.error('[Store saveActiveFile] No projectID or activeFilePath.');
      return;
    }

    const fileState = activeFileDetails.value;
    if (!fileState) {
        ElMessage.error('Could not find active file details to save.');
        console.error('[Store saveActiveFile] No fileState for activeFilePath:', activeFilePath.value);
        return;
    }

    // Check for file conflicts
    if (fileState.isDeleted) {
      ElMessage.error(`Cannot save "${getFilename(activeFilePath.value)}": File has been deleted externally.`);
      return;
    }

    if (fileState.externallyModified) {
      // Show conflict warning and ask user what to do
      try {
        await ElMessageBox.confirm(
          `File "${getFilename(activeFilePath.value)}" has been modified externally. Saving will overwrite the external changes. Do you want to continue?`,
          'File Conflict',
          {
            confirmButtonText: 'Overwrite',
            cancelButtonText: 'Cancel',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        );
        // User chose to overwrite - clear the external modification flag
        fileState.externallyModified = false;
      } catch (error) {
        // User cancelled the save
        console.log('[Store saveActiveFile] Save cancelled due to file conflict');
        return;
      }
    }

    if (!isCurrentFileDirty.value) {
      ElMessage.info('No changes to save.');
      console.log('[Store saveActiveFile] No changes detected (isCurrentFileDirty is false).');
      return;
    }

    if (fileState.currentContent === null) { // Should not happen if dirty is true based on originalContent not null
        ElMessage.error('Cannot save null content.');
        console.error('[Store saveActiveFile] currentContent is null for active file.');
        return;
    }

    const path = activeFilePath.value;
    const contentToSave = fileState.currentContent;

    // Optimistic: can set loading state here if desired
    // openFiles.value[path] = { ...fileState, loading: true };

    try {
      ElMessage({ message: `Saving ${getFilename(path)}...`, type: 'info', duration: 1500 });
      await fsApi.saveFileContent(currentProjectID.value, path, contentToSave);
      // Update originalContent to match currentContent after successful save
      openFiles.value[path].originalContent = contentToSave;
      // Clear any conflict flags
      openFiles.value[path].isDeleted = false;
      openFiles.value[path].isRenamed = false;
      openFiles.value[path].externallyModified = false;
      // openFiles.value[path].loading = false; // if loading state was set
      ElMessage.success(`${getFilename(path)} saved successfully!`);
      console.log(`[Store saveActiveFile] File ${path} saved successfully.`);
    } catch (err: any) {
      console.error(`[Store saveActiveFile] Error saving file ${path}:`, err);
      // openFiles.value[path].loading = false; // if loading state was set
      // openFiles.value[path].error = err.message || 'Failed to save file.';
      ElMessage.error(err.message || `Failed to save ${getFilename(path)}.`);
    }
  };

  const createFSObject = async (path: string, isDir: boolean, content?: string /* Added optional content for file creation */) => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      errorState.value = { ...errorState.value, [path]: 'No project selected.' };
      console.error('Cannot create FS object: No project selected.');
      return;
    }
    if (!path) {
        errorState.value = { ...errorState.value, [path]: 'Invalid path provided.' };
        console.error('Cannot create FS object: Invalid path.');
        return;
    }
    const relativePath = path;
    const itemType = isDir ? 'directory' : 'file';
    loadingState.value = { ...loadingState.value, [relativePath]: true };
    errorState.value = { ...errorState.value, [relativePath]: null };

    try {
      console.log(`Store: Attempting to create ${itemType} at relative path: ${relativePath} in project: ${projectID}`);
      // Pass relative path and optional content (already base64 encoded by caller if file)
      await fsApi.createFSObject(projectID, relativePath, isDir, content);
      console.log(`Store: Successfully created ${itemType} at ${relativePath}. Refreshing parent directory.`);

      const parentPath = getParentPath(relativePath);
      await fetchDirectory(parentPath);

    } catch (err: any) {
      console.error(`Store: Error creating ${itemType} at ${relativePath}:`, err);
      errorState.value = { ...errorState.value, [relativePath]: err.message || `Failed to create ${itemType}.` };
    } finally {
      loadingState.value = { ...loadingState.value, [relativePath]: false };
    }
  };

  const deleteObject = async (path: string) => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      errorState.value = { ...errorState.value, [path]: 'No project selected.' };
      console.error('Cannot delete FS object: No project selected.');
      throw new Error('No project selected.');
    }
    if (!path) {
        errorState.value = { ...errorState.value, [path]: 'Invalid path provided.' };
        console.error('Cannot delete FS object: Invalid path.');
        throw new Error('Invalid path provided.');
    }
    const relativePath = path;
    errorState.value = { ...errorState.value, [relativePath]: null };

    try {
      console.log(`Store: Attempting to delete ${relativePath} in project: ${projectID}`);
      await fsApi.deleteFSObject(projectID, relativePath);
      console.log(`Store: Successfully deleted ${relativePath}. Refreshing parent directory.`);

      const parentPath = getParentPath(relativePath);
      if (tree.value[parentPath]) {
        tree.value[parentPath] = tree.value[parentPath].filter(item => item.path !== relativePath);
      }
      if (openFiles.value[relativePath]) {
        delete openFiles.value[relativePath];
        if (activeFilePath.value === relativePath) {
          setActiveFile(null);
        }
      }
    } catch (err: any) {
      console.error(`Store: Error deleting ${relativePath}:`, err);
      errorState.value = { ...errorState.value, [relativePath]: err.message || `Failed to delete.` };
      throw err;
    }
  };

  const renameObject = async (oldPath: string, newPath: string) => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      errorState.value = { ...errorState.value, [oldPath]: 'No project selected.' };
      console.error('Cannot rename FS object: No project selected.');
      throw new Error('No project selected.');
    }
    if (!oldPath || !newPath) {
        errorState.value = { ...errorState.value, [oldPath]: 'Invalid path provided.' };
        console.error('Cannot rename FS object: Invalid path(s).');
        throw new Error('Invalid path(s) provided.');
    }
     if (oldPath === newPath) {
        console.warn('RenameObject: oldPath and newPath are the same.');
        return;
    }

    errorState.value = { ...errorState.value, [oldPath]: null, [newPath]: null };

    try {
      console.log(`Store: Attempting to rename ${oldPath} to ${newPath} in project: ${projectID}`);
      await fsApi.renameFSObject(projectID, oldPath, newPath);
      console.log(`Store: Successfully renamed ${oldPath} to ${newPath}. Updating state.`);

      const oldParentPath = getParentPath(oldPath);
      const newParentPath = getParentPath(newPath);

      // Update tree for old parent
      if (tree.value[oldParentPath]) {
        tree.value[oldParentPath] = tree.value[oldParentPath].filter(item => item.path !== oldPath);
      }
      // Fetch new parent to get the updated item details (or construct locally if preferred)
      await fetchDirectory(newParentPath);
      // If old and new parent are different, also fetch old parent to reflect removal
      if (oldParentPath !== newParentPath) {
          await fetchDirectory(oldParentPath);
      }

      // Update openFiles and activeFilePath if the renamed file was open/active
      if (openFiles.value[oldPath]) {
        const fileData = openFiles.value[oldPath];
        delete openFiles.value[oldPath];
        openFiles.value[newPath] = fileData;
        if (activeFilePath.value === oldPath) {
          setActiveFile(newPath);
        }
      }
    } catch (err: any) {
      console.error(`Store: Error renaming ${oldPath} to ${newPath}:`, err);
      errorState.value = { ...errorState.value, [oldPath]: err.message || `Failed to rename.` };
      throw err;
    }
  };

  function getParentPath(relativePath: string): string {
    if (!relativePath || relativePath === '/') return "";
    const lastSlash = relativePath.lastIndexOf('/');
    if (lastSlash === -1) return "";
    if (lastSlash === 0 && relativePath.length > 1) return "/"; // handles /file.txt, parent is root ""
    if (lastSlash === 0 && relativePath.length === 1) return ""; // path is just "/", has no parent in this context
    return relativePath.substring(0, lastSlash);
  }

  function getFilename(filePath: string): string {
    if (!filePath) return "";
    return filePath.substring(filePath.lastIndexOf('/') + 1);
  }
  
  // --- File System Watching Methods ---
  
  /**
   * Starts file system watching for the current project
   */
  const startFileWatching = async () => {
    const projectID = currentProjectID.value;
    if (!projectID) {
      console.warn('[fsStore] Cannot start file watching: No project selected');
      return;
    }
    
    // Stop any existing watcher
    stopFileWatching();
    
    const controller = new AbortController();
    fileWatchController.value = controller;
    fileWatchError.value = null;
    
    const baseURL = import.meta.env.VITE_API_BASE_URL || '';
    const watchURL = `${baseURL}/projects/${projectID}/watch`;
    
    console.log('[fsStore] Starting file system watching for project:', projectID);
    
    try {
      await fetchEventSource(watchURL, {
        method: 'GET',
        signal: controller.signal,
        credentials: 'include',
        
        async onopen(response: Response) {
          if (response.ok) {
            console.log('[fsStore] File watch SSE connection opened');
            isFileWatchConnected.value = true;
            fileWatchError.value = null;
          } else {
            throw new Error(`Failed to open file watch connection: ${response.status} ${response.statusText}`);
          }
        },
        
        onmessage(msg: EventSourceMessage) {
          try {
            console.log(`[fsStore] File watch SSE message: type='${msg.event}', data=${msg.data}`);
            const eventType = msg.event as keyof { 'file_change': any, 'error': any, 'connected': any };
            const eventData = JSON.parse(msg.data);
            
            handleFileWatchEvent({ type: eventType, data: eventData } as FileSystemWatchSSEEvent);
          } catch (err) {
            console.error('[fsStore] Failed to parse file watch SSE event:', err, 'Raw data:', msg.data);
          }
        },
        
        onclose() {
          console.log('[fsStore] File watch SSE connection closed');
          isFileWatchConnected.value = false;
        },
        
        onerror(err: any) {
          console.error('[fsStore] File watch SSE error:', err);
          fileWatchError.value = err?.message || 'File watching connection error';
          isFileWatchConnected.value = false;
          // Note: fetchEventSource will automatically retry on errors
        }
      });
    } catch (error: any) {
      console.error('[fsStore] Failed to start file watching:', error);
      fileWatchError.value = error.message || 'Failed to start file watching';
      isFileWatchConnected.value = false;
    }
  };
  
  /**
   * Stops file system watching
   */
  const stopFileWatching = () => {
    if (fileWatchController.value) {
      console.log('[fsStore] Stopping file system watching');
      fileWatchController.value.abort();
      fileWatchController.value = null;
    }
    isFileWatchConnected.value = false;
    fileWatchError.value = null;
  };
  
  /**
   * Handles file system watch events
   */
  const handleFileWatchEvent = (event: FileSystemWatchSSEEvent) => {
    console.log('[fsStore] Processing file watch event:', event);
    
    switch (event.type) {
      case 'connected':
        console.log('[fsStore] File watch connected:', event.data);
        break;
        
      case 'error':
        console.error('[fsStore] File watch error:', event.data);
        fileWatchError.value = (event.data as { message: string }).message;
        break;
        
      case 'file_change':
        handleFileChangeEvent(event.data as FileChangeEvent);
        break;
        
      default:
        console.warn('[fsStore] Unknown file watch event type:', event.type);
    }
  };
  
  /**
   * Handles individual file change events
   */
  const handleFileChangeEvent = async (changeEvent: FileChangeEvent) => {
    const { type, path, old_path, is_dir } = changeEvent;
    console.log(`[fsStore] File change: ${type} - ${path}${old_path ? ` (from ${old_path})` : ''} (${is_dir ? 'dir' : 'file'})`);
    
    try {
      // 1. Refresh file browser for the parent directory
      const parentPath = getParentPath(path);
      console.log(`[fsStore] Refreshing directory: ${parentPath}`);
      await fetchDirectory(parentPath);
      
      // If it's a rename, also refresh the old parent directory
      if (type === 'rename' && old_path) {
        const oldParentPath = getParentPath(old_path);
        if (oldParentPath !== parentPath) {
          console.log(`[fsStore] Refreshing old directory: ${oldParentPath}`);
          await fetchDirectory(oldParentPath);
        }
      }
      
      // 2. Handle open file changes
      if (!is_dir) {
        await handleOpenFileChange(changeEvent);
      }
      
      // Show user notification for important changes
      showFileChangeNotification(changeEvent);
      
    } catch (error) {
      console.error('[fsStore] Error handling file change event:', error);
    }
  };
  
  /**
   * Handles changes to files that are currently open in the editor
   */
  const handleOpenFileChange = async (changeEvent: FileChangeEvent) => {
    const { type, path, old_path } = changeEvent;
    
    // Check if the changed file is currently open
    let affectedFilePath: string | null = null;
    
    if (type === 'rename' && old_path && openFiles.value[old_path]) {
      // File was renamed and was open under the old path
      affectedFilePath = old_path;
    } else if (openFiles.value[path]) {
      // File was modified/deleted and is currently open
      affectedFilePath = path;
    }
    
    if (!affectedFilePath) {
      return; // File is not currently open
    }
    
    const fileState = openFiles.value[affectedFilePath];
    
    switch (type) {
      case 'delete':
        console.log(`[fsStore] Open file deleted: ${affectedFilePath}`);
        fileState.isDeleted = true;
        fileState.externallyModified = false;
        ElMessage.warning(`File "${getFilename(affectedFilePath)}" has been deleted externally`);
        break;
        
      case 'rename':
        console.log(`[fsStore] Open file renamed: ${affectedFilePath} -> ${path}`);
        if (old_path) {
          // Move the file state to the new path
          openFiles.value[path] = { ...fileState, isRenamed: true };
          delete openFiles.value[old_path];
          
          // Update active file path if necessary
          if (activeFilePath.value === old_path) {
            activeFilePath.value = path;
          }
          
          ElMessage.info(`File "${getFilename(old_path)}" has been renamed to "${getFilename(path)}"`);
        }
        break;
        
      case 'modify':
        console.log(`[fsStore] Open file modified externally: ${affectedFilePath}`);
        // Check if user has unsaved changes
        const isDirty = fileState.currentContent !== fileState.originalContent;
        
        if (isDirty) {
          // User has unsaved changes - mark as externally modified for conflict resolution
          fileState.externallyModified = true;
          ElMessage.warning(`File "${getFilename(affectedFilePath)}" has been modified externally. Your changes may conflict.`);
        } else {
          // No local changes - safely reload the file content
          console.log(`[fsStore] Reloading externally modified file: ${affectedFilePath}`);
          await fetchFileContent(affectedFilePath);
          ElMessage.info(`File "${getFilename(affectedFilePath)}" has been updated`);
        }
        break;
        
      case 'create':
        // File was created - this shouldn't affect open files normally
        break;
    }
  };
  
  /**
   * Shows user notifications for file changes
   */
  const showFileChangeNotification = (changeEvent: FileChangeEvent) => {
    const { type, path, is_dir } = changeEvent;
    const itemType = is_dir ? 'folder' : 'file';
    const itemName = getFilename(path);
    
    // Only show notifications for non-trivial changes and avoid spam
    switch (type) {
      case 'create':
        if (is_dir) {
          console.log(`[fsStore] New ${itemType} created: ${itemName}`);
          // Could show a subtle notification here if desired
        }
        break;
      case 'delete':
        if (is_dir) {
          ElMessage.info(`${itemType} "${itemName}" has been deleted`);
        }
        break;
      // Modify and rename notifications are handled in handleOpenFileChange
    }
  };
  
  /**
   * Closes an open file, removing it from the openFiles state.
   * If the closed file was the active file, activeFilePath is reset.
   * @param filePath - The path of the file to close.
   */
  const closeFile = (filePath: string) => {
    if (!filePath) {
      console.warn('[Store closeFile] Attempted to close a file with an invalid path.');
      return;
    }

    if (openFiles.value[filePath]) {
      delete openFiles.value[filePath];
      console.log(`[Store closeFile] File closed: ${filePath}`);

      if (activeFilePath.value === filePath) {
        activeFilePath.value = null;
        console.log('[Store closeFile] Active file was closed, activeFilePath reset.');
      }
    } else {
      console.warn(`[Store closeFile] Attempted to close a file not currently open: ${filePath}`);
    }
  };

  return {
    currentProjectID,
    activeFilePath,
    tree,
    openFiles,
    loadingState,
    errorState,
    // File watching state
    isFileWatchConnected,
    fileWatchError,
    // Getters
    isLoadingTree,
    treeError,
    getCurrentFileContent, // Updated getter
    isFileLoading,
    getFileError,
    activeFileDetails,    // New getter
    isCurrentFileDirty,   // New getter
    canSaveCurrentFile,   // New getter
    // Actions
    setCurrentProjectID,
    setActiveFile,        // New action
    fetchDirectory,
    fetchFileContent,
    updateOpenFileContent,// New action
    saveActiveFile,       // New action
    createFSObject,
    deleteObject,
    renameObject,
    closeFile,            // Expose the new action
    // File watching actions
    startFileWatching,
    stopFileWatching,
    getRawFileState: (path: string) => openFiles.value[path],
  };
}); 