import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  AgentConversation,
  AgentMessage,
  SendMessageRequest,
  SSEChatEvent, // This type is based on SSEChatEventDataMap
  SSEChatEventDataMap, // For direct use in parsing
  FetchEventSourceController // New import
} from '@/api/agent.js' // Assuming FetchEventSourceController is exported from api/agent.ts
import {
  getProjectConversation,
  // sendMessage, // Old sendMessage is not used
  clearConversation,
  exportConversation,
  // createAgentStream, // Old createAgentStream is not used
  sendMessageAndStream // New API function
} from '@/api/agent.js'
import type { EventSourceMessage } from '@microsoft/fetch-event-source'; // For typing msg in onMessage
import { useFsStore } from './fs.js' // Import to trigger file system refresh

// File system related tool names that should trigger refresh
const FILE_SYSTEM_TOOLS = [
  'mcp.projectfs.create_fs_object',
  'mcp.projectfs.write_file', 
  'mcp.projectfs.delete_fs_object',
  'mcp.projectfs.rename_fs_object',
  'mcp.projectfs.replace_in_file'
]

export const useAgentStore = defineStore('agent', () => {
  // State
  const currentConversation = ref<AgentConversation | null>(null)
  const isLoading = ref(false)
  const isStreaming = ref(false)
  const error = ref<string | null>(null)
  const eventSourceController = ref<FetchEventSourceController | null>(null); // ADDED
  const lastMessageError = ref<{ messageId: string; error: any } | null>(null)

  // Computed: Directly return the messages from the conversation.
  // This is much more efficient as it doesn't create a new array on every change.
  const messages = computed(() => {
    return currentConversation.value?.messages || []
  })

  const hasConversation = computed(() => currentConversation.value !== null)

  // Helper function to detect if content contains file system tool results
  const detectFileSystemToolResult = (content: string): boolean => {
    if (!content) return false
    
    console.log('[AgentStore] Checking content for file system tools:', content.substring(0, 200) + (content.length > 200 ? '...' : ''))
    
    // Check for tool result pattern: <tool_result tool_name="xxx">...</tool_result>
    const toolResultPattern = /<tool_result\s+tool_name="([^"]+)">.*?<\/tool_result>/gs
    const matches = content.matchAll(toolResultPattern)
    
    let foundFileSystemTool = false
    for (const match of matches) {
      const toolName = match[1]
      console.log('[AgentStore] Found tool result:', toolName)
      if (FILE_SYSTEM_TOOLS.includes(toolName)) {
        console.log('[AgentStore] File system tool detected:', toolName)
        foundFileSystemTool = true
      } else {
        console.log('[AgentStore] Non-file-system tool detected:', toolName)
      }
    }
    
    // Also check for use_mcp_tool patterns in case tools are being called
    const toolCallPattern = /<use_mcp_tool>[\s\S]*?<tool_name>([^<]+)<\/tool_name>[\s\S]*?<\/use_mcp_tool>/gs
    const toolCallMatches = content.matchAll(toolCallPattern)
    
    for (const match of toolCallMatches) {
      const toolName = match[1]?.trim()
      const fullToolName = `mcp.projectfs.${toolName}`
      console.log('[AgentStore] Found tool call:', fullToolName)
      if (FILE_SYSTEM_TOOLS.includes(fullToolName)) {
        console.log('[AgentStore] File system tool call detected:', fullToolName)
        foundFileSystemTool = true
      }
    }
    
    console.log('[AgentStore] File system tool detection result:', foundFileSystemTool)
    return foundFileSystemTool
  }

  // Helper function to trigger file system refresh
  const triggerFileSystemRefresh = async (projectId: string): Promise<void> => {
    try {
      console.log('[AgentStore] triggerFileSystemRefresh called for project:', projectId)
      const fsStore = useFsStore()
      console.log('[AgentStore] Current fs store project ID:', fsStore.currentProjectID)
      
      if (fsStore.currentProjectID === projectId) {
        console.log('[AgentStore] Project IDs match, triggering file system refresh for project:', projectId)
        // Refresh root directory
        await fsStore.fetchDirectory('')
        console.log('[AgentStore] File system refresh completed successfully')
      } else {
        console.log('[AgentStore] Project IDs do not match, skipping refresh. Expected:', projectId, 'Current:', fsStore.currentProjectID)
      }
    } catch (err) {
      console.error('[AgentStore] Failed to refresh file system:', err)
    }
  }

  // Actions
  const loadConversation = async (projectId: string): Promise<void> => {
    if (!projectId) return
    try {
      isLoading.value = true
      error.value = null
      const conversation = await getProjectConversation(projectId)
      currentConversation.value = conversation
      console.log('[AgentStore] Loaded conversation:', conversation)
      
      // Check if the last message is a user message and set lastMessageError for retry
      if (conversation && conversation.messages.length > 0) {
        const lastMessage = conversation.messages[conversation.messages.length - 1]
        if (lastMessage.role === 'user') {
          // If the last message is from the user, it means there might have been an error
          // and no assistant response was received, so we should show the retry button
          lastMessageError.value = {
            messageId: lastMessage.msg_id || '',
            error: 'Previous message may have failed to receive response'
          }
          console.log('[AgentStore] Last message is user message, enabling retry:', lastMessage.msg_id)
        } else {
          // Clear any existing error state if the last message is from assistant
          lastMessageError.value = null
        }
      }
    } catch (err) {
      console.error('[AgentStore] Failed to load conversation:', err)
      error.value = err instanceof Error ? err.message : 'Failed to load conversation'
    } finally {
      isLoading.value = false
    }
  }

  const sendUserMessage = async (projectId: string, data: SendMessageRequest): Promise<void> => {
    if (!projectId || !data.message.trim()) return

    console.log('[AgentStore] sendUserMessage called with:', {
      projectId,
      message: data.message.substring(0, 100) + (data.message.length > 100 ? '...' : ''),
      messageLength: data.message.length
    })

    // Ensure any previous stream is stopped
    if (isStreaming.value) {
      console.log('[AgentStore] Stopping previous stream before starting new one')
      stopStreaming();
    }
    lastMessageError.value = null

    try {
      isLoading.value = true;
      isStreaming.value = true;
      error.value = null;

      // Add user message immediately to the conversation display, only if it's not a retry
      if (!data.retry_message_id) {
        const userMessage: AgentMessage = {
          msg_id: `user-${Date.now()}`, // Temporary client-side ID, will be replaced by server-side msg_id
          role: 'user',
          msg_type: 'user_input',
          content: data.message,
          timestamp: new Date().toISOString(),
          status: 'complete',
        };
        if (currentConversation.value) {
          currentConversation.value.messages.push(userMessage);
        } else {
          // Initialize conversation if it's null, though loadConversation should typically precede this
          currentConversation.value = {
            project_id: projectId,
            conversation_id: '', // Will be set by first agent message or backend
            messages: [userMessage],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            message_count: 1
          };
        }
      }

      console.log('[AgentStore] Sending message and starting stream via POST...');

      eventSourceController.value = sendMessageAndStream(
        projectId,
        data,
        async () => { // onOpen
          console.log('[AgentStore] SSE connection opened via POST.');
          // isLoading.value could be set to false here if we only care about connection opening
          // but usually, we wait for the full stream.
        },
        (msg: EventSourceMessage) => { // onMessage
          console.log('[AgentStore] Raw SSE EventSourceMessage:', msg);
          try {
            // msg.event is the event type string (e.g., 'message_start')
            // msg.data is the JSON string payload
            const eventType = msg.event as keyof SSEChatEventDataMap;
            const eventData = JSON.parse(msg.data);
            
            // Reconstruct an SSEChatEvent-like object for handleStreamEvent if preferred,
            // or handle directly here. For now, passing to handleStreamEvent.
            // Ensure the structure passed to handleStreamEvent matches what it expects.
            // The `type` will be from msg.event, `data` from JSON.parse(msg.data)
            handleStreamEvent({ type: eventType, data: eventData } as any, projectId);

          } catch (err) {
            console.error('[AgentStore] Failed to parse SSE event data:', err, 'Raw data:', msg.data);
            // Potentially set an error state or dispatch an error event
          }
        },
        (err: any) => { // onError
          console.error('[AgentStore] SSE stream error:', err);
          error.value = err?.message || 'Streaming error occurred';
          handleStreamError(err)
          // stopStreaming(); // Stop on error. handleStreamError will call it.
        },
        () => { // onClose
          console.log('[AgentStore] SSE connection closed by server or abort.');
          // This is called when the stream is properly closed by the server end or by abort.
          // `message_complete` from the server should ideally trigger the final state updates.
          // stopStreaming() might be called by message_complete or error, so be mindful of double calls.
          if (isStreaming.value) { // If still marked as streaming, then it was an unexpected close.
             // stopStreaming(); // Redundant if message_complete or error handles this.
          }
        }
      );

    } catch (err) {
      console.error('[AgentStore] Failed to send message or initiate stream:', err);
      error.value = err instanceof Error ? err.message : 'Failed to send/stream message';
      isLoading.value = false;
      isStreaming.value = false;
      if (eventSourceController.value) {
        eventSourceController.value.abort();
        eventSourceController.value = null;
      }
    }
  }

  const handleStreamEvent = (event: SSEChatEvent, projectId: string): void => {
    // This function now receives an object that looks like our defined SSEChatEvent
    // It uses event.type and event.data (which is already parsed JSON)
    console.log('[AgentStore] Handling Parsed SSE Event:', event.type, event.data);

    switch (event.type) {
      case 'connected':
        console.log('[AgentStore] SSE connection established (from handleStreamEvent):', event.data.message)
        break

      case 'keepalive':
        console.log('[AgentStore] SSE keepalive (from handleStreamEvent):', event.data.timestamp)
        break

      case 'message_start': {
        const { message_id, conversation_id } = event.data;
        console.log('[AgentStore] Message start, message_id:', message_id, 'conversation_id:', conversation_id);
        
        if (currentConversation.value) {
          if (currentConversation.value.conversation_id === '' && conversation_id) {
            currentConversation.value.conversation_id = conversation_id;
          }
            const existingMessageIndex = currentConversation.value.messages.findIndex(m => m.msg_id === message_id);
            if (existingMessageIndex === -1) {
              currentConversation.value.messages.push({
                msg_id: message_id,
              role: 'assistant', // Default role, will be updated by content_delta
              msg_type: 'assistant_output', // Default type
                content: '', // Start with empty content
                timestamp: new Date().toISOString(),
              status: 'streaming',
            });
          }
        }
        break;
      }

      case 'content_delta': {
        const { chunk } = event.data;
        const { Delta, MessageID, Marker, Error: errorFromChunk, Role, MsgType } = chunk;

        if (currentConversation.value) {
          if (errorFromChunk) {
            handleStreamError(errorFromChunk)
            return
          }

          let targetMessage = currentConversation.value.messages.find(m => m.msg_id === MessageID);

          if (!targetMessage) {
            // Do not create a new assistant message if the first chunk is empty or just a marker.
            // A placeholder is created on 'message_start' now.
            if (!Delta) return;

            targetMessage = {
                msg_id: MessageID,
              role: Role || 'assistant',
              msg_type: MsgType,
                content: '',
                timestamp: new Date().toISOString(),
              status: 'streaming',
            };
            currentConversation.value.messages.push(targetMessage);
            } else {
            if (Role && targetMessage.role !== Role) targetMessage.role = Role;
            if (MsgType && targetMessage.msg_type !== MsgType) targetMessage.msg_type = MsgType;
          }

                      targetMessage.content += (Delta || '');
          
          // Note: File system changes are now handled by the file watcher in fsStore
          // No need to manually detect and refresh here
            
          if (Marker === 'end') {
            targetMessage.status = 'complete';
            console.log(`[AgentStore] Message turn ended for ${MessageID}. Content is finalized.`);
          }
        }
        break;
      }

      case 'message_complete': {
        console.log('[AgentStore] Overall stream complete (message_complete event)');
        
        // Finalize status of any streaming messages
        if (currentConversation.value) {
          currentConversation.value.messages.forEach(m => {
            if (m.status === 'streaming') {
              m.status = 'complete';
            }
          });
        }
        
        stopStreaming();
        break;
      }
      
      case 'error': {
        const { message, code, message_id: relatedMessageID } = event.data; // event.data is SSEErrorData
        console.error('[AgentStore] SSE error event from server:', message, 'Code:', code, 'Related Message ID:', relatedMessageID);
        error.value = message;
        handleStreamError({ message, code })
        // stopStreaming(); // stopStreaming will set isLoading and isStreaming to false and abort controller.
        break;
      }
    }
  }

  const handleStreamError = (error: any) => {
    stopStreaming();
    // Find the last user message in the conversation
    const lastMessage = [...(currentConversation.value?.messages || [])].reverse().find(m => m.role === 'user');
    if (lastMessage) {
      lastMessageError.value = {
        messageId: lastMessage.msg_id,
        error: error,
      };
      // remove the temp assistant message
      const tempAssistantMessageIndex = currentConversation.value?.messages.findIndex(m => m.status === 'streaming')
      if (tempAssistantMessageIndex !== -1 && tempAssistantMessageIndex) {
        currentConversation.value?.messages.splice(tempAssistantMessageIndex, 1)
      }
    }
  };

  const retryLastMessage = async (): Promise<void> => {
    if (!lastMessageError.value || !currentConversation.value) return;

    const messageToRetry = currentConversation.value.messages.find(
      (m) => m.msg_id === lastMessageError.value!.messageId
    );

    if (!messageToRetry || !currentConversation.value.project_id) return;
    
    const requestData: SendMessageRequest = {
      message: messageToRetry.content,
      retry_message_id: messageToRetry.msg_id,
    }

    // Reuse sendUserMessage logic by simply calling it. It will handle the state reset.
    await sendUserMessage(currentConversation.value.project_id, requestData);
  }

  const stopStreaming = (): void => {
    console.log('[AgentStore] stopStreaming() called.');
    if (eventSourceController.value) {
      console.log('[AgentStore] Aborting SSE connection controller.');
      eventSourceController.value.abort();
      eventSourceController.value = null;
    }
    isLoading.value = false;
    isStreaming.value = false;
    console.log('[AgentStore] Streaming stopped and states reset.');
  }

  const clearMessages = async (projectId: string): Promise<void> => {
    if (!projectId || !currentConversation.value) return;
    try {
      isLoading.value = true;
      error.value = null;
      await clearConversation(projectId);
      if (currentConversation.value) {
        currentConversation.value.messages = [];
      }
      if (isStreaming.value) {
        stopStreaming();
      }
      console.log('[AgentStore] Conversation cleared.');
    } catch (err) {
      console.error('[AgentStore] Failed to clear conversation:', err);
      error.value = err instanceof Error ? err.message : 'Failed to clear conversation';
    } finally {
      isLoading.value = false;
    }
  }

  const exportChat = async (
    projectId: string,
    format: 'markdown' | 'json' | 'txt' = 'markdown'
  ): Promise<void> => {
    if (!projectId) return;
    try {
      isLoading.value = true;
      error.value = null;

      // exportConversation现在返回 { data: Blob, filename: string }
      const { data: blobContent, filename } = await exportConversation(projectId, format);
      
      // 直接使用返回的 Blob 和 filename
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blobContent); // Use data (Blob)
      link.download = filename;                      // Use parsed filename
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
      
      console.log('[AgentStore] Conversation exported:', filename);
    } catch (err) {
      console.error('[AgentStore] Failed to export conversation:', err);
      error.value = err instanceof Error ? err.message : 'Failed to export conversation';
    } finally {
      isLoading.value = false;
    }
  }

  const reset = (): void => {
    stopStreaming();
    currentConversation.value = null;
    error.value = null;
    console.log('[AgentStore] Store reset.');
  }

  return {
    currentConversation,
    isLoading,
    isStreaming,
    error,
    messages,
    hasConversation,
    loadConversation,
    sendUserMessage,
    retryLastMessage,
    lastMessageError,
    stopStreaming,
    clearMessages,
    exportChat,
    reset,
    // Note: File system monitoring is now handled by fsStore's file watcher
    // Debug functions moved to fsStore if needed
  }
}) 