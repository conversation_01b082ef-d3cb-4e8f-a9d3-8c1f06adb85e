import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/api/types.js'
import { loginUser as loginUserAPI, registerUser as registerUserAPI, type LoginRequest, type RegisterRequest } from '@/api/user.js'
import axiosInstance from '@/utils/request.js'

export const useUserStore = defineStore('user', () => {
  // State
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const isInitialized = ref(false) // Track if we've checked initial auth status

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => !!user.value?.is_admin) // NEW: Admin status
  const userName = computed(() => user.value?.username || '')
  const userDisplayName = computed(() => user.value?.full_name || user.value?.username || '')

  // Actions
  
  /**
   * Initialize user state by checking current authentication status
   */
  const initializeAuth = async () => {
    if (isInitialized.value) return

    console.log('[UserStore] Initializing auth state...')
    
    // First try to load user from localStorage
    const cachedUser = localStorage.getItem('user')
    console.log('[UserStore] Cached user data:', cachedUser)
    
    if (cachedUser) {
      try {
        const parsedUser = JSON.parse(cachedUser)
        user.value = parsedUser
        console.log('[UserStore] Loaded user from cache:', {
          username: user.value?.username,
          fullName: user.value?.full_name,
          id: user.value?.id
        })
      } catch (e) {
        console.warn('[UserStore] Failed to parse cached user, clearing cache', e)
        localStorage.removeItem('user')
      }
    } else {
      console.log('[UserStore] No cached user found')
    }

    try {
      // Try to verify with backend (but don't fail if it doesn't work)
      console.log('[UserStore] Attempting to verify with backend...')
      const response = await axiosInstance.get('/auth/profile')
      user.value = response.data
      console.log('[UserStore] User authenticated:', user.value?.username)
    } catch (error) {
      // User not authenticated, clear state
      console.log('[UserStore] User not authenticated:', error)
      user.value = null
    } finally {
      isInitialized.value = true
      console.log('[UserStore] Auth initialization complete:', {
        authenticated: !!user.value,
        username: user.value?.username,
        hasLocalStorage: !!localStorage.getItem('user')
      })
    }
  }

  /**
   * Login user
   */
  const login = async (credentials: LoginRequest) => {
    isLoading.value = true
    try {
      console.log('[UserStore] Attempting login for:', credentials.username)
      const response = await loginUserAPI(credentials)
      user.value = response.user
      
      // Save user to localStorage for persistence
      const userJson = JSON.stringify(response.user)
      localStorage.setItem('user', userJson)
      console.log('[UserStore] User logged in and cached:', {
        username: user.value?.username,
        fullName: user.value?.full_name,
        savedToStorage: userJson
      })
      
      return response
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Register user
   */
  const register = async (userData: RegisterRequest) => {
    isLoading.value = true
    try {
      console.log('[UserStore] Attempting registration for:', userData.username)
      const response = await registerUserAPI(userData)
      user.value = response.user
      
      // Save user to localStorage for persistence
      const userJson = JSON.stringify(response.user)
      localStorage.setItem('user', userJson)
      console.log('[UserStore] User registered and cached:', {
        username: user.value?.username,
        fullName: user.value?.full_name,
        savedToStorage: userJson
      })
      
      return response
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Logout user
   */
  const logout = async () => {
    try {
      await axiosInstance.post('/auth/logout')
    } catch (error) {
      console.warn('Logout request failed:', error)
    } finally {
      // Always clear local state regardless of API call result
      user.value = null
      localStorage.removeItem('user')
      console.log('[UserStore] User logged out and cache cleared')
    }
  }

  /**
   * Clear user state (for authentication errors)
   */
  const clearAuth = () => {
    user.value = null
    localStorage.removeItem('user')
    console.log('[UserStore] Auth state cleared')
  }

  return {
    // State
    user,
    isLoading,
    isInitialized,
    
    // Getters
    isAuthenticated,
    isAdmin,
    userName,
    userDisplayName,
    
    // Actions
    initializeAuth,
    login,
    register,
    logout,
    clearAuth
  }
}) 