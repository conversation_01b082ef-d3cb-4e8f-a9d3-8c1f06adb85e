import { defineStore } from 'pinia';
import type { Terminal } from '@xterm/xterm'; // Import Terminal type

// Interface for a single terminal instance managed by the store
export interface TerminalInstance {
  id: string; // Unique ID for the tab and terminal instance
  projectId: string; // ID of the project this terminal is associated with
  name: string; // Display name for the terminal tab (e.g., "Terminal 1", "zsh @ /workspace")
  xterm: Terminal | null; // The Xterm.js Terminal object, null if not fully initialized
  socket: WebSocket | null; // The WebSocket connection to the backend
  shell?: string; // Optional: desired shell (e.g., /bin/bash)
  workDir?: string; // Optional: desired working directory in the container
  isLoading: boolean; // True if the terminal is currently being set up (socket connecting, xterm attaching)
  hasError: boolean; // True if there was an error setting up or during connection
  errorMessage?: string; // Optional error message
  isDisconnected: boolean; // True if the terminal is disconnected and can be reconnected
}

interface TerminalState {
  terminals: TerminalInstance[];
  activeTerminalId: string | null;
  nextTerminalNumericId: number; // To help generate unique names like "Terminal 1", "Terminal 2"
}

export const useTerminalStore = defineStore('terminal', {
  state: (): TerminalState => ({
    terminals: [],
    activeTerminalId: null,
    nextTerminalNumericId: 1,
  }),

  actions: {
    /**
     * Opens a new terminal instance and adds it to the managed list.
     * WebSocket connection and Xterm.js instance creation will be handled
     * by the TerminalView component when it mounts, using the ID generated here.
     * @param projectId The ID of the project for which to open the terminal.
     * @param options Optional shell and working directory.
     * @returns The ID of the newly created terminal instance.
     */
    openNewTerminal(projectId: string, options?: { shell?: string; workDir?: string }): string {
      const terminalId = `term-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
      const numericId = this.nextTerminalNumericId++;
      const newTerminal: TerminalInstance = {
        id: terminalId,
        projectId,
        name: `Terminal ${numericId}`, // Simple default name
        xterm: null,
        socket: null,
        shell: options?.shell,
        workDir: options?.workDir,
        isLoading: true, // Initially true until TerminalView sets it up
        hasError: false,
        isDisconnected: false,
      };

      this.terminals.push(newTerminal);
      this.activeTerminalId = terminalId; // Activate the new terminal
      return terminalId;
    },

    /**
     * Closes a terminal instance and removes it from the list.
     * This action primarily handles removing the terminal from the store.
     * The TerminalView component's onUnmounted hook should handle
     * closing the WebSocket and disposing of the Xterm.js instance.
     * @param terminalId The ID of the terminal to close.
     */
    closeTerminal(terminalId: string): void {
      const index = this.terminals.findIndex(t => t.id === terminalId);
      if (index !== -1) {
        const closedTerminal = this.terminals[index];
        // The actual closing of socket and xterm disposal should be done
        // by the component instance when it's unmounted.
        // We can signal the component or rely on its unmount lifecycle.
        this.terminals.splice(index, 1);

        if (this.activeTerminalId === terminalId) {
          // If the closed terminal was active, activate the last one in the list, or none if empty
          this.activeTerminalId = this.terminals.length > 0
            ? this.terminals[this.terminals.length - 1].id
            : null;
        }
      }
    },

    /**
     * Sets the active terminal tab.
     * @param terminalId The ID of the terminal to activate.
     */
    setActiveTerminal(terminalId: string | null): void {
      if (terminalId === null || this.terminals.some(t => t.id === terminalId)) {
        this.activeTerminalId = terminalId;
      }
    },

    // --- Actions to be called by TerminalView component ---

    /**
     * Updates the store when a terminal's Xterm.js instance is initialized.
     * @param terminalId The ID of the terminal.
     * @param xtermInstance The initialized Xterm.js Terminal object.
     */
    setTerminalInstance(terminalId: string, xtermInstance: Terminal): void {
      const terminal = this.terminals.find(t => t.id === terminalId);
      if (terminal) {
        terminal.xterm = xtermInstance;
      }
    },

    /**
     * Updates the store when a terminal's WebSocket is connected.
     * @param terminalId The ID of the terminal.
     * @param socketInstance The connected WebSocket object.
     */
    setTerminalSocket(terminalId: string, socketInstance: WebSocket): void {
      const terminal = this.terminals.find(t => t.id === terminalId);
      if (terminal) {
        terminal.socket = socketInstance;
        terminal.isLoading = false; // Socket connected, no longer loading (unless xterm fails to attach)
        terminal.hasError = false;
        terminal.errorMessage = undefined;
      }
    },

    /**
     * Marks a terminal as having encountered an error during setup or connection.
     * @param terminalId The ID of the terminal.
     * @param message Optional error message.
     */
    setTerminalError(terminalId: string, message?: string): void {
      const terminal = this.terminals.find(t => t.id === terminalId);
      if (terminal) {
        terminal.isLoading = false;
        terminal.hasError = true;
        terminal.errorMessage = message;
        terminal.xterm = null; // Clear instances if error occurred
        terminal.socket = null;
        terminal.isDisconnected = false;
      }
    },

    /**
     * Marks a terminal as disconnected but available for reconnection.
     * @param terminalId The ID of the terminal.
     * @param message Optional disconnection message.
     */
    setTerminalDisconnected(terminalId: string, message?: string): void {
      const terminal = this.terminals.find(t => t.id === terminalId);
      if (terminal) {
        terminal.isLoading = false;
        terminal.hasError = false;
        terminal.isDisconnected = true;
        terminal.errorMessage = message;
        terminal.socket = null; // Clear socket, but keep xterm instance
      }
    },

    /**
     * Resets terminal state for reconnection attempt.
     * @param terminalId The ID of the terminal.
     */
    resetTerminalForReconnection(terminalId: string): void {
      const terminal = this.terminals.find(t => t.id === terminalId);
      if (terminal) {
        terminal.isLoading = true;
        terminal.hasError = false;
        terminal.isDisconnected = false;
        terminal.errorMessage = undefined;
        terminal.socket = null;
        // Keep xterm instance for reconnection
      }
    },

    /**
     * Clears the loading state for a terminal, e.g., after successful setup.
     * @param terminalId The ID of the terminal.
     */
    clearTerminalLoading(terminalId: string): void {
        const terminal = this.terminals.find(t => t.id === terminalId);
        if (terminal) {
            terminal.isLoading = false;
        }
    }

    // TODO:
    // - Action to send data through WebSocket (e.g., for resize)
    //   sendTerminalData(terminalId: string, data: string | ArrayBufferLike | Blob | ArrayBufferView): void
    // - Action to update terminal name/options if needed later
  },

  getters: {
    getTerminals: (state): TerminalInstance[] => state.terminals,
    getActiveTerminal: (state): TerminalInstance | undefined => {
      return state.terminals.find(t => t.id === state.activeTerminalId);
    },
    getTerminalById: (state) => {
      return (id: string): TerminalInstance | undefined => state.terminals.find(t => t.id === id);
    }
  },
}); 