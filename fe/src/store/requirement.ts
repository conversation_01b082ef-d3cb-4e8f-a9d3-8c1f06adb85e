import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { 
  Requirement, 
  Question, 
  Answer, 
  CreateRequirementRequest, 
  CreateRequirementWithImagesRequest,
  UpdateRequirementRequest,
  AnswerSubmission 
} from '@/api/requirement.js'
import * as requirementApi from '@/api/requirement.js'

// Define state structure for requirements pagination
interface RequirementPagination {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
}

export const useRequirementStore = defineStore('requirement', () => {
  // State
  const requirements = ref<Requirement[]>([])
  const currentRequirement = ref<Requirement | null>(null)
  const pagination = ref<RequirementPagination>({
    page: 1,
    limit: 10,
    total: 0,
    total_pages: 0
  })
  
  // Loading states
  const isLoading = ref(false)
  const isCreating = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const isGeneratingQuestions = ref(false)
  const isSubmittingAnswers = ref(false)
  const isGeneratingWireframe = ref(false)
  
  // Error states
  const error = ref<string | null>(null)
  const operationError = ref<string | null>(null)

  // Getters
  const hasRequirements = computed(() => requirements.value.length > 0)
  const totalRequirements = computed(() => pagination.value.total)
  const currentPage = computed(() => pagination.value.page)
  const hasNextPage = computed(() => pagination.value.page < pagination.value.total_pages)
  const hasPreviousPage = computed(() => pagination.value.page > 1)
  
  const requirementsByStatus = computed(() => (status: string) => {
    return requirements.value.filter(req => req.status === status)
  })
  
  const pendingRequirements = computed(() => requirementsByStatus.value('pending'))
  const processingRequirements = computed(() => requirementsByStatus.value('processing'))
  const planGeneratingRequirements = computed(() => requirementsByStatus.value('plan_generating'))
  const completedRequirements = computed(() => requirementsByStatus.value('completed'))
  
  const currentRequirementQuestions = computed(() => {
    return currentRequirement.value?.questions || []
  })
  
  const currentRequirementAnswers = computed(() => {
    return currentRequirement.value?.answers || []
  })
  
  const isCurrentRequirementComplete = computed(() => {
    return currentRequirement.value?.status === 'completed'
  })

  const isCurrentRequirementPlanGenerating = computed(() => {
    return currentRequirement.value?.status === 'plan_generating'
  })

  const currentRequirementHasDevelopmentPlan = computed(() => {
    return currentRequirement.value?.development_plan && 
           currentRequirement.value.development_plan.trim().length > 0
  })

  const currentRequirementHasWireframe = computed(() => {
    return currentRequirement.value?.wireframe_html && 
           currentRequirement.value.wireframe_html.trim().length > 0
  })

  const currentRequirementHasImages = computed(() => {
    return currentRequirement.value?.reference_images && 
           currentRequirement.value.reference_images.length > 0
  })

  const currentRequirementAnalyzedImages = computed(() => {
    return currentRequirement.value?.reference_images?.filter(img => img.analyzed_at) || []
  })

  // Helper functions
  const clearErrors = () => {
    error.value = null
    operationError.value = null
  }
  
  const handleError = (err: any, context: string) => {
    const errorMessage = err?.response?.data?.error || err?.message || `${context} failed`
    console.error(`[RequirementStore] ${context} error:`, err)
    operationError.value = errorMessage
    ElMessage.error(errorMessage)
  }

  // Deep comparison utility for requirements
  const requirementsEqual = (req1: Requirement, req2: Requirement): boolean => {
    return (
      req1.id === req2.id &&
      req1.status === req2.status &&
      req1.updated_at === req2.updated_at &&
      req1.title === req2.title &&
      req1.development_plan === req2.development_plan &&
      req1.wireframe_html === req2.wireframe_html &&
      req1.questions.length === req2.questions.length &&
      req1.answers.length === req2.answers.length &&
      req1.reference_images.length === req2.reference_images.length
    )
  }

  // Update individual requirement in list without causing re-render
  const updateRequirementInList = (updatedRequirement: Requirement) => {
    const index = requirements.value.findIndex(req => req.id === updatedRequirement.id)
    if (index !== -1) {
      const currentRequirement = requirements.value[index]
      if (!requirementsEqual(currentRequirement, updatedRequirement)) {
        requirements.value[index] = updatedRequirement
        console.log('[RequirementStore] Updated requirement in list:', updatedRequirement.id, updatedRequirement.status)
      }
    }
  }

  // Actions
  
  /**
   * Create a new requirement
   */
  const createRequirement = async (data: CreateRequirementRequest): Promise<Requirement | null> => {
    isCreating.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Creating requirement:', data.title)
      const response = await requirementApi.createRequirement(data)
      
      // Add new requirement to the beginning of the list
      requirements.value.unshift(response.requirement)
      pagination.value.total += 1
      
      ElMessage.success('Requirement created successfully')
      console.log('[RequirementStore] Requirement created:', response.requirement.id)
      
      return response.requirement
    } catch (err: any) {
      handleError(err, 'Create requirement')
      return null
    } finally {
      isCreating.value = false
    }
  }

  /**
   * Create a new requirement with reference images
   */
  const createRequirementWithImages = async (data: CreateRequirementWithImagesRequest): Promise<Requirement | null> => {
    isCreating.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Creating requirement with images:', {
        title: data.title,
        imageCount: data.images.length
      })
      
      const response = await requirementApi.createRequirementWithImages(data)
      
      // Add new requirement to the beginning of the list
      requirements.value.unshift(response.requirement)
      pagination.value.total += 1
      
      ElMessage.success('Requirement with images created successfully')
      console.log('[RequirementStore] Requirement with images created:', response.requirement.id)
      
      return response.requirement
    } catch (err: any) {
      handleError(err, 'Create requirement with images')
      return null
    } finally {
      isCreating.value = false
    }
  }

  /**
   * Create a new requirement with unified interface supporting images and website URL
   */
  const createRequirementUnified = async (formData: FormData): Promise<Requirement | null> => {
    isCreating.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Creating requirement with unified interface')
      
      const response = await requirementApi.createRequirementUnified(formData)
      
      // Add new requirement to the beginning of the list
      requirements.value.unshift(response.requirement)
      pagination.value.total += 1
      
      console.log('[RequirementStore] Requirement created with unified interface:', response.requirement.id)
      
      return response.requirement
    } catch (err: any) {
      handleError(err, 'Create requirement with unified interface')
      return null
    } finally {
      isCreating.value = false
    }
  }
  
  /**
   * Fetch requirements with pagination and optional status filtering
   */
  const fetchRequirements = async (page: number = 1, limit: number = 10, status?: string) => {
    isLoading.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Fetching requirements:', { page, limit, status })
      const response = await requirementApi.getRequirements(page, limit, status)
      
      requirements.value = response.requirements
      pagination.value = response.pagination
      
      console.log('[RequirementStore] Requirements fetched:', requirements.value.length)
    } catch (err: any) {
      error.value = err?.response?.data?.error || err?.message || 'Failed to fetch requirements'
      console.error('[RequirementStore] Fetch requirements error:', err)
      if (error.value) {
        ElMessage.error(error.value)
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Silent polling fetch - updates only changed items without causing UI flicker
   */
  const silentFetchRequirements = async (page: number = 1, limit: number = 10, status?: string) => {
    try {
      console.log('[RequirementStore] Silent fetching requirements:', { page, limit, status })
      const response = await requirementApi.getRequirements(page, limit, status)
      
      // Only update pagination if it changed
      if (pagination.value.total !== response.pagination.total || 
          pagination.value.total_pages !== response.pagination.total_pages) {
        pagination.value = response.pagination
      }
      
      // Smart update: only update requirements that actually changed
      const newRequirements = response.requirements
      
      // If the list length changed, we need to replace the array
      if (requirements.value.length !== newRequirements.length) {
        requirements.value = newRequirements
        console.log('[RequirementStore] Requirements list length changed, full update required')
        return
      }
      
      // Compare and update individual requirements
      let hasChanges = false
      newRequirements.forEach((newReq, index) => {
        const currentReq = requirements.value[index]
        if (currentReq && !requirementsEqual(currentReq, newReq)) {
          requirements.value[index] = newReq
          hasChanges = true
          console.log('[RequirementStore] Silent update requirement:', newReq.id, newReq.status)
        }
      })
      
      if (!hasChanges) {
        console.log('[RequirementStore] No changes detected in silent fetch')
      }
    } catch (err: any) {
      // Silent fail - don't show error messages during polling
      console.error('[RequirementStore] Silent fetch requirements error:', err)
    }
  }
  
  /**
   * Fetch a specific requirement by ID
   */
  const fetchRequirement = async (id: string): Promise<Requirement | null> => {
    isLoading.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Fetching requirement:', id)
      const response = await requirementApi.getRequirement(id)
      
      currentRequirement.value = response.requirement
      console.log('[RequirementStore] Requirement fetched:', response.requirement.title)
      
      return response.requirement
    } catch (err: any) {
      handleError(err, 'Fetch requirement')
      currentRequirement.value = null
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * Generate AI questions for a requirement
   */
  const generateQuestions = async (id: string): Promise<boolean> => {
    isGeneratingQuestions.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Generating questions for requirement:', id)
      const response = await requirementApi.generateQuestions(id)
      
      // Update current requirement if it matches
      if (currentRequirement.value?.id === id) {
        currentRequirement.value = response.requirement
      }
      
      // Update requirement in the list
      const index = requirements.value.findIndex(req => req.id === id)
      if (index !== -1) {
        requirements.value[index] = response.requirement
      }
      
      ElMessage.success('Questions generated successfully')
      console.log('[RequirementStore] Questions generated:', response.requirement.questions.length)
      
      return true
    } catch (err: any) {
      handleError(err, 'Generate questions')
      return false
    } finally {
      isGeneratingQuestions.value = false
    }
  }
  
  /**
   * Submit answers to AI-generated questions
   */
  const submitAnswers = async (id: string, answers: AnswerSubmission[]): Promise<boolean> => {
    isSubmittingAnswers.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Submitting answers for requirement:', id)
      const response = await requirementApi.submitAnswers(id, answers)
      
      // Update current requirement if it matches
      if (currentRequirement.value?.id === id) {
        currentRequirement.value = response.requirement
      }
      
      // Update requirement in the list
      const index = requirements.value.findIndex(req => req.id === id)
      if (index !== -1) {
        requirements.value[index] = response.requirement
      }
      
      ElMessage.success('Answers submitted successfully')
      console.log('[RequirementStore] Answers submitted for requirement:', id)
      
      return true
    } catch (err: any) {
      handleError(err, 'Submit answers')
      return false
    } finally {
      isSubmittingAnswers.value = false
    }
  }
  
  /**
   * Generate an interactive HTML wireframe for a requirement
   */
  const generateWireframe = async (id: string): Promise<boolean> => {
    isGeneratingWireframe.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Generating wireframe for requirement:', id)
      const response = await requirementApi.generateWireframe(id)
      
      // Update current requirement if it matches
      if (currentRequirement.value?.id === id) {
        currentRequirement.value = response.requirement
      }
      
      // Update requirement in the list
      const index = requirements.value.findIndex(req => req.id === id)
      if (index !== -1) {
        requirements.value[index] = response.requirement
      }
      
      ElMessage.success('Wireframe generated successfully')
      console.log('[RequirementStore] Wireframe generated for requirement:', id)
      
      return true
    } catch (err: any) {
      handleError(err, 'Generate wireframe')
      return false
    } finally {
      isGeneratingWireframe.value = false
    }
  }
  
  /**
   * Update an existing requirement
   */
  const updateRequirement = async (id: string, data: UpdateRequirementRequest): Promise<boolean> => {
    isUpdating.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Updating requirement:', id)
      await requirementApi.updateRequirement(id, data)
      
      // Refresh current requirement if it matches
      if (currentRequirement.value?.id === id) {
        await fetchRequirement(id)
      }
      
      // Refresh requirements list
      await fetchRequirements(pagination.value.page, pagination.value.limit)
      
      ElMessage.success('Requirement updated successfully')
      console.log('[RequirementStore] Requirement updated:', id)
      
      return true
    } catch (err: any) {
      handleError(err, 'Update requirement')
      return false
    } finally {
      isUpdating.value = false
    }
  }
  
  /**
   * Delete a requirement
   */
  const deleteRequirement = async (id: string): Promise<boolean> => {
    isDeleting.value = true
    clearErrors()
    
    try {
      console.log('[RequirementStore] Deleting requirement:', id)
      await requirementApi.deleteRequirement(id)
      
      // Remove from requirements list
      const index = requirements.value.findIndex(req => req.id === id)
      if (index !== -1) {
        requirements.value.splice(index, 1)
        pagination.value.total = Math.max(0, pagination.value.total - 1)
      }
      
      // Clear current requirement if it matches the deleted one
      if (currentRequirement.value?.id === id) {
        currentRequirement.value = null
      }
      
      ElMessage.success('Requirement deleted successfully')
      console.log('[RequirementStore] Requirement deleted:', id)
      
      return true
    } catch (err: any) {
      handleError(err, 'Delete requirement')
      return false
    } finally {
      isDeleting.value = false
    }
  }
  
  /**
   * Set current requirement (for viewing/editing)
   */
  const setCurrentRequirement = (requirement: Requirement | null) => {
    currentRequirement.value = requirement
    console.log('[RequirementStore] Current requirement set:', requirement?.id)
  }
  
  /**
   * Clear current requirement
   */
  const clearCurrentRequirement = () => {
    currentRequirement.value = null
    console.log('[RequirementStore] Current requirement cleared')
  }
  
  /**
   * Refresh requirements list
   */
  const refreshRequirements = async () => {
    await fetchRequirements(pagination.value.page, pagination.value.limit)
  }
  
  /**
   * Go to next page
   */
  const nextPage = async () => {
    if (hasNextPage.value) {
      await fetchRequirements(pagination.value.page + 1, pagination.value.limit)
    }
  }
  
  /**
   * Go to previous page
   */
  const previousPage = async () => {
    if (hasPreviousPage.value) {
      await fetchRequirements(pagination.value.page - 1, pagination.value.limit)
    }
  }
  
  /**
   * Go to specific page
   */
  const goToPage = async (page: number) => {
    if (page >= 1 && page <= pagination.value.total_pages) {
      await fetchRequirements(page, pagination.value.limit)
    }
  }
  
  /**
   * Clear all state (useful for logout or project switch)
   */
  const clearState = () => {
    requirements.value = []
    currentRequirement.value = null
    pagination.value = { page: 1, limit: 10, total: 0, total_pages: 0 }
    clearErrors()
    console.log('[RequirementStore] State cleared')
  }

  return {
    // State
    requirements,
    currentRequirement,
    pagination,
    
    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isGeneratingQuestions,
    isSubmittingAnswers,
    isGeneratingWireframe,
    
    // Error states
    error,
    operationError,
    
    // Getters
    hasRequirements,
    totalRequirements,
    currentPage,
    hasNextPage,
    hasPreviousPage,
    requirementsByStatus,
    pendingRequirements,
    processingRequirements,
    planGeneratingRequirements,
    completedRequirements,
    currentRequirementQuestions,
    currentRequirementAnswers,
    isCurrentRequirementComplete,
    isCurrentRequirementPlanGenerating,
    currentRequirementHasDevelopmentPlan,
    currentRequirementHasWireframe,
    currentRequirementHasImages,
    currentRequirementAnalyzedImages,
    
    // Actions
    createRequirement,
    createRequirementWithImages,
    createRequirementUnified,
    fetchRequirements,
    silentFetchRequirements,
    fetchRequirement,
    generateQuestions,
    submitAnswers,
    generateWireframe,
    updateRequirement,
    deleteRequirement,
    setCurrentRequirement,
    clearCurrentRequirement,
    refreshRequirements,
    nextPage,
    previousPage,
    goToPage,
    clearState,
    clearErrors,
    updateRequirementInList
  }
})