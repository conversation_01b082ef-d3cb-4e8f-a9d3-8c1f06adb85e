import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  listLLMConfigs,
  createLLMConfig,
  updateLLMConfig,
  deleteLLMConfig,
  setActiveLLMConfig,
  type LLMConfig,
  type CreateLLMConfigRequest,
  type UpdateLLMConfigRequest,
} from '@/api/llm_config.js'; // Import API functions and types

export const useLLMSettingStore = defineStore('llmSetting', () => {
  // State
  const llmConfigs = ref<LLMConfig[]>([]);
  const activeLLMConfig = ref<LLMConfig | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const getLLMConfigs = computed(() => llmConfigs.value);
  const getActiveLLMConfig = computed(() => activeLLMConfig.value);
  const getLLMConfigById = computed(() => (id: string) => llmConfigs.value.find(config => config.id === id));

  // Actions
  const fetchLLMConfigs = async (): Promise<void> => {
    isLoading.value = true;
    error.value = null;
    try {
      const configs = await listLLMConfigs();
      llmConfigs.value = configs;
      // Also determine the active config from the fetched list
      activeLLMConfig.value = configs.find(config => config.is_active) || null;
      console.log('[LLMSettingStore] Fetched LLM configs:', configs);
    } catch (err: any) {
      console.error('[LLMSettingStore] Failed to fetch LLM configs:', err);
      error.value = err.message || 'Failed to fetch LLM configurations.';
      ElMessage.error(error.value || 'Failed to fetch LLM configurations.'); // Fix linter error
    } finally {
      isLoading.value = false;
    }
  };

  const createNewLLMConfig = async (data: CreateLLMConfigRequest): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await createLLMConfig(data);
      ElMessage.success(response.message || 'LLM configuration created successfully!');
      await fetchLLMConfigs(); // Refresh the list
      console.log('[LLMSettingStore] Created new LLM config:', response.id);
      return true;
    } catch (err: any) {
      console.error('[LLMSettingStore] Failed to create LLM config:', err);
      error.value = err.message || 'Failed to create LLM configuration.';
      ElMessage.error(error.value || 'Failed to create LLM configuration.'); // Fix linter error
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const updateExistingLLMConfig = async (id: string, data: UpdateLLMConfigRequest): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      await updateLLMConfig(id, data);
      ElMessage.success('LLM configuration updated successfully!');
      await fetchLLMConfigs(); // Refresh the list
      console.log('[LLMSettingStore] Updated LLM config:', id);
      return true;
    } catch (err: any) {
      console.error('[LLMSettingStore] Failed to update LLM config:', err);
      error.value = err.message || 'Failed to update LLM configuration.';
      ElMessage.error(error.value || 'Failed to update LLM configuration.'); // Fix linter error
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteExistingLLMConfig = async (id: string): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      await deleteLLMConfig(id);
      ElMessage.success('LLM configuration deleted successfully!');
      await fetchLLMConfigs(); // Refresh the list
      console.log('[LLMSettingStore] Deleted LLM config:', id);
      return true;
    } catch (err: any) {
      console.error('[LLMSettingStore] Failed to delete LLM config:', err);
      error.value = err.message || 'Failed to delete LLM configuration.';
      ElMessage.error(error.value || 'Failed to delete LLM configuration.'); // Fix linter error
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const setAsActiveLLMConfig = async (id: string): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await setActiveLLMConfig(id);
      ElMessage.success(response.message || 'LLM configuration set as active!');
      await fetchLLMConfigs(); // Refresh the list to reflect new active status
      console.log('[LLMSettingStore] Set LLM config as active:', id);
      return true;
    } catch (err: any) {
      console.error('[LLMSettingStore] Failed to set LLM config as active:', err);
      error.value = err.message || 'Failed to set LLM configuration as active.';
      ElMessage.error(error.value || 'Failed to set LLM configuration as active.'); // Fix linter error
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    llmConfigs,
    activeLLMConfig,
    isLoading,
    error,
    getLLMConfigs,
    getActiveLLMConfig,
    getLLMConfigById,
    fetchLLMConfigs,
    createNewLLMConfig,
    updateExistingLLMConfig,
    deleteExistingLLMConfig,
    setAsActiveLLMConfig,
  };
}); 