<template>
  <div class="app-layout grid h-full w-full">
    <TopToolbar class="layout-toolbar" />
    <div class="layout-content grid h-full" ref="layoutContentRef" :style="gridStyle">
      <IconBar 
        class="layout-iconbar" 
        :active-right-tab-id="rightActiveTabId"
        @toggle-agent-tab="handleToggleAgentTab" 
      />
      <FileBrowser class="layout-filebrowser" @open-file="handleOpenFile" />
      <MainColumn
        ref="leftColRef" 
        class="layout-leftcol"
        :tabs="leftColumnComputedTabs"
        :initial-active-tab-id="leftActiveTabId"
        column-bg-color="#333338"
        :show-terminal-tabs="false"
        @update:active-tab-id="(newId: string | null) => leftActiveTabId = newId"
        @close-tab="closeLeftTab"
      />
      <!-- Splitter -->
      <div 
        class="layout-splitter cursor-col-resize bg-gray-600 hover:bg-blue-500 w-1.5 h-full"
        @mousedown="startResize"
      >
      </div>
      <MainColumn
        class="layout-rightcol"
        :tabs="rightColumnComputedTabs"
        :initial-active-tab-id="rightActiveTabId"
        column-bg-color="#404045"
        @update:active-tab-id="(newId: string | null) => rightActiveTabId = newId"
        @close-tab="closeRightTab"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  defineProps,
  onMounted,
  onBeforeUnmount,
  computed,
  shallowRef, // Use shallowRef for DOM refs
  nextTick,
} from 'vue';
import TopToolbar from '@/components/TopToolbar.vue'
import IconBar from '@/components/IconBar.vue'
import FileBrowser from '@/components/FileBrowser.vue'
import MainColumn from '@/components/MainColumn.vue'
import { useFsStore } from '@/store/fs';
import { useTerminalStore, type TerminalInstance } from '@/store/terminal'; // Import terminal store
import { getLanguageFromPath } from '@/utils/languageMap';
import type { FileSystemObject } from '@/api/types';

interface AppTab { // Renamed to avoid conflict if Tab is used elsewhere, and to be specific
  id: string;
  title: string;
  type: 'editor' | 'terminal' | 'other'; // Added terminal type
  language?: string;
  projectId?: string; // For terminal tabs
  // isModified might be derived if needed
}

// --- Props --- Define projectID prop
const props = defineProps({
  projectID: {
    type: String,
    required: true,
  },
});

const fsStore = useFsStore();
const terminalStore = useTerminalStore(); // Initialize terminal store

// --- Refs for DOM elements ---
const layoutContentRef = shallowRef<HTMLDivElement | null>(null);
const leftColRef = shallowRef<InstanceType<typeof MainColumn> | null>(null);

// --- State for focused column ---
const lastInteractedColumn = ref<'left' | 'right' | null>(null);

// --- Resizing State ---
const isResizing = ref(false);
const startX = ref(0);
const initialLeftWidth = ref(0);
const leftColWidth = ref<number | null>(null); // Store the calculated width in pixels
const MIN_COL_WIDTH = 400; // Minimum width for main columns

// --- Grid Style --- 
const gridStyle = computed(() => {
  // Base style includes fixed columns
  let gridTemplateColumns = '50px 200px'; 
  if (leftColWidth.value !== null) {
    // If resizing has happened, use the calculated pixel width for left col,
    // and let the right col take the remaining space (1fr)
    gridTemplateColumns += ` ${leftColWidth.value}px 1.5px 1fr`; // Add splitter width
  } else {
    // Initial state: use minmax and fractional units
    gridTemplateColumns += ` minmax(${MIN_COL_WIDTH}px, 1fr) 1.5px minmax(${MIN_COL_WIDTH}px, 1fr)`;
  }
  return {
    gridTemplateColumns,
  };
});

// --- Resize Handlers ---
const startResize = (event: MouseEvent) => {
  if (!leftColRef.value || !leftColRef.value.$el) return;
  isResizing.value = true;
  startX.value = event.clientX;
  // Use the actual rendered width as the starting point
  initialLeftWidth.value = (leftColRef.value.$el as HTMLElement).offsetWidth;
  // Add listeners to the window
  window.addEventListener('mousemove', handleResizing);
  window.addEventListener('mouseup', stopResize);
  // Prevent text selection during drag
  document.body.style.userSelect = 'none';
};

const handleResizing = (event: MouseEvent) => {
  if (!isResizing.value || !layoutContentRef.value) return;
  
  const currentX = event.clientX;
  const deltaX = currentX - startX.value;
  let newLeftWidth = initialLeftWidth.value + deltaX;
  
  // Calculate available width for the two resizable columns
  const fixedWidth = 50 + 200 + 1.5; // IconBar + FileBrowser + Splitter
  const totalResizableWidth = layoutContentRef.value.offsetWidth - fixedWidth;
  const maxLeftWidth = totalResizableWidth - MIN_COL_WIDTH; // Max width for left col to allow min for right col
  
  // Apply constraints
  newLeftWidth = Math.max(MIN_COL_WIDTH, newLeftWidth); // Ensure min width for left
  newLeftWidth = Math.min(maxLeftWidth, newLeftWidth); // Ensure min width for right

  leftColWidth.value = newLeftWidth;
};

const stopResize = () => {
  if (isResizing.value) {
    isResizing.value = false;
    // Remove listeners from the window
    window.removeEventListener('mousemove', handleResizing);
    window.removeEventListener('mouseup', stopResize);
    // Re-enable text selection
    document.body.style.userSelect = '';
  }
};

// --- Lifecycle Hooks for listener cleanup ---
onBeforeUnmount(() => {
  // Ensure listeners are removed if component is unmounted while resizing
  stopResize(); 
});

// --- Project ID Handling & Initial Active File ---
onMounted(() => {
  if (props.projectID) {
    if (fsStore.currentProjectID !== props.projectID) {
        fsStore.setCurrentProjectID(props.projectID);
    }
    // Initial global active file path is now primarily set by watchers on left/rightActiveTabId.
    // Check initial active tabs for both columns to set global focus
    if (leftActiveTabId.value && leftColumnComputedTabs.value.find(t => t.id === leftActiveTabId.value)?.type === 'editor') {
        lastInteractedColumn.value = 'left';
        fsStore.setActiveFile(leftActiveTabId.value);
        console.log(`[AppLayout onMounted] Initializing global active file to left column: ${leftActiveTabId.value}`);
    } else if (rightActiveTabId.value && rightColumnComputedTabs.value.find(t => t.id === rightActiveTabId.value)?.type === 'editor') { // If right can also have editors
        lastInteractedColumn.value = 'right';
        fsStore.setActiveFile(rightActiveTabId.value);
        console.log(`[AppLayout onMounted] Initializing global active file to right column: ${rightActiveTabId.value}`);
    } else {
        fsStore.setActiveFile(null); // No initial editor tab active
    }
  }
});

watch(() => props.projectID, (newProjectID) => {
  if (newProjectID) {
    if (fsStore.currentProjectID !== newProjectID) {
        fsStore.setCurrentProjectID(newProjectID);
        // When project changes, clear active tabs from previous project and active file in store
        // leftTabs.value = []; // This ref will be removed
        leftActiveTabId.value = null;
        // rightTabs (static) remain, terminals are cleared by terminalStore if needed, fsStore clears openFiles
        // rightActiveTabId can be reset to a default for the static/terminal tabs.
        rightActiveTabId.value = staticRightTabs.value.length > 0 ? staticRightTabs.value[0].id : null; // Default to first static/terminal tab
        fsStore.setActiveFile(null);
    }
  }
}, { immediate: false });

// --- Tab State & Handling ---
// const leftTabs = ref<Tab[]>([/* Removed Agent Tab */]); // REMOVE THIS LINE
const leftActiveTabId = ref<string | null>(null);

// Static definition for "other" tabs in the right column
const staticRightTabs = ref<AppTab[]>([
  { id: 'storage', title: 'Object Storage', type: 'other' },
  { id: 'agent', title: 'Agent', type: 'other' },
]);
const rightActiveTabId = ref<string | null>(staticRightTabs.value.length > 0 ? staticRightTabs.value[0].id : null);


// --- Computed Tab Lists for MainColumns ---
const leftColumnComputedTabs = computed<AppTab[]>(() => {
  return Object.entries(fsStore.openFiles).map(([filePath, fileState]) => ({
    id: filePath,
    title: filePath.split('/').pop() || filePath, // Basic name extraction
    type: 'editor',
    language: getLanguageFromPath(filePath),
    get isModified() {
      return fileState.currentContent !== fileState.originalContent;
    }
  }));
});

const rightColumnComputedTabs = computed<AppTab[]>(() => {
  const terminalTabs: AppTab[] = terminalStore.getTerminals.map((term: TerminalInstance) => ({
    id: term.id,
    title: term.name,
    type: 'terminal',
    projectId: term.projectId,
  }));
  
  // Add projectId to static tabs
  const staticTabsWithProjectId: AppTab[] = staticRightTabs.value.map(tab => ({
    ...tab,
    projectId: props.projectID // Add current projectID to all static tabs
  }));
  
  return [...terminalTabs, ...staticTabsWithProjectId];
});

// Helper to track previous right column tabs for new tab detection
const previousRightTabs = ref<AppTab[]>([]);

watch(rightColumnComputedTabs, (newTabs, oldTabs) => {
  if (!oldTabs && previousRightTabs.value.length > 0) {
    // This handles the case where oldTabs is undefined on initial deep watch trigger
    // but we have a record from a previous non-deep watch or manual update.
    oldTabs = previousRightTabs.value;
  }

  const newTerminalTabs = newTabs.filter(t => t.type === 'terminal');
  const oldTerminalTabs = oldTabs ? oldTabs.filter(t => t.type === 'terminal') : [];

  if (newTerminalTabs.length > oldTerminalTabs.length) {
    const newlyAddedTerminal = newTerminalTabs.find(newTermTab => 
      !oldTerminalTabs.some(oldTermTab => oldTermTab.id === newTermTab.id)
    );

    if (newlyAddedTerminal) {
      console.log(`[AppLayout Watcher rightColumnComputedTabs] New terminal tab detected: ${newlyAddedTerminal.id}. Setting as active in right column.`);
      rightActiveTabId.value = newlyAddedTerminal.id;
      // If a terminal becomes active, we might want to ensure no file is "active" globally
      // if the terminal takes precedence or focus.
      if (fsStore.activeFilePath && rightActiveTabId.value === newlyAddedTerminal.id) {
         // fsStore.setActiveFilePath(null); // Already handled by rightActiveTabId watcher
      }
    }
  }
  // Update the snapshot for the next comparison
  previousRightTabs.value = [...newTabs];
}, { deep: true, immediate: true }); // deep: true if Tab objects can change internally and affect logic, immediate for initial run


// --- Watchers for Active Tabs in Columns to set Global Active File ---
watch(leftActiveTabId, (newPath, oldPath) => {
  console.log(`[AppLayout Watcher leftActiveTabId] Changed from ${oldPath} to ${newPath}`);
  // Find tab info from the computed list for the left column
  const tabInfo = leftColumnComputedTabs.value.find(t => t.id === newPath);
  if (newPath && tabInfo?.type === 'editor') {
    lastInteractedColumn.value = 'left';
    fsStore.setActiveFile(newPath);
    console.log(`[AppLayout Watcher leftActiveTabId] Left column editor tab ${newPath} active. Global focus set.`);
  } else if (!newPath && lastInteractedColumn.value === 'left') {
    const rightEditorTab = rightColumnComputedTabs.value.find(t => t.id === rightActiveTabId.value && t.type === 'editor'); // Check if right can have editor focus
    if (rightEditorTab) {
      fsStore.setActiveFile(rightEditorTab.id);
      lastInteractedColumn.value = 'right';
      console.log(`[AppLayout Watcher leftActiveTabId] Left column cleared. Global focus passed to right editor: ${rightEditorTab.id}`);
    } else {
       // If right column primarily shows terminals or other, prioritize its active tab regardless of type if left is cleared
      const activeRightTab = rightColumnComputedTabs.value.find(t => t.id === rightActiveTabId.value);
      if (activeRightTab && activeRightTab.type === 'terminal') { // Example: focus terminal if active
          fsStore.setActiveFile(null); // Terminals don't use fsStore.activeFile
          // terminalStore.setActiveTerminal(activeRightTab.id); // MainColumn already does this
          console.log(`[AppLayout Watcher leftActiveTabId] Left column cleared. Right column has active terminal ${activeRightTab.id}. Global file focus cleared.`);
      } else {
        fsStore.setActiveFile(null);
        console.log(`[AppLayout Watcher leftActiveTabId] Left column cleared. No other editor/specific tab to focus. Global file focus cleared.`);
      }
    }
  }
});

watch(rightActiveTabId, (newPath, oldPath) => {
  console.log(`[AppLayout Watcher rightActiveTabId] Changed from ${oldPath} to ${newPath}`);
  const tabInfo = rightColumnComputedTabs.value.find(t => t.id === newPath);

  // If right column's active tab is an editor (assuming it can be)
  if (newPath && tabInfo?.type === 'editor') {
    lastInteractedColumn.value = 'right';
    fsStore.setActiveFile(newPath);
    console.log(`[AppLayout Watcher rightActiveTabId] Right column editor tab ${newPath} active. Global focus set.`);
  } else if (newPath && tabInfo?.type === 'terminal') {
    lastInteractedColumn.value = 'right';
    fsStore.setActiveFile(null); // Terminals don't use activeFilePath from fsStore
    // terminalStore.setActiveTerminal(newPath); // MainColumn's internal watcher for activeTabId will handle this if it's a terminal tab
    console.log(`[AppLayout Watcher rightActiveTabId] Right column terminal tab ${newPath} active. Global file focus cleared.`);
  } else if (!newPath && lastInteractedColumn.value === 'right') {
    const leftEditorTab = leftColumnComputedTabs.value.find(t => t.id === leftActiveTabId.value && t.type === 'editor');
    if (leftEditorTab) {
      fsStore.setActiveFile(leftEditorTab.id);
      lastInteractedColumn.value = 'left';
      console.log(`[AppLayout Watcher rightActiveTabId] Right column cleared. Global focus passed to left editor: ${leftEditorTab.id}`);
    } else {
      fsStore.setActiveFile(null);
      console.log(`[AppLayout Watcher rightActiveTabId] Right column cleared. No other editor tab to focus. Global focus cleared.`);
    }
  } else if (newPath && tabInfo?.type === 'other') { // Handle static "other" tabs
    lastInteractedColumn.value = 'right';
    fsStore.setActiveFile(null); // "Other" tabs don't use activeFilePath from fsStore
    console.log(`[AppLayout Watcher rightActiveTabId] Right column other tab ${newPath} active. Global file focus cleared.`);
  }
});

const handleOpenFile = async (file: FileSystemObject) => {
  if (file.is_dir) return;

  const filePath = file.path;
  // Check if already open (fsStore.openFiles is source of truth for editor tabs)
  if (!fsStore.openFiles[filePath]) {
    console.log(`[AppLayout handleOpenFile] File ${filePath} not in fsStore.openFiles. Fetching content...`);
    // fsStore.fetchFileContent will add it to openFiles, leftColumnComputedTabs will update.
    await fsStore.fetchFileContent(filePath).catch(err => { // await to ensure it's processed before setting active
        console.error(`[AppLayout handleOpenFile] Error fetching ${filePath}:`, err);
    });
  } else {
    console.log(`[AppLayout handleOpenFile] File ${filePath} already in fsStore.openFiles.`);
  }
  
  // Activate tab in the left column
  leftActiveTabId.value = filePath;
  console.log(`[AppLayout handleOpenFile] Left active tab ID set to: ${filePath}`);
  // The watcher for leftActiveTabId will handle setting fsStore.activeFilePath if it's an editor.
};

const closeLeftTab = (tabId: string) => {
  console.log(`[AppLayout closeLeftTab] Attempting to close left tab: ${tabId}`);
  const tabToClose = leftColumnComputedTabs.value.find(t => t.id === tabId);

  if (tabToClose && tabToClose.type === 'editor') {
    fsStore.closeFile(tabId); // This will also handle resetting activeFilePath if needed
    // Determine next active tab
    // Wait for computed list to update
    nextTick(() => {
      if (leftActiveTabId.value === tabId || !leftColumnComputedTabs.value.find(t => t.id === leftActiveTabId.value)) { // If closed tab was active or active disappeared
        if (leftColumnComputedTabs.value.length > 0) {
          // Attempt to find a reasonable next tab (e.g., the first one)
          // More sophisticated logic (e.g., previous tab) could be added here
          const newActiveTab = leftColumnComputedTabs.value[0];
          leftActiveTabId.value = newActiveTab.id;
          console.log(`[AppLayout closeLeftTab] Closed ${tabId}. New active left tab: ${newActiveTab.id}`);
        } else {
          leftActiveTabId.value = null;
          console.log(`[AppLayout closeLeftTab] Closed ${tabId}. No tabs left in left column.`);
        }
      }
    });
  }
};

const closeRightTab = (tabId: string) => {
  console.log(`[AppLayout closeRightTab] Attempting to close right tab: ${tabId}`);
  const tabToClose = rightColumnComputedTabs.value.find(t => t.id === tabId);

  if (tabToClose) {
    if (tabToClose.type === 'terminal') {
      terminalStore.closeTerminal(tabId);
    } else if (tabToClose.type === 'other') {
      // Remove from staticRightTabs if it's closable and managed there
      const staticTabIndex = staticRightTabs.value.findIndex(t => t.id === tabId);
      if (staticTabIndex !== -1) {
        staticRightTabs.value.splice(staticTabIndex, 1);
        console.log(`[AppLayout closeRightTab] Closed static 'other' tab: ${tabId}`);
      }
    }
    // Determine next active tab
    nextTick(() => {
       if (rightActiveTabId.value === tabId || !rightColumnComputedTabs.value.find(t => t.id === rightActiveTabId.value)) {
        if (rightColumnComputedTabs.value.length > 0) {
          const newActiveTab = rightColumnComputedTabs.value[0];
          rightActiveTabId.value = newActiveTab.id;
          console.log(`[AppLayout closeRightTab] Closed ${tabId}. New active right tab: ${newActiveTab.id}`);
        } else {
          rightActiveTabId.value = null;
          console.log(`[AppLayout closeRightTab] Closed ${tabId}. No tabs left in right column.`);
        }
      }
    });
  }
};

const handleToggleAgentTab = (projectId: string) => {
  console.log(`[AppLayout handleToggleAgentTab] Toggle Agent tab for project: ${projectId}`);
  
  // Check if Agent tab exists in the right column
  const agentTab = rightColumnComputedTabs.value.find(t => t.id === 'agent');
  
  if (agentTab) {
    // If Agent tab exists, check if it's already active
    if (rightActiveTabId.value === 'agent') {
      // Agent tab is already active, do nothing or optionally focus it
      console.log(`[AppLayout handleToggleAgentTab] Agent tab is already active`);
      return;
    } else {
      // Agent tab exists but not active, switch to it
      rightActiveTabId.value = 'agent';
      console.log(`[AppLayout handleToggleAgentTab] Switched to existing Agent tab`);
      return;
    }
  } else {
    // Agent tab doesn't exist (was closed), add it back and make it active
    const agentTabExists = staticRightTabs.value.find(t => t.id === 'agent');
    if (!agentTabExists) {
      staticRightTabs.value.push({ id: 'agent', title: 'Agent', type: 'other' });
      console.log(`[AppLayout handleToggleAgentTab] Added Agent tab back to staticRightTabs`);
    }
    // Wait for computed to update, then set as active
    nextTick(() => {
      rightActiveTabId.value = 'agent';
      console.log(`[AppLayout handleToggleAgentTab] Set Agent tab as active`);
    });
  }
};

</script>

<style scoped>
.app-layout {
  grid-template-rows: 40px 1fr;
  background-color: #1a1a1f;
  /* Ensure grid container takes full viewport space */
  height: 100vh;
  width: 100vw;
  display: grid; /* Explicitly set display grid */
}

.layout-toolbar {
  grid-row: 1;
  grid-column: 1 / -1;
}

.layout-content {
  grid-row: 2;
  grid-column: 1;
  /* Dynamic grid-template-columns applied via :style="gridStyle" */
  overflow: hidden;
  display: grid; /* Explicitly set display grid */
  /* Ensure this container fills its parent grid row */
  height: 100%;
  width: 100%;
}

/* Ensure columns take full height AND width of their track and handle overflow */
.layout-iconbar,
.layout-filebrowser,
.layout-leftcol,
.layout-splitter, /* Include splitter */
.layout-rightcol {
  height: 100%;
  width: 100%; /* Ensure they take full width of their grid area */
  min-height: 0;
  min-width: 0; /* Add min-width: 0 for grid/flex item sizing */
  overflow: hidden; /* Apply overflow hidden here */
}

.layout-iconbar {
  grid-column: 1;
}

.layout-filebrowser {
  grid-column: 2;
}

.layout-leftcol {
  grid-column: 3;
  background-color: #333338;
}

.layout-splitter {
  grid-column: 4; /* Splitter occupies its own column */
  background-color: #4a4a4f; /* Slightly different color */
  /* width: 6px; */ /* Width is set by grid-template-columns now */
  cursor: col-resize;
  z-index: 10; /* Ensure splitter is clickable */
  transition: background-color 0.15s ease;
}
.layout-splitter:hover {
   background-color: #5a8dee; /* Highlight on hover */
}

.layout-rightcol {
  grid-column: 5; /* Right column is now 5th */
  background-color: #404045;
}
</style> 