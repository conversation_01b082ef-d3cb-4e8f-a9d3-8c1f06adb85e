---
type: "always_apply"
---

# @guolao/vue-monaco-editor Integration Guide (.cursorrule)

This document outlines key API usage for the `@guolao/vue-monaco-editor` component, focusing on event handling and data binding for the standard editor.

## Key Component: VueMonacoEditor

This is the standard editor component provided by the library.

### Props for Data Binding (Content)

*   **`value`**:
    *   Type: `string`
    *   Description: Represents the content of the editor.
    *   **Usage for Two-Way Binding**: Use `v-model:value="yourRef"` to achieve two-way data binding.
        ```vue
        <template>
          <VueMonacoEditor v-model:value="codeContent" ... />
        </template>
        <script setup lang="ts">
        import { ref } from 'vue';
        const codeContent = ref('// initial content');
        </script>
        ```
    *   **Alternative for One-Way Binding + Event**:
        *   Prop: `:value="yourRef"` (or `:model-value="yourRef"` if the component internally maps `model-value` to `value` for the editor instance, but `value` is the documented prop for content).
        *   Event: `@change` (see below).

### Events for Content Changes

*   **`@change`**:
    *   Signature: `(value: string | undefined, event: monaco.editor.IModelContentChangedEvent) => void`
    *   Description: Executes when the editor's content value changes.
    *   **Usage with `v-model:value`**: `v-model:value` handles the update automatically. You can still listen to `@change` for additional logic if needed (e.g., logging, triggering other actions).
        ```vue
        <template>
          <VueMonacoEditor 
            v-model:value="codeContent"
            @change="handleContentChange"
            ... 
          />
        </template>
        <script setup lang="ts">
        import { ref } from 'vue';
        const codeContent = ref('');
        const handleContentChange = (newValue, event) => {
          console.log('Editor content changed via @change:', newValue);
          // codeContent.value is already updated by v-model:value
        };
        </script>
        ```
    *   **Usage without `v-model:value` (Manual Update)**: If you are binding the value one-way (e.g., `:value="codeContent"`), you **must** use `@change` to update your ref.
        ```vue
        <template>
          <VueMonacoEditor 
            :value="codeContent"
            @change="updateCodeContent"
            ... 
          />
        </template>
        <script setup lang="ts">
        import { ref } from 'vue';
        const codeContent = ref('');
        const updateCodeContent = (newValue, event) => {
          if (newValue !== undefined) {
            codeContent.value = newValue;
          }
        };
        </script>
        ```

### Other Important Props and Events

*   **`language`**:
    *   Type: `string`
    *   Description: Sets the language of the editor model.
*   **`theme`**:
    *   Type: `'vs' | 'vs-dark'` (or custom themes if defined)
    *   Default: `'vs'`
    *   Description: Sets the editor theme.
*   **`options`**:
    *   Type: `object` (corresponds to `monaco.editor.IStandaloneEditorConstructionOptions`)
    *   Description: Allows passing various Monaco editor construction options.
*   **`@mount`**:
    *   Signature: `(editor: monaco.editor.IStandaloneCodeEditor, monaco: Monaco) => void`
    *   Description: Executes after the editor instance has been created. Useful for getting direct access to the editor instance and the `monaco` API object.
        ```vue
        <script setup lang="ts">
        import { shallowRef } from 'vue';
        const editorInstance = shallowRef(null);
        const handleMount = (editor, monaco) => {
          editorInstance.value = editor;
          // Now you can use editorInstance.value.focus(), etc.
        };
        </script>
        ```

### Common Pitfalls / Reminders:

1.  **Event for `v-model` like behavior**: The primary event for content changes is `@change`. Do not confuse with `@update:modelValue` unless the library's Vue 3 wrapper specifically implements it that way (the documentation for `@guolao/vue-monaco-editor` points to `v-model:value` and `@change`).
2.  **Data Binding Prop**: The documented prop for the content is `value`.
3.  **CDN Loading**: This library loads Monaco Editor from a CDN by default. Ensure `loader.config({...})` is set up correctly in your application's entry point (e.g., `main.ts`) if you need to customize CDN paths or load Monaco from `node_modules`.

This rule prioritizes the documented API from [npmjs.com/package/@guolao/vue-monaco-editor](mdc:https:/www.npmjs.com/package/@guolao/vue-monaco-editor).
