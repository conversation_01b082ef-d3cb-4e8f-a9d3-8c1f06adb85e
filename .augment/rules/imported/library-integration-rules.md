---
type: "always_apply"
---

# 第三方库集成通用规则

在集成任何第三方库，特别是新库、复杂库或与构建环境（如 Vite, Webpack）有特定集成要求的库时，务必遵循以下规则，以确保集成顺利并高效排错：

1.  **优先查阅官方文档（针对特定版本）：**
    *   **API 核实:** 仔细阅读文档，确认库的核心 API 用法，包括但不限于：
        *   初始化/配置方法（如 `Library.config()`, `createInstance()`）。
        *   组件的事件名、Props 和 Slot（对于 UI 库）。
        *   获取内部实例或状态的方法（如事件回调、Template Refs、暴露的方法）。
    *   **版本匹配:** 确保查阅的文档版本与你项目中安装的库版本一致。

2.  **关注构建环境集成要求：**
    *   **构建工具配置:** 检查文档或社区是否有针对你当前构建工具（Vite, Webpack, Rollup 等）的特定配置说明（如插件、Worker 设置、全局变量定义等）。
    *   **加载方式:** 明确库是通过 `node_modules` 导入打包，还是通过 CDN 异步加载，并采用相应的配置和错误处理策略。

3.  **验证基础：**
    *   **依赖安装:** 遇到 `Cannot find module`, `Failed to resolve import` 等错误时，**首要检查**该库及其必要的对等依赖（peer dependencies）是否已正确安装。
    *   **配置执行:** 确认所有必需的初始化或配置代码（如在 `main.ts` 或组件 `onMounted` 中）**已被执行**且未报错。

4.  **主动寻求信息与验证：**
    *   **不确定时搜索:** 如果官方文档不清晰、遇到预期之外的行为或与环境相关的错误，**立即使用 Web 搜索工具**查找：
        *   特定库 + 构建工具的集成示例和教程。
        *   GitHub Issues 或 Stack Overflow 上相关的错误报告和解决方案。
        *   社区维护的配置模板或插件。
    *   **最小化测试:** 在独立的、最简化的代码片段中测试库的基本功能和配置，以隔离问题。

5.  **调试优先级：**
    *   **基础优先:** 先确保库的加载、配置和基本 API 调用正常工作，再集成到复杂的应用逻辑中。
    *   **日志验证:** 在关键步骤（如配置加载、实例获取、事件触发）添加明确的日志，验证每一步是否符合预期。

**核心原则：** **不基于假设或过往经验进行集成。对每个第三方库（尤其是不熟悉的库），都应主动查阅其在当前环境下的最新文档和社区实践，验证基础配置和 API 调用，遇到不确定性时立即寻求外部信息。** 