---
type: "always_apply"
---

# 后端架构要求

## 1. 技术栈

*   **语言:** Go (v1.23.0)
*   **Web 框架:** Gin (`github.com/gin-gonic/gin` v1.10.0) - 用于处理 HTTP 请求和路由。
*   **数据库:** MongoDB (`go.mongodb.org/mongo-driver` v1.17.3) - 主要的数据存储。
*   **配置管理:**
    *   Viper (`github.com/spf13/viper` v1.20.1) - 用于加载本地配置文件。
*   **日志:** Zap (`go.uber.org/zap` v1.27.0) - 高性能结构化日志库。
*   **Session 管理:** Gin Sessions (`github.com/gin-contrib/sessions` v1.0.3) + Redis Store (`github.com/boj/redistore` v1.4.1) - 用于用户会话管理。
*   **WebSocket:** Gorilla WebSocket (`github.com/gorilla/websocket` v1.5.3) - 用于实现 WebSocket 通信，尤其在 Runtime 模块的终端功能中。
*   **LLM 集成:** 
    *   OpenAI Compatible API (`github.com/sashabaranov/go-openai` v1.38.3) - 支持与大语言模型进行交互，用于 AI Agent 功能。
    *   Tokenizer (`github.com/tiktoken-go/tokenizer` v0.6.2) - 用于文本分词和 token 计算。
*   **内部服务/协议:** MCP (`github.com/mark3labs/mcp-go` v0.23.1) - 集成了内部的 MCP (Model Context Protocol) 服务，用于工具调用和AI Agent集成。
*   **容器化:** Docker (`github.com/docker/docker` v28.1.1+incompatible) - 运行时环境基于 Docker 容器。
*   **认证与加密:** BCrypt (`golang.org/x/crypto` v0.37.0) - 用于密码哈希和安全认证。
*   **测试:** Go Test + Testify (`github.com/stretchr/testify` v1.10.0) - 单元测试和集成测试框架。
*   **UUID生成:** Google UUID (`github.com/google/uuid` v1.6.0) - 用于生成唯一标识符。
*   **CORS支持:** Gin CORS (`github.com/gin-contrib/cors` v1.7.5) - 跨域资源共享支持。

## 2. 代码架构

*   **整体结构:** 单体应用 (Monolith)，但内部通过目录结构实现了良好的模块化。
*   **主要目录结构:**
    *   `main.go`: 应用主入口，负责初始化、路由注册和服务器启动。
    *   `internal/`: 包含项目内部代码，这些代码不希望被项目外部直接导入。
        *   `projectstore/`: 项目数据存储相关实现 (MongoDB)。
        *   `userstore/`: 用户数据存储相关实现 (MongoDB)。
        *   `agentstore/`: Agent 对话数据存储相关实现 (MongoDB)。
        *   `runtimestore/`: 运行时环境数据存储相关实现 (MongoDB)。
        *   `requirementstore/`: 需求数据存储相关实现 (MongoDB)。
        *   `storage/`: 通用的存储接口或实现（如文件存储交互逻辑）。
            *   `localfs/`: 本地文件系统存储实现，实现了 ProjectStorage 接口。
        *   `runtime/`: 运行时相关具体实现。
            *   `docker/`: Docker provider 实现，负责与容器运行时环境交互。
    *   `config/`: 配置加载和管理逻辑。
        *   `config.yaml`: 主要的配置文件。
        *   `config.go`: 加载和解析配置的代码。
        *   `mcp_servers.json`: MCP 服务器相关配置。
    *   `model/`: 数据模型定义（用于数据库映射、API请求/响应等）。
        *   `project/`: 项目相关的模型。
        *   `user/`: 用户相关的模型。
        *   `agent/`: Agent 对话和消息相关的模型。
        *   `multiagent/`: 多 Agent 对话和消息相关的模型。
        *   `runtime/`: 运行时相关的模型 (例如，容器信息、终端会话等)。
        *   `requirement/`: 需求、问题、回答、开发计划和线框图相关的模型。
    *   `domain/`: 核心业务领域模型和业务规则，按领域划分。
        *   `project/`: 项目领域逻辑，包含项目生命周期管理。
        *   `user/`: 用户领域逻辑，包含认证、授权等。
        *   `agent/`: Agent 领域逻辑，包含对话管理、LLM 交互、工具调用等，支持 ReAct 架构。
        *   `multiagent/`: 多 Agent 领域逻辑，包含对话管理、LLM 交互、工具调用等，支持分层多 agent 架构。
        *   `projectfs/`: 项目文件系统相关的领域逻辑。
        *   `runtime/`: 运行时领域逻辑，包含容器生命周期管理、终端交互等。
        *   `requirement/`: 需求工程领域逻辑，包含需求创建、图片分析、问题生成、开发计划和线框图生成。
    *   `service/`: 业务逻辑层/应用服务层，通常包含 HTTP handlers，处理业务流程。
        *   `user/`: 用户相关的服务 (HTTP handlers)，包含注册、登录、用户管理等。
        *   `projectfs/`: 项目文件系统相关的服务 (HTTP handlers)。
        *   `runtime/`: 运行时相关的服务，包含 WebSocket 终端连接处理。
        *   `agent/`: Agent 相关的服务，包含对话管理、流式响应等。
        *   `multiagent/`: 多 Agent 相关的服务，包含对话管理、流式响应等。
        *   `requirement/`: 需求相关的服务，处理需求创建、查询、更新、删除以及相关生成任务的API。
    *   `pkg/`: 项目内可共享的通用工具函数或库，也可以被外部项目导入。
        *   `mcp_hub/`: MCP Hub 相关逻辑，包含 MCP 工具执行和客户端管理。
        *   `middleware/`: Gin 中间件，包含认证中间件等。
        *   `auth/`: 认证和授权相关的工具或库 (BCrypt 哈希等)。
        *   `llm/`: 大语言模型交互相关的工具或库。
            *   `provider/`: LLM 提供者实现，如 OpenAI Compatible 客户端。
        *   `mongodb/`: MongoDB 客户端封装或辅助函数。
        *   `storage/`: 通用存储客户端或辅助函数，定义了 ProjectStorage 接口。
        *   `tokenizer/`: 文本分词和 token 计算相关工具。
    *   `mcp/`: 与内部 MCP 服务交互的逻辑封装。
        *   `projectfs/`: 处理与项目文件系统相关的 MCP 逻辑。
        *   `runtime/`: 处理与运行时环境相关的 MCP 逻辑。
    *   `prompts/`: 包含与 AI 模型交互相关的提示模板或配置。
        *   `.tmpl` 文件: Go 模板文件，用于生成系统提示。
        *   `systemprompt.go`: 用于加载和管理系统提示。
    *   `system/`: 系统相关的构建和部署脚本。
        *   `docker-compose.yml`: Docker Compose 配置。
        *   `Dockerfile.cached`: 缓存优化的 Dockerfile。
        *   构建脚本: 如 `build-cached.sh`, `run.sh` 等。
    *   `Dockerfile`: 用于构建 Docker 镜像的配置文件。
    *   `go.mod`, `go.sum`: Go 模块依赖管理文件。
    *   `test_workspaces/`: 测试用的工作区目录。

## 3. 逻辑分层

项目的主要业务逻辑遵循以下三层结构：

1.  **Service (服务层):**
    *   位于 `service/` 目录下。
    *   主要负责定义 Gin HTTP Handlers。
    *   处理 HTTP 请求的解析、基本校验，并调用 `domain` 层执行核心业务逻辑。
    *   组装并返回 HTTP 响应。
    *   支持 WebSocket 连接处理 (如终端服务)。
    *   模块包括: `user/`, `projectfs/`, `runtime/`, `agent/` 等。

2.  **Domain (领域层):**
    *   位于 `domain/` 目录下。
    *   包含核心业务逻辑和规则的实现。
    *   操作 `model` 层定义的数据结构。
    *   是业务的核心，被 `service` 层和 `mcp` 层依赖。
    *   包含复杂的业务编排，如项目创建时同时创建运行时环境。
    *   模块包括: `project/`, `user/`, `agent/`, `projectfs/`, `runtime/` 等。

3.  **Model (模型层):**
    *   位于 `model/` 目录下。
    *   定义项目共享的数据结构（如数据库实体、API 请求/响应体、DTOs 等）。
    *   通常只包含数据定义，不包含业务逻辑。
    *   模块包括: `project/`, `user/`, `agent/`, `runtime/` 等。

**补充说明:**

*   **Internal vs Pkg:** `internal/` 目录用于存放项目私有的代码（如数据库具体实现、Docker provider），而 `pkg/` 用于存放可以被其他项目复用的代码（如通用的认证库、LLM 客户端等）。
*   **MCP 层:** `mcp/` 目录处于与 `service` 层相似的位置，负责处理 MCP 协议相关的接入和逻辑，并依赖 `domain` 层来完成业务操作。MCP 提供了 SSE (Server-Sent Events) 接口用于与外部系统集成。
*   **入口与初始化 (`main.go`):**
    *   所有 HTTP 路由 (Gin routes) 的定义集中在 `main.go` 中，提供了一个全局的路由视图。
    *   `domain` 和 `service` (以及 `mcp` 等，包括 `runtime`、`agent` 模块) 的初始化和依赖注入也在 `main.go` 中完成，清晰地展示了服务的启动流程和依赖关系。
    *   依赖注入顺序: 配置加载 → 数据库连接 → 存储层 → 领域层 → 服务层 → 路由注册。
*   **数据持久化:** 具体的数据库交互逻辑（如 CRUD 操作）倾向于放在 `internal/` 下对应的 `store` 子目录中（例如 `internal/userstore`, `internal/agentstore`, `internal/runtimestore`）。`domain` 层会定义存储库接口，并依赖这些接口。`main.go` 负责将 `internal` 中的具体实现注入到 `domain` 层。
    *   **MongoDB 操作封装:** 对于所有 `internal/xxxstore` 中与 MongoDB 的直接交互，应优先使用项目内提供的 `git.nevint.com/fota3/t-rex/pkg/mongodb` 包中封装的辅助函数或客户端（如果存在）。这有助于统一数据库操作模式、错误处理和日志记录。直接使用 `go.mongodb.org/mongo-driver` 的原始 API 应仅限于 `pkg/mongodb` 包内部或在封装库无法满足特定高级需求时。

## 4. 编码风格

*   **遵循 Go 官方规范:** 代码格式化、命名约定等符合 Go 社区标准。
*   **结构清晰:** 代码按功能和层次进行组织，易于理解和维护。
*   **依赖注入:** 通过构造函数或初始化方法注入配置、日志等依赖，降低耦合度。所有依赖注入在 `main.go` 中集中管理。
*   **错误处理:** 使用 Go 的标准错误处理机制，关键初始化失败时使用 `panic`。重要的错误应该包含足够的上下文信息。
*   **并发安全:** 涉及到共享资源的组件（如 MCP 客户端管理器、History Manager）需要考虑线程安全。
*   **测试:** 包含单元测试代码 (e.g., `service_test.go`)，使用 Testify 框架进行断言和模拟。
*   **日志记录:** 使用结构化日志 (Zap)，每个模块都应该有命名的 logger (如 `logger.Named("ProjectFSService")`)。
*   **代码注释:** 每个函数、方法、结构体的声明时，都应该有详细的介绍作为注释。在重要的逻辑前也应该有注释，注释的语言是**英文**，除非用户明确声明使用其他语言写注释，默认使用**英文**。
*   **类型命名约定:**
    *   **`domain` 目录:**
        *   在 `domain/<module_name>/` 路径下的包（例如 `domain/user/`），其主要对外暴露的结构体类型应命名为 `Domain` (例如 `type Domain struct {}`)。
        *   不应在结构体名称前添加模块名作为前缀（例如，不是 `UserDomain`）。
        *   当在其他层（如 `service` 层）需要导入多个 `domain` 包且它们都导出了名为 `Domain` 的结构体时，应使用 Go 的 `import rename` 功能来解决命名冲突。例如：
            ```go
            import (
                UserDomain "git.nevint.com/fota3/t-rex/domain/user"
                ProjectDomain "git.nevint.com/fota3/t-rex/domain/project"
            )
            // 然后可以使用 UserDomain.Domain 和 ProjectDomain.Domain
            ```
    *   **`service` 目录:**
        *   在 `service/<module_name>/` 路径下的包，其主要对外暴露的业务逻辑处理结构体类型应命名为 `Service` (例如 `type Service struct {}`)。

## 5. JSON API 规范

*   **JSON字段命名:** 所有JSON tag应使用 `snake_case` 格式，以符合RESTful API设计规范。
    *   ✅ 正确: `json:"user_id"`
    *   ❌ 错误: `json:"userId"`
*   **API响应一致性:** 确保所有API响应的字段命名保持一致的snake_case格式。
*   **前后端数据格式对齐:** 前端TypeScript接口定义应与后端JSON响应格式保持一致。
*   **SSE事件数据格式:** Server-Sent Events中的数据字段也应遵循snake_case命名规范。

## 6. 特定架构要求

### 6.1 MCP (Model Context Protocol) 集成
*   MCP 是项目中重要的协议层，用于与外部工具和AI Agent进行交互。
*   MCP 服务器配置通过 `config/mcp_servers.json` 管理。
*   MCP 客户端管理器 (`pkg/mcp_hub/`) 负责管理多个 MCP 服务器连接。
*   MCP 工具执行器实现了统一的工具调用接口。

### 6.2 AI Agent架构
*   Agent 使用 ReAct (Reasoning and Acting) 架构。
*   支持流式响应，通过 SSE 与前端进行实时通信。
*   包含历史管理器用于管理对话上下文和 token 限制。
*   系统提示通过模板系统动态生成。

### 6.3 Runtime环境管理
*   基于 Docker 的容器化运行时环境。
*   每个项目可以有独立的运行时容器。
*   支持 WebSocket 终端连接，提供实时交互。
*   运行时环境的生命周期与项目生命周期绑定。

### 6.4 文件系统抽象
*   `pkg/storage/ProjectStorage` 接口定义了项目文件系统的抽象。
*   `internal/storage/localfs/` 提供了本地文件系统的实现。
*   支持安全的路径验证，防止目录逃逸攻击。
*   所有文件操作都在项目沙箱内进行。

### 6.5 需求 (Requirement) 与开发工作流
*   **核心流程:** 项目引入了一个以用户为中心的需求工程工作流，允许用户将一个抽象的想法（“需求”）转化为具体的开发计划和UI原型。
*   **需求创建:** 用户可以创建一个包含详细描述和参考图片的需求。系统支持异步处理上传的图片，通过AI进行深度分析。
*   **交互式细化:**
    *   **问题生成:** 系统会根据初始需求和图片分析结果，智能地生成一系列澄清问题，以帮助用户完善想法。
    *   **回答提交:** 用户回答这些问题后，系统将获得足够的信息来制定开发计划。
*   **自动化生成:**
    *   **开发计划:** 根据用户的回答，AI会自动生成一份详细的、结构化的开发计划（Markdown格式）。
    *   **线框图生成:** 基于开发计划，系统能进一步生成多种（例如5套）不同风格的、可交互的HTML线框图，为用户提供直观的UI/UX原型。
*   **异步状态管理:** 整个工作流是异步的，需求会经历多个状态，如 `pending` (待处理), `image_analyzing` (图片分析中), `processing` (处理中), `plan_generating` (生成计划中), `wireframe_generating` (生成线框图中), 和 `completed` (已完成)。前端通过轮询机制来获取和展示最新的状态。

*   `pkg/storage/ProjectStorage` 接口定义了项目文件系统的抽象。
*   `internal/storage/localfs/` 提供了本地文件系统的实现。
*   支持安全的路径验证，防止目录逃逸攻击。
*   所有文件操作都在项目沙箱内进行。
