# Operating System Files
.DS_Store
Thumbs.db
._*

# IDE Configuration
.idea/
.vscode/
*.suo
*.user
*.sln.docstates

# Log files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*~
*.tmp
*.swp

# Node.js / Frontend Dependencies & Build Output (typically under 'fe/')
node_modules/
/fe/dist/
/fe/coverage/
.npm/
.yarn/
*.tsbuildinfo
*.local
.env*
!.env.example
.nuxt/
.output/

# Go / Backend Build Output & Generated Data (typically under 'be/')
# Add your Go executable name here if applicable, e.g., /be/t-rex
# Example: /be/my_go_app
be/.trex_workspaces/
# Dependency directories (if using Go modules, vendor is often ignored)
# vendor/
be/t-rex
be/system/cache
