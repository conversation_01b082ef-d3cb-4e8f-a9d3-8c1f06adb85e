---
description: 
globs: 
alwaysApply: true
---
# 前端架构要求

本文档主要聚焦于当前项目的前端架构约束，在本项目的前端代码书写中，请优先遵循本约束。

## 1. 项目概述

T-Rex 项目前端是一个基于 Vue 3 的 Web IDE 应用，提供项目管理、文件编辑、终端操作和 AI Agent 聊天等功能。

## 2. 技术栈

*   **框架:** Vue.js (`vue` v3.4.31)
*   **构建工具:** Vite (`vite` v5.3.3) - 提供快速的冷启动和热模块替换 (HMR)
*   **语言:** TypeScript (`typescript` v5.2.2) - 提供静态类型检查
*   **UI 库:** Element Plus (`element-plus` v2.7.7) - 提供丰富的后台组件
*   **状态管理:** Pinia (`pinia` v2.1.7) - Vue 官方推荐的状态管理库
*   **路由:** Vue Router (`vue-router` v4.4.0) - 处理前端路由和页面导航
*   **HTTP 请求:** Axios (`axios` v1.7.2) - 用于与后端 API 进行交互
*   **CSS 工具:** UnoCSS (`unocss` v0.61.3) - 原子化 CSS 框架，用于快速构建样式
*   **包管理器:** Yarn
*   **核心功能库:**
    *   `@guolao/vue-monaco-editor` v1.5.5: Monaco Editor 的 Vue 3 集成库，用于代码编辑
    *   `monaco-editor` v0.52.2: Monaco Editor 核心库
    *   `@xterm/xterm` v5.5.0: 核心的 Web 终端 UI 组件库
    *   `@xterm/addon-fit` v0.10.0: Xterm.js 插件，用于使终端尺寸适应其容器大小
    *   `@xterm/addon-web-links` v0.11.0: Xterm.js 插件，用于自动检测和激活终端输出中的 URL
    *   `@xterm/addon-attach` v0.11.0: Xterm.js 插件，用于处理 WebSocket 连接
    *   `@microsoft/fetch-event-source` v2.0.1: 用于处理 Server-Sent Events (SSE)
    *   `vue3-click-away` v1.2.4: Vue 3 点击外部区域指令
    *   `@element-plus/icons-vue` v2.3.1: Element Plus 图标库

## 3. 代码架构

*   **项目结构:** 遵循 Vite + Vue 3 项目的标准结构
*   **主要目录 (`fe/src/`):**
    *   `main.ts`: 应用入口文件，初始化 Vue 实例、插件（Router, Pinia, ElementPlus 等）、Monaco Editor 配置并挂载应用
    *   `App.vue`: 根 Vue 组件，只包含 `<router-view />`
    *   `api/`: 封装对后端接口的请求函数，按模块划分
        *   `user.ts`: 用户认证相关的 API 调用（登录、注册、用户信息等）
        *   `project.ts`: 项目管理相关的 API 调用（项目列表、创建项目、删除项目等）
        *   `fs.ts`: 文件系统相关的 API 调用（文件操作、目录管理、文件内容读写等）
        *   `agent.ts`: AI Agent 相关的 API 调用（对话管理、SSE 流式响应、消息发送等）
        *   `types.ts`: 通用的 TypeScript 类型定义（User、Project、FileSystemObject 等）
        *   `requirement.ts`: 需求管理相关的API调用（创建、查询、更新、删除、生成问题、提交答案、生成线框图等）。
    *   `components/`: 存放可复用的 UI 组件（原子组件、业务组件）
        *   `Auth/`: 认证相关组件
            *   `LoginForm.vue`: 登录表单组件
            *   `RegisterForm.vue`: 注册表单组件
        *   `message/`: AI Agent 消息展示相关组件
            *   `AssistantMessage.vue`: 助手消息组件，支持完成状态、后续问题、工具调用结果等
            *   `UserMessage.vue`: 用户消息组件
        *   `FileBrowser.vue`: 文件和目录树的展示、交互（创建、重命名、删除、打开文件）
        *   `IconBar.vue`: 左侧的图标导航栏，包含终端和 Agent 按钮
        *   `MainColumn.vue`: 主内容区域，支持多 Tab 显示（编辑器、终端、其他类型）
        *   `TopToolbar.vue`: 顶部工具栏，包含保存、重建 Runtime、项目切换器、用户菜单等
        *   `ProjectSwitcher.vue`: 项目切换器组件，支持项目列表、搜索、创建新项目
        *   `TerminalView.vue`: 封装 Xterm.js 实例，处理终端的创建、WebSocket 连接、重连等
        *   `AgentChat.vue`: AI Agent 聊天界面的主组件，包含消息列表、输入框、SSE 流式响应
        *   `MessageRenderer.vue`: 消息渲染组件，根据消息类型选择对应的渲染组件
        *   `MessageContent.vue`: 消息内容组件，处理基础的文本内容展示
        *   `DiffViewer.vue`: 文件差异查看器，基于 Monaco Diff Editor，支持文件变更可视化
        *   `RequirementForm.vue`: 用于创建和编辑需求的可复用表单组件。
        *   `QuestionList.vue`: 展示AI生成的问题列表。
        *   `AnswerForm.vue`: 提供用户回答问题的界面。
        *   `ImageUploadArea.vue`: 支持图片上传、预览和删除的区域组件。
        *   `AIModelSelector.vue`: 用于选择不同AI模型的下拉组件。
    *   `layout/`: 存放定义应用**整体结构或区域布局**的组件
        *   `AppLayout.vue`: IDE 界面的主要布局，定义了包含 `TopToolbar`、`IconBar`、`FileBrowser` 和两个 `MainColumn` 的整体结构，支持列宽调整
    *   `router/`: Vue Router 的配置
        *   `index.ts`: 路由配置文件，定义了登录、注册、项目列表、IDE 界面等路由
    *   `store/`: Pinia 的状态模块，按业务或功能划分
        *   `fs.ts`: 核心的状态管理模块，负责管理文件系统状态（目录树、打开的文件内容、修改状态）、活动项目、文件相关的 API 调用等
        *   `terminal.ts`: 管理终端相关状态，包括终端实例列表、活动终端、WebSocket 连接状态等
        *   `user.ts`: 管理用户相关状态，包含用户信息、认证状态、登录注册逻辑等
        *   `agent.ts`: 管理 AI Agent 相关状态，包含对话历史、SSE 流式处理、Agent 交互状态、文件系统工具检测等
        *   `requirement.ts`: 管理需求相关的状态，包括需求列表、分页、创建/更新/删除操作的状态、以及异步任务（如问题生成、开发计划生成、线框图生成）的状态和轮询逻辑。
    *   `styles/`: 全局 CSS 样式
        *   `main.css`: 全局样式文件，包含 Element Plus 暗色主题覆盖
    *   `utils/`: 通用工具函数
        *   `request.ts`: 封装 Axios 实例，包含请求/响应拦截器、认证处理、错误处理等
        *   `languageMap.ts`: 文件扩展名到 Monaco Editor 语言的映射工具函数
    *   `views/`: 存放**页面级别**的组件，代表完整的页面或路由目标
        *   `LoginView.vue`: 用户登录页面，使用 `LoginForm` 组件
        *   `RegisterView.vue`: 用户注册页面，使用 `RegisterForm` 组件
        *   `ProjectsView.vue`: 用户项目列表页面，展示项目列表，提供创建和删除项目功能
        *   `NewProjectView.vue`: 创建新项目的页面（可能已被 ProjectsView 集成）
        *   `HomeView.vue`: 项目的主入口页面和仪表盘。它为未登录用户提供了快速创建项目的引导，并为已登录用户提供了一个功能完整的需求管理中心。

## 4. 逻辑分层

前端代码内部体现了清晰的分层架构：

1.  **视图层 (View):**
    *   由 `views/` 中的页面组件和 `components/` 中的可复用组件构成
    *   负责 UI 的展示和用户交互的响应
    *   使用 Vue 3 Composition API、Element Plus 组件构建界面
    *   集成 Monaco Editor 提供代码编辑功能，Xterm.js 提供终端界面

2.  **状态管理层 (State Management):**
    *   由 `store/` 中的 Pinia stores 负责（`fs.ts`, `terminal.ts`, `user.ts`, `agent.ts`）
    *   管理全局应用状态和跨组件共享的数据
    *   封装对状态的读取（getters）和修改（actions）
    *   处理复杂的业务逻辑，如文件系统操作、终端管理、SSE 流处理

3.  **路由层 (Routing):**
    *   由 `router/` 负责
    *   定义页面路由规则，实现页面跳转和导航控制
    *   包含简单的认证守卫（主要依赖后端 API 进行认证）

4.  **API 交互层 (API Interaction):**
    *   由 `api/` 目录下的模块负责
    *   封装 Axios 请求、WebSocket 连接管理和 SSE 处理
    *   提供语义化的函数供业务逻辑调用
    *   处理请求/响应的拦截、数据格式转换（camelCase ↔ snake_case）、错误处理等

5.  **业务逻辑 (Business Logic):**
    *   分散在 Vue 组件的 `<script setup>` 部分和 Pinia stores 的 `actions` 中
    *   响应用户交互，调用 API 层获取数据或建立连接
    *   调用状态管理层更新状态，最终驱动视图更新

## 5. 核心功能架构

### 5.1 文件系统管理
*   **FileBrowser**: 树形展示项目文件结构，支持右键菜单操作（创建、重命名、删除）
*   **Monaco Editor**: 集成在 MainColumn 中，支持多文件 Tab 编辑，语法高亮，代码智能提示
*   **文件状态管理**: fsStore 管理文件内容、修改状态、保存逻辑，支持 Base64 编码传输

### 5.2 终端系统
*   **TerminalView**: 基于 Xterm.js 的终端组件，支持完整的终端功能
*   **WebSocket 连接**: 与后端建立实时终端连接，支持断线重连
*   **多终端支持**: terminalStore 管理多个终端实例，支持 Tab 切换

### 5.3 AI Agent 系统
*   **AgentChat**: 聊天界面主组件，支持消息历史、输入框、实时响应
*   **SSE 流处理**: 实时接收和展示 AI 响应，支持流式输出
*   **消息渲染**: 支持多种消息类型（文本、工具调用、文件差异、后续问题等）
*   **文件系统集成**: 自动检测并响应文件系统工具操作，触发界面刷新

### 5.4 项目管理
*   **项目列表**: 展示用户项目，支持搜索、创建和删除
*   **项目切换**: TopToolbar 中的 ProjectSwitcher，快速切换项目
*   **IDE 界面**: AppLayout 提供完整的 IDE 体验，支持布局调整

### 5.5 需求工程与自动化 (通过 HomeView)
*   **需求仪表盘**: `HomeView.vue` 作为已登录用户的需求管理中心，集中展示和管理所有需求。
*   **完整创建流程**: 用户可以在此页面创建新需求，包括填写详细描述、选择AI模型，以及上传参考图片。
*   **交互式需求细化**:
    *   用户可以在需求卡片上直接触发AI生成问题。
    *   通过弹出的详情对话框，用户可以查看问题并提交答案。
*   **自动化内容生成**:
    *   提交答案后，系统会自动触发开发计划的生成。
    *   用户可以查看Markdown格式的开发计划，并进一步触发交互式HTML线框图的生成。
*   **异步状态跟踪**: 整个界面通过 `requirementStore` 中的轮询逻辑，实时更新需求的状态（如图片分析中、计划生成中、线框图生成中），为用户提供即时反馈。
*   **多方案线框图查看**: 生成的线框图支持多套方案切换和预览，用户可以在安全的沙箱化`iframe`中与原型进行交互。

## 6. 编码风格

*   **TypeScript**: 利用 TypeScript 进行类型约束，提高代码健壮性
*   **Vue 3 Composition API**: 大量使用 `<script setup>` 语法糖，使代码更简洁、逻辑更聚合
*   **响应式设计**: 使用 `ref`, `computed`, `watch` 等 Vue 3 响应式 API
*   **模块化**: 代码按功能组织在不同的目录和文件中
*   **组件化**: 大量使用可复用组件提高开发效率和代码一致性
*   **类型安全**: 所有 API 接口、状态管理都有完整的 TypeScript 类型定义
*   **代码注释**: 每个函数、方法、组件都应该有详细的英文注释，重要逻辑前也应该有注释

## 7. JSON API 规范

*   **JSON字段命名**: 所有JSON tag应使用 `snake_case` 格式，以符合RESTful API设计规范
    *   ✅ 正确: `json:"user_id"`
    *   ❌ 错误: `json:"userId"`
*   **API响应一致性**: 确保所有API响应的字段命名保持一致的snake_case格式
*   **前后端数据格式对齐**: 前端TypeScript接口定义应与后端JSON响应格式保持一致
*   **SSE事件数据格式**: Server-Sent Events中的数据字段也应遵循snake_case命名规范

## 8. 样式规范

*   **UnoCSS**: 使用原子化 CSS 类进行样式编写
*   **暗色主题**: 整体采用暗色主题设计，与 VS Code 风格一致
*   **Element Plus 覆盖**: 通过全局样式覆盖 Element Plus 组件的默认样式
*   **作用域样式**: 组件级别使用 `<style scoped>` 避免样式污染
*   **深度选择器**: 使用 `:deep()` 选择器覆盖第三方组件样式

## 9. 数据流管理

*   **单向数据流**: 遵循 Vue/Pinia 的单向数据流原则
*   **响应式更新**: 状态变更自动触发视图更新
*   **异步操作**: 使用 async/await 处理异步操作，统一错误处理
*   **实时通信**: WebSocket（终端）和 SSE（AI Agent）用于实时数据交换

## 10. 性能优化

*   **懒加载**: 路由级别的代码分割
*   **组件缓存**: 使用 `shallowRef` 优化大对象的响应式性能
*   **虚拟滚动**: 消息列表等长列表使用合适的滚动策略
*   **Monaco Editor**: 配置 Web Workers 提升编辑器性能
*   **SSE 优化**: 合理处理 SSE 事件，避免频繁的 DOM 更新

## 11. 错误处理

*   **全局错误拦截**: Axios 拦截器统一处理 API 错误
*   **用户友好提示**: 使用 Element Plus 的 Message 组件显示操作结果
*   **状态恢复**: 连接断开时提供重连机制（终端、Agent）
*   **表单验证**: 表单输入的客户端验证和错误显示

# CSS 样式覆盖指南 (特别是针对第三方 UI 库)

## 问题背景

在尝试覆盖第三方 UI 组件库（如 Element Plus, Vuetify 等）的默认样式时，可能会遇到看似简单的样式修改（如移除边框、修改背景色）反复尝试却无法生效的情况，即使使用了 `:deep()` 或 `!important`。

## 原因分析

这种情况通常由以下几个关键原因导致：

1.  **组件库样式的复杂性和优先级 (Specificity):**
    *   UI 库内部 CSS 结构复杂，可能使用高优先级的选择器。
    *   库自身的样式规则优先级可能压制 `:deep()` 或 `!important`。

2.  **`scoped` 样式的局限性:**
    *   `:deep()` 虽然能穿透作用域，但生成的选择器优先级不一定能胜过库的原始规则。

3.  **CSS 变量与直接属性:**
    *   仅修改库暴露的 CSS 变量 (e.g., `--el-card-border-color`) 可能无效，如果库在其他地方以更高优先级直接设置了对应属性 (e.g., `border`)。

4.  **缓存:** 浏览器或开发服务器缓存有时会导致样式更新不及时。

## 解决方案与策略

当遇到难以覆盖的第三方库样式时，可以按以下顺序尝试策略：

1.  **基础检查 (优先):**
    *   **强制刷新:** `Cmd+Shift+R` / `Ctrl+Shift+R` 清除浏览器缓存。
    *   **重启开发服务器:** 停止并重新启动 Vite/Webpack 等。

2.  **开发者工具检查:**
    *   使用浏览器开发者工具检查目标元素，查看是哪个 CSS 规则在实际生效，确定是 `border`、`box-shadow` 还是其他属性导致的问题。
    *   注意查看样式的来源和选择器优先级。

3.  **提升样式优先级 (在 `scoped` 内):**
    *   **增加选择器具体性:** 尝试使用更具体的选择器路径结合 `:deep()`。
    *   **使用 `!important`:** 谨慎使用，虽然有时有效，但不是最佳实践，且可能仍被覆盖。

4.  **覆盖 CSS 变量 (如果库支持且文档推荐):**
    *   查找库文档，看是否可以通过覆盖其 CSS 自定义属性 (变量) 来修改样式。
    *   ```css
      /* 在 scoped 或 global style 中 */
      :deep(.target-component-class) {
        --library-variable-name: new-value !important;
      }
      ```

5.  **直接覆盖属性 (更强制):**
    *   直接在 `:deep()` 规则中覆盖目标 CSS 属性，并使用 `!important`。
    *   ```css
      /* 在 scoped style 中 */
      .wrapper-class :deep(.target-component-class) {
        border: none !important;
        box-shadow: none !important; /* 根据需要覆盖 */
      }
      ```

6.  **全局样式覆盖 (最终手段):**
    *   如果 `scoped` 内的所有尝试都失败，可以创建一个不带 `scoped` 的 `<style>` 块，直接针对组件库的类名进行覆盖。
    *   这种方法会影响所有使用该组件的地方，需要谨慎使用，但通常是优先级最高的覆盖方式。
    *   ```css
      /* 在 .vue 文件顶部或全局 CSS 文件中 */
      .el-card { /* 直接使用库的类名 */
        border: none !important;
        box-shadow: none !important;
        background-color: desired-color !important;
      }
      ```

**核心原则:** 优先尝试作用域内的、优先级较低的方法。当遇到困难时，逐步提升策略的"强制性"，并利用开发者工具精确诊断问题根源。全局样式覆盖应作为最后的手段。

## 12. 组件修改与调试的核心原则 (必读)

为了避免在组件开发和问题修复过程中发生由于上下文缺失、错误假设和过度修改导致的连锁故障，所有开发者（特别是 AI 助手）在修改任何 Vue 组件前，必须严格遵守以下原则。这些原则旨在确保修改的精确性、安全性和对现有代码结构的尊重。

### 原则一：绝对禁止孤立修改 (No Isolated Changes)

- **规则**: 在修改一个子组件 (`Child.vue`) 之前，**必须**首先阅读并理解其所有直接父组件 (`Parent.vue`) 是如何调用它的。
- **检查清单**:
    -   父组件向子组件传递了哪些 `props`？（名称、类型、是否可选）
    -   父组件监听了子组件的哪些 `@emit` 事件？
    -   子组件被放置在父组件的什么布局环境中？（例如，`flex` 容器、`grid` 布局、有无特定的对齐或尺寸限制类）
- **反面教材**: 直接修改 `UserMessage.vue` 的布局样式，却没有意识到其父组件 `AgentChat.vue` 已经通过 `max-w-[90%]` 和 `flex` 完美地控制了其最大宽度和对齐，导致样式冲突和显示异常。

### 原则二：验证数据流，而非假设 (Verify Data Flow, Don't Assume)

- **规则**: 不要假设一个组件的 `props` 中存在某个字段。如果代码中需要访问 `props.message.timestamp`，必须首先确认 `props.message` 对象及其 `timestamp` 字段是否总是被稳定地提供。
- **检查清单**:
    -   查看父组件传递该 `prop` 的地方，确认数据源。
    -   对于可选字段，在使用前必须进行空值或 `undefined` 检查。
- **反面教材**: 在 `UserMessage.vue` 中直接使用 `message.timestamp`，但其中间组件 `MessageRenderer.vue` 在某些逻辑分支下没有传递完整的 `message` 对象，导致 `Cannot read properties of undefined` 运行时错误。

### 原则三：最小化、精确化修复 (Minimal, Precise Fixes)

- **规则**: 修复 Bug 的首要目标是**以最小的代价解决问题**。严禁在修复过程中进行不相关的代码“重构”、“优化”或“美化”。
- **检查清单**:
    -   这次修改是否只影响到了问题的直接原因？
    -   是否引入了新的样式或逻辑？如果是，是否有必要？
    -   能否用一行 CSS/JS 解决问题？如果可以，就不要改动三行。
- **反面教材**: 为了解决一个简单的对齐问题，引入了 `el-card` 组件，彻底改变了 `UserMessage.vue` 的 DOM 结构和样式，引发了更多、更严重的新问题。正确的做法应该是在现有结构上微调 CSS。

### 原则四：先分析定位，再动手编码 (Analyze First, Code Later)

- **规则**: 在编写任何修复代码之前，必须能够用清晰的语言解释问题的**根本原因 (Root Cause)**。
- **检查清单**:
    -   问题是什么？(What is the problem?)
    -   为什么会发生？(Why did it happen? - The root cause)
    -   我打算怎么修复？(How will I fix it?)
    -   这个修复为什么是有效且安全的？(Why is this fix effective and safe?)
- **反面教材**: 面对文字垂直排列问题，反复尝试不同的 CSS 属性（`inline-block`, `fit-content`），而不是先停下来分析是由于父子组件布局冲突导致的。这属于无效的试错，而非有效的调试。

**核心原则:** 优先尝试作用域内的、优先级较低的方法。当遇到困难时，逐步提升策略的"强制性"，并利用开发者工具精确诊断问题根源。全局样式覆盖应作为最后的手段。
**构建测试** 优先用vite构建 忽略ts可能的错误