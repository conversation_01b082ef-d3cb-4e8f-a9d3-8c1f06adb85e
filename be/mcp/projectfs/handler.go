package projectfs

import (
	"context" // MCP handlers often use context
	"errors"  // Needed for error handling
	"fmt"     // Needed for error formatting
	"os"      // Needed for os.ErrNotExist check
	"regexp"
	"strings"
	"time"

	"git.nevint.com/fota3/t-rex/domain/projectfs" // Depend on domain layer interface
	"git.nevint.com/fota3/t-rex/pkg/storage"      // For FileInfo etc.

	// Assuming this is the MCP library used
	"go.uber.org/zap"
	// Import MCP error types if available, e.g.:
	// mcpErrors "github.com/mark3labs/mcp-go/errors"
	"encoding/json" // Needed for marshaling results

	"github.com/mark3labs/mcp-go/mcp"
	mcpServerLib "github.com/mark3labs/mcp-go/server"
)

// ProjectFSHandler handles MCP requests related to project filesystem operations.
type ProjectFSHandler struct {
	domain *projectfs.Domain // Dependency on the domain layer interface
	logger *zap.Logger
}

// NewProjectFSHandler creates a new MCP handler for projectfs.
func NewProjectFSHandler(domain *projectfs.Domain, logger *zap.Logger) *ProjectFSHandler { // Accept domain
	if logger == nil {
		logger = zap.NewNop()
	}
	return &ProjectFSHandler{
		domain: domain, // Store domain
		logger: logger.Named("MCPProjectFSHandler"),
	}
}

// --- MCP Operation Request/Response Structs ---

// ListDirectoryMCPRequest defines the parameters for the ListDirectory MCP operation.
type ListDirectoryMCPRequest struct {
	ProjectID string `json:"projectID"`
	Path      string `json:"path"` // Defaults to "." if empty, handled by domain layer now potentially
}

// ListDirectoryMCPResponse defines the result for the ListDirectory MCP operation.
type ListDirectoryMCPResponse struct {
	Files []storage.FileInfo `json:"files"`
}

// --- CreateFSObject ---

// CreateFSObjectMCPRequest defines parameters for creating a file or directory.
type CreateFSObjectMCPRequest struct {
	ProjectID string `json:"projectID"`
	Path      string `json:"path"`
	IsDir     bool   `json:"isDir"`
	Content   string `json:"content,omitempty"` // Plain text content for files
}

// CreateFSObjectMCPResponse contains info about the created object.
type CreateFSObjectMCPResponse struct {
	FileInfo storage.FileInfo `json:"fileInfo"`
}

// --- ReadFile ---

// ReadFileMCPRequest defines parameters for reading a file.
type ReadFileMCPRequest struct {
	ProjectID string `json:"projectID"`
	Path      string `json:"path"`
}

// ReadFileMCPResponse contains the file content.
type ReadFileMCPResponse struct {
	Content string `json:"content"` // Plain text content
}

// --- WriteFile ---

// WriteFileMCPRequest defines parameters for writing a file.
type WriteFileMCPRequest struct {
	ProjectID string `json:"projectID"`
	Path      string `json:"path"`
	Content   string `json:"content"` // Plain text content
}

// WriteFileMCPResponse is empty for successful writes.
type WriteFileMCPResponse struct {
	Success bool   `json:"success"`
	Path    string `json:"path"`
	Message string `json:"message"`
}

// --- DeleteFSObject ---

// DeleteFSObjectMCPRequest defines parameters for deleting a file or directory.
type DeleteFSObjectMCPRequest struct {
	ProjectID string `json:"projectID"`
	Path      string `json:"path"`
}

// DeleteFSObjectMCPResponse is empty for successful deletions.
type DeleteFSObjectMCPResponse struct {
	Success bool   `json:"success"`
	Path    string `json:"path"`
	Message string `json:"message"`
}

// DeleteFSObject handles the mcp.projectfs.delete_fs_object request.
func (h *ProjectFSHandler) DeleteFSObject(ctx context.Context, req *DeleteFSObjectMCPRequest) (*DeleteFSObjectMCPResponse, error) {
	h.logger.Info("Handling MCP DeleteFSObject request", zap.String("projectID", req.ProjectID), zap.String("path", req.Path))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.Path == "" {
		// Note: Domain layer already prevents deleting root (path=".")
		return nil, fmt.Errorf("mcp: invalid argument: path is required")
	}

	// Call domain Delete
	err := h.domain.Delete(ctx, req.ProjectID, req.Path)
	if err != nil {
		h.logger.Warn("MCP DeleteFSObject failed: Domain error", zap.String("path", req.Path), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	// Return empty response on success
	return &DeleteFSObjectMCPResponse{
		Success: true,
		Message: "File deleted successfully",
		Path:    req.Path,
	}, nil
}

// --- RenameFSObject ---

// RenameFSObjectMCPRequest defines parameters for renaming/moving a file or directory.
type RenameFSObjectMCPRequest struct {
	ProjectID string `json:"projectID"`
	OldPath   string `json:"oldPath"`
	NewPath   string `json:"newPath"`
}

// RenameFSObjectMCPResponse is empty for successful renames/moves.
type RenameFSObjectMCPResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// --- ReplaceInFile ---

// ReplaceInFileRequest defines parameters for the replace_in_file operation
type ReplaceInFileRequest struct {
	ProjectID string `json:"projectID"`
	FilePath  string `json:"filePath"`
	Diff      string `json:"diff"`    // SEARCH/REPLACE blocks content
	Message   string `json:"message"` // Optional commit message
}

// ReplaceInFileResponse contains the result of the replace operation
type ReplaceInFileResponse struct {
	Success          bool            `json:"success"`
	FilePath         string          `json:"filePath"`
	ReplacementCount int             `json:"replacementCount"`
	UnifiedDiff      string          `json:"unifiedDiff"`
	ModifiedContent  string          `json:"modifiedContent"`
	OriginalContent  string          `json:"originalContent"`
	PreviewChanges   []ChangePreview `json:"previewChanges"`
}

// ChangePreview represents a single replacement operation for preview
type ChangePreview struct {
	SearchContent  string `json:"searchContent"`
	ReplaceContent string `json:"replaceContent"`
	StartLine      int    `json:"startLine"`
	EndLine        int    `json:"endLine"`
	ContextBefore  string `json:"contextBefore"`
	ContextAfter   string `json:"contextAfter"`
}

// SearchReplaceBlock represents a single SEARCH/REPLACE block
type SearchReplaceBlock struct {
	SearchContent  string
	ReplaceContent string
	BlockIndex     int
}

// RenameFSObject handles the mcp.projectfs.rename_fs_object request.
func (h *ProjectFSHandler) RenameFSObject(ctx context.Context, req *RenameFSObjectMCPRequest) (*RenameFSObjectMCPResponse, error) {
	h.logger.Info("Handling MCP RenameFSObject request",
		zap.String("projectID", req.ProjectID),
		zap.String("oldPath", req.OldPath),
		zap.String("newPath", req.NewPath))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.OldPath == "" {
		return nil, fmt.Errorf("mcp: invalid argument: oldPath is required")
	}
	if req.NewPath == "" {
		return nil, fmt.Errorf("mcp: invalid argument: newPath is required")
	}
	// Note: Domain layer already prevents renaming root

	// Call domain Rename
	err := h.domain.Rename(ctx, req.ProjectID, req.OldPath, req.NewPath)
	if err != nil {
		h.logger.Warn("MCP RenameFSObject failed: Domain error",
			zap.String("oldPath", req.OldPath),
			zap.String("newPath", req.NewPath),
			zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	// Return empty response on success
	return &RenameFSObjectMCPResponse{}, nil
}

// --- MCP Handler Methods ---

// ListDirectory handles the mcp.projectfs.list_directory request.
func (h *ProjectFSHandler) ListDirectory(ctx context.Context, req *ListDirectoryMCPRequest) (*ListDirectoryMCPResponse, error) {
	h.logger.Info("Handling MCP ListDirectory request", zap.String("projectID", req.ProjectID), zap.String("path", req.Path))

	// Basic validation (could be more extensive)
	if req.ProjectID == "" {
		h.logger.Warn("MCP ListDirectory failed: Missing projectID")
		// Return MCP-specific invalid argument error if available
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required") // Example error format
	}
	// Let domain layer handle path validation and defaulting "."

	files, err := h.domain.ListDirectory(ctx, req.ProjectID, req.Path)
	if err != nil {
		h.logger.Warn("MCP ListDirectory failed: Domain error", zap.Error(err))
		return nil, mapDomainErrorToMCPError(err) // Use a helper to map errors
	}

	if files == nil {
		files = []storage.FileInfo{} // Ensure non-null response
	}

	return &ListDirectoryMCPResponse{Files: files}, nil
}

// mapDomainErrorToMCPError converts domain/storage errors to MCP errors.
// Note: This is a basic example. The MCP library might provide standard error types/codes.
func mapDomainErrorToMCPError(err error) error {
	if errors.Is(err, os.ErrNotExist) {
		// Return MCP "Not Found" error
		return fmt.Errorf("mcp: not found: %w", err) // Example format
	} else if errors.Is(err, storage.ErrPathExists) {
		// Return MCP "Already Exists" error
		return fmt.Errorf("mcp: already exists: %w", err) // Example format
	} else if errors.Is(err, storage.ErrInvalidPath) || errors.Is(err, projectfs.ErrCannotOperateRoot) {
		// Return MCP "Invalid Argument" error
		return fmt.Errorf("mcp: invalid argument: %w", err) // Example format
	}
	// Default to a generic internal error
	return fmt.Errorf("mcp: internal error: %w", err) // Example format
}

// CreateFSObject handles the mcp.projectfs.create_fs_object request.
func (h *ProjectFSHandler) CreateFSObject(ctx context.Context, req *CreateFSObjectMCPRequest) (*CreateFSObjectMCPResponse, error) {
	h.logger.Info("Handling MCP CreateFSObject request",
		zap.String("projectID", req.ProjectID),
		zap.String("path", req.Path),
		zap.Bool("isDir", req.IsDir))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.Path == "" {
		return nil, fmt.Errorf("mcp: invalid argument: path is required")
	}

	var info storage.FileInfo
	var err error

	if req.IsDir {
		// Create Directory
		info, err = h.domain.CreateDirectory(ctx, req.ProjectID, req.Path)
	} else {
		// Create File with plain text content
		contentBytes := []byte(req.Content)
		info, err = h.domain.CreateFile(ctx, req.ProjectID, req.Path, contentBytes)
	}

	if err != nil {
		h.logger.Warn("MCP CreateFSObject failed: Domain error", zap.String("path", req.Path), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	return &CreateFSObjectMCPResponse{FileInfo: info}, nil
}

// ReadFile handles the mcp.projectfs.read_file request.
func (h *ProjectFSHandler) ReadFile(ctx context.Context, req *ReadFileMCPRequest) (*ReadFileMCPResponse, error) {
	h.logger.Info("Handling MCP ReadFile request", zap.String("projectID", req.ProjectID), zap.String("path", req.Path))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.Path == "" {
		return nil, fmt.Errorf("mcp: invalid argument: path is required")
	}

	// Call domain ReadFile
	contentBytes, err := h.domain.ReadFile(ctx, req.ProjectID, req.Path)
	if err != nil {
		h.logger.Warn("MCP ReadFile failed: Domain error", zap.String("path", req.Path), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	// Content is returned as plain text string
	return &ReadFileMCPResponse{Content: string(contentBytes)}, nil
}

// WriteFile handles the mcp.projectfs.write_file request.
func (h *ProjectFSHandler) WriteFile(ctx context.Context, req *WriteFileMCPRequest) (*WriteFileMCPResponse, error) {
	h.logger.Info("Handling MCP WriteFile request",
		zap.String("projectID", req.ProjectID),
		zap.String("path", req.Path),
		zap.Int("content_length", len(req.Content)))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.Path == "" {
		return nil, fmt.Errorf("mcp: invalid argument: path is required")
	}

	// Content is now plain text
	contentBytes := []byte(req.Content)

	// Call domain WriteFile
	err := h.domain.WriteFile(ctx, req.ProjectID, req.Path, contentBytes)
	if err != nil {
		h.logger.Warn("MCP WriteFile failed: Domain error", zap.String("path", req.Path), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}
	// Add some success message
	return &WriteFileMCPResponse{
		Success: true,
		Message: "File written successfully",
		Path:    req.Path,
	}, nil
}

// ReplaceInFile handles the mcp.projectfs.replace_in_file request
func (h *ProjectFSHandler) ReplaceInFile(ctx context.Context, req *ReplaceInFileRequest) (*ReplaceInFileResponse, error) {
	h.logger.Info("Handling MCP ReplaceInFile request",
		zap.String("projectID", req.ProjectID),
		zap.String("filePath", req.FilePath))

	// Basic validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.FilePath == "" {
		return nil, fmt.Errorf("mcp: invalid argument: filePath is required")
	}
	if req.Diff == "" {
		return nil, fmt.Errorf("mcp: invalid argument: diff content is required")
	}

	// Read the original file content
	originalContent, err := h.domain.ReadFile(ctx, req.ProjectID, req.FilePath)
	if err != nil {
		h.logger.Warn("MCP ReplaceInFile failed: Could not read file",
			zap.String("filePath", req.FilePath), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	// Parse SEARCH/REPLACE blocks
	blocks, err := h.parseSearchReplaceBlocks(req.Diff)
	if err != nil {
		return nil, fmt.Errorf("mcp: invalid diff format: %w", err)
	}

	if len(blocks) == 0 {
		return nil, fmt.Errorf("mcp: invalid argument: no valid SEARCH/REPLACE blocks found")
	}

	// Apply replacements
	modifiedContent, previewChanges, replacementCount, err := h.applyReplacements(string(originalContent), blocks)
	if err != nil {
		return nil, fmt.Errorf("mcp: replacement failed: %w", err)
	}

	// Write the modified content back to file
	err = h.domain.WriteFile(ctx, req.ProjectID, req.FilePath, []byte(modifiedContent))
	if err != nil {
		h.logger.Warn("MCP ReplaceInFile failed: Could not write file",
			zap.String("filePath", req.FilePath), zap.Error(err))
		return nil, mapDomainErrorToMCPError(err)
	}

	// Generate unified diff for display
	unifiedDiff := h.generateUnifiedDiff(req.FilePath, string(originalContent), modifiedContent)

	h.logger.Info("MCP ReplaceInFile completed successfully",
		zap.String("filePath", req.FilePath),
		zap.Int("replacements", replacementCount))

	return &ReplaceInFileResponse{
		Success:          true,
		ReplacementCount: replacementCount,
		FilePath:         req.FilePath,
		UnifiedDiff:      unifiedDiff,
		ModifiedContent:  modifiedContent,
		OriginalContent:  string(originalContent),
		PreviewChanges:   previewChanges,
	}, nil
}

// parseSearchReplaceBlocks parses the diff content to extract SEARCH/REPLACE blocks
func (h *ProjectFSHandler) parseSearchReplaceBlocks(diffContent string) ([]SearchReplaceBlock, error) {
	// Regular expression to match SEARCH/REPLACE blocks
	blockRegex := regexp.MustCompile(`(?s)<<<<<<< SEARCH\s*\n(.*?)\n=======\s*\n(.*?)\n>>>>>>> REPLACE`)

	matches := blockRegex.FindAllStringSubmatch(diffContent, -1)
	if len(matches) == 0 {
		return nil, fmt.Errorf("no valid SEARCH/REPLACE blocks found")
	}

	var blocks []SearchReplaceBlock
	for i, match := range matches {
		if len(match) != 3 {
			continue
		}

		searchContent := match[1]
		replaceContent := match[2]

		blocks = append(blocks, SearchReplaceBlock{
			SearchContent:  searchContent,
			ReplaceContent: replaceContent,
			BlockIndex:     i,
		})
	}

	return blocks, nil
}

// applyReplacements applies all search/replace blocks to the content
func (h *ProjectFSHandler) applyReplacements(originalContent string, blocks []SearchReplaceBlock) (string, []ChangePreview, int, error) {
	content := originalContent
	var previewChanges []ChangePreview
	totalReplacements := 0

	// Apply blocks in order (important for correct line number tracking)
	for _, block := range blocks {
		// Find the search content in the current content
		searchContent := block.SearchContent
		replaceContent := block.ReplaceContent

		// Check if search content exists
		if !strings.Contains(content, searchContent) {
			return "", nil, 0, fmt.Errorf("search content not found in block %d: %q",
				block.BlockIndex, searchContent)
		}

		// Find line numbers for preview
		startLine, endLine := h.findLineNumbers(content, searchContent)

		// Get context for preview
		contextBefore, contextAfter := h.getContext(content, searchContent, 2)

		// Create preview change
		previewChanges = append(previewChanges, ChangePreview{
			SearchContent:  searchContent,
			ReplaceContent: replaceContent,
			StartLine:      startLine,
			EndLine:        endLine,
			ContextBefore:  contextBefore,
			ContextAfter:   contextAfter,
		})

		// Perform the replacement (only first occurrence)
		newContent := strings.Replace(content, searchContent, replaceContent, 1)
		if newContent != content {
			totalReplacements++
			content = newContent
		}
	}

	return content, previewChanges, totalReplacements, nil
}

// findLineNumbers finds the start and end line numbers of the search content
func (h *ProjectFSHandler) findLineNumbers(content, searchContent string) (int, int) {
	lines := strings.Split(content, "\n")
	searchLines := strings.Split(searchContent, "\n")

	for i := 0; i <= len(lines)-len(searchLines); i++ {
		match := true
		for j := 0; j < len(searchLines); j++ {
			if i+j >= len(lines) || lines[i+j] != searchLines[j] {
				match = false
				break
			}
		}
		if match {
			return i + 1, i + len(searchLines) // 1-based line numbers
		}
	}

	return 1, 1 // fallback
}

// getContext gets context lines before and after the search content
func (h *ProjectFSHandler) getContext(content, searchContent string, contextLines int) (string, string) {
	lines := strings.Split(content, "\n")
	searchLines := strings.Split(searchContent, "\n")

	// Find the search content position
	for i := 0; i <= len(lines)-len(searchLines); i++ {
		match := true
		for j := 0; j < len(searchLines); j++ {
			if i+j >= len(lines) || lines[i+j] != searchLines[j] {
				match = false
				break
			}
		}
		if match {
			// Get context before
			beforeStart := max(0, i-contextLines)
			beforeEnd := i
			contextBefore := strings.Join(lines[beforeStart:beforeEnd], "\n")

			// Get context after
			afterStart := i + len(searchLines)
			afterEnd := min(len(lines), afterStart+contextLines)
			contextAfter := strings.Join(lines[afterStart:afterEnd], "\n")

			return contextBefore, contextAfter
		}
	}

	return "", ""
}

// generateUnifiedDiff generates a unified diff format string
func (h *ProjectFSHandler) generateUnifiedDiff(filePath, original, modified string) string {
	if original == modified {
		return ""
	}

	// Simple unified diff generation (could be enhanced with a proper diff library)
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	diff := fmt.Sprintf("--- a/%s\t%s\n", filePath, timestamp)
	diff += fmt.Sprintf("+++ b/%s\t%s\n", filePath, timestamp)

	originalLines := strings.Split(original, "\n")
	modifiedLines := strings.Split(modified, "\n")

	// Basic diff algorithm (could be replaced with a more sophisticated one)
	diff += fmt.Sprintf("@@ -1,%d +1,%d @@\n", len(originalLines), len(modifiedLines))

	maxLines := max(len(originalLines), len(modifiedLines))
	for i := 0; i < maxLines; i++ {
		if i < len(originalLines) && i < len(modifiedLines) {
			if originalLines[i] != modifiedLines[i] {
				diff += fmt.Sprintf("-%s\n", originalLines[i])
				diff += fmt.Sprintf("+%s\n", modifiedLines[i])
			} else {
				diff += fmt.Sprintf(" %s\n", originalLines[i])
			}
		} else if i < len(originalLines) {
			diff += fmt.Sprintf("-%s\n", originalLines[i])
		} else if i < len(modifiedLines) {
			diff += fmt.Sprintf("+%s\n", modifiedLines[i])
		}
	}

	return diff
}

// Helper functions
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// --- Tool Registration ---

// RegisterTools defines the MCP tools and registers them with the given MCP server.
func (h *ProjectFSHandler) RegisterTools(mcpServer *mcpServerLib.MCPServer) {
	h.logger.Info("Registering MCP ProjectFS tools...")

	// 1. ListDirectory
	listDirTool := mcp.NewTool("mcp.projectfs.list_directory",
		mcp.WithDescription("List files and directories within a project path."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("path", mcp.Description("The path within the project to list. Defaults to root ('.').")),
	)
	mcpServer.AddTool(listDirTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		path, err := getStringArg(request.Params.Arguments, "path", false)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &ListDirectoryMCPRequest{ProjectID: projectID, Path: path}
		resp, handlerErr := h.ListDirectory(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", listDirTool.Name))

	// 2. CreateFSObject
	createObjTool := mcp.NewTool("mcp.projectfs.create_fs_object",
		mcp.WithDescription("Create a file or directory within a project path."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("path", mcp.Required(), mcp.Description("The path of the object to create.")),
		mcp.WithBoolean("isDir", mcp.Description("Set to true to create a directory, false for a file.")),
		// TODO: Add 'content' (base64 string) as an optional parameter for file creation?
	)
	mcpServer.AddTool(createObjTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		path, err := getStringArg(request.Params.Arguments, "path", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		isDir, err := getBoolArg(request.Params.Arguments, "isDir", false) // Default to file
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		// TODO: Handle optional 'content' argument if added to tool definition

		req := &CreateFSObjectMCPRequest{ProjectID: projectID, Path: path, IsDir: isDir /* Content: contentArg */}
		resp, handlerErr := h.CreateFSObject(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", createObjTool.Name))

	// 3. ReadFile
	readFileTool := mcp.NewTool("mcp.projectfs.read_file",
		mcp.WithDescription("Read the content of a file within a project."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("path", mcp.Required(), mcp.Description("The path of the file to read.")),
	)
	mcpServer.AddTool(readFileTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		path, err := getStringArg(request.Params.Arguments, "path", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &ReadFileMCPRequest{ProjectID: projectID, Path: path}
		resp, handlerErr := h.ReadFile(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", readFileTool.Name))

	// 4. WriteFile
	writeFileTool := mcp.NewTool("mcp.projectfs.write_file",
		mcp.WithDescription("Write content to a file within a project (creates or overwrites)."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("path", mcp.Required(), mcp.Description("The path of the file to write.")),
		mcp.WithString("content", mcp.Required(), mcp.Description("The plain text content to write.")),
	)
	mcpServer.AddTool(writeFileTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		path, err := getStringArg(request.Params.Arguments, "path", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		content, err := getStringArg(request.Params.Arguments, "content", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &WriteFileMCPRequest{ProjectID: projectID, Path: path, Content: content}
		resp, handlerErr := h.WriteFile(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", writeFileTool.Name))

	// 5. DeleteFSObject
	deleteObjTool := mcp.NewTool("mcp.projectfs.delete_fs_object",
		mcp.WithDescription("Delete a file or directory within a project."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("path", mcp.Required(), mcp.Description("The path of the object to delete.")),
	)
	mcpServer.AddTool(deleteObjTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		path, err := getStringArg(request.Params.Arguments, "path", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &DeleteFSObjectMCPRequest{ProjectID: projectID, Path: path}
		resp, handlerErr := h.DeleteFSObject(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", deleteObjTool.Name))

	// 6. RenameFSObject
	renameObjTool := mcp.NewTool("mcp.projectfs.rename_fs_object",
		mcp.WithDescription("Rename or move a file or directory within a project."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("oldPath", mcp.Required(), mcp.Description("The current path of the object.")),
		mcp.WithString("newPath", mcp.Required(), mcp.Description("The desired new path of the object.")),
	)
	mcpServer.AddTool(renameObjTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		oldPath, err := getStringArg(request.Params.Arguments, "oldPath", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		newPath, err := getStringArg(request.Params.Arguments, "newPath", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &RenameFSObjectMCPRequest{ProjectID: projectID, OldPath: oldPath, NewPath: newPath}
		resp, handlerErr := h.RenameFSObject(ctx, req) // Use h. receiver
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", renameObjTool.Name))

	// 7. ReplaceInFile

	diffDescription := `Performs a find-and-replace operation on a file using a special diff format.

CRITICAL: The format must be followed exactly.

EXAMPLE FORMAT:
<<<<<<< SEARCH
The exact text to find (including indentation and newlines).
=======
The text to replace it with.
>>>>>>> REPLACE

IMPORTANT NOTES:
1.  The "<<<<<<< SEARCH" and ">>>>>>> REPLACE" delimiters MUST contain **exactly 7** '<' or '>' characters.
2.  The "=======" separator MUST be on its own line between the SEARCH and REPLACE blocks.
3.  For multiple replacements, concatenate blocks together."`

	replaceInFileTool := mcp.NewTool("mcp.projectfs.replace_in_file",
		mcp.WithDescription("Apply SEARCH/REPLACE blocks to modify file content with diff preview."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("filePath", mcp.Required(), mcp.Description("The path of the file to modify.")),
		mcp.WithString("diff", mcp.Required(), mcp.Description(diffDescription)),
		mcp.WithString("message", mcp.Description("Optional commit message for the changes.")),
	)
	mcpServer.AddTool(replaceInFileTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		filePath, err := getStringArg(request.Params.Arguments, "filePath", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		diff, err := getStringArg(request.Params.Arguments, "diff", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}
		message, err := getStringArg(request.Params.Arguments, "message", false)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		req := &ReplaceInFileRequest{ProjectID: projectID, FilePath: filePath, Diff: diff, Message: message}
		resp, handlerErr := h.ReplaceInFile(ctx, req)
		return resultToMCPJsonResult(resp, handlerErr)
	})
	h.logger.Debug("Registered tool", zap.String("name", replaceInFileTool.Name))
}

// --- Helpers (moved from main.go, made private) ---

// getStringArg extracts string arg from MCP request
func getStringArg(args map[string]interface{}, key string, required bool) (string, error) {
	val, ok := args[key]
	if !ok {
		if required {
			return "", fmt.Errorf("missing required argument: %s", key)
		}
		return "", nil // Return empty string if optional and not present
	}
	strVal, ok := val.(string)
	if !ok {
		return "", fmt.Errorf("invalid argument type for %s: expected string, got %T", key, val)
	}
	return strVal, nil
}

// getBoolArg extracts boolean arg from MCP request
func getBoolArg(args map[string]interface{}, key string, defaultValue bool) (bool, error) {
	val, ok := args[key]
	if !ok {
		return defaultValue, nil // Return default if not present
	}
	boolVal, ok := val.(bool)
	if !ok {
		return defaultValue, fmt.Errorf("invalid argument type for %s: expected boolean, got %T", key, val)
	}
	return boolVal, nil
}

// resultToMCPJsonResult converts handler result to MCP Tool Result (JSON)
func resultToMCPJsonResult(result interface{}, err error) (*mcp.CallToolResult, error) {
	if err != nil {
		// Use the handler's error mapping logic before creating MCP error
		mappedErr := mapDomainErrorToMCPError(err)                            // Ensure this helper is available and used
		return mcp.NewToolResultErrorFromErr("handler error", mappedErr), nil // Pass the mapped error
	}
	if result == nil {
		// Use mcp.NewToolResult(nil) for empty success, which should be handled correctly by the protocol
		// Returning an empty JSON object might be misinterpreted.
		return mcp.NewToolResultText("{}"), nil // Use empty JSON object for nil success
	}
	// Marshal the successful result to JSON
	jsonBytes, marshalErr := json.Marshal(result)
	if marshalErr != nil {
		return mcp.NewToolResultErrorFromErr("failed to marshal result to JSON", marshalErr), nil
	}
	// Use NewToolResultText for JSON string results
	return mcp.NewToolResultText(string(jsonBytes)), nil
}
