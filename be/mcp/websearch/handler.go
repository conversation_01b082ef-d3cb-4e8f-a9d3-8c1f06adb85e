package websearch

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url" // Import net/url for QueryEscape
	"time"

	"go.uber.org/zap"

	"github.com/mark3labs/mcp-go/mcp"
	mcpServerLib "github.com/mark3labs/mcp-go/server"
)

// WebSearchHandler handles MCP requests related to web search operations.
type WebSearchHandler struct {
	logger *zap.Logger
	// Configuration for Google Custom Search API
	googleAPIKey string
	googleCX     string
	httpClient   *http.Client // For making external HTTP requests
}

// NewWebSearchHandler creates a new MCP handler for web search.
// It requires the Google API Key and Custom Search Engine ID.
func NewWebSearchHandler(logger *zap.Logger, googleAPIKey, googleCX string) *WebSearchHandler {
	if logger == nil {
		logger = zap.NewNop()
	}
	return &WebSearchHandler{
		logger:       logger.Named("MCPWebSearchHandler"),
		googleAPIKey: googleAPIKey,
		googleCX:     googleCX,
		httpClient:   &http.Client{Timeout: 10 * time.Second}, // Default HTTP client with timeout
	}
}

// --- MCP Operation Request/Response Structs ---

// WebSearchRequest defines the parameters for the mcp.web.search operation.
type WebSearchRequest struct {
	Query string `json:"query"`         // The search query string
	Num   int    `json:"num,omitempty"` // Number of search results to return (default: 10, max: 10)
}

// WebSearchResponse defines the result for the mcp.web.search operation.
type WebSearchResponse struct {
	Results      []SearchResult `json:"results"`
	TotalResults string         `json:"total_results"`
}

// SearchResult represents a single item in the search results.
type SearchResult struct {
	Title   string `json:"title"`
	Link    string `json:"link"`
	Snippet string `json:"snippet"`
}

// --- Google Custom Search API Response Structs (internal to this package) ---
// These structs are designed to unmarshal the JSON response from Google Custom Search API.
// We only include fields relevant to our WebSearchResponse.

type googleSearchAPIResponse struct {
	Items             []googleSearchAPIItem            `json:"items"`
	SearchInformation googleSearchAPISearchInformation `json:"searchInformation"`
}

type googleSearchAPIItem struct {
	Title   string `json:"title"`
	Link    string `json:"link"`
	Snippet string `json:"snippet"`
}

type googleSearchAPISearchInformation struct {
	TotalResults string `json:"totalResults"`
}

// WebSearch handles the mcp.web.search request.
func (h *WebSearchHandler) WebSearch(ctx context.Context, req *WebSearchRequest) (*WebSearchResponse, error) {
	h.logger.Info("Handling MCP WebSearch request",
		zap.String("query", req.Query),
		zap.String("api_key_prefix", h.googleAPIKey[:5]+"..."), // Log only prefix for security
		zap.String("cx", h.googleCX),
		zap.Int("requested_num_results", req.Num),
	)

	// Basic validation
	if req.Query == "" {
		return nil, fmt.Errorf("mcp: invalid argument: query is required")
	}

	// Set default and max for 'num'
	numResults := 10
	if req.Num > 0 && req.Num <= 10 { // Google CSE API max is 10 results per page
		numResults = req.Num
	} else if req.Num > 10 {
		h.logger.Warn("Requested more than 10 results, capping to 10", zap.Int("requested", req.Num))
		numResults = 10
	}

	// Construct Google Custom Search API URL
	// Referencing: https://developers.google.com/custom-search/v1/using_rest
	// Base URL: https://www.googleapis.com/customsearch/v1
	// Parameters: key, cx, q, num
	searchURL := fmt.Sprintf("https://www.googleapis.com/customsearch/v1?key=%s&cx=%s&q=%s&num=%d",
		h.googleAPIKey, h.googleCX, url.QueryEscape(req.Query), numResults)

	h.logger.Debug("Constructed Google Search API URL", zap.String("url", searchURL))

	// Make HTTP GET request
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		h.logger.Error("Failed to create HTTP request for Google Search API", zap.Error(err))
		return nil, fmt.Errorf("failed to create search request: %w", err)
	}

	httpResp, err := h.httpClient.Do(httpReq)
	if err != nil {
		h.logger.Error("Failed to perform HTTP request to Google Search API", zap.Error(err))
		return nil, fmt.Errorf("failed to perform search: %w", err)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		// Read the entire response body for detailed error message
		respBodyBytes, readErr := io.ReadAll(httpResp.Body)
		if readErr != nil {
			h.logger.Error("Failed to read error response body from Google Search API", zap.Error(readErr))
			return nil, fmt.Errorf("Google Search API error: status %d (failed to read error body)", httpResp.StatusCode)
		}
		var errorBody map[string]interface{}
		json.Unmarshal(respBodyBytes, &errorBody) // Use Unmarshal to process bytes
		h.logger.Error("Google Search API returned non-200 status",
			zap.Int("status", httpResp.StatusCode),
			zap.ByteString("raw_error_body", respBodyBytes),
			zap.Any("parsed_error_body", errorBody), // Log parsed error body if available
		)
		return nil, fmt.Errorf("Google Search API error: status %d. Response: %s", httpResp.StatusCode, string(respBodyBytes))
	}

	// Parse JSON response
	var apiResponse googleSearchAPIResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&apiResponse); err != nil {
		h.logger.Error("Failed to decode Google Search API response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse search results: %w", err)
	}

	// Convert API response to our WebSearchResponse format
	var results []SearchResult
	for _, item := range apiResponse.Items {
		results = append(results, SearchResult{
			Title:   item.Title,
			Link:    item.Link,
			Snippet: item.Snippet,
		})
	}

	return &WebSearchResponse{
		Results:      results,
		TotalResults: apiResponse.SearchInformation.TotalResults,
	}, nil
}

// RegisterTools registers web search MCP tools with the MCP server.
func (h *WebSearchHandler) RegisterTools(mcpServer *mcpServerLib.MCPServer) {
	h.logger.Info("Registering MCP WebSearch tools...")

	// mcp.web.search tool
	webSearchTool := mcp.NewTool("mcp.web.search",
		mcp.WithDescription("Perform a web search using Google Custom Search API."),
		mcp.WithString("query", mcp.Required(), mcp.Description("The search query string.")),
		mcp.WithNumber("num", mcp.Description("Number of search results to return (default: 10, max: 10).")),
	)

	mcpServer.AddTool(webSearchTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		query, err := getStringArg(request.Params.Arguments, "query", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		num := 0 // Default to 0, so handler can apply its own default
		if numVal, exists := request.Params.Arguments["num"]; exists {
			if numFloat, ok := numVal.(float64); ok {
				num = int(numFloat)
			}
		}

		req := &WebSearchRequest{
			Query: query,
			Num:   num,
		}

		resp, handlerErr := h.WebSearch(ctx, req)
		return resultToMCPJsonResult(resp, handlerErr)
	})

	h.logger.Debug("Registered tool", zap.String("name", webSearchTool.Name))
}

// --- Helper Functions (copied from other handlers for consistency) ---

// getStringArg extracts string arg from MCP request
func getStringArg(args map[string]interface{}, key string, required bool) (string, error) {
	val, ok := args[key]
	if !ok {
		if required {
			return "", fmt.Errorf("missing required argument: %s", key)
		}
		return "", nil // Return empty string if optional and not present
	}
	strVal, ok := val.(string)
	if !ok {
		return "", fmt.Errorf("invalid argument type for %s: expected string, got %T", key, val)
	}
	return strVal, nil
}

// resultToMCPJsonResult converts handler result to MCP Tool Result (JSON)
func resultToMCPJsonResult(result interface{}, err error) (*mcp.CallToolResult, error) {
	if err != nil {
		// For web search, generic handler error is sufficient, no specific domain errors to map like projectfs
		return mcp.NewToolResultErrorFromErr("handler error", err), nil
	}
	if result == nil {
		return mcp.NewToolResultText("{}"), nil
	}
	// Marshal the successful result to JSON
	jsonBytes, marshalErr := json.Marshal(result)
	if marshalErr != nil {
		return mcp.NewToolResultErrorFromErr("failed to marshal result to JSON", marshalErr), nil
	}
	// Use NewToolResultText for JSON string results
	return mcp.NewToolResultText(string(jsonBytes)), nil
}
