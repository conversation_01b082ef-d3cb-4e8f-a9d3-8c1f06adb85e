package runtime

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	mcpServerLib "github.com/mark3labs/mcp-go/server"
	"go.uber.org/zap"

	runtimeDomain "git.nevint.com/fota3/t-rex/domain/runtime"
)

// RuntimeHandler handles MCP requests related to runtime operations
type RuntimeHandler struct {
	domain *runtimeDomain.Domain
	logger *zap.Logger
}

// NewRuntimeHandler creates a new MCP handler for runtime operations
func NewRuntimeHandler(domain *runtimeDomain.Domain, logger *zap.Logger) *RuntimeHandler {
	if logger == nil {
		logger = zap.NewNop()
	}
	return &RuntimeHandler{
		domain: domain,
		logger: logger.Named("MCPRuntimeHandler"),
	}
}

// BashExecRequest defines the parameters for executing bash commands
type BashExecRequest struct {
	ProjectID string `json:"projectID"`
	Command   string `json:"command"`
	WorkDir   string `json:"workDir,omitempty"`
	Timeout   int    `json:"timeout,omitempty"` // seconds
}

// BashExecResponse contains the command execution result
type BashExecResponse struct {
	Stdout   string `json:"stdout"`
	Stderr   string `json:"stderr"`
	ExitCode int    `json:"exit_code"`
	Command  string `json:"command"`
}

// ExecuteBashCommand handles the mcp.runtime.bash_exec request
func (h *RuntimeHandler) ExecuteBashCommand(ctx context.Context, req *BashExecRequest) (*BashExecResponse, error) {
	h.logger.Info("Handling MCP BashExec request",
		zap.String("projectID", req.ProjectID),
		zap.String("command", req.Command),
		zap.String("workDir", req.WorkDir))

	// Parameter validation
	if req.ProjectID == "" {
		return nil, fmt.Errorf("mcp: invalid argument: projectID is required")
	}
	if req.Command == "" {
		return nil, fmt.Errorf("mcp: invalid argument: command is required")
	}

	// Set default timeout - default to 30s, max 300s (5 minutes)
	timeout := 30 * time.Second
	if req.Timeout > 0 && req.Timeout <= 300 {
		timeout = time.Duration(req.Timeout) * time.Second
	}

	// Execute command through domain layer
	result, err := h.domain.ExecuteCommand(ctx, req.ProjectID, req.Command, req.WorkDir, timeout)
	if err != nil {
		h.logger.Warn("MCP BashExec failed",
			zap.String("projectID", req.ProjectID),
			zap.String("command", req.Command),
			zap.Error(err))
		return nil, fmt.Errorf("command execution failed: %w", err)
	}

	return &BashExecResponse{
		Stdout:   result.Stdout,
		Stderr:   result.Stderr,
		ExitCode: result.ExitCode,
		Command:  result.Command,
	}, nil
}

// RegisterTools registers runtime MCP tools with the MCP server
func (h *RuntimeHandler) RegisterTools(mcpServer *mcpServerLib.MCPServer) {
	h.logger.Info("Registering MCP Runtime tools...")

	// bash_exec tool
	bashExecTool := mcp.NewTool("mcp.runtime.bash_exec",
		mcp.WithDescription("Execute a bash command in the project's runtime container."),
		mcp.WithString("projectID", mcp.Required(), mcp.Description("The ID of the project.")),
		mcp.WithString("command", mcp.Required(), mcp.Description("The bash command to execute.")),
		mcp.WithString("workDir", mcp.Description("Working directory for the command (default: /workspace).")),
		mcp.WithNumber("timeout", mcp.Description("Timeout in seconds (default: 30, max: 300).")),
	)

	mcpServer.AddTool(bashExecTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		projectID, err := getStringArg(request.Params.Arguments, "projectID", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		command, err := getStringArg(request.Params.Arguments, "command", true)
		if err != nil {
			return mcp.NewToolResultErrorFromErr("arg error", err), nil
		}

		workDir, _ := getStringArg(request.Params.Arguments, "workDir", false)

		var timeout int
		if timeoutVal, exists := request.Params.Arguments["timeout"]; exists {
			if timeoutFloat, ok := timeoutVal.(float64); ok {
				timeout = int(timeoutFloat)
			}
		}

		req := &BashExecRequest{
			ProjectID: projectID,
			Command:   command,
			WorkDir:   workDir,
			Timeout:   timeout,
		}

		resp, handlerErr := h.ExecuteBashCommand(ctx, req)
		return resultToMCPJsonResult(resp, handlerErr)
	})

	h.logger.Debug("Registered tool", zap.String("name", bashExecTool.Name))
}

// Helper functions

// getStringArg extracts string arg from MCP request
func getStringArg(args map[string]interface{}, key string, required bool) (string, error) {
	val, ok := args[key]
	if !ok {
		if required {
			return "", fmt.Errorf("missing required argument: %s", key)
		}
		return "", nil
	}
	strVal, ok := val.(string)
	if !ok {
		return "", fmt.Errorf("invalid argument type for %s: expected string, got %T", key, val)
	}
	return strVal, nil
}

// resultToMCPJsonResult converts handler result to MCP Tool Result (JSON)
func resultToMCPJsonResult(result interface{}, err error) (*mcp.CallToolResult, error) {
	if err != nil {
		return mcp.NewToolResultErrorFromErr("handler error", err), nil
	}
	if result == nil {
		return mcp.NewToolResultText("{}"), nil
	}

	// Serialize result to JSON text
	jsonBytes, jsonErr := json.Marshal(result)
	if jsonErr != nil {
		return mcp.NewToolResultErrorFromErr("json marshal error", jsonErr), nil
	}
	return mcp.NewToolResultText(string(jsonBytes)), nil
}
