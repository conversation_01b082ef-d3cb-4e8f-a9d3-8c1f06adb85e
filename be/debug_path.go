package main

import (
	"fmt"
	"os"
)

func main() {
	// Print current working directory
	cwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("Error getting current directory: %v\n", err)
		return
	}
	fmt.Printf("Current working directory: %s\n", cwd)
	
	// Change directory like in main.go
	fmt.Println("Changing to ../..")
	if err := os.Chdir("../.."); err != nil {
		fmt.Printf("Failed to change directory: %v\n", err)
		return
	}
	
	// Print new working directory
	cwd, err = os.Getwd()
	if err != nil {
		fmt.Printf("Error getting current directory: %v\n", err)
		return
	}
	fmt.Printf("New working directory: %s\n", cwd)
	
	// Check if be/config exists
	if _, err := os.Stat("be/config"); os.IsNotExist(err) {
		fmt.Println("be/config directory does not exist")
	} else {
		fmt.Println("be/config directory exists")
	}
	
	// Check if be/config/config.yaml exists
	if _, err := os.Stat("be/config/config.yaml"); os.IsNotExist(err) {
		fmt.Println("be/config/config.yaml file does not exist")
	} else {
		fmt.Println("be/config/config.yaml file exists")
	}
	
	// List files in be/config
	files, err := os.ReadDir("be/config")
	if err != nil {
		fmt.Printf("Error reading be/config directory: %v\n", err)
		return
	}
	
	fmt.Println("Files in be/config:")
	for _, file := range files {
		fmt.Printf("  %s\n", file.Name())
	}
}
