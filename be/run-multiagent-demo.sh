#!/bin/bash

echo "=== Multi-Agent Real LLM Demo Runner ==="
echo ""

# Check if config file exists
if [ ! -f "config/config.yaml" ]; then
    echo "Error: config/config.yaml not found!"
    echo "Please make sure you're running this script from the 'be' directory"
    exit 1
fi

# Check if OpenAI config is set
if ! grep -q "api_key:" config/config.yaml; then
    echo "Warning: No API key found in config.yaml"
    echo "Please make sure your OpenAI configuration is set up correctly"
fi

echo "Found configuration file: config/config.yaml"
echo ""

# Option 1: Run as test
echo "Option 1: Run as test"
echo "Command: SIMPLE_LLM_DEMO=true go test ./domain/multiagent -v -run TestSimpleLLMDemo"
echo ""

# Option 2: Run as standalone program
echo "Option 2: Run as standalone program"
echo "Command: go run ./cmd/multiagent-demo/main.go"
echo ""

# Ask user which option to use
read -p "Choose option (1 or 2): " choice

case $choice in
    1)
        echo "Running as test..."
        SIMPLE_LLM_DEMO=true go test ./domain/multiagent -v -run TestSimpleLLMDemo
        ;;
    2)
        echo "Running as standalone program..."
        go run ./cmd/multiagent-demo/main.go
        ;;
    *)
        echo "Invalid choice. Please run the script again and choose 1 or 2."
        exit 1
        ;;
esac

echo ""
echo "Demo execution completed!"
