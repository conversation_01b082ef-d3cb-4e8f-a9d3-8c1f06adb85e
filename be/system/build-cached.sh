#!/bin/bash

# T-Rex Development Environment Builder (Cached Version)
# Simple build script using pre-downloaded packages

set -e

# Configuration
IMAGE_NAME="t-rex/dev-env"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if cache directory exists and has files
if [[ ! -d "cache" ]] || [[ -z "$(ls -A cache 2>/dev/null)" ]]; then
    print_error "Cache directory is empty or doesn't exist."
    print_error "Please run './download-packages.sh' first to download required packages."
    exit 1
fi

print_status "🚀 Building T-Rex Development Environment (Cached Version)..."
print_status "Image: ${FULL_IMAGE_NAME}"
print_status "Platform: ARM64"
print_status "Using cached packages: YES"

echo
print_status "📁 Using cached packages:"
ls -la cache/

echo
print_status "Starting Docker build process..."
start_time=$(date +%s)

# Build arguments for proxy (if set)
BUILD_ARGS=""
if [[ -n "${HTTP_PROXY}" ]]; then
    BUILD_ARGS="${BUILD_ARGS} --build-arg HTTP_PROXY=${HTTP_PROXY}"
    print_info "HTTP Proxy: ${HTTP_PROXY}"
fi
if [[ -n "${HTTPS_PROXY}" ]]; then
    BUILD_ARGS="${BUILD_ARGS} --build-arg HTTPS_PROXY=${HTTPS_PROXY}"
    print_info "HTTPS Proxy: ${HTTPS_PROXY}"
fi
if [[ -n "${NO_PROXY}" ]]; then
    BUILD_ARGS="${BUILD_ARGS} --build-arg NO_PROXY=${NO_PROXY}"
    print_info "No Proxy: ${NO_PROXY}"
fi

# Build command
if docker build \
    --platform linux/arm64 \
    --file Dockerfile.cached \
    --tag "${FULL_IMAGE_NAME}" \
    ${BUILD_ARGS} \
    .; then
    
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    print_status "✅ Build completed successfully!"
    print_status "⏱️  Build time: ${build_time} seconds"
    print_status "🏷️  Image: ${FULL_IMAGE_NAME}"
    
    # Show image size
    if command -v docker > /dev/null 2>&1; then
        image_size=$(docker images "${FULL_IMAGE_NAME}" --format "table {{.Size}}" | tail -n 1)
        print_info "📦 Image size: ${image_size}"
    fi
    
    echo
    print_status "🎉 Your T-Rex development environment is ready!"
    print_status "🚀 Quick start:"
    echo -e "   ${BLUE}./run.sh start${NC}     # Start the environment"
    echo -e "   ${BLUE}./run.sh shell${NC}     # Get a shell in the environment"
    echo -e "   ${BLUE}./check-versions.sh${NC} # Check installed versions"
    
else
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    print_error "❌ Build failed after ${build_time} seconds"
    print_error "Please check the error messages above for details."
    exit 1
fi 