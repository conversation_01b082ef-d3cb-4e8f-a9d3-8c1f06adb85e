version: '3.8'

services:
  dev-env:
    build:
      context: .
      dockerfile: Dockerfile
      platform: linux/arm64
      args:
        - HTTP_PROXY=${HTTP_PROXY:-}
        - HTTPS_PROXY=${HTTPS_PROXY:-}
        - NO_PROXY=${NO_PROXY:-}
    image: t-rex/dev-env:latest
    container_name: t-rex-dev-env
    
    # Interactive terminal
    stdin_open: true
    tty: true
    
    # Environment variables
    environment:
      - HTTP_PROXY=${HTTP_PROXY:-}
      - HTTPS_PROXY=${HTTPS_PROXY:-}
      - NO_PROXY=${NO_PROXY:-}
    
    # Volume mounts
    volumes:
      # Mount current directory to workspace
      - .:/workspace
      # Mount Docker socket for Docker-in-Docker functionality
      - /var/run/docker.sock:/var/run/docker.sock
      # Persistent volumes for development tools
      - go-modules:/home/<USER>/go/pkg/mod
      - node-modules:/home/<USER>/.npm
      - pip-cache:/home/<USER>/.cache/pip
      - vscode-server:/home/<USER>/.vscode-server
    
    # Network configuration
    networks:
      - dev-network
    
    # Port mappings for common development servers
    ports:
      - "3000:3000"    # React/Vue/Angular dev server
      - "8080:8080"    # Spring Boot / Go server
      - "8000:8000"    # Django / FastAPI
      - "5000:5000"    # Flask
      - "4200:4200"    # Angular CLI
      - "8888:8888"    # Jupyter Notebook
      - "9000:9000"    # Additional port
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits (adjust based on your machine)
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
        reservations:
          memory: 2G
          cpus: '1'

# Named volumes for persistence
volumes:
  go-modules:
    driver: local
  node-modules:
    driver: local
  pip-cache:
    driver: local
  vscode-server:
    driver: local

# Network for isolation
networks:
  dev-network:
    driver: bridge 