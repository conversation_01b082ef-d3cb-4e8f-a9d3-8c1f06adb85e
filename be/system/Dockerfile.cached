# Multi-language Development Environment for ARM64 (Cached Version)
# Base: Ubuntu Latest with cached packages for offline builds
FROM ubuntu:latest

# Build arguments for proxy configuration
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

# Set environment variables for proxy
ENV http_proxy=${HTTP_PROXY}
ENV https_proxy=${HTTPS_PROXY}
ENV no_proxy=${NO_PROXY}
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set locale
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Copy cached installation files
COPY cache/ /tmp/cache/

# Update and install all system packages in one layer
RUN apt-get update && \
    # Add deadsnakes PPA for Python 3.11
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
        # Basic system tools
        curl \
        wget \
        vim \
        git \
        htop \
        mtr \
        tree \
        jq \
        unzip \
        zip \
        tar \
        gzip \
        rsync \
        netcat-openbsd \
        nmap \
        openssh-client \
        ca-certificates \
        apt-transport-https \
        gnupg \
        lsb-release \
        # Build essentials for C/C++
        build-essential \
        gcc \
        g++ \
        make \
        cmake \
        pkg-config \
        # Python and its dependencies
        python3.11 \
        python3.11-venv \
        python3.11-dev \
        python3-pip \
        libssl-dev \
        libffi-dev \
        libbz2-dev \
        libreadline-dev \
        libsqlite3-dev \
        libncurses5-dev \
        libncursesw5-dev \
        xz-utils \
        tk-dev \
        libxml2-dev \
        libxmlsec1-dev \
        liblzma-dev \
        # Java
        openjdk-21-jdk \
        # Database tools
        mysql-client \
        postgresql-client \
        sqlite3 \
        # Version control and collaboration
        subversion \
        mercurial \
        # Text processing tools
        gawk \
        sed \
        grep \
        # Archive tools
        p7zip-full \
        unrar \
        # Network tools
        telnet \
        tcpdump \
        traceroute \
        net-tools \
        # Additional useful tools
        procps \
        psmisc \
        nano \
        less \
        man-db \
        file \
        sudo && \
    rm -rf /var/lib/apt/lists/*

# Set up Python alternatives
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1 && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Create a non-root user for development
RUN useradd -m -s /bin/bash developer && \
    usermod -aG sudo developer && \
    mkdir -p /etc/sudoers.d && \
    echo "developer ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/developer

# Install Go from cached file
ENV GO_VERSION=1.24.3
ENV GOPATH=/home/<USER>/go
ENV PATH=$PATH:/usr/local/go/bin:$GOPATH/bin
RUN tar -C /usr/local -xzf /tmp/cache/go${GO_VERSION}.linux-arm64.tar.gz && \
    mkdir -p $GOPATH/{bin,src,pkg} && \
    chown -R developer:developer $GOPATH

# Set Java environment
ENV JAVA_HOME=/usr/lib/jvm/java-21-openjdk-arm64
ENV PATH=$PATH:$JAVA_HOME/bin

# Skip pip upgrade to avoid conflicts with system package
# RUN python3 -m pip install --upgrade pip --break-system-packages

# Install Node.js using cached NVM script
ENV NODE_VERSION=22.15.0
ENV NVM_DIR=/usr/local/nvm
RUN mkdir -p $NVM_DIR && \
    chmod +x /tmp/cache/nvm-install.sh && \
    bash /tmp/cache/nvm-install.sh && \
    . $NVM_DIR/nvm.sh && \
    nvm install $NODE_VERSION && \
    nvm alias default $NODE_VERSION && \
    nvm use default
ENV PATH=$NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH

# Install global Node.js tools
RUN npm install -g \
        yarn \
        pnpm \
        typescript \
        ts-node \
        @vue/cli \
        @angular/cli \
        create-react-app \
        eslint \
        prettier \
        nodemon \
        pm2 \
        serve

# Install Docker CLI using cached GPG key
RUN gpg --dearmor < /tmp/cache/docker.gpg > /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=arm64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# Install Rust using cached script
RUN chmod +x /tmp/cache/rust-install.sh && \
    bash /tmp/cache/rust-install.sh -y && \
    . ~/.cargo/env
ENV PATH=/root/.cargo/bin:$PATH

# Install basic Python packages (minimal to avoid conflicts)
RUN pip3 install --break-system-packages \
        requests \
        numpy

# Skip Go tools installation to avoid network timeouts
# RUN go install golang.org/x/tools/gopls@latest && \
#     go install github.com/air-verse/air@latest && \
#     go install github.com/swaggo/swag/cmd/swag@latest

# Set up bash configuration for developer user
RUN echo 'export PATH=$PATH:/usr/local/go/bin:$GOPATH/bin' >> /home/<USER>/.bashrc && \
    echo 'export GOPATH=/home/<USER>/go' >> /home/<USER>/.bashrc && \
    echo 'export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-arm64' >> /home/<USER>/.bashrc && \
    echo 'source /usr/local/nvm/nvm.sh' >> /home/<USER>/.bashrc && \
    echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc && \
    echo 'alias la="ls -A"' >> /home/<USER>/.bashrc && \
    echo 'alias l="ls -CF"' >> /home/<USER>/.bashrc && \
    chown developer:developer /home/<USER>/.bashrc

# Clean up cache files
RUN rm -rf /tmp/cache

# Create workspace directory
RUN mkdir -p /workspace && chown -R developer:developer /workspace

# Switch to developer user
USER developer
WORKDIR /workspace

# Set default shell
CMD ["/bin/bash"] 