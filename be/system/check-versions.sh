#!/bin/bash

# T-Rex Development Environment Version Checker
# This script checks the versions of all installed development tools

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "T-Rex Development Environment Versions"
echo -e "========================================${NC}"
echo

echo -e "${GREEN}Programming Languages:${NC}"
echo -e "  Go: $(go version | cut -d' ' -f3)"
echo -e "  Java: $(java --version 2>/dev/null | head -1 || echo 'Not found')"
echo -e "  Python: $(python --version 2>/dev/null || echo 'Not found')"
echo -e "  Node.js: $(node --version 2>/dev/null || echo 'Not found')"
echo -e "  Rust: $(rustc --version 2>/dev/null || echo 'Not found')"
echo -e "  GCC: $(gcc --version 2>/dev/null | head -1 || echo 'Not found')"
echo

echo -e "${GREEN}Package Managers:${NC}"
echo -e "  npm: $(npm --version 2>/dev/null || echo 'Not found')"
echo -e "  yarn: $(yarn --version 2>/dev/null || echo 'Not found')"
echo -e "  pip: $(pip --version 2>/dev/null | cut -d' ' -f2 || echo 'Not found')"
echo

echo -e "${GREEN}Development Tools:${NC}"
echo -e "  Git: $(git --version | cut -d' ' -f3)"
echo -e "  Docker CLI: $(docker --version 2>/dev/null | cut -d' ' -f3 | tr -d ',' || echo 'Not found')"
echo -e "  Vim: $(vim --version 2>/dev/null | head -1 | cut -d' ' -f5 || echo 'Not found')"
echo

echo -e "${GREEN}Go Tools:${NC}"
echo -e "  gopls: $(gopls version 2>/dev/null || echo 'Not found')"
echo -e "  air: $(air -v 2>/dev/null | head -1 || echo 'Not found')"
echo

echo -e "${GREEN}Node.js Tools:${NC}"
echo -e "  TypeScript: $(tsc --version 2>/dev/null || echo 'Not found')"
echo -e "  ESLint: $(eslint --version 2>/dev/null || echo 'Not found')"
echo -e "  Prettier: $(prettier --version 2>/dev/null || echo 'Not found')"
echo

echo -e "${GREEN}Python Tools:${NC}"
echo -e "  Jupyter: $(jupyter --version 2>/dev/null | head -1 || echo 'Not found')"
echo -e "  Black: $(black --version 2>/dev/null || echo 'Not found')"
echo -e "  pytest: $(pytest --version 2>/dev/null | head -1 || echo 'Not found')"
echo

echo -e "${GREEN}System Info:${NC}"
echo -e "  OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"')"
echo -e "  Architecture: $(uname -m)"
echo -e "  Kernel: $(uname -r)"
echo

echo -e "${YELLOW}Environment Variables:${NC}"
echo -e "  GOPATH: $GOPATH"
echo -e "  JAVA_HOME: $JAVA_HOME"
echo -e "  NODE_VERSION: $NODE_VERSION"
echo

echo -e "${BLUE}========================================"
echo -e "Version check completed!"
echo -e "========================================${NC}" 