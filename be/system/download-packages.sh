#!/bin/bash

# T-Rex Development Environment Package Downloader
# Downloads all required packages to local cache for offline Docker builds

set -e

# Configuration
CACHE_DIR="./cache"
GO_VERSION="1.24.3"
NODE_VERSION="22.15.0"
NVM_VERSION="0.39.7"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Function to download file if not exists
download_if_not_exists() {
    local url=$1
    local filename=$2
    local filepath="${CACHE_DIR}/${filename}"
    
    if [[ -f "$filepath" ]]; then
        print_info "File already exists: $filename"
        return 0
    fi
    
    print_status "Downloading $filename..."
    if curl -fsSL "$url" -o "$filepath"; then
        print_status "✅ Downloaded: $filename"
        return 0
    else
        print_error "❌ Failed to download: $filename"
        return 1
    fi
}

# Create cache directory
mkdir -p "$CACHE_DIR"

print_status "🚀 Starting package download process..."
print_status "Cache directory: $CACHE_DIR"
echo

# Download Go
print_info "📦 Downloading Go $GO_VERSION..."
download_if_not_exists \
    "https://golang.org/dl/go${GO_VERSION}.linux-arm64.tar.gz" \
    "go${GO_VERSION}.linux-arm64.tar.gz"

# Download NVM installation script
print_info "📦 Downloading NVM $NVM_VERSION..."
download_if_not_exists \
    "https://raw.githubusercontent.com/nvm-sh/nvm/v${NVM_VERSION}/install.sh" \
    "nvm-install.sh"

# Download Rust installation script
print_info "📦 Downloading Rust installer..."
download_if_not_exists \
    "https://sh.rustup.rs" \
    "rust-install.sh"

# Download Docker GPG key
print_info "📦 Downloading Docker GPG key..."
download_if_not_exists \
    "https://download.docker.com/linux/ubuntu/gpg" \
    "docker.gpg"

echo
print_status "✅ Package download completed!"
print_status "📁 Cache directory contents:"
ls -la "$CACHE_DIR/"

echo
print_status "🎉 Ready for offline Docker build!"
print_status "Run: ./build-cached.sh" 