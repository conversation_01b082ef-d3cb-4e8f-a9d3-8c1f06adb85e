#!/bin/bash

# T-Rex Development Environment Runner
# Quick start script for the development environment

set -e

# Configuration
IMAGE_NAME="t-rex/dev-env"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="t-rex-dev-env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start     Start the development environment"
    echo "  stop      Stop the development environment"
    echo "  shell     Open a shell in the running container"
    echo "  build     Build the Docker image"
    echo "  clean     Remove container and image"
    echo "  status    Show container status"
    echo "  logs      Show container logs"
    echo ""
    echo "Options:"
    echo "  -p, --proxy    Use system proxy settings"
    echo "  -v, --volume   Mount current directory as volume"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start --volume        # Start with volume mounting"
    echo "  $0 start --proxy         # Start with proxy settings"
    echo "  $0 shell                 # Open shell in running container"
    echo "  $0 build --proxy         # Build image with proxy"
}

# Function to check if image exists
check_image() {
    if ! docker image inspect $FULL_IMAGE_NAME > /dev/null 2>&1; then
        print_warning "Docker image $FULL_IMAGE_NAME not found."
        read -p "Would you like to build it now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            build_image "$@"
        else
            print_error "Image is required to run the container."
            exit 1
        fi
    fi
}

# Function to build image
build_image() {
    print_status "Building Docker image..."
    BUILD_ARGS=""
    
    if [[ " $* " =~ " --proxy " ]] || [[ " $* " =~ " -p " ]]; then
        if [ ! -z "$HTTP_PROXY" ]; then
            BUILD_ARGS="$BUILD_ARGS --build-arg HTTP_PROXY=$HTTP_PROXY"
            print_info "Using HTTP_PROXY: $HTTP_PROXY"
        fi
        if [ ! -z "$HTTPS_PROXY" ]; then
            BUILD_ARGS="$BUILD_ARGS --build-arg HTTPS_PROXY=$HTTPS_PROXY"
            print_info "Using HTTPS_PROXY: $HTTPS_PROXY"
        fi
    fi
    
    docker build --platform linux/arm64 $BUILD_ARGS -t $FULL_IMAGE_NAME .
    print_status "Image built successfully!"
}

# Function to start container
start_container() {
    print_status "Starting T-Rex development environment..."
    
    # Check if container is already running
    if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
        print_warning "Container $CONTAINER_NAME is already running."
        return 0
    fi
    
    # Remove existing stopped container
    if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
        print_info "Removing existing container..."
        docker rm $CONTAINER_NAME
    fi
    
    # Prepare run arguments
    RUN_ARGS="-it --name $CONTAINER_NAME"
    
    # Add proxy settings if requested
    if [[ " $* " =~ " --proxy " ]] || [[ " $* " =~ " -p " ]]; then
        if [ ! -z "$HTTP_PROXY" ]; then
            RUN_ARGS="$RUN_ARGS -e HTTP_PROXY=$HTTP_PROXY"
        fi
        if [ ! -z "$HTTPS_PROXY" ]; then
            RUN_ARGS="$RUN_ARGS -e HTTPS_PROXY=$HTTPS_PROXY"
        fi
        if [ ! -z "$NO_PROXY" ]; then
            RUN_ARGS="$RUN_ARGS -e NO_PROXY=$NO_PROXY"
        fi
    fi
    
    # Add volume mounting if requested
    if [[ " $* " =~ " --volume " ]] || [[ " $* " =~ " -v " ]]; then
        RUN_ARGS="$RUN_ARGS -v $(pwd):/workspace"
        print_info "Mounting current directory to /workspace"
    fi
    
    # Add common port mappings
    RUN_ARGS="$RUN_ARGS -p 3000:3000 -p 8080:8080 -p 8000:8000 -p 5000:5000 -p 4200:4200 -p 8888:8888"
    
    # Run the container
    docker run $RUN_ARGS $FULL_IMAGE_NAME
}

# Function to open shell in running container
open_shell() {
    if ! docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
        print_error "Container $CONTAINER_NAME is not running."
        print_info "Start the container first with: $0 start"
        exit 1
    fi
    
    print_status "Opening shell in $CONTAINER_NAME..."
    docker exec -it $CONTAINER_NAME /bin/bash
}

# Function to stop container
stop_container() {
    if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
        print_status "Stopping container $CONTAINER_NAME..."
        docker stop $CONTAINER_NAME
        print_status "Container stopped."
    else
        print_warning "Container $CONTAINER_NAME is not running."
    fi
}

# Function to show status
show_status() {
    print_info "Container Status:"
    if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q $CONTAINER_NAME; then
        docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo "Container $CONTAINER_NAME is not running."
    fi
    
    echo ""
    print_info "Image Information:"
    if docker image inspect $FULL_IMAGE_NAME > /dev/null 2>&1; then
        docker images $FULL_IMAGE_NAME --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    else
        echo "Image $FULL_IMAGE_NAME not found."
    fi
}

# Function to show logs
show_logs() {
    if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
        print_status "Showing logs for $CONTAINER_NAME..."
        docker logs -f $CONTAINER_NAME
    else
        print_error "Container $CONTAINER_NAME not found."
    fi
}

# Function to clean up
clean_up() {
    print_warning "This will remove the container and image. Continue? (y/N)"
    read -p "" -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Stop and remove container
        if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}" | grep -q $CONTAINER_NAME; then
            print_info "Removing container $CONTAINER_NAME..."
            docker rm -f $CONTAINER_NAME
        fi
        
        # Remove image
        if docker image inspect $FULL_IMAGE_NAME > /dev/null 2>&1; then
            print_info "Removing image $FULL_IMAGE_NAME..."
            docker rmi $FULL_IMAGE_NAME
        fi
        
        print_status "Cleanup completed."
    else
        print_info "Cleanup cancelled."
    fi
}

# Main script logic
case "${1:-}" in
    "start")
        check_image "${@:2}"
        start_container "${@:2}"
        ;;
    "stop")
        stop_container
        ;;
    "shell")
        open_shell
        ;;
    "build")
        build_image "${@:2}"
        ;;
    "clean")
        clean_up
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "-h"|"--help"|"help"|"")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac 