# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*
CONTRIBUTING*
LICENSE*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Go specific
vendor/
*.exe
*.exe~

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Java specific
*.class
*.jar
*.war
target/

# Important: DO NOT ignore cache/ directory as it contains required packages
# cache/

# Temporary files
*.tmp
*.temp
*.log

# Docker related
Dockerfile.*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Package managers
.npm
.yarn 