package imageanalyzer

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"git.nevint.com/fota3/t-rex/domain/agent"
	agentModel "git.nevint.com/fota3/t-rex/model/agent"
	requirementmodel "git.nevint.com/fota3/t-rex/model/requirement"
	"go.uber.org/zap"
)

// LLMClientFactory defines the interface for creating LLM clients
type LLMClientFactory interface {
	GetLLMClient(ctx context.Context, modelName string) (agent.LLMClient, error)
}

// Service handles AI-powered image analysis
type Service struct {
	llmClientFactory LLMClientFactory
	uploadDir        string
	defaultModelName string
	logger           *zap.Logger
}

// NewService creates a new image analyzer service
func NewService(llmClientFactory LLMClientFactory, uploadDir string, defaultModelName string, logger *zap.Logger) (*Service, error) {
	if llmClientFactory == nil {
		return nil, fmt.Errorf("LLM client factory is required")
	}
	if uploadDir == "" {
		return nil, fmt.Errorf("upload directory is required")
	}
	if defaultModelName == "" {
		return nil, fmt.Errorf("default model name is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	return &Service{
		llmClientFactory: llmClientFactory,
		uploadDir:        uploadDir,
		defaultModelName: defaultModelName,
		logger:           logger.Named("image_analyzer"),
	}, nil
}

// AnalyzeImage performs comprehensive multi-dimensional analysis of an image
func (s *Service) AnalyzeImage(ctx context.Context, imagePath string, modelName string) (*requirementmodel.ImageAnalysis, error) {
	s.logger.Info("Starting image analysis",
		zap.String("image_path", imagePath))

	// Read and encode image
	imageBase64, err := s.encodeImageToBase64(imagePath)
	if err != nil {
		s.logger.Error("Failed to encode image",
			zap.String("image_path", imagePath),
			zap.Error(err))
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	// Generate analysis prompt
	prompt := s.generateAnalysisPrompt()

	// Create message with image
	messages := []agentModel.Message{
		{
			Role:    agentModel.RoleUser,
			Content: prompt,
			Images:  []string{imageBase64},
		},
	}

	// Use provided model name or fall back to default
	if modelName == "" {
		modelName = s.defaultModelName
	}

	// Get LLM client for the specific model
	llmClient, err := s.llmClientFactory.GetLLMClient(ctx, modelName)
	if err != nil {
		s.logger.Error("Failed to get LLM client for image analysis",
			zap.Error(err),
			zap.String("image_path", imagePath),
			zap.String("model_name", modelName))
		return nil, fmt.Errorf("failed to get LLM client for model '%s': %w", modelName, err)
	}

	// Call LLM for analysis with increased limits and timeout
	temperature := float32(0.5)
	maxTokens := 8000 // Increased from 4000 to accommodate detailed Chinese responses
	response, err := llmClient.ChatCompletionNonStreaming(ctx, &agent.ChatCompletionRequest{
		Model:       modelName, // Use appropriate model
		Messages:    messages,
		Temperature: &temperature, // Lower temperature for more consistent analysis
		MaxTokens:   &maxTokens,   // Increased token limit for comprehensive analysis
	})
	if err != nil {
		s.logger.Error("Failed to get LLM analysis",
			zap.String("image_path", imagePath),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get LLM analysis: %w", err)
	}

	s.logger.Debug("Raw LLM response received",
		zap.String("image_path", imagePath),
		zap.Int("response_length", len(response.Content)))

	// Parse the structured response with improved error handling
	analysis, err := s.parseAnalysisResponse(response.Content)
	if err != nil {
		s.logger.Error("Failed to parse analysis response",
			zap.String("image_path", imagePath),
			zap.String("raw_response", response.Content),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	s.logger.Info("Image analysis completed successfully",
		zap.String("image_path", imagePath))

	return analysis, nil
}

// encodeImageToBase64 reads an image file and encodes it to base64
func (s *Service) encodeImageToBase64(imagePath string) (string, error) {
	fullPath := filepath.Join(s.uploadDir, imagePath)

	// Security check: ensure the path is within upload directory
	if !strings.HasPrefix(fullPath, s.uploadDir) {
		return "", fmt.Errorf("invalid image path")
	}

	file, err := os.Open(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read image data: %w", err)
	}

	// Determine MIME type based on file extension
	ext := strings.ToLower(filepath.Ext(imagePath))
	var mimeType string
	switch ext {
	case ".jpg", ".jpeg":
		mimeType = "image/jpeg"
	case ".png":
		mimeType = "image/png"
	case ".gif":
		mimeType = "image/gif"
	case ".webp":
		mimeType = "image/webp"
	default:
		mimeType = "image/jpeg" // Default fallback
	}

	encoded := base64.StdEncoding.EncodeToString(data)
	return fmt.Sprintf("data:%s;base64,%s", mimeType, encoded), nil
}

// generateAnalysisPrompt creates a comprehensive prompt for image analysis
func (s *Service) generateAnalysisPrompt() string {
	return `请对这张图片进行智能分析，根据业务相关性选择合适的分析路径，并提供结构化的JSON响应。

**重要：请用中文回复所有内容，包括JSON字段的值。**

**分析策略：**

**第一步：业务方向判断**
首先分析图片中体现的业务方向，判断是否与当前用户需求的业务方向一致：

**路径A：业务功能分析**（当业务方向一致时）
说明用户提供的是准确的业务功能图或需求设计图，需要进行完整的业务和视觉分析：

1. **业务维度分析** - 重点关注
   - 业务领域和类型
   - 核心业务功能识别
   - 业务流程和逻辑
   - 功能模块划分

2. **视觉设计分析** - 配合业务
   - UI组件和布局设计
   - 交互逻辑和用户体验
   - 视觉风格和设计语言
   - 色彩方案和视觉层次

**路径B：页面UI参考分析**（当业务方向不一致时）
说明用户提供的是页面UI设计参考图，重点分析视觉设计风格，忽略具体业务内容：

1. **色彩设计分析** - **最高优先级**
   - 主色调具体描述（如深蓝色#1a365d、浅蓝色#3182ce等）
   - 辅助色彩和强调色的具体色值或详细描述
   - 背景色系和层次（深色主题/浅色主题/渐变等）
   - 文字色彩层次（主要文字、次要文字、链接色等）
   - 按钮和交互元素的色彩设计
   - 整体色彩饱和度和明度特征

2. **视觉设计元素** - 核心重点
   - 整体设计风格和美学特征
   - 布局结构和组件设计
   - 字体、图标和视觉元素
   - 圆角、阴影、边框等细节设计

3. **UI/UX设计原则**
   - 界面设计模式和规范
   - 用户交互体验特点
   - 视觉层次和信息架构
   - 响应式设计考虑

4. **设计风格特征**
   - 现代/传统设计趋势
   - 简约/复杂程度
   - 专业/休闲风格定位
   - 品牌调性和氛围

**响应格式：**
请将你的分析作为有效的JSON对象返回，结构如下：

{
  "visual_elements": {
    "main_subjects": ["主要界面元素", "核心组件", "等等"],
    "composition": "整体布局和结构描述",
    "color_palette": ["主色调(具体色值或详细描述)", "辅助色", "强调色", "背景色", "文字色"],
    "lighting_style": "视觉明暗和对比特征",
    "lines_shapes": "线条风格和几何设计元素",
    "texture_pattern": "材质效果和图案特征"
  },
  "technical_style": {
    "medium": "设计媒介和制作技术",
    "artistic_style": "整体设计风格定义",
    "technique": "具体设计技法和手段",
    "quality": "设计执行质量和专业度"
  },
  "emotional_tone": {
    "overall_mood": "整体设计氛围和感受",
    "emotional_tone": "传达的情感调性",
    "sensory_impact": "用户感官体验和印象",
    "energy_level": "视觉活跃度和动态感"
  },
  "narrative_symbolic": {
    "narrative": "设计叙事和用户故事",
    "symbols_metaphors": ["视觉隐喻", "设计象征", "概念表达"],
    "thematic_content": "主题内容和设计概念",
    "cultural_context": "文化背景和设计语境"
  },
  "business_dimension": {
    "business_direction": "识别的业务方向（如果与需求一致则详细分析，不一致则标注为'UI参考'）",
    "business_function": "业务功能识别（路径A详细，路径B概括）",
    "business_process": "业务流程分析（路径A详细，路径B忽略）"
  },
  "summary": "根据选择的分析路径，提供针对性的开发指导建议"
}

**关键原则：**
1. **业务相关性优先**：先判断业务方向一致性，再选择分析重点
2. **色彩信息精准提取**：对于UI参考图，必须详细准确地提取色彩信息，这是线框图设计的关键依据
3. **避免误导信息**：如果是UI参考图，不要深入分析不相关的业务逻辑
4. **实用导向**：分析结果要能直接指导后续的设计和开发工作
5. **准确性保证**：确保分析结果与图片实际内容相符，不过度解读
6. **中文表达**：所有分析内容使用准确的中文专业术语

**输出要求：**
- 在summary中明确说明采用了哪种分析路径（A或B）
- 如果是路径B，在business_dimension中标注"此为UI参考图，业务信息仅供参考"
- **特别重要：如果是路径B，必须在color_palette中提供尽可能详细的色彩描述，包括具体色值、色彩名称或RGB描述，这些信息将直接用于线框图的色彩设计**
- 在summary中专门说明提取的色彩方案如何应用到后续的UI设计中
- 提供具体可操作的设计建议和技术实现指导`
}

// parseAnalysisResponse parses the LLM's JSON response into structured data
func (s *Service) parseAnalysisResponse(response string) (*requirementmodel.ImageAnalysis, error) {
	s.logger.Debug("Parsing analysis response",
		zap.Int("response_length", len(response)))

	// Clean the response to extract JSON
	jsonStr := s.extractJSON(response)
	if jsonStr == "" {
		s.logger.Warn("No valid JSON found, attempting to extract partial JSON")
		// Try to extract partial JSON by finding incomplete structures
		jsonStr = s.extractPartialJSON(response)
		if jsonStr == "" {
			return nil, fmt.Errorf("no valid JSON found in response")
		}
	}

	s.logger.Debug("Extracted JSON for parsing",
		zap.Int("json_length", len(jsonStr)))

	// Parse the JSON response with flexible structure
	var parsedData struct {
		VisualElements struct {
			MainSubjects   []string `json:"main_subjects"`
			Composition    string   `json:"composition"`
			ColorPalette   []string `json:"color_palette"`
			LightingStyle  string   `json:"lighting_style"`
			LinesShapes    string   `json:"lines_shapes"`
			TexturePattern string   `json:"texture_pattern"`
		} `json:"visual_elements"`
		TechnicalStyle struct {
			Medium        string `json:"medium"`
			ArtisticStyle string `json:"artistic_style"`
			Technique     string `json:"technique"`
			Quality       string `json:"quality"`
		} `json:"technical_style"`
		EmotionalTone struct {
			OverallMood   string `json:"overall_mood"`
			EmotionalTone string `json:"emotional_tone"`
			SensoryImpact string `json:"sensory_impact"`
			EnergyLevel   string `json:"energy_level"`
		} `json:"emotional_tone"`
		NarrativeSymbolic struct {
			Narrative        string   `json:"narrative"`
			SymbolsMetaphors []string `json:"symbols_metaphors"`
			ThematicContent  string   `json:"thematic_content"`
			CulturalContext  string   `json:"cultural_context"`
		} `json:"narrative_symbolic"`
		BusinessDimension struct {
			BusinessDirection string `json:"business_direction"`
			BusinessFunction  string `json:"business_function"`
			BusinessProcess   string `json:"business_process"`
		} `json:"business_dimension"`
		Summary string `json:"summary"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &parsedData); err != nil {
		s.logger.Warn("Failed to parse complete JSON, attempting partial parsing",
			zap.Error(err))
		// Try to parse as much as possible from truncated JSON
		return s.parsePartialResponse(jsonStr)
	}

	// Convert to our internal structure
	analysis := &requirementmodel.ImageAnalysis{
		VisualElements: requirementmodel.VisualElementsAnalysis{
			MainSubjects:   parsedData.VisualElements.MainSubjects,
			Composition:    parsedData.VisualElements.Composition,
			ColorPalette:   parsedData.VisualElements.ColorPalette,
			LightingStyle:  parsedData.VisualElements.LightingStyle,
			LinesShapes:    parsedData.VisualElements.LinesShapes,
			TexturePattern: parsedData.VisualElements.TexturePattern,
		},
		TechnicalStyle: requirementmodel.TechnicalStyleAnalysis{
			Medium:        parsedData.TechnicalStyle.Medium,
			ArtisticStyle: parsedData.TechnicalStyle.ArtisticStyle,
			Technique:     parsedData.TechnicalStyle.Technique,
			Quality:       parsedData.TechnicalStyle.Quality,
		},
		EmotionalTone: requirementmodel.EmotionalToneAnalysis{
			OverallMood:   parsedData.EmotionalTone.OverallMood,
			EmotionalTone: parsedData.EmotionalTone.EmotionalTone,
			SensoryImpact: parsedData.EmotionalTone.SensoryImpact,
			EnergyLevel:   parsedData.EmotionalTone.EnergyLevel,
		},
		NarrativeSymbolic: requirementmodel.NarrativeSymbolicAnalysis{
			Narrative:        parsedData.NarrativeSymbolic.Narrative,
			SymbolsMetaphors: parsedData.NarrativeSymbolic.SymbolsMetaphors,
			ThematicContent:  parsedData.NarrativeSymbolic.ThematicContent,
			CulturalContext:  parsedData.NarrativeSymbolic.CulturalContext,
		},
		BusinessDimension: requirementmodel.BusinessDimensionAnalysis{
			BusinessDirection: parsedData.BusinessDimension.BusinessDirection,
			BusinessFunction:  parsedData.BusinessDimension.BusinessFunction,
			BusinessProcess:   parsedData.BusinessDimension.BusinessProcess,
		},
		Summary: parsedData.Summary,
	}

	// Relaxed validation - accept if we have ANY meaningful content
	if s.hasValidContent(analysis) {
		s.logger.Info("Successfully parsed analysis with valid content")
		return analysis, nil
	}

	return nil, fmt.Errorf("analysis response appears to be empty or invalid")
}

// extractJSON attempts to find and extract JSON from the response
func (s *Service) extractJSON(response string) string {
	// Look for JSON content between ```json and ``` or { and }
	start := strings.Index(response, "{")
	if start == -1 {
		return ""
	}

	// Find the matching closing brace
	openBraces := 0
	for i := start; i < len(response); i++ {
		switch response[i] {
		case '{':
			openBraces++
		case '}':
			openBraces--
			if openBraces == 0 {
				return response[start : i+1]
			}
		}
	}

	return ""
}

// extractPartialJSON attempts to extract partial JSON from truncated responses
func (s *Service) extractPartialJSON(response string) string {
	// Look for the start of JSON
	start := strings.Index(response, "{")
	if start == -1 {
		return ""
	}

	// Find the last valid JSON structure we can extract
	// This is a best-effort approach to salvage partial content
	lastValidEnd := start
	openBraces := 0

	for i := start; i < len(response); i++ {
		switch response[i] {
		case '{':
			openBraces++
		case '}':
			openBraces--
			if openBraces == 0 {
				return response[start : i+1]
			}
			// Mark last position where we had valid structure
			if openBraces > 0 {
				lastValidEnd = i
			}
		case '"':
			// Skip string content to avoid counting braces inside strings
			i++
			for i < len(response) && response[i] != '"' {
				if response[i] == '\\' {
					i++ // Skip escaped character
				}
				i++
			}
		}
	}

	// If we couldn't find a complete JSON, try to create a valid one
	// by truncating at the last safe point and adding closing braces
	if lastValidEnd > start {
		partial := response[start : lastValidEnd+1]
		// Attempt to close the JSON structure
		for openBraces > 0 {
			partial += "}"
			openBraces--
		}
		s.logger.Debug("Created partial JSON",
			zap.String("original_length", fmt.Sprintf("%d", len(response))),
			zap.String("partial_length", fmt.Sprintf("%d", len(partial))))
		return partial
	}

	return ""
}

// parsePartialResponse attempts to parse truncated JSON responses
func (s *Service) parsePartialResponse(jsonStr string) (*requirementmodel.ImageAnalysis, error) {
	s.logger.Info("Attempting to parse partial JSON response")

	// Create an analysis with empty structure
	analysis := &requirementmodel.ImageAnalysis{
		VisualElements:    requirementmodel.VisualElementsAnalysis{},
		TechnicalStyle:    requirementmodel.TechnicalStyleAnalysis{},
		EmotionalTone:     requirementmodel.EmotionalToneAnalysis{},
		NarrativeSymbolic: requirementmodel.NarrativeSymbolicAnalysis{},
		Summary:           "",
	}

	// Try to extract what we can using string matching
	if strings.Contains(jsonStr, "visual_elements") {
		s.extractVisualElements(jsonStr, &analysis.VisualElements)
	}
	if strings.Contains(jsonStr, "technical_style") {
		s.extractTechnicalStyle(jsonStr, &analysis.TechnicalStyle)
	}
	if strings.Contains(jsonStr, "emotional_tone") {
		s.extractEmotionalTone(jsonStr, &analysis.EmotionalTone)
	}
	if strings.Contains(jsonStr, "narrative_symbolic") {
		s.extractNarrativeSymbolic(jsonStr, &analysis.NarrativeSymbolic)
	}
	if strings.Contains(jsonStr, "business_dimension") {
		s.extractBusinessDimension(jsonStr, &analysis.BusinessDimension)
	}

	// Try to extract summary
	if summaryMatch := strings.Index(jsonStr, "\"summary\""); summaryMatch != -1 {
		if summary := s.extractStringValue(jsonStr[summaryMatch:], "summary"); summary != "" {
			analysis.Summary = summary
		}
	}

	// Check if we extracted any meaningful content
	if s.hasValidContent(analysis) {
		s.logger.Info("Successfully extracted partial analysis content")
		return analysis, nil
	}

	return nil, fmt.Errorf("could not extract meaningful content from partial response")
}

// hasValidContent checks if the analysis contains any meaningful content
func (s *Service) hasValidContent(analysis *requirementmodel.ImageAnalysis) bool {
	if analysis == nil {
		return false
	}

	// Check if we have content in any of the main sections
	hasVisualContent := analysis.VisualElements.Composition != "" ||
		len(analysis.VisualElements.MainSubjects) > 0 ||
		len(analysis.VisualElements.ColorPalette) > 0

	hasTechnicalContent := analysis.TechnicalStyle.Medium != "" ||
		analysis.TechnicalStyle.ArtisticStyle != ""

	hasEmotionalContent := analysis.EmotionalTone.OverallMood != "" ||
		analysis.EmotionalTone.EmotionalTone != ""

	hasNarrativeContent := analysis.NarrativeSymbolic.Narrative != "" ||
		len(analysis.NarrativeSymbolic.SymbolsMetaphors) > 0

	hasSummary := analysis.Summary != ""

	// Accept if we have content in any section or a summary
	return hasVisualContent || hasTechnicalContent || hasEmotionalContent || hasNarrativeContent || hasSummary
}

// Helper methods for extracting specific sections from partial JSON

func (s *Service) extractVisualElements(jsonStr string, ve *requirementmodel.VisualElementsAnalysis) {
	if composition := s.extractStringValue(jsonStr, "composition"); composition != "" {
		ve.Composition = composition
	}
	if lightingStyle := s.extractStringValue(jsonStr, "lighting_style"); lightingStyle != "" {
		ve.LightingStyle = lightingStyle
	}
	if linesShapes := s.extractStringValue(jsonStr, "lines_shapes"); linesShapes != "" {
		ve.LinesShapes = linesShapes
	}
	if texturePattern := s.extractStringValue(jsonStr, "texture_pattern"); texturePattern != "" {
		ve.TexturePattern = texturePattern
	}
	// Extract arrays would be more complex, focusing on strings for now
}

func (s *Service) extractTechnicalStyle(jsonStr string, ts *requirementmodel.TechnicalStyleAnalysis) {
	if medium := s.extractStringValue(jsonStr, "medium"); medium != "" {
		ts.Medium = medium
	}
	if artisticStyle := s.extractStringValue(jsonStr, "artistic_style"); artisticStyle != "" {
		ts.ArtisticStyle = artisticStyle
	}
	if technique := s.extractStringValue(jsonStr, "technique"); technique != "" {
		ts.Technique = technique
	}
	if quality := s.extractStringValue(jsonStr, "quality"); quality != "" {
		ts.Quality = quality
	}
}

func (s *Service) extractEmotionalTone(jsonStr string, et *requirementmodel.EmotionalToneAnalysis) {
	if overallMood := s.extractStringValue(jsonStr, "overall_mood"); overallMood != "" {
		et.OverallMood = overallMood
	}
	if emotionalTone := s.extractStringValue(jsonStr, "emotional_tone"); emotionalTone != "" {
		et.EmotionalTone = emotionalTone
	}
	if sensoryImpact := s.extractStringValue(jsonStr, "sensory_impact"); sensoryImpact != "" {
		et.SensoryImpact = sensoryImpact
	}
	if energyLevel := s.extractStringValue(jsonStr, "energy_level"); energyLevel != "" {
		et.EnergyLevel = energyLevel
	}
}

func (s *Service) extractNarrativeSymbolic(jsonStr string, ns *requirementmodel.NarrativeSymbolicAnalysis) {
	if narrative := s.extractStringValue(jsonStr, "narrative"); narrative != "" {
		ns.Narrative = narrative
	}
	if thematicContent := s.extractStringValue(jsonStr, "thematic_content"); thematicContent != "" {
		ns.ThematicContent = thematicContent
	}
	if culturalContext := s.extractStringValue(jsonStr, "cultural_context"); culturalContext != "" {
		ns.CulturalContext = culturalContext
	}
}

func (s *Service) extractBusinessDimension(jsonStr string, bd *requirementmodel.BusinessDimensionAnalysis) {

	if businessDirection := s.extractStringValue(jsonStr, "business_direction"); businessDirection != "" {
		bd.BusinessDirection = businessDirection
	}
	if businessFunction := s.extractStringValue(jsonStr, "business_function"); businessFunction != "" {
		bd.BusinessFunction = businessFunction
	}
	if businessProcess := s.extractStringValue(jsonStr, "business_process"); businessProcess != "" {
		bd.BusinessProcess = businessProcess
	}
}

// extractStringValue extracts a string value for a given key from JSON text
func (s *Service) extractStringValue(jsonStr, key string) string {
	// Simple string extraction - look for "key": "value"
	keyPattern := fmt.Sprintf("\"%s\":", key)
	keyIndex := strings.Index(jsonStr, keyPattern)
	if keyIndex == -1 {
		return ""
	}

	// Find the start of the string value
	valueStart := keyIndex + len(keyPattern)
	for valueStart < len(jsonStr) && (jsonStr[valueStart] == ' ' || jsonStr[valueStart] == '\t' || jsonStr[valueStart] == '\n') {
		valueStart++
	}

	if valueStart >= len(jsonStr) || jsonStr[valueStart] != '"' {
		return ""
	}

	valueStart++ // Skip opening quote
	valueEnd := valueStart

	// Find the end of the string value, handling escaped quotes
	for valueEnd < len(jsonStr) {
		if jsonStr[valueEnd] == '"' && (valueEnd == valueStart || jsonStr[valueEnd-1] != '\\') {
			break
		}
		valueEnd++
	}

	if valueEnd >= len(jsonStr) {
		// String is truncated, return what we have
		return jsonStr[valueStart:]
	}

	return jsonStr[valueStart:valueEnd]
}
