package mongodb

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Pipeline[T Table, V any] struct {
	t        T
	cli      *mongo.Client
	dbName   string
	pipeline bson.A
}

func NewPipeline[T Table, V any](cli *mongo.Client, dbName string) (*Pipeline[T, V], error) {
	if cli == nil {
		return nil, errors.New("mongodb client cannot be nil")
	}
	if dbName == "" {
		return nil, errors.New("mongodb database name cannot be empty")
	}
	d := &Pipeline[T, V]{
		t:        *new(T),
		cli:      cli,
		dbName:   dbName,
		pipeline: bson.A{},
	}
	return d, nil
}

func (d Pipeline[T, V]) AddStage(stages ...any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, stages...)
	return d
}

func (d <PERSON>peline[T, V]) Dump() bson.A {
	return d.pipeline
}

func (d Pipeline[T, V]) Aggregate(ctx context.Context, opts ...*options.AggregateOptions) ([]V, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	cur, err := coll.Aggregate(ctx, d.pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	res := []V{}
	if err := cur.All(ctx, &res); err != nil {
		return nil, err
	}
	return res, nil
}
