package mongodb

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type chainCtx[T Table] struct {
	d      ORM[T]
	opts   []*options.FindOptions // options from the outer caller, will be conbined with the internal option
	opt    *options.FindOptions
	size   int64
	page   int64
	ctx    context.Context
	filter bson.M
}

// PageSize set the page size, 0 means no limit
func (c chainCtx[T]) PageSize(size uint32) chainCtx[T] {
	if c.opt == nil {
		c.opt = options.Find()
	}
	c.size = int64(size)
	c.opt.SetLimit(c.size)
	if c.page != 0 {
		c.opt.SetSkip(c.size * (c.page - 1))
	}
	return c
}

// PageNum set the page number, 0 do nothing
func (c chainCtx[T]) PageNum(num uint32) chainCtx[T] {
	if c.opt == nil {
		c.opt = options.Find()
	}
	if num == 0 {
		return c
	}
	c.page = int64(num)
	/*
		if c.size == 0 {
			c.size = 10
		}
	*/
	c.opt.SetSkip(c.size * (c.page - 1))
	return c
}

func (c chainCtx[T]) Sort(sort bson.M) chainCtx[T] {
	if c.opt == nil {
		c.opt = options.Find()
	}
	c.opt.SetSort(sort)
	return c
}

func (c chainCtx[T]) Do() (int64, []T, error) {
	coll := c.d.cli.Database(c.d.dbName).Collection(c.d.t.CollectionName())
	total, err := coll.CountDocuments(c.ctx, c.filter)
	if err != nil {
		return 0, nil, err
	}
	if total == 0 {
		return 0, nil, nil
	}
	if c.opts == nil {
		c.opts = []*options.FindOptions{}
	}
	if c.opt == nil {
		c.opt = options.Find()
	}
	c.opts = append(c.opts, c.opt)
	cur, err := coll.Find(c.ctx, c.filter, c.opts...)
	if err != nil {
		return 0, nil, err
	}
	orders := []T{}
	if err := cur.All(c.ctx, &orders); err != nil {
		return 0, nil, err
	}
	return total, orders, nil
}
