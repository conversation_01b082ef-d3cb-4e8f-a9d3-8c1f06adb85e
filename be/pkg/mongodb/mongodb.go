package mongodb

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Mgo can still exist as a global client initialized for the application,
// but the ORM/Pipeline will not depend on it by default.
var Mgo *mongo.Client

// dbName is removed as a package-level variable managed by Init.
// var dbName string

// DefaultDB is removed as a global variable for direct use by the package.
// var DefaultDB *mongo.Database

// Init initializes the global client Mgo based on provided configuration.
// The caller is responsible for reading config values (e.g., using Viper).
func Init(connectionString string, databaseName string) error {
	if connectionString == "" {
		return errors.New("mongodb connection string cannot be empty")
	}
	// We no longer set a global dbName here. The caller should manage the name.
	// dbName = databaseName
	// if dbName == "" {
	// 	return errors.New("mongodb database name cannot be empty") // Caller should ensure this
	// }

	ctx := context.TODO()
	clientOptions := options.Client().ApplyURI(connectionString)
	var err error
	Mgo, err = mongo.Connect(ctx, clientOptions)
	if err != nil {
		return err
	}
	// Optional: Ping the server to verify connection
	err = Mgo.Ping(ctx, nil)
	if err != nil {
		// Attempt to close the client if ping fails after connection
		_ = Mgo.Disconnect(ctx)
		return err
	}
	return nil
}

// GetDefaultDBName is removed as the package no longer manages a single default name.
// func GetDefaultDBName() string {
// 	return dbName
// }

// InitCli remains mostly the same, as it already takes a connection string.
func InitCli(conntectString string) (*mongo.Client, error) {
	if conntectString == "" {
		return nil, errors.New("connection string cannot be empty")
	}
	uri := conntectString
	ctx := context.TODO()
	clientOptions := options.Client().ApplyURI(uri)
	cli, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, err
	}
	// Optional: Ping the server to verify connection
	err = cli.Ping(ctx, nil)
	if err != nil {
		// Attempt to close the client if ping fails after connection
		_ = cli.Disconnect(ctx)
		return nil, err
	}
	return cli, nil
}
