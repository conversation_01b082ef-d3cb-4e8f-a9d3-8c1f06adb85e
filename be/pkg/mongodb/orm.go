package mongodb

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Table interface {
	CollectionName() string
}

type ORM[T Table] struct {
	t      T
	cli    *mongo.Client
	dbName string
}

type OptionFunc[T Table] func(*ORM[T])

// Collection returns the underlying mongo.Collection for the ORM.
// This is useful for operations not directly exposed by the ORM, like index management.
func (d *ORM[T]) Collection() *mongo.Collection {
	return d.cli.Database(d.dbName).Collection(d.t.CollectionName())
}

func SetCli[T Table](cli *mongo.Client) OptionFunc[T] {
	return func(d *ORM[T]) {
		d.cli = cli
	}
}

func SetDBName[T Table](name string) OptionFunc[T] {
	return func(d *ORM[T]) {
		d.dbName = name
	}
}

// New create a new ORM object
func New[T Table](cli *mongo.Client, dbName string, opts ...OptionFunc[T]) (*ORM[T], error) {
	if cli == nil {
		return nil, errors.New("mongodb client cannot be nil")
	}
	if dbName == "" {
		return nil, errors.New("mongodb database name cannot be empty")
	}
	d := &ORM[T]{
		t:      *new(T),
		cli:    cli,
		dbName: dbName,
	}
	for _, opt := range opts {
		opt(d)
	}
	return d, nil
}

// NewWithObj create a new ORM object with an object
func NewWithObj[T Table](obj T, cli *mongo.Client, dbName string, opts ...OptionFunc[T]) (*ORM[T], error) {
	if cli == nil {
		return nil, errors.New("mongodb client cannot be nil")
	}
	if dbName == "" {
		return nil, errors.New("mongodb database name cannot be empty")
	}
	d := &ORM[T]{
		t:      obj,
		cli:    cli,
		dbName: dbName,
	}
	for _, opt := range opts {
		opt(d)
	}
	return d, nil
}

func ToAnySlice[T any](objs []T) []any {
	var res []any
	if objs == nil {
		return nil
	}
	for _, obj := range objs {
		res = append(res, obj)
	}
	return res
}

func (d ORM[T]) Count(ctx context.Context, filter bson.M) (int64, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	total, err := coll.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return total, nil
}

// Insert insert an object into the collection
func (d ORM[T]) Insert(ctx context.Context, obj T, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	res, err := coll.InsertOne(ctx, &obj, opts...)
	if err != nil {
		return res, err
	}
	return res, nil
}

// InsertMany insert many objects into the collection
func (d ORM[T]) InsertMany(ctx context.Context, objs []T, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.InsertMany(ctx, ToAnySlice(objs), opts...)
}

func (d ORM[T]) Update(ctx context.Context, filter bson.M, fields any, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.UpdateOne(ctx, filter, fields, opts...)
}

func (d ORM[T]) UpdateMany(ctx context.Context, filter bson.M, fields any, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.UpdateMany(ctx, filter, fields, opts...)
}

func (d ORM[T]) UpdateSet(ctx context.Context, filter bson.M, fields any, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.UpdateOne(ctx, filter, bson.M{"$set": fields}, opts...)
}

func (d ORM[T]) UpdateSetMany(ctx context.Context, filter bson.M, fields any, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.UpdateMany(ctx, filter, bson.M{"$set": fields}, opts...)
}

func (d ORM[T]) Delete(ctx context.Context, filter bson.M, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error) {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.DeleteOne(ctx, filter, opts...)
}

func (d ORM[T]) Replace(ctx context.Context, id primitive.ObjectID, obj T) error {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	_, err := coll.ReplaceOne(ctx, bson.M{"_id": id}, &obj)
	return err
}

func (d ORM[T]) Drop(ctx context.Context) error {
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	return coll.Drop(ctx)
}

// FindOneByFilter find one object by filter, return object and error
func (d ORM[T]) FindOneByFilter(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (T, error) {
	return d.GetOneByFilter(ctx, filter, opts...)
}

// GetOneByFilter equal to FindOneByFilter
func (d ORM[T]) GetOneByFilter(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (T, error) {
	res := new(T)
	coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
	err := coll.FindOne(ctx, filter, opts...).Decode(res)
	return *res, err
}

// FindOneByField find one object by field name and field value, return object and error
func (d ORM[T]) FindOneByField(ctx context.Context, field string, value any, opts ...*options.FindOneOptions) (T, error) {
	return d.GetOneByFilter(ctx, bson.M{field: value}, opts...)
}

// GetOneByField equal to FindOneByField
func (d ORM[T]) GetOneByField(ctx context.Context, field string, value any, opts ...*options.FindOneOptions) (T, error) {
	return d.GetOneByFilter(ctx, bson.M{field: value}, opts...)
}

// FindManyByFilter find many objects by filter, return chainCtx[T]
func (d ORM[T]) FindManyByFilter(ctx context.Context, filter bson.M, opts ...*options.FindOptions) chainCtx[T] {
	return d.GetManyByFilter(ctx, filter, opts...)
}

// GetManyByFilter get many objects by filter, return chainCtx[T]
func (d ORM[T]) GetManyByFilter(ctx context.Context, filter bson.M, opts ...*options.FindOptions) chainCtx[T] {
	return chainCtx[T]{d: d, ctx: ctx, filter: filter, opts: opts, opt: options.Find()}
}

// GetManyByField get many objects by field name and field value, return chainCtx[T]
func (d ORM[T]) GetManyByField(ctx context.Context, field string, value any, opts ...*options.FindOptions) chainCtx[T] {
	return chainCtx[T]{d: d, ctx: ctx, filter: bson.M{field: value}, opts: opts, opt: options.Find()}
}
