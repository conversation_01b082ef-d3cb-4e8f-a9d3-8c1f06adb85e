//go:build go1.23
// +build go1.23

package mongodb

import (
	"context"
	"iter"
)

func (d Pipeline[T, V]) AggregateIter(ctx context.Context) iter.Seq2[V, error] {
	return func(yield func(V, error) bool) {
		var v V
		coll := d.cli.Database(d.dbName).Collection(d.t.CollectionName())
		cur, err := coll.Aggregate(ctx, d.pipeline)
		if err != nil {
			yield(v, err)
			return
		}
		defer cur.Close(ctx)
		for cur.Next(ctx) {
			var v V
			if err := cur.Decode(&v); err != nil {
				yield(v, err)
				return
			}
			if !yield(v, nil) {
				return
			}
		}
		if err := cur.Err(); err != nil {
			yield(v, err)
		}
	}
}
