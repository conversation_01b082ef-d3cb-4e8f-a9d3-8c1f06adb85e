package mongodb

import "go.mongodb.org/mongo-driver/bson"

func (d Pipeline[T, V]) AddFields(fields any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$addFields": fields})
	return d
}

func (d Pipeline[T, V]) Bucket(bucket any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$bucket": bucket})
	return d
}

func (d Pipeline[T, V]) BucketAuto(bucketAuto any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$bucketAuto": bucketAuto})
	return d
}

func (d Pipeline[T, V]) ChangeStream(changeStream any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$changeStream": changeStream})
	return d
}

/*
func (d Pipeline[T, V]) ChangeStreamSplitLargeEvent(changeStreamSplitLargeEvent any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$changeStreamSplitLargeEvent": changeStreamSplitLargeEvent})
	return d
}
*/

func (d Pipeline[T, V]) CollStats(collStats any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$collStats": collStats})
	return d
}

func (d Pipeline[T, V]) Count(field string) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$count": field})
	return d
}

func (d Pipeline[T, V]) CurrentOp(currentOp any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$currentOp": currentOp})
	return d
}

func (d Pipeline[T, V]) Densify(densify any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$densify": densify})
	return d
}

// Documents

func (d Pipeline[T, V]) Facet(facets any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$facet": facets})
	return d
}

// fill

func (d Pipeline[T, V]) Group(group any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$group": group})
	return d
}

func (d Pipeline[T, V]) GeoNear(geoNear any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$geoNear": geoNear})
	return d
}

func (d Pipeline[T, V]) GraphLookup(graphLookup any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$graphLookup": graphLookup})
	return d
}

func (d Pipeline[T, V]) IndexStats() Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$indexStats": bson.M{}})
	return d
}

func (d Pipeline[T, V]) Limit(limit int) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$limit": limit})
	return d
}

// listLocalSessions is for db.aggregate() only
/*
func (d Pipeline[T, V]) ListLocalSessions() Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$listLocalSessions": bson.M{}})
	return d
}
*/

// listSessions
func (d Pipeline[T, V]) ListSessions(doc any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$listSessions": doc})
	return d
}

// listSampledQueries

// listSearchIndexes

func (d Pipeline[T, V]) Lookup(lookup any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$lookup": lookup})
	return d
}

func (d Pipeline[T, V]) Match(query any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$match": query})
	return d
}

func (d Pipeline[T, V]) Merge(merge any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$merge": merge})
	return d
}

func (d Pipeline[T, V]) Out(outCollection string) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$out": outCollection})
	return d
}

// planCacheStats

func (d Pipeline[T, V]) Project(projection any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$project": projection})
	return d
}

// queryStats

// redact

func (d Pipeline[T, V]) ReplaceRoot(newRoot any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$replaceRoot": bson.M{"newRoot": newRoot}})
	return d
}

// replaceWith

func (d Pipeline[T, V]) Sample(size int) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$sample": bson.M{"size": size}})
	return d
}

// search

// searchBeta

func (d Pipeline[T, V]) Set(fields any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$set": fields})
	return d
}

// setWindowFields

// shardedDataDistribution

func (d Pipeline[T, V]) Skip(skip int) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$skip": skip})
	return d
}

func (d Pipeline[T, V]) Sort(sort any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$sort": sort})
	return d
}

func (d Pipeline[T, V]) SortByCount(expression any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$sortByCount": expression})
	return d
}

func (d Pipeline[T, V]) UnionWith(unionWith any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$unionWith": unionWith})
	return d
}

func (d Pipeline[T, V]) Unset(fields any) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$unset": fields})
	return d
}

func (d Pipeline[T, V]) Unwind(path string) Pipeline[T, V] {
	d.pipeline = append(d.pipeline, bson.M{"$unwind": path})
	return d
}

// vectorSearch
