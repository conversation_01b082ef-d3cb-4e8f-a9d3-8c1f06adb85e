//go:build go1.23
// +build go1.23

package mongodb

import "iter"

func (c chainCtx[T]) Iter() iter.Seq2[T, error] {
	return func(yield func(T, error) bool) {
		var v T
		cur, err := c.d.cli.Database(c.d.dbName).Collection(c.d.t.CollectionName()).Find(c.ctx, c.filter, c.opt)
		if err != nil {
			yield(v, err)
			return
		}
		defer cur.Close(c.ctx)
		for cur.Next(c.ctx) {
			var v T
			if err := cur.Decode(&v); err != nil {
				yield(v, err)
				return
			}
			if !yield(v, nil) {
				return
			}
		}
		if err := cur.Err(); err != nil {
			yield(v, err)
		}
	}
}
