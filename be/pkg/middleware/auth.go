package middleware

import (
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

const (
	// UserIDKey is the key used to store the user ID in the Gin context after successful authentication.
	UserIDKey = "userID"
	// UsernameKey is the key used to store the username in the Gin context.
	UsernameKey = "username"
	// IsAdminKey is the key used to store the admin status in the Gin context.
	IsAdminKey = "isAdmin"
)

// AuthRequired is a Gin middleware that checks for a valid user session.
// It expects the userID to be stored in the session (e.g., by the login handler).
// If the session is valid, it extracts the userID and potentially other user info
// and sets them in the Gin context for downstream handlers.
// If the session is invalid or missing, it aborts the request with 401 Unauthorized.
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		userIDRaw := session.Get(UserIDKey) // Get raw value

		var userIDStr string
		var ok bool

		// Check if userID exists in the session and is of expected type (string for Hex ID)
		userIDStr, ok = userIDRaw.(string)
		if !ok || userIDStr == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: Session token invalid or missing"})
			return
		}

		// --- Session is valid ---

		// Set the user ID in the Gin context so downstream handlers can access it.
		c.Set(UserIDKey, userIDStr) // Now userIDStr is accessible

		// Optionally, set other retrieved session data in the context
		if usernameRaw := session.Get(UsernameKey); usernameRaw != nil {
			if usernameStr, ok := usernameRaw.(string); ok {
				c.Set(UsernameKey, usernameStr)
			}
		}
		if isAdminRaw := session.Get(IsAdminKey); isAdminRaw != nil {
			if isAdminBool, ok := isAdminRaw.(bool); ok {
				c.Set(IsAdminKey, isAdminBool)
			}
		}

		// Continue to the next handler
		c.Next()
	}
}

// Optional: AdminRequired middleware (builds on AuthRequired)
/*
func AdminRequired() gin.HandlerFunc {
    return func(c *gin.Context) {
        // First, run the standard auth check
        AuthRequired()(c)
        // If the request was aborted by AuthRequired, c.IsAborted() will be true
        if c.IsAborted() {
            return
        }

        // Check if the user is an admin
        isAdmin, exists := c.Get(IsAdminKey)
        if !exists || !isAdmin.(bool) {
            c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Forbidden: Administrator privileges required"})
            return
        }

        // User is authenticated and is an admin
        c.Next()
    }
}
*/
