package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AdminRequired is a Gin middleware that checks if the authenticated user has administrative privileges.
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := c.Must<PERSON>et("logger").(*zap.SugaredLogger) // Assume logger is set by a preceding middleware

		userIDAny, exists := c.Get(UserIDKey)
		if !exists {
			logger.Warn("AdminRequired: User ID not found in context, authentication middleware likely missing or failed.")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		isAdminAny, exists := c.Get(IsAdminKey) // Assume IsAdmin is set by a preceding authentication middleware
		if !exists {
			logger.Warn("AdminRequired: IsAdmin status not found in context for user ID", zap.Any("userID", userIDAny))
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Access denied: Admin status unknown"})
			return
		}

		isAdmin, ok := isAdminAny.(bool)
		if !ok {
			logger.Error("AdminRequired: IsAdmin value in context is not a boolean", zap.Any("isAdminValue", isAdminAny))
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Access denied: Invalid admin status"})
			return
		}

		if !isAdmin {
			logger.Warn("AdminRequired: Access denied for non-admin user", zap.Any("userID", userIDAny))
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Access denied: Administrator privileges required"})
			return
		}

		c.Next() // User is an admin, proceed to the next handler
	}
}

// UserIDKey and IsAdminKey are context keys used to store user ID and admin status.
// These should be set by the authentication middleware.
