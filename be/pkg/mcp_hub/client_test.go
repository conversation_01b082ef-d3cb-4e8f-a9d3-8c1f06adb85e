package mcp_hub

import (
	"context"
	"encoding/json"
	"log"
	"testing"

	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/prompts"
)

var (
	mcpServersConfig *MCPServersConfig
)

func init() {
	var err error
	mcpServersConfig, err = LoadConfigFromFile("../../config/mcp_servers.json")
	if err != nil {
		log.Fatalf("Failed to load MCP server configuration: %v", err)
	}
	log.Printf("Loaded configuration for %d MCP server(s)", len(mcpServersConfig.MCPServers))
}

func newClientManager() (*ClientManager, error) {
	logger, err := zap.NewDevelopment()
	if err != nil {
		log.Fatalf("can't initialize zap logger: %v", err)
	}
	return NewClientManager(mcpServersConfig, logger)
}

// TestGetServerInfos tests the GetServerInfos method of the ClientManager.
func TestGetServerInfos(t *testing.T) {
	mcManager, err := newClientManager()
	if err != nil {
		log.Fatalf("Failed to initialize MCP Client Manager (some clients might have failed): %v", err)
	}

	serverInfos := mcManager.GetServerInfos()
	str, _ := json.Marshal(serverInfos)
	log.Printf("Server infos: %s", string(str))
}

func TestListMCPServers(t *testing.T) {
	mcManager, err := newClientManager()
	if err != nil {
		log.Fatalf("Failed to initialize MCP Client Manager (some clients might have failed): %v", err)
	}

	toolRes := mcManager.ListMCPServers()
	str, _ := json.Marshal(toolRes)
	log.Printf("Tool res: %s", string(str))

	promptData := prompts.PromptDynamicData{
		MCPServers: prompts.MCPInfo{
			Servers: toolRes,
		},
	}
	prompt, err := prompts.GenerateSystemPrompt(promptData)
	if err != nil {
		log.Fatalf("Failed to generate system prompt: %v", err)
	}
	log.Printf("System prompt: %s", prompt)
}

func TestRouteCallTool(t *testing.T) {
	t.Skip("Skipping this test as it's an integration test requiring a live server and is prone to race conditions.")

	mcManager, err := newClientManager()
	if err != nil {
		log.Fatalf("Failed to initialize MCP Client Manager (some clients might have failed): %v", err)
	}

	serviceName := "filesystem"
	toolName := "list_directory"
	toolParams := map[string]interface{}{
		"path": "/Users/<USER>",
	}

	result, err := mcManager.RouteCallTool(context.Background(), serviceName, toolName, toolParams)
	if err != nil {
		log.Fatalf("Failed to route call tool: %v", err)
	}
	log.Printf("Result: \n%s\n", result)
}
