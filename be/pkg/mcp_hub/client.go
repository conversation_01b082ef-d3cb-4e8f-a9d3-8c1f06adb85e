package mcp_hub

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync" // Needed for running discovery concurrently
	"time" // Needed for context timeout

	"github.com/google/uuid"
	mcpClient "github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"go.uber.org/zap"

	domainAgent "git.nevint.com/fota3/t-rex/domain/agent"
	"git.nevint.com/fota3/t-rex/model/agent"
)

// ServerInfo holds discovered information about a connected MCP server.
type ServerInfo struct {
	Name             string                // Logical name from config
	InitializeResult *mcp.InitializeResult // Result from Initialize call
	Tools            []mcp.Tool            // Use []mcp.Tool based on library examples
	FullCommand      string
	// Add Resources and Prompts fields if needed later
}

// ClientManager manages connections to multiple MCP servers and their discovered info.
type ClientManager struct {
	logger        *zap.Logger
	clients       map[string]mcpClient.MCPClient // Map of serverName -> connected client instance
	serverInfos   map[string]*ServerInfo         // Map of serverName -> discovered server info
	mu            sync.RWMutex                   // Protects maps during concurrent initialization
	serversConfig *MCPServersConfig              // Keep the config for polling
	ctx           context.Context                // Context to manage lifecycle of polling goroutines
	cancel        context.CancelFunc             // Function to cancel the context
}

// NewClientManager creates a manager and starts background routines to connect to
// and initialize all enabled MCP servers, and discover their capabilities (tools).
// It returns immediately without waiting for connections to be established.
func NewClientManager(serversConfig *MCPServersConfig, logger *zap.Logger) (*ClientManager, error) {
	if serversConfig == nil || serversConfig.MCPServers == nil {
		return nil, fmt.Errorf("MCP servers configuration is required")
	}
	if logger == nil {
		logger = zap.NewNop()
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &ClientManager{
		logger:        logger.Named("MCPClientManager"),
		clients:       make(map[string]mcpClient.MCPClient),
		serverInfos:   make(map[string]*ServerInfo),
		serversConfig: serversConfig,
		ctx:           ctx,
		cancel:        cancel,
	}

	manager.logger.Info("Initializing MCP Client Manager and starting connection polls...", zap.Int("numServers", len(serversConfig.MCPServers)))

	// Start background polling for each server
	for name, config := range serversConfig.MCPServers {
		if config.Disabled {
			manager.logger.Info("Skipping disabled MCP server", zap.String("serverName", name))
			continue
		}

		go manager.manageServerConnection(name, config)
	}

	manager.logger.Info("MCP Client Manager initialized and polling started.")
	return manager, nil
}

const pollingInterval = 30 * time.Second

// manageServerConnection runs a loop to keep a persistent connection to a single MCP server.
// It will attempt to connect, and if it fails, it will poll periodically.
// This method is designed to be run in a goroutine.
func (m *ClientManager) manageServerConnection(serverName string, configEntry MCPServerConfigEntry) {
	m.logger.Info("Starting connection manager for server", zap.String("serverName", serverName))
	ticker := time.NewTicker(pollingInterval)
	defer ticker.Stop()

	for {
		// Check if client is already connected before attempting to connect
		m.mu.RLock()
		_, isConnected := m.clients[serverName]
		m.mu.RUnlock()

		if !isConnected {
			m.logger.Info("Attempting to connect to MCP server", zap.String("serverName", serverName))
			clientInstance, serverInfo := m.initializeSingleClient(serverName, configEntry)
			if clientInstance != nil && serverInfo != nil {
				m.mu.Lock()
				m.clients[serverName] = clientInstance
				m.serverInfos[serverName] = serverInfo
				m.mu.Unlock()
				m.logger.Info("Successfully connected and discovered MCP client", zap.String("serverName", serverName), zap.Int("discoveredTools", len(serverInfo.Tools)))
			} else {
				m.logger.Warn("Failed to initialize MCP client, will retry...", zap.String("serverName", serverName))
			}
		}

		// Wait for the next tick or until the manager is closed
		select {
		case <-ticker.C:
			// Continue to next iteration
		case <-m.ctx.Done():
			m.logger.Info("Stopping connection manager for server due to shutdown signal.", zap.String("serverName", serverName))
			// Ensure client is closed on shutdown
			m.mu.RLock()
			client, exists := m.clients[serverName]
			m.mu.RUnlock()
			if exists {
				_ = client.Close()
			}
			return
		}
	}
}

// initializeSingleClient handles connection, initialization, and tool discovery for one server.
// Returns the client instance and server info on success, otherwise nil.
func (m *ClientManager) initializeSingleClient(serverName string, configEntry MCPServerConfigEntry) (mcpClient.MCPClient, *ServerInfo) {
	// This function uses the long-lived manager context (m.ctx) for the connection itself (for SSE).
	// Short-lived contexts are created for specific RPC calls to enforce timeouts.

	var clientInstance mcpClient.MCPClient
	var err error

	// 1. Establish Connection
	if configEntry.URL != "" {
		// SSE Client
		m.logger.Debug("Connecting to SSE MCP server", zap.String("serverName", serverName), zap.String("url", configEntry.URL))
		sseClient, startErr := mcpClient.NewSSEMCPClient(configEntry.URL)
		if startErr != nil {
			m.logger.Error("Failed to create SSE client instance", zap.String("serverName", serverName), zap.Error(startErr))
			return nil, nil
		}
		// Use the long-lived manager context to start the client, so the connection persists.
		startErr = sseClient.Start(m.ctx)
		if startErr != nil {
			m.logger.Error("Failed to start/connect SSE client", zap.String("serverName", serverName), zap.Error(startErr))
			_ = sseClient.Close()
			return nil, nil
		}
		clientInstance = sseClient
	} else if configEntry.Command != "" {
		// Stdio Client - its lifecycle is tied to the process, not a context in the same way.
		m.logger.Debug("Starting Stdio MCP server process", zap.String("serverName", serverName), zap.String("command", configEntry.Command))
		env := os.Environ()
		stdioClient, startErr := mcpClient.NewStdioMCPClient(configEntry.Command, env, configEntry.Args...)
		if startErr != nil {
			m.logger.Error("Failed to start Stdio client process", zap.String("serverName", serverName), zap.Error(startErr))
			return nil, nil
		}
		clientInstance = stdioClient
	} else {
		m.logger.Warn("Skipping MCP server entry with no URL or Command", zap.String("serverName", serverName))
		return nil, nil
	}

	// Create a short-lived context for the initialization RPC calls.
	reqCtx, cancel := context.WithTimeout(m.ctx, 15*time.Second)
	defer cancel()

	// 2. Initialize Call
	m.logger.Debug("Sending Initialize request", zap.String("serverName", serverName))
	// Construct InitializeRequest correctly based on definition
	initReq := mcp.InitializeRequest{
		// Request: // Embedded fields likely handled by the library
		Params: InitializeParams{
			ProtocolVersion: "", // Use the constant from the library
			ClientInfo: mcp.Implementation{
				Name:    "T-Rex Backend Client",
				Version: "1.0.0", // TODO: Get version dynamically?
			},
			Capabilities: mcp.ClientCapabilities{
				// Define client capabilities if needed, e.g., CompletionProvider: true
			},
		},
	}
	initRes, err := clientInstance.Initialize(reqCtx, initReq)
	if err != nil {
		m.logger.Error("MCP Initialize call failed", zap.String("serverName", serverName), zap.Error(err))
		_ = clientInstance.Close()
		return nil, nil
	}
	if initRes == nil {
		m.logger.Error("MCP Initialize call returned nil result", zap.String("serverName", serverName))
		_ = clientInstance.Close()
		return nil, nil
	}
	m.logger.Debug("Initialize successful", zap.String("serverName", serverName), zap.Any("serverInfo", initRes.ServerInfo), zap.Any("capabilities", initRes.Capabilities))

	// 3. Discover Tools (if supported)
	serverInfo := &ServerInfo{
		Name:             serverName,
		InitializeResult: initRes,
		Tools:            make([]mcp.Tool, 0), // Use []mcp.Tool
		FullCommand:      fmt.Sprintf("%s %s", configEntry.Command, strings.Join(configEntry.Args, " ")),
	}

	// Check for tool capability - **ASSUMPTION:** Field name might be different!
	// Check the actual definition of mcp.ServerCapabilities if this fails.
	if initRes.Capabilities.Tools != nil { // Tentative check
		m.logger.Debug("Server supports tools, listing tools...", zap.String("serverName", serverName))
		listToolsReq := mcp.ListToolsRequest{
			// Request: // Embedded fields likely handled by the library
			// Params can be empty
		}
		listToolsRes, err := clientInstance.ListTools(reqCtx, listToolsReq)
		if err != nil {
			m.logger.Error("MCP ListTools call failed", zap.String("serverName", serverName), zap.Error(err))
		} else if listToolsRes != nil {
			// Assuming ListToolsResult.Tools is of type []mcp.Tool
			serverInfo.Tools = listToolsRes.Tools
			m.logger.Debug("Discovered tools", zap.String("serverName", serverName), zap.Int("count", len(serverInfo.Tools)))
			// Add Debug log for every tool's name and description
			for _, tool := range serverInfo.Tools {
				m.logger.Debug("Tool", zap.String("serverName", serverName), zap.String("toolName", tool.Name), zap.String("toolDescription", tool.Description))
			}
		}
	} else {
		m.logger.Debug("Server does not support tools", zap.String("serverName", serverName))
	}

	// TODO: Add ListResources, ListPrompts discovery if needed based on capabilities

	return clientInstance, serverInfo
}

// GetServerInfos returns the discovered information for all successfully initialized servers.
func (m *ClientManager) GetServerInfos() map[string]*ServerInfo {
	m.mu.RLock() // Use read lock for accessing the map
	defer m.mu.RUnlock()

	// Return a copy to prevent modification? Or assume read-only usage.
	// For now, returning direct map.
	infos := make(map[string]*ServerInfo, len(m.serverInfos))
	for k, v := range m.serverInfos {
		infos[k] = v // Shallow copy is okay here as ServerInfo contains pointers/slices
	}
	return infos
}

func (m *ClientManager) ListMCPServers() map[string][]mcp.Tool {
	m.mu.RLock() // Use read lock for accessing the map
	defer m.mu.RUnlock()

	toolRes := make(map[string][]mcp.Tool, len(m.serverInfos))
	for k, v := range m.serverInfos {
		toolRes[k] = v.Tools
	}
	return toolRes
}

// RouteCallTool finds the appropriate client and calls its CallTool method.
// If the client connection is stale, it attempts to transparently reconnect.
func (m *ClientManager) RouteCallTool(ctx context.Context, targetServerName string, toolName string, params map[string]interface{}) (string, error) {
	m.logger.Debug("Routing tool call to server", zap.String("targetServerName", targetServerName), zap.String("toolName", toolName), zap.Any("params", params))

	// Step 1: Optimistic check with a read lock
	m.mu.RLock()
	clientInstance, found := m.clients[targetServerName]
	m.mu.RUnlock()

	if !found {
		m.logger.Warn("MCP client not found for target server. It might be offline or still connecting.", zap.String("targetServer", targetServerName))
		return "", fmt.Errorf("no active MCP client found for server: %s", targetServerName)
	}

	// Step 2: Ping the client to check if the connection is alive
	if err := clientInstance.Ping(ctx); err == nil {
		m.logger.Debug("MCP client ping successful. Proceeding with existing connection.", zap.String("serverName", targetServerName))
		return m.executeToolCallWithClient(ctx, clientInstance, targetServerName, toolName, params)
	}

	// Step 3: Ping failed. The connection is stale. Acquire a write lock to handle re-initialization.
	m.logger.Warn("MCP client ping failed, attempting to reconnect...", zap.String("serverName", targetServerName))
	m.mu.Lock()
	defer m.mu.Unlock()

	// Step 4: Double-check the client's state now that we have a write lock.
	// It's possible another goroutine reconnected it while we were waiting for the lock.
	clientInstance, found = m.clients[targetServerName]
	if found {
		if err := clientInstance.Ping(ctx); err == nil {
			// It was reconnected by another process. We can use this healthy client.
			m.logger.Info("MCP client was reconnected by another process. Using the new client.", zap.String("serverName", targetServerName))
			return m.executeToolCallWithClient(ctx, clientInstance, targetServerName, toolName, params)
		}
		// The client in the map is also stale. Close it before replacing.
		m.logger.Debug("Closing stale client before re-initialization.", zap.String("serverName", targetServerName))
		_ = clientInstance.Close()
	}

	// Step 5: The client is definitely stale or gone. Perform inline re-initialization.
	configEntry, configFound := m.serversConfig.MCPServers[targetServerName]
	if !configFound {
		m.logger.Error("Configuration for MCP server not found during reconnect attempt.", zap.String("serverName", targetServerName))
		return "", fmt.Errorf("internal error: configuration for server %s disappeared", targetServerName)
	}

	m.logger.Info("Attempting inline re-initialization of MCP client.", zap.String("serverName", targetServerName))
	newClient, newServerInfo := m.initializeSingleClient(targetServerName, configEntry)

	if newClient == nil {
		m.logger.Error("Inline re-initialization of MCP client failed.", zap.String("serverName", targetServerName))
		// Ensure the stale client is removed from the maps so the poller tries again later.
		delete(m.clients, targetServerName)
		delete(m.serverInfos, targetServerName)
		return "", fmt.Errorf("failed to reconnect to MCP server: %s", targetServerName)
	}

	m.logger.Info("Inline re-initialization successful. Updating client instance.", zap.String("serverName", targetServerName))
	m.clients[targetServerName] = newClient
	m.serverInfos[targetServerName] = newServerInfo

	// Step 6: Execute the tool call with the newly created client.
	return m.executeToolCallWithClient(ctx, newClient, targetServerName, toolName, params)
}

// executeToolCallWithClient handles the final part of sending a tool call request
// to a client that is assumed to be healthy.
func (m *ClientManager) executeToolCallWithClient(ctx context.Context, clientInstance mcpClient.MCPClient, targetServerName string, toolName string, params map[string]interface{}) (string, error) {
	// Construct the request correctly
	toolUseID := uuid.NewString()
	request := mcp.CallToolRequest{
		// Request struct is embedded, typically no need to set fields directly unless overriding defaults
		Params: CallToolParams{
			Name:      toolName, // Correct field for tool name
			Arguments: params,   // Correct field for arguments
		},
		Request: mcp.Request{
			Method: string(mcp.MethodToolsCall),
		},
	}

	// Call the specific client's CallTool method
	m.logger.Debug("Sending MCP CallToolRequest",
		zap.String("targetServer", targetServerName),
		zap.String("toolUseID", toolUseID),
		zap.Any("request", request),
	)
	result, err := clientInstance.CallTool(ctx, request)
	if err != nil {
		// Communication or client-side error
		m.logger.Error("MCP client communication error during CallTool",
			zap.String("targetServer", targetServerName),
			zap.String("toolName", toolName),
			zap.String("toolUseID", toolUseID),
			zap.Error(err),
		)
		return "", fmt.Errorf("mcp client error for server '%s', tool '%s': %w", targetServerName, toolName, err)
	}
	m.logger.Debug("MCP CallToolResult", zap.String("targetServer", targetServerName), zap.Any("result", result))

	// Parse the result
	return parseCallToolResult(result, toolName, toolUseID, m.logger)
}

// CloseAll closes connections for all managed MCP clients.
func (m *ClientManager) CloseAll() error {
	m.logger.Info("Closing all MCP clients and stopping polling...")
	m.cancel() // Signal all polling goroutines to stop

	m.mu.Lock()
	defer m.mu.Unlock()

	var firstErr error
	for name, client := range m.clients {
		m.logger.Debug("Closing client connection", zap.String("serverName", name))
		if err := client.Close(); err != nil {
			m.logger.Error("Error closing MCP client", zap.String("serverName", name), zap.Error(err))
			if firstErr == nil {
				firstErr = err
			}
		}
	}
	// Clear the maps after closing
	m.clients = make(map[string]mcpClient.MCPClient)
	m.serverInfos = make(map[string]*ServerInfo)

	m.logger.Info("All MCP clients closed.")
	return firstErr
}

// parseCallToolResult processes the mcp.CallToolResult.
func parseCallToolResult(result *mcp.CallToolResult, toolName, toolUseID string, logger *zap.Logger) (string, error) {
	// 检查结果是否有任何内容
	if result == nil || len(result.Content) == 0 {
		logger.Debug("MCP tool call returned empty result", zap.String("toolName", toolName), zap.String("toolUseID", toolUseID))
		return "", nil
	}

	// 查找第一个TextContent类型的内容
	var textContent *mcp.TextContent
	for _, content := range result.Content {
		if tc, ok := content.(mcp.TextContent); ok && tc.Type == "text" {
			textContent = &tc
			break
		}
	}

	return textContent.Text, nil
}

// marshalResultToBytes tries to convert common result types to []byte.
func marshalResultToBytes(rawResult interface{}) ([]byte, error) {
	// ... (Implementation as previously applied) ...
	switch v := rawResult.(type) {
	case json.RawMessage:
		return v, nil
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	default:
		if v == nil { // Explicitly handle nil interface value
			return nil, nil // Return nil bytes, nil error for nil input
		}
		return nil, fmt.Errorf("unhandled result type: %T", v)
	}
}

// 实现 domain/agent/interfaces.go 的 MCPToolExecutor 接口
type MCPToolExecutorImpl struct {
	clientManager *ClientManager
}

// 构造函数
func NewMCPToolExecutorImpl(cm *ClientManager) *MCPToolExecutorImpl {
	return &MCPToolExecutorImpl{clientManager: cm}
}

// ExecuteToolCall 实现
func (e *MCPToolExecutorImpl) ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error) {
	if toolCall == nil {
		return nil, ErrNilToolCall
	}
	server := toolCall.MCPService
	method := toolCall.MCPMethod
	params, ok := toolCall.MCPParams.(map[string]interface{})
	if !ok {
		return nil, ErrInvalidToolParams
	}
	if server == "" || method == "" {
		return nil, ErrInvalidToolCall
	}
	result, err := e.clientManager.RouteCallTool(ctx, server, method, params)
	if err != nil {
		return nil, err
	}
	return &agent.ToolResult{
		CallID:  toolCall.CallID,
		Content: result,
	}, nil
}

func (e *MCPToolExecutorImpl) ListMCPServers(ctx context.Context) map[string][]mcp.Tool {
	return e.clientManager.ListMCPServers()
}

var _ domainAgent.MCPToolExecutor = (*MCPToolExecutorImpl)(nil)

// 错误定义
var (
	ErrNilToolCall       = fmt.Errorf("toolCall is nil")
	ErrInvalidToolParams = fmt.Errorf("toolCall.MCPParams is not map[string]interface{}")
	ErrInvalidToolCall   = fmt.Errorf("toolCall.MCPService or MCPMethod is empty")
)
