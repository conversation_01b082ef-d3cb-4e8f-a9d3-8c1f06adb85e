package mcp_hub

import (
	"encoding/json"
	"fmt"
	"os"
)

// MCPServerConfigEntry represents a single MCP server configuration entry
// matching the structure used by tools like Cursor.
type MCPServerConfigEntry struct {
	URL         string            `json:"url,omitempty"` // SSE URL if applicable
	Command     string            `json:"command,omitempty"`
	Args        []string          `json:"args,omitempty"`
	Disabled    bool              `json:"disabled,omitempty"`
	AutoApprove []string          `json:"autoApprove,omitempty"`
	Env         map[string]string `json:"env,omitempty"` // Added Env based on common patterns
	// Add other fields if needed based on the exact config format
}

// MCPServersConfig represents the top-level MCP server configuration file structure.
type MCPServersConfig struct {
	MCPServers map[string]MCPServerConfigEntry `json:"mcpServers"`
}

// LoadConfigFromFile reads an MCP server configuration JSON file from the specified path
// and unmarshals it into the MCPServersConfig struct.
func LoadConfigFromFile(filePath string) (*MCPServersConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read MCP config file '%s': %w", filePath, err)
	}

	var config MCPServersConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal MCP config file '%s': %w", filePath, err)
	}

	if config.MCPServers == nil {
		// Initialize the map if the json file was empty or only contained an empty object
		config.MCPServers = make(map[string]MCPServerConfigEntry)
	}

	return &config, nil
}
