package mcp_hub

import "github.com/mark3labs/mcp-go/mcp"

// InitializeParams is the parameters for the Initialize request.
type InitializeParams struct {
	ProtocolVersion string                 `json:"protocolVersion"`
	Capabilities    mcp.ClientCapabilities `json:"capabilities"`
	ClientInfo      mcp.Implementation     `json:"clientInfo"`
}

// CallToolParams is the parameters for the CallTool request.
type CallToolParams struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments,omitempty"`
	Meta      *struct {
		ProgressToken mcp.ProgressToken `json:"progressToken,omitempty"`
	} `json:"_meta,omitempty"`
}
