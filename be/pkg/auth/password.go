package auth

import (
	"fmt"

	domain "git.nevint.com/fota3/t-rex/domain/user" // Import domain interface
	"golang.org/x/crypto/bcrypt"
)

// BcryptHasher implements the domain.user.PasswordHasher interface using bcrypt.
type BcryptHasher struct{}

// NewBcryptHasher creates a new BcryptHasher instance.
func NewBcryptHasher() *BcryptHasher {
	return &BcryptHasher{}
}

// Compile-time check to ensure BcryptHasher implements the interface.
var _ domain.PasswordHasher = (*BcryptHasher)(nil)

// Hash generates a bcrypt hash for the given password.
func (h *BcryptHasher) Hash(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(bytes), nil
}

// Check compares a plain text password with a stored bcrypt hash.
func (h *BcryptHasher) Check(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil // If err is nil, the password matches
}

// --- Standalone functions (can be kept for direct use if needed, or removed) ---

// HashPassword generates a bcrypt hash for the given password.
// It uses the default cost provided by the bcrypt package.
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(bytes), nil
}

// CheckPasswordHash compares a plain text password with a stored bcrypt hash.
// Returns true if the password matches the hash, false otherwise.
func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil // If err is nil, the password matches
}
