package fileupload

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
)

// Service handles file upload operations
type Service struct {
	uploadDir string
	maxSize   int64
	logger    *zap.Logger
}

// NewService creates a new file upload service
func NewService(uploadDir string, maxSize int64, logger *zap.Logger) (*Service, error) {
	if uploadDir == "" {
		return nil, fmt.Errorf("upload directory is required")
	}
	if maxSize <= 0 {
		maxSize = 10 * 1024 * 1024 // Default 10MB
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	// Create upload directory if it doesn't exist
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %w", err)
	}

	return &Service{
		uploadDir: uploadDir,
		maxSize:   maxSize,
		logger:    logger.Named("file_upload"),
	}, nil
}

// UploadedFile represents the result of a successful file upload
type UploadedFile struct {
	ID       string `json:"id"`
	FileName string `json:"file_name"`
	FilePath string `json:"file_path"`
	FileSize int64  `json:"file_size"`
	MimeType string `json:"mime_type"`
}

// SupportedImageTypes defines the allowed image MIME types
var SupportedImageTypes = map[string]bool{
	"image/jpeg": true,
	"image/jpg":  true,
	"image/png":  true,
	"image/gif":  true,
	"image/webp": true,
}

// UploadImage handles image file upload with validation
func (s *Service) UploadImage(file *multipart.FileHeader) (*UploadedFile, error) {
	s.logger.Info("Starting image upload",
		zap.String("filename", file.Filename),
		zap.Int64("size", file.Size))

	// Validate file size
	if file.Size > s.maxSize {
		s.logger.Warn("File size exceeds limit",
			zap.String("filename", file.Filename),
			zap.Int64("size", file.Size),
			zap.Int64("max_size", s.maxSize))
		return nil, fmt.Errorf("file size %d bytes exceeds limit of %d bytes", file.Size, s.maxSize)
	}

	// Open the uploaded file
	src, err := file.Open()
	if err != nil {
		s.logger.Error("Failed to open uploaded file",
			zap.String("filename", file.Filename),
			zap.Error(err))
		return nil, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	// Detect MIME type
	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read file for MIME detection: %w", err)
	}
	
	// Reset file pointer
	src.Seek(0, 0)
	
	// Simple MIME type detection based on file extension
	ext := strings.ToLower(filepath.Ext(file.Filename))
	var mimeType string
	switch ext {
	case ".jpg", ".jpeg":
		mimeType = "image/jpeg"
	case ".png":
		mimeType = "image/png"
	case ".gif":
		mimeType = "image/gif"
	case ".webp":
		mimeType = "image/webp"
	default:
		return nil, fmt.Errorf("unsupported image format: %s", ext)
	}

	// Validate MIME type
	if !SupportedImageTypes[mimeType] {
		s.logger.Warn("Unsupported image type",
			zap.String("filename", file.Filename),
			zap.String("mime_type", mimeType))
		return nil, fmt.Errorf("unsupported image type: %s", mimeType)
	}

	// Generate unique filename
	fileID, err := generateFileID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate file ID: %w", err)
	}

	// Create subdirectory based on current date
	dateDir := time.Now().Format("2006/01/02")
	fullUploadDir := filepath.Join(s.uploadDir, dateDir)
	if err := os.MkdirAll(fullUploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create date directory: %w", err)
	}

	// Create destination file path
	filename := fmt.Sprintf("%s%s", fileID, ext)
	filePath := filepath.Join(fullUploadDir, filename)
	relativePath := filepath.Join(dateDir, filename)

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		s.logger.Error("Failed to create destination file",
			zap.String("filepath", filePath),
			zap.Error(err))
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	// Copy file content
	size, err := io.Copy(dst, src)
	if err != nil {
		s.logger.Error("Failed to copy file content",
			zap.String("filepath", filePath),
			zap.Error(err))
		// Clean up partial file
		os.Remove(filePath)
		return nil, fmt.Errorf("failed to copy file content: %w", err)
	}

	s.logger.Info("Image upload completed successfully",
		zap.String("file_id", fileID),
		zap.String("filename", file.Filename),
		zap.String("filepath", relativePath),
		zap.Int64("size", size),
		zap.String("mime_type", mimeType))

	return &UploadedFile{
		ID:       fileID,
		FileName: file.Filename,
		FilePath: relativePath,
		FileSize: size,
		MimeType: mimeType,
	}, nil
}

// generateFileID creates a unique identifier for uploaded files
func generateFileID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// DeleteFile removes an uploaded file from disk
func (s *Service) DeleteFile(relativePath string) error {
	if relativePath == "" {
		return fmt.Errorf("file path cannot be empty")
	}

	fullPath := filepath.Join(s.uploadDir, relativePath)
	
	// Security check: ensure the path is within upload directory
	if !strings.HasPrefix(fullPath, s.uploadDir) {
		s.logger.Warn("Attempted to delete file outside upload directory",
			zap.String("relative_path", relativePath),
			zap.String("full_path", fullPath))
		return fmt.Errorf("invalid file path")
	}

	if err := os.Remove(fullPath); err != nil {
		if os.IsNotExist(err) {
			s.logger.Debug("File already deleted or does not exist",
				zap.String("filepath", fullPath))
			return nil // Consider this success
		}
		s.logger.Error("Failed to delete file",
			zap.String("filepath", fullPath),
			zap.Error(err))
		return fmt.Errorf("failed to delete file: %w", err)
	}

	s.logger.Info("File deleted successfully",
		zap.String("filepath", relativePath))
	return nil
} 