package factory

import (
	"context"
	"fmt"
	"net/http"
	"sync"

	"go.uber.org/zap"

	agentDomain "git.nevint.com/fota3/t-rex/domain/agent"
	aimodelDomain "git.nevint.com/fota3/t-rex/domain/aimodel"
	"git.nevint.com/fota3/t-rex/model/aimodel"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"
	providerLegacy "git.nevint.com/fota3/t-rex/pkg/llm/provider_legacy"
)

// LLMClientFactory creates LLM clients based on AI model configurations
type LLMClientFactory struct {
	aiModelDomain *aimodelDomain.Domain
	httpClient    *http.Client
	logger        *zap.SugaredLogger
	clientCache   map[string]*AIModelLLMClientAdapter
	cacheMutex    sync.RWMutex
}

// AIModelLLMClientAdapter adapts the legacy OpenAI client to work with the standard LLMClient interface
type AIModelLLMClientAdapter struct {
	client  *providerLegacy.OpenAICompatibleClient
	aiModel aimodel.AIModel
}

// ChatCompletionStream implements the domain.LLMClient interface using AIModel
// The LLMConfig parameter is ignored since this adapter uses AIModel configuration
func (a *AIModelLLMClientAdapter) ChatCompletionStream(ctx context.Context, _ modelLLM.LLMConfig, req *agentDomain.ChatCompletionRequest) (<-chan agentDomain.ChatCompletionStreamResponse, error) {
	return a.client.ChatCompletionStreamWithAIModel(ctx, a.aiModel, req)
}

// ChatCompletionNonStreaming implements the domain.LLMClient interface using AIModel
func (a *AIModelLLMClientAdapter) ChatCompletionNonStreaming(ctx context.Context, req *agentDomain.ChatCompletionRequest) (*agentDomain.ChatCompletionResponse, error) {
	return a.client.ChatCompletionNonStreamingWithAIModel(ctx, a.aiModel, req)
}

// Compile-time check to ensure AIModelLLMClientAdapter implements the interface.
var _ agentDomain.LLMClient = (*AIModelLLMClientAdapter)(nil)

// NewLLMClientFactory creates a new LLM client factory
func NewLLMClientFactory(aiModelDomain *aimodelDomain.Domain, httpClient *http.Client, logger *zap.Logger) *LLMClientFactory {
	return &LLMClientFactory{
		aiModelDomain: aiModelDomain,
		httpClient:    httpClient,
		logger:        logger.Sugar(),
		clientCache:   make(map[string]*AIModelLLMClientAdapter),
	}
}

// GetLLMClient returns an LLM client for the specified model name
// It caches clients to avoid recreating them for the same model
func (f *LLMClientFactory) GetLLMClient(ctx context.Context, modelName string) (agentDomain.LLMClient, error) {
	if modelName == "" {
		return nil, fmt.Errorf("model name cannot be empty")
	}

	// Check cache first
	f.cacheMutex.RLock()
	if client, exists := f.clientCache[modelName]; exists {
		f.cacheMutex.RUnlock()
		return client, nil
	}
	f.cacheMutex.RUnlock()

	// Get AI model configuration from database
	aiModel, err := f.aiModelDomain.GetAIModelByName(ctx, modelName)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI model configuration for '%s': %w", modelName, err)
	}

	if !aiModel.IsActive {
		return nil, fmt.Errorf("AI model '%s' is not active", modelName)
	}

	// Create LLM client configuration
	llmConfig := providerLegacy.OpenAICompatibleConfig{
		APIKey:     aiModel.APIKey,
		BaseURL:    aiModel.APIBase,
		HTTPClient: f.httpClient,
	}

	// Create LLM client
	client, err := providerLegacy.NewOpenAICompatibleClient(llmConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client for model '%s': %w", modelName, err)
	}

	// Create adapter
	adapter := &AIModelLLMClientAdapter{
		client:  client,
		aiModel: *aiModel,
	}

	// Cache the adapter
	f.cacheMutex.Lock()
	f.clientCache[modelName] = adapter
	f.cacheMutex.Unlock()

	f.logger.Infof("Created and cached LLM client for model: %s", modelName)
	return adapter, nil
}

// ClearCache clears the client cache (useful when AI model configurations change)
func (f *LLMClientFactory) ClearCache() {
	f.cacheMutex.Lock()
	defer f.cacheMutex.Unlock()

	f.clientCache = make(map[string]*AIModelLLMClientAdapter)
	f.logger.Info("LLM client cache cleared")
}

// ClearCacheForModel clears the cache for a specific model
func (f *LLMClientFactory) ClearCacheForModel(modelName string) {
	f.cacheMutex.Lock()
	defer f.cacheMutex.Unlock()

	delete(f.clientCache, modelName)
	f.logger.Infof("LLM client cache cleared for model: %s", modelName)
}
