package provider

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time" // New import for http.Client timeout

	openai "github.com/sashabaranov/go-openai"

	domain "git.nevint.com/fota3/t-rex/domain/agent"
	model "git.nevint.com/fota3/t-rex/model/agent"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm" // New import for LLMConfig
)

// OpenAICompatibleClient implements the domain.LLMClient interface using the sashabaranov/go-openai library
// for OpenAI compatible APIs.
type OpenAICompatibleClient struct {
	httpClient *http.Client // Keep an optional shared http client
}

// OpenAICompatibleConfig holds configuration for the OpenAI compatible client.
// It primarily defines how the client is *initialized*, not its per-call config.
type OpenAICompatibleConfig struct {
	OrgID      string       // Optional: for general client config
	HTTPClient *http.Client // Optional: can be shared across client instances
}

// NewOpenAICompatibleClient creates a new client instance for OpenAI compatible APIs.
// This client no longer stores APIKey/BaseURL directly, as they come per-call from llmConfig.
func NewOpenAICompatibleClient(cfg OpenAICompatibleConfig) (*OpenAICompatibleClient, error) {
	// No APIKey check here, as it's passed dynamically per-call.
	// If cfg.HTTPClient is nil, a default http client will be used when constructing openai.Client.
	return &OpenAICompatibleClient{
		httpClient: cfg.HTTPClient,
	}, nil
}

// Compile-time check to ensure OpenAICompatibleClient implements the interface.
var _ domain.LLMClient = (*OpenAICompatibleClient)(nil)

// ChatCompletionStream implements the domain.LLMClient interface.
// It now takes a modelLLM.LLMConfig to dynamically configure the LLM call.
func (c *OpenAICompatibleClient) ChatCompletionStream(ctx context.Context, llmConfig modelLLM.LLMConfig, req *domain.ChatCompletionRequest) (<-chan domain.ChatCompletionStreamResponse, error) {
	// Dynamically create openai.Client based on the provided llmConfig
	if llmConfig.APIKey == "" {
		return nil, errors.New("LLM config API key is required for ChatCompletionStream")
	}

	openAIConfig := openai.DefaultConfig(llmConfig.APIKey)
	if llmConfig.APIBase != "" {
		openAIConfig.BaseURL = llmConfig.APIBase
	}
	// Reuse the http client if available
	if c.httpClient != nil {
		openAIConfig.HTTPClient = c.httpClient
	} else {
		// If no shared client, use a default with timeout
		openAIConfig.HTTPClient = &http.Client{Timeout: 30 * time.Minute}
	}

	dynamicClient := openai.NewClientWithConfig(openAIConfig)

	// Map our domain request to openai compatible request format
	openaiReq, err := mapToOpenAICompatRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to map domain request to openai compatible request: %w", err)
	}

	stream, err := dynamicClient.CreateChatCompletionStream(ctx, openaiReq)
	if err != nil {
		var apiErr *openai.APIError
		if errors.As(err, &apiErr) {
			return nil, fmt.Errorf("OpenAI API error (status %d, type %s): %w", apiErr.HTTPStatusCode, apiErr.Type, err)
		}
		return nil, fmt.Errorf("failed to create chat completion stream: %w", err)
	}

	respChan := make(chan domain.ChatCompletionStreamResponse)

	go func() {
		defer stream.Close()
		defer close(respChan)

		for {
			response, err := stream.Recv()

			select {
			case <-ctx.Done():
				respChan <- domain.ChatCompletionStreamResponse{Error: ctx.Err()}
				return
			default:
			}

			if errors.Is(err, io.EOF) {
				return
			}
			if err != nil {
				respChan <- domain.ChatCompletionStreamResponse{Error: fmt.Errorf("error receiving stream response: %w", err)}
				return
			}

			domainResp, mapErr := mapFromOpenAICompatResponse(&response)
			if mapErr != nil {
				respChan <- domain.ChatCompletionStreamResponse{Error: fmt.Errorf("error mapping openai compatible response: %w", mapErr)}
				return
			}
			respChan <- *domainResp
		}
	}()

	return respChan, nil
}

// ChatCompletionNonStreaming implements the domain.LLMClient interface for non-streaming calls.
func (c *OpenAICompatibleClient) ChatCompletionNonStreaming(ctx context.Context, req *domain.ChatCompletionRequest) (*domain.ChatCompletionResponse, error) {
	// TODO:
	return nil, nil
}

// --- Mapping functions ---

// mapToOpenAICompatRequest converts our domain request to sashabaranov/go-openai request format.
func mapToOpenAICompatRequest(req *domain.ChatCompletionRequest) (openai.ChatCompletionRequest, error) {
	out := openai.ChatCompletionRequest{
		Model:       req.Model,
		Stream:      true,
		Messages:    make([]openai.ChatCompletionMessage, len(req.Messages)),
		Temperature: dereferenceFloat32(req.Temperature),
		MaxTokens:   dereferenceInt(req.MaxTokens),
	}

	for i, msg := range req.Messages {
		// Handle messages with images using MultiContent
		if len(msg.Images) > 0 {
			multiContent := []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: msg.Content,
				},
			}

			// Add each image as a separate content part
			for _, image := range msg.Images {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL:    image,
						Detail: openai.ImageURLDetailAuto,
					},
				})
			}

			out.Messages[i] = openai.ChatCompletionMessage{
				Role:         mapRoleToOpenAICompat(msg.Role),
				MultiContent: multiContent,
			}
		} else {
			// Handle text-only messages
			out.Messages[i] = openai.ChatCompletionMessage{
				Role:    mapRoleToOpenAICompat(msg.Role),
				Content: msg.Content,
			}
		}
	}

	return out, nil
}

// mapRoleToOpenAICompat converts our internal Role type to the library's string constants.
func mapRoleToOpenAICompat(role model.Role) string {
	switch role {
	case model.RoleSystem:
		return openai.ChatMessageRoleSystem
	case model.RoleUser:
		return openai.ChatMessageRoleUser
	case model.RoleAssistant:
		return openai.ChatMessageRoleAssistant
	default:
		return string(role)
	}
}

// mapFromOpenAICompatResponse converts a sashabaranov/go-openai stream response chunk to our domain response.
func mapFromOpenAICompatResponse(resp *openai.ChatCompletionStreamResponse) (*domain.ChatCompletionStreamResponse, error) {
	if len(resp.Choices) == 0 {
		return &domain.ChatCompletionStreamResponse{IsFinal: false}, nil
	}

	choice := resp.Choices[0]
	domainResp := &domain.ChatCompletionStreamResponse{}

	domainResp.ContentDelta = choice.Delta.Content

	if choice.FinishReason != "" {
		domainResp.IsFinal = true
	}

	return domainResp, nil
}

// --- Helper functions ---

// dereferenceFloat32 safely dereferences a *float32, returning 0.0 if nil.
func dereferenceFloat32(ptr *float32) float32 {
	if ptr == nil {
		return 0.0
	}
	return *ptr
}

// dereferenceInt safely dereferences an *int, returning 0 if nil.
func dereferenceInt(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// mapToOpenAICompatRequestNonStreaming converts our domain request to sashabaranov/go-openai request format for non-streaming calls.
func mapToOpenAICompatRequestNonStreaming(req *domain.ChatCompletionRequest) (openai.ChatCompletionRequest, error) {
	out := openai.ChatCompletionRequest{
		Model:       req.Model,
		Stream:      false, // Set to false for non-streaming calls
		Messages:    make([]openai.ChatCompletionMessage, len(req.Messages)),
		Temperature: dereferenceFloat32(req.Temperature),
		MaxTokens:   dereferenceInt(req.MaxTokens),
	}

	for i, msg := range req.Messages {
		// Handle messages with images using MultiContent
		if len(msg.Images) > 0 {
			multiContent := []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: msg.Content,
				},
			}

			// Add each image as a separate content part
			for _, image := range msg.Images {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL:    image,
						Detail: openai.ImageURLDetailAuto,
					},
				})
			}

			out.Messages[i] = openai.ChatCompletionMessage{
				Role:         mapRoleToOpenAICompat(msg.Role),
				MultiContent: multiContent,
			}
		} else {
			// Handle text-only messages
			out.Messages[i] = openai.ChatCompletionMessage{
				Role:    mapRoleToOpenAICompat(msg.Role),
				Content: msg.Content,
			}
		}
	}

	return out, nil
}

// mapFromOpenAICompatNonStreamingResponse converts a sashabaranov/go-openai non-streaming response to our domain response.
func mapFromOpenAICompatNonStreamingResponse(resp *openai.ChatCompletionResponse) (*domain.ChatCompletionResponse, error) {
	if len(resp.Choices) == 0 {
		return &domain.ChatCompletionResponse{}, nil
	}

	choice := resp.Choices[0]
	domainResp := &domain.ChatCompletionResponse{
		Content:      choice.Message.Content,
		FinishReason: string(choice.FinishReason),
		ToolCalls:    make([]model.ToolCall, 0), // TODO: Implement tool call mapping if needed
	}

	// Map tool calls if present (for future use)
	if len(choice.Message.ToolCalls) > 0 {
		// TODO: Implement tool call mapping when tool support is added
	}

	return domainResp, nil
}
