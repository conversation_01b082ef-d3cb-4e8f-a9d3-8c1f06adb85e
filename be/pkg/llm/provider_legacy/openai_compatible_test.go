package provider

import (
	"context"
	"testing"
	"time"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	domain "git.nevint.com/fota3/t-rex/domain/agent"
	model "git.nevint.com/fota3/t-rex/model/agent"
	aimodel "git.nevint.com/fota3/t-rex/model/aimodel"
)

const modelName = "deepseek/deepseek-chat-v3-0324:free"

// setupConfig 从配置文件加载LLM配置
func setupConfig(t *testing.T) (string, string) {
	// 首先尝试使用正式配置
	configFile := "../../../config/config.yaml"
	viper.SetConfigFile(configFile)

	err := viper.ReadInConfig()
	if err != nil {
		t.Fatalf("读取配置文件失败: %v", err)
	}

	apiKey := viper.GetString("openai.api_key")
	apiBase := viper.GetString("openai.api_base")

	require.NotEmpty(t, apiKey, "OpenAI API Key不能为空")
	require.NotEmpty(t, apiBase, "OpenAI API Base不能为空")

	return apiKey, apiBase
}

// TestNewOpenAICompatibleClient 测试客户端初始化
func TestNewOpenAICompatibleClient(t *testing.T) {
	apiKey, apiBase := setupConfig(t)
	tests := []struct {
		name        string
		config      OpenAICompatibleConfig
		expectError bool
	}{
		{
			name: "有效配置",
			config: OpenAICompatibleConfig{
				APIKey:  apiKey,
				BaseURL: apiBase,
			},
			expectError: false,
		},
		{
			name: "缺少API Key",
			config: OpenAICompatibleConfig{
				APIKey:  "",
				BaseURL: "https://api.example.com/v1",
			},
			expectError: true,
		},
		{
			name: "缺少Base URL",
			config: OpenAICompatibleConfig{
				APIKey:  apiKey,
				BaseURL: "",
			},
			expectError: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			client, err := NewOpenAICompatibleClient(tc.config)

			if tc.expectError {
				assert.Error(t, err)
				assert.Nil(t, client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
			}
		})
	}
}

// TestNewOpenAICompatibleClientWithConfig 测试使用配置文件初始化客户端
func TestNewOpenAICompatibleClientWithConfig(t *testing.T) {
	apiKey, apiBase := setupConfig(t)

	client, err := NewOpenAICompatibleClient(OpenAICompatibleConfig{
		APIKey:  apiKey,
		BaseURL: apiBase,
	})

	require.NoError(t, err)
	require.NotNil(t, client)
}

// 测试请求映射函数mapToOpenAICompatRequest
func TestMapToOpenAICompatRequest(t *testing.T) {
	temperature := float32(0.7)
	maxTokens := 1000

	// 创建一个基本的请求对象
	req := &domain.ChatCompletionRequest{
		Model:       modelName,
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		Messages: []model.Message{
			{
				Role:    model.RoleSystem,
				Content: "你是一个有用的助手。",
			},
			{
				Role:    model.RoleUser,
				Content: "你好！",
			},
		},
	}

	openaiReq, err := mapToOpenAICompatRequest(req, modelName)
	require.NoError(t, err)

	// 验证基本字段是否正确映射
	assert.Equal(t, modelName, openaiReq.Model)
	assert.Equal(t, true, openaiReq.Stream)
	assert.Equal(t, temperature, openaiReq.Temperature)
	assert.Equal(t, maxTokens, openaiReq.MaxTokens)

	// 验证消息是否正确映射
	require.Len(t, openaiReq.Messages, 2)
	assert.Equal(t, "system", openaiReq.Messages[0].Role)
	assert.Equal(t, "你是一个有用的助手。", openaiReq.Messages[0].Content)
	assert.Equal(t, "user", openaiReq.Messages[1].Role)
	assert.Equal(t, "你好！", openaiReq.Messages[1].Content)
}

// TestChatCompletionStreamWithAIModel 使用AIModel测试流式API调用
func TestChatCompletionStreamWithAIModel(t *testing.T) {
	apiKey, apiBase := setupConfig(t)

	client, err := NewOpenAICompatibleClient(OpenAICompatibleConfig{
		APIKey:  apiKey,
		BaseURL: apiBase,
	})
	require.NoError(t, err)

	// 创建测试用的AIModel
	testAIModel := aimodel.AIModel{
		APIKey:    apiKey,
		APIBase:   apiBase,
		ModelName: modelName,
		IsActive:  true,
	}

	// 创建简单的聊天请求
	req := &domain.ChatCompletionRequest{
		Model: modelName,
		Messages: []model.Message{
			{
				Role:    model.RoleSystem,
				Content: "你是一个有用的助手。请简短回答。",
			},
			{
				Role:    model.RoleUser,
				Content: "你好，请告诉我今天是星期几？",
			},
		},
	}

	// 调用流式接口
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	respChan, err := client.ChatCompletionStreamWithAIModel(ctx, testAIModel, req)
	require.NoError(t, err)

	// 收集所有响应
	var content string

	for resp := range respChan {
		if resp.Error != nil {
			t.Logf("Stream error: %v", resp.Error)
			continue
		}
		content += resp.ContentDelta
		t.Logf("Received: %s", resp.ContentDelta)
	}

	t.Logf("完整回复: %s", content)
	assert.NotEmpty(t, content)
}

// TestChatCompletionNonStreamingWithAIModel 测试非流式API调用
func TestChatCompletionNonStreamingWithAIModel(t *testing.T) {
	apiKey, apiBase := setupConfig(t)

	client, err := NewOpenAICompatibleClient(OpenAICompatibleConfig{
		APIKey:  apiKey,
		BaseURL: apiBase,
	})
	require.NoError(t, err)

	// 创建测试用的AIModel
	testAIModel := aimodel.AIModel{
		APIKey:    apiKey,
		APIBase:   apiBase,
		ModelName: modelName,
		IsActive:  true,
	}

	// 创建简单的聊天请求
	req := &domain.ChatCompletionRequest{
		Model: modelName,
		Messages: []model.Message{
			{
				Role:    model.RoleSystem,
				Content: "你是一个有用的助手。请简短回答。",
			},
			{
				Role:    model.RoleUser,
				Content: "请告诉我1+1等于多少？",
			},
		},
	}

	// 调用非流式接口
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	resp, err := client.ChatCompletionNonStreamingWithAIModel(ctx, testAIModel, req)
	require.NoError(t, err)
	require.NotNil(t, resp)

	t.Logf("非流式回复: %s", resp.Content)
	t.Logf("完成原因: %s", resp.FinishReason)

	assert.NotEmpty(t, resp.Content)
	assert.NotEmpty(t, resp.FinishReason)
}

// TestMapToOpenAICompatRequestNonStreaming 测试非流式请求映射函数
func TestMapToOpenAICompatRequestNonStreaming(t *testing.T) {
	temperature := float32(0.7)
	maxTokens := 1000

	// 创建一个基本的请求对象
	req := &domain.ChatCompletionRequest{
		Model:       modelName,
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		Messages: []model.Message{
			{
				Role:    model.RoleSystem,
				Content: "你是一个有用的助手。",
			},
			{
				Role:    model.RoleUser,
				Content: "你好！",
			},
		},
	}

	openaiReq, err := mapToOpenAICompatRequestNonStreaming(req, modelName)
	require.NoError(t, err)

	// 验证基本字段是否正确映射
	assert.Equal(t, modelName, openaiReq.Model)
	assert.Equal(t, false, openaiReq.Stream) // 非流式应该为false
	assert.Equal(t, temperature, openaiReq.Temperature)
	assert.Equal(t, maxTokens, openaiReq.MaxTokens)

	// 验证消息是否正确映射
	require.Len(t, openaiReq.Messages, 2)
	assert.Equal(t, "system", openaiReq.Messages[0].Role)
	assert.Equal(t, "你是一个有用的助手。", openaiReq.Messages[0].Content)
	assert.Equal(t, "user", openaiReq.Messages[1].Role)
	assert.Equal(t, "你好！", openaiReq.Messages[1].Content)
}
