package provider

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	openai "github.com/sashabaranov/go-openai"

	domain "git.nevint.com/fota3/t-rex/domain/agent"
	model "git.nevint.com/fota3/t-rex/model/agent"
	modelAI "git.nevint.com/fota3/t-rex/model/aimodel" // Import AIModel for legacy support
)

// OpenAICompatibleClient implements LLM client functionality using the sashabaranov/go-openai library
// for OpenAI compatible APIs. This legacy version works with AIModel configurations.
// Note: This client is designed to be used through the AIModelLLMClientAdapter, not directly.
type OpenAICompatibleClient struct {
	httpClient *http.Client // Keep an optional shared http client
}

// OpenAICompatibleConfig holds configuration for the OpenAI compatible client.
type OpenAICompatibleConfig struct {
	APIKey     string       // API key for the OpenAI compatible service
	BaseURL    string       // Base URL for the API endpoint
	HTTPClient *http.Client // Optional: can be shared across client instances
}

// NewOpenAICompatibleClient creates a new client instance for OpenAI compatible APIs.
func NewOpenAICompatibleClient(cfg OpenAICompatibleConfig) (*OpenAICompatibleClient, error) {
	// Validate required config
	if cfg.APIKey == "" {
		return nil, errors.New("API key is required for OpenAI compatible client")
	}
	if cfg.BaseURL == "" {
		return nil, errors.New("base URL is required for OpenAI compatible client")
	}

	return &OpenAICompatibleClient{
		httpClient: cfg.HTTPClient,
	}, nil
}

// ChatCompletionStreamWithAIModel implements streaming chat completion using AIModel configuration.
func (c *OpenAICompatibleClient) ChatCompletionStreamWithAIModel(ctx context.Context, aiModel modelAI.AIModel, req *domain.ChatCompletionRequest) (<-chan domain.ChatCompletionStreamResponse, error) {
	// Validate AI model configuration
	if aiModel.APIKey == "" {
		return nil, errors.New("AI model API key is required for ChatCompletionStream")
	}
	if !aiModel.IsActive {
		return nil, fmt.Errorf("AI model '%s' is not active", aiModel.ModelName)
	}

	openAIConfig := openai.DefaultConfig(aiModel.APIKey)
	if aiModel.APIBase != "" {
		openAIConfig.BaseURL = aiModel.APIBase
	}
	// Reuse the http client if available
	if c.httpClient != nil {
		openAIConfig.HTTPClient = c.httpClient
	} else {
		// If no shared client, use a default with timeout
		openAIConfig.HTTPClient = &http.Client{Timeout: 30 * time.Minute}
	}

	dynamicClient := openai.NewClientWithConfig(openAIConfig)

	// Use the model name from AIModel, but allow override from request
	modelName := req.Model
	if modelName == "" {
		modelName = aiModel.ModelName
	}

	// Map our domain request to openai compatible request format
	openaiReq, err := mapToOpenAICompatRequest(req, modelName)
	if err != nil {
		return nil, fmt.Errorf("failed to map domain request to openai compatible request: %w", err)
	}

	stream, err := dynamicClient.CreateChatCompletionStream(ctx, openaiReq)
	if err != nil {
		var apiErr *openai.APIError
		if errors.As(err, &apiErr) {
			return nil, fmt.Errorf("OpenAI API error (status %d, type %s): %w", apiErr.HTTPStatusCode, apiErr.Type, err)
		}
		return nil, fmt.Errorf("failed to create chat completion stream: %w", err)
	}

	respChan := make(chan domain.ChatCompletionStreamResponse)

	go func() {
		defer stream.Close()
		defer close(respChan)

		for {
			response, err := stream.Recv()

			select {
			case <-ctx.Done():
				respChan <- domain.ChatCompletionStreamResponse{Error: ctx.Err()}
				return
			default:
			}

			if errors.Is(err, io.EOF) {
				return
			}
			if err != nil {
				respChan <- domain.ChatCompletionStreamResponse{Error: fmt.Errorf("error receiving stream response: %w", err)}
				return
			}

			domainResp, mapErr := mapFromOpenAICompatResponse(&response)
			if mapErr != nil {
				respChan <- domain.ChatCompletionStreamResponse{Error: fmt.Errorf("error mapping openai compatible response: %w", mapErr)}
				return
			}
			respChan <- *domainResp
		}
	}()

	return respChan, nil
}

// ChatCompletionNonStreamingWithAIModel implements non-streaming chat completion using AIModel.
func (c *OpenAICompatibleClient) ChatCompletionNonStreamingWithAIModel(ctx context.Context, aiModel modelAI.AIModel, req *domain.ChatCompletionRequest) (*domain.ChatCompletionResponse, error) {
	// Validate AI model configuration
	if aiModel.APIKey == "" {
		return nil, errors.New("AI model API key is required for ChatCompletionNonStreaming")
	}
	if !aiModel.IsActive {
		return nil, fmt.Errorf("AI model '%s' is not active", aiModel.ModelName)
	}

	openAIConfig := openai.DefaultConfig(aiModel.APIKey)
	if aiModel.APIBase != "" {
		openAIConfig.BaseURL = aiModel.APIBase
	}
	// Reuse the http client if available
	if c.httpClient != nil {
		openAIConfig.HTTPClient = c.httpClient
	} else {
		// If no shared client, use a default with timeout
		openAIConfig.HTTPClient = &http.Client{Timeout: 30 * time.Minute}
	}

	dynamicClient := openai.NewClientWithConfig(openAIConfig)

	// Use the model name from AIModel, but allow override from request
	modelName := req.Model
	if modelName == "" {
		modelName = aiModel.ModelName
	}

	openaiReq, err := mapToOpenAICompatRequestNonStreaming(req, modelName)
	if err != nil {
		return nil, fmt.Errorf("failed to map domain request to openai compatible request: %w", err)
	}

	response, err := dynamicClient.CreateChatCompletion(ctx, openaiReq)
	if err != nil {
		var apiErr *openai.APIError
		if errors.As(err, &apiErr) {
			return nil, fmt.Errorf("OpenAI API error (status %d, type %s): %w", apiErr.HTTPStatusCode, apiErr.Type, err)
		}
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	domainResp, mapErr := mapFromOpenAICompatNonStreamingResponse(&response)
	if mapErr != nil {
		return nil, fmt.Errorf("error mapping openai compatible response: %w", mapErr)
	}

	return domainResp, nil
}

// --- Mapping functions ---

// mapToOpenAICompatRequest converts our domain request to sashabaranov/go-openai request format.
func mapToOpenAICompatRequest(req *domain.ChatCompletionRequest, modelName string) (openai.ChatCompletionRequest, error) {
	out := openai.ChatCompletionRequest{
		Model:       modelName, // Use provided model name
		Stream:      true,
		Messages:    make([]openai.ChatCompletionMessage, len(req.Messages)),
		Temperature: dereferenceFloat32(req.Temperature),
		MaxTokens:   dereferenceInt(req.MaxTokens),
	}

	for i, msg := range req.Messages {
		// Handle messages with images using MultiContent
		if len(msg.Images) > 0 {
			multiContent := []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: msg.Content,
				},
			}

			// Add each image as a separate content part
			for _, image := range msg.Images {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL:    image,
						Detail: openai.ImageURLDetailAuto,
					},
				})
			}

			out.Messages[i] = openai.ChatCompletionMessage{
				Role:         mapRoleToOpenAICompat(msg.Role),
				MultiContent: multiContent,
			}
		} else {
			// Handle text-only messages
			out.Messages[i] = openai.ChatCompletionMessage{
				Role:    mapRoleToOpenAICompat(msg.Role),
				Content: msg.Content,
			}
		}
	}

	return out, nil
}

// mapRoleToOpenAICompat converts our internal Role type to the library's string constants.
func mapRoleToOpenAICompat(role model.Role) string {
	switch role {
	case model.RoleSystem:
		return openai.ChatMessageRoleSystem
	case model.RoleUser:
		return openai.ChatMessageRoleUser
	case model.RoleAssistant:
		return openai.ChatMessageRoleAssistant
	default:
		return string(role)
	}
}

// mapFromOpenAICompatResponse converts a sashabaranov/go-openai stream response chunk to our domain response.
func mapFromOpenAICompatResponse(resp *openai.ChatCompletionStreamResponse) (*domain.ChatCompletionStreamResponse, error) {
	if len(resp.Choices) == 0 {
		return &domain.ChatCompletionStreamResponse{IsFinal: false}, nil
	}

	choice := resp.Choices[0]
	domainResp := &domain.ChatCompletionStreamResponse{}

	domainResp.ContentDelta = choice.Delta.Content

	if choice.FinishReason != "" {
		domainResp.IsFinal = true
	}

	return domainResp, nil
}

// --- Helper functions ---

// dereferenceFloat32 safely dereferences a *float32, returning 0.0 if nil.
func dereferenceFloat32(ptr *float32) float32 {
	if ptr == nil {
		return 0.0
	}
	return *ptr
}

// dereferenceInt safely dereferences an *int, returning 0 if nil.
func dereferenceInt(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// mapToOpenAICompatRequestNonStreaming converts our domain request to sashabaranov/go-openai request format for non-streaming calls.
func mapToOpenAICompatRequestNonStreaming(req *domain.ChatCompletionRequest, modelName string) (openai.ChatCompletionRequest, error) {
	out := openai.ChatCompletionRequest{
		Model:       modelName, // Use provided model name
		Stream:      false,     // Set to false for non-streaming calls
		Messages:    make([]openai.ChatCompletionMessage, len(req.Messages)),
		Temperature: dereferenceFloat32(req.Temperature),
		MaxTokens:   dereferenceInt(req.MaxTokens),
	}

	for i, msg := range req.Messages {
		// Handle messages with images using MultiContent
		if len(msg.Images) > 0 {
			multiContent := []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: msg.Content,
				},
			}

			// Add each image as a separate content part
			for _, image := range msg.Images {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL:    image,
						Detail: openai.ImageURLDetailAuto,
					},
				})
			}

			out.Messages[i] = openai.ChatCompletionMessage{
				Role:         mapRoleToOpenAICompat(msg.Role),
				MultiContent: multiContent,
			}
		} else {
			// Handle text-only messages
			out.Messages[i] = openai.ChatCompletionMessage{
				Role:    mapRoleToOpenAICompat(msg.Role),
				Content: msg.Content,
			}
		}
	}

	return out, nil
}

// mapFromOpenAICompatNonStreamingResponse converts a sashabaranov/go-openai non-streaming response to our domain response.
func mapFromOpenAICompatNonStreamingResponse(resp *openai.ChatCompletionResponse) (*domain.ChatCompletionResponse, error) {
	if len(resp.Choices) == 0 {
		return &domain.ChatCompletionResponse{}, nil
	}

	choice := resp.Choices[0]
	domainResp := &domain.ChatCompletionResponse{
		Content:      choice.Message.Content,
		FinishReason: string(choice.FinishReason),
		ToolCalls:    make([]model.ToolCall, 0), // TODO: Implement tool call mapping if needed
	}

	// Map tool calls if present (for future use)
	if len(choice.Message.ToolCalls) > 0 {
		// TODO: Implement tool call mapping when tool support is added
	}

	return domainResp, nil
}
