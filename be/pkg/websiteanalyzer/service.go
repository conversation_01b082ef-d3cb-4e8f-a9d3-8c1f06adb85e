package websiteanalyzer

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"git.nevint.com/fota3/t-rex/domain/agent"
	agentModel "git.nevint.com/fota3/t-rex/model/agent"
	requirementmodel "git.nevint.com/fota3/t-rex/model/requirement"
	"go.uber.org/zap"
)

// LLMClientFactory defines the interface for creating LLM clients
type LLMClientFactory interface {
	GetLLMClient(ctx context.Context, modelName string) (agent.LLMClient, error)
}

// WebsiteAnalyzer defines the interface for AI-powered website analysis
type WebsiteAnalyzer interface {
	AnalyzeWebsite(ctx context.Context, url string, modelName string) (*requirementmodel.WebsiteAnalysis, error)
}

// Service implements the WebsiteAnalyzer interface
type Service struct {
	llmClientFactory LLMClientFactory
	defaultModelName string
	logger           *zap.Logger
}

// NewService creates a new website analyzer service
func NewService(llmClientFactory LLMClientFactory, defaultModelName string, logger *zap.Logger) (*Service, error) {
	if llmClientFactory == nil {
		return nil, fmt.Errorf("LLM client factory is required")
	}
	if defaultModelName == "" {
		return nil, fmt.Errorf("default model name is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	return &Service{
		llmClientFactory: llmClientFactory,
		defaultModelName: defaultModelName,
		logger:           logger.Named("website_analyzer"),
	}, nil
}

// AnalyzeWebsite performs comprehensive analysis of a website using AI
func (s *Service) AnalyzeWebsite(ctx context.Context, url string, modelName string) (*requirementmodel.WebsiteAnalysis, error) {
	s.logger.Info("Starting website analysis",
		zap.String("url", url))

	// Use provided model name or fall back to default
	if modelName == "" {
		modelName = s.defaultModelName
	}

	// For now, create a basic analysis based on the URL
	// In the future, this could be enhanced with actual web scraping
	analysis, err := s.analyzeWithAI(ctx, url, modelName)
	if err != nil {
		s.logger.Error("Failed to analyze website with AI",
			zap.String("url", url),
			zap.Error(err))
		return nil, fmt.Errorf("failed to analyze website with AI: %w", err)
	}

	s.logger.Info("Website analysis completed successfully",
		zap.String("url", url))

	return analysis, nil
}



// analyzeWithAI uses AI to analyze the website and generate structured analysis
func (s *Service) analyzeWithAI(ctx context.Context, url string, modelName string) (*requirementmodel.WebsiteAnalysis, error) {
	// Generate analysis prompt
	prompt := s.generateAnalysisPrompt(url)

	// Create LLM client
	llmClient, err := s.llmClientFactory.GetLLMClient(ctx, modelName)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	// Prepare messages
	messages := []agentModel.Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	// Set parameters for analysis
	temperature := float32(0.3) // Lower temperature for more consistent analysis
	maxTokens := 4000           // Sufficient tokens for comprehensive analysis

	// Call LLM for analysis
	response, err := llmClient.ChatCompletionNonStreaming(ctx, &agent.ChatCompletionRequest{
		Model:       modelName,
		Messages:    messages,
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
	})
	if err != nil {
		s.logger.Error("Failed to get LLM analysis",
			zap.String("url", url),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get LLM analysis: %w", err)
	}

	// Parse the response
	analysis, err := s.parseAnalysisResponse(response.Content)
	if err != nil {
		s.logger.Error("Failed to parse analysis response",
			zap.String("url", url),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	return analysis, nil
}

// generateAnalysisPrompt creates a comprehensive prompt for website analysis
func (s *Service) generateAnalysisPrompt(url string) string {
	return fmt.Sprintf(`请分析以下网站的设计特征，并以JSON格式返回分析结果。请从以下5个维度进行分析：

1. **视觉设计 (visual_design)**
   - color_scheme: 主要配色方案和调色板
   - typography: 字体选择和文本样式
   - layout: 整体布局结构
   - visual_style: 视觉风格（现代、传统、极简等）
   - imagery_style: 图像和视觉元素风格
   - spacing_density: 留白和元素密度

2. **用户体验 (user_experience)**
   - navigation: 导航结构和可用性
   - interaction_style: 交互方式和反馈
   - responsiveness: 响应式设计质量
   - accessibility: 无障碍设计考虑
   - user_flow: 用户流程清晰度

3. **内容结构 (content_structure)**
   - content_types: 内容类型
   - information_hierarchy: 信息组织层次
   - content_tone: 文本内容语调和风格
   - content_density: 信息量和密度

4. **技术实现 (technical_aspects)**
   - framework_tech: 检测到的框架或技术
   - performance: 性能特征
   - modern_features: 使用的现代网页功能
   - code_quality: 代码质量和最佳实践

5. **品牌调性 (brand_tone)**
   - brand_personality: 整体品牌个性
   - emotional_tone: 传达的情感基调
   - target_audience: 明显的目标受众
   - business_type: 业务或组织类型

**输出格式：**
请严格按照以下JSON格式返回，直接返回JSON对象，不要使用代码块包装：

{
  "visual_design": {
    "color_scheme": ["颜色1", "颜色2", "颜色3"],
    "typography": "字体和文本样式描述",
    "layout": "布局结构描述",
    "visual_style": "视觉风格描述",
    "imagery_style": "图像风格描述",
    "spacing_density": "留白和密度描述"
  },
  "user_experience": {
    "navigation": "导航描述",
    "interaction_style": "交互风格描述",
    "responsiveness": "响应式设计描述",
    "accessibility": "无障碍描述",
    "user_flow": "用户流程描述"
  },
  "content_structure": {
    "content_types": ["内容类型1", "内容类型2"],
    "information_hierarchy": "信息层次描述",
    "content_tone": "内容语调描述",
    "content_density": "内容密度描述"
  },
  "technical_aspects": {
    "framework_tech": "技术框架描述",
    "performance": "性能描述",
    "modern_features": "现代功能描述",
    "code_quality": "代码质量描述"
  },
  "brand_tone": {
    "brand_personality": "品牌个性描述",
    "emotional_tone": "情感基调描述",
    "target_audience": "目标受众描述",
    "business_type": "业务类型描述"
  },
  "summary": "综合分析总结，包括设计风格建议和实施要点"
}

**重要提醒：**
- 请直接返回JSON对象，不要添加任何解释性文字
- 不要使用反引号代码块包装
- 确保JSON格式正确，所有字符串都用双引号包围
- 所有字段都必须填写，即使是简短的描述
- 请用中文回答所有描述内容

网站URL: %s`, url)
}

// parseAnalysisResponse parses the LLM's JSON response into structured data
func (s *Service) parseAnalysisResponse(response string) (*requirementmodel.WebsiteAnalysis, error) {
	s.logger.Debug("Parsing website analysis response",
		zap.Int("response_length", len(response)),
		zap.String("raw_response", response))

	// Clean the response to extract JSON
	jsonStr := s.extractJSON(response)
	if jsonStr == "" {
		s.logger.Warn("No valid JSON found in response",
			zap.String("raw_response", response))
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	s.logger.Debug("Extracted JSON from response",
		zap.String("extracted_json", jsonStr))

	// Parse JSON into WebsiteAnalysis struct
	var analysis requirementmodel.WebsiteAnalysis
	if err := json.Unmarshal([]byte(jsonStr), &analysis); err != nil {
		s.logger.Error("Failed to unmarshal JSON response, using fallback analysis",
			zap.Error(err),
			zap.String("json", jsonStr))
		// Return a fallback analysis based on the URL
		return s.createFallbackAnalysis(response), nil
	}

	// Validate that we have meaningful content
	if !s.hasValidContent(&analysis) {
		s.logger.Warn("Analysis response lacks meaningful content, using fallback")
		return s.createFallbackAnalysis(response), nil
	}

	s.logger.Info("Successfully parsed website analysis response")
	return &analysis, nil
}

// extractJSON extracts JSON content from the response
func (s *Service) extractJSON(response string) string {
	// First try to find JSON within code blocks
	if strings.Contains(response, "```json") {
		startMarker := "```json"
		endMarker := "```"
		startIdx := strings.Index(response, startMarker)
		if startIdx != -1 {
			startIdx += len(startMarker)
			endIdx := strings.Index(response[startIdx:], endMarker)
			if endIdx != -1 {
				jsonContent := strings.TrimSpace(response[startIdx : startIdx+endIdx])
				if strings.HasPrefix(jsonContent, "{") && strings.HasSuffix(jsonContent, "}") {
					return jsonContent
				}
			}
		}
	}

	// Try to find JSON within regular code blocks
	if strings.Contains(response, "```") {
		lines := strings.Split(response, "\n")
		inCodeBlock := false
		var jsonLines []string
		for _, line := range lines {
			if strings.HasPrefix(line, "```") {
				if inCodeBlock {
					// End of code block
					break
				} else {
					// Start of code block
					inCodeBlock = true
					continue
				}
			}
			if inCodeBlock {
				jsonLines = append(jsonLines, line)
			}
		}
		if len(jsonLines) > 0 {
			jsonContent := strings.TrimSpace(strings.Join(jsonLines, "\n"))
			if strings.HasPrefix(jsonContent, "{") && strings.HasSuffix(jsonContent, "}") {
				return jsonContent
			}
		}
	}

	// Look for JSON object boundaries in the raw response
	startIdx := strings.Index(response, "{")
	if startIdx == -1 {
		return ""
	}

	// Find the matching closing brace
	braceCount := 0
	endIdx := -1
	for i := startIdx; i < len(response); i++ {
		if response[i] == '{' {
			braceCount++
		} else if response[i] == '}' {
			braceCount--
			if braceCount == 0 {
				endIdx = i + 1
				break
			}
		}
	}

	if endIdx == -1 {
		return ""
	}

	return response[startIdx:endIdx]
}

// createFallbackAnalysis creates a basic analysis when AI parsing fails
func (s *Service) createFallbackAnalysis(originalResponse string) *requirementmodel.WebsiteAnalysis {
	return &requirementmodel.WebsiteAnalysis{
		VisualDesign: requirementmodel.VisualDesignAnalysis{
			ColorScheme:     []string{"现代色彩", "专业配色"},
			Typography:     "标准网页字体",
			Layout:         "响应式布局",
			VisualStyle:    "现代简洁风格",
			ImageryStyle:   "专业图片风格",
			SpacingDensity: "适中的留白设计",
		},
		UserExperience: requirementmodel.UserExperienceAnalysis{
			Navigation:       "标准导航结构",
			InteractionStyle: "用户友好的交互",
			Responsiveness:  "移动端适配",
			Accessibility:   "基础无障碍功能",
			UserFlow:        "清晰的用户流程",
		},
		ContentStructure: requirementmodel.ContentStructureAnalysis{
			ContentTypes:          []string{"文本内容", "图片内容"},
			InformationHierarchy:  "层次化信息结构",
			ContentTone:           "专业友好的语调",
			ContentDensity:        "适中的内容密度",
		},
		TechnicalAspects: requirementmodel.TechnicalAspectsAnalysis{
			FrameworkTech:   "现代Web技术",
			Performance:     "良好的性能表现",
			ModernFeatures:  "标准Web功能",
			CodeQuality:     "规范的代码结构",
		},
		BrandTone: requirementmodel.BrandToneAnalysis{
			BrandPersonality: "专业可信",
			EmotionalTone:    "友好亲和",
			TargetAudience:   "广泛用户群体",
			BusinessType:     "企业服务",
		},
		Summary: "由于AI分析响应解析失败，提供了基础的网站分析结果。建议手动补充更详细的分析信息。",
	}
}

// hasValidContent checks if the analysis contains meaningful content
func (s *Service) hasValidContent(analysis *requirementmodel.WebsiteAnalysis) bool {
	if analysis == nil {
		return false
	}

	// Check if we have content in any of the main sections
	hasVisualContent := analysis.VisualDesign.Layout != "" ||
		len(analysis.VisualDesign.ColorScheme) > 0 ||
		analysis.VisualDesign.VisualStyle != ""

	hasUXContent := analysis.UserExperience.Navigation != "" ||
		analysis.UserExperience.InteractionStyle != ""

	hasContentStructure := len(analysis.ContentStructure.ContentTypes) > 0 ||
		analysis.ContentStructure.InformationHierarchy != ""

	hasBrandContent := analysis.BrandTone.BrandPersonality != "" ||
		analysis.BrandTone.EmotionalTone != ""

	hasSummary := analysis.Summary != ""

	return hasVisualContent || hasUXContent || hasContentStructure || hasBrandContent || hasSummary
}