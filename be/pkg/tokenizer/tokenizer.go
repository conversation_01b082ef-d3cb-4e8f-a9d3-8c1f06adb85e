package tokenizer

import (
	"fmt"
	"sync"

	"github.com/tiktoken-go/tokenizer"
)

// Service provides a thread-safe way to access and use a tokenizer.
type Service struct {
	mu       sync.RWMutex
	codec    tokenizer.Codec
	encoding string
}

// NewService creates and initializes a new tokenizer service using a tiktoken encoding.
func NewService(encoding string) (*Service, error) {
	// Get the tokenizer codec by its encoding name.
	codec, err := tokenizer.Get(tokenizer.Encoding(encoding))
	if err != nil {
		return nil, fmt.Errorf("failed to get tokenizer for encoding '%s': %w", encoding, err)
	}

	return &Service{
		codec:    codec,
		encoding: encoding,
	}, nil
}

// CountTokens calculates the number of tokens for a given text.
func (s *Service) CountTokens(text string) (int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.codec == nil {
		return 0, fmt.Errorf("tokenizer codec is not initialized")
	}

	// Encode the text to get the token ids.
	ids, _, err := s.codec.Encode(text)
	if err != nil {
		return 0, fmt.E<PERSON><PERSON>("failed to encode text: %w", err)
	}

	return len(ids), nil
}

// GetEncoding returns the name of the encoding the tokenizer is for.
func (s *Service) GetEncoding() string {
	return s.encoding
}
