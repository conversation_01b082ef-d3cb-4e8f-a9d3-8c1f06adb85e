package storage

import (
	"context"
	"errors" // Import errors package
	"io/fs"  // 使用 io/fs.FileInfo 以便与 os 包兼容
	"path/filepath"
	"time"
)

// Standard errors for the storage package
var (
	ErrInvalidPath = errors.New("invalid path")
	ErrPathExists  = errors.New("path already exists")
	ErrNotExist    = errors.New("path does not exist")
	// Add other standard errors as needed, e.g.:
	// ErrPermissionDenied = errors.New("permission denied")
	// ErrNotADirectory    = errors.New("not a directory")
	// ErrIsADirectory     = errors.New("is a directory")
)

// FileInfo represents information about a file or directory.
// Mimics os.FileInfo but includes the full path relative to the project root.
type FileInfo struct {
	Name    string    `json:"name"`     // base name of the file
	Path    string    `json:"path"`     // path relative to project root
	IsDir   bool      `json:"is_dir"`   // abbreviation for Mode().IsDir()
	Size    int64     `json:"size"`     // length in bytes for regular files; system-dependent for others
	ModTime time.Time `json:"mod_time"` // modification time
	// We might omit Mode() and Sys() for simplicity in the API unless needed.
	// Mode    os.FileMode `json:"-"`      // file mode bits
	// Sys     interface{} `json:"-"`      // underlying data source (can return nil)
}

// ProjectStorage 定义了与项目文件存储交互的基础接口 (V1 - 无 Git)。
type ProjectStorage interface {
	// --- 基本文件/目录 CRUD ---

	// ReadFile 读取指定项目文件内容。
	// filePath 是相对于项目根目录的路径。
	ReadFile(ctx context.Context, projectID, filePath string) ([]byte, error)

	// WriteFile 向指定项目文件写入内容。
	// filePath 是相对于项目根目录的路径。
	// content 是要写入的完整内容。
	// overwrite 控制是否覆盖已存在的文件。
	// 实现应确保自动创建必要的父目录。
	WriteFile(ctx context.Context, projectID, filePath string, content []byte, overwrite bool) error

	// ListDirectory 列出指定项目目录下的内容。
	// dirPath 是相对于项目根目录的路径。
	ListDirectory(ctx context.Context, projectID, dirPath string) ([]FileInfo, error)

	// CreateDirectory 在指定项目中创建目录。
	// dirPath 是相对于项目根目录的路径。
	// 实现应支持递归创建父目录 (类似 mkdir -p)。
	CreateDirectory(ctx context.Context, projectID, dirPath string) error

	// Delete 删除指定项目中的文件或目录。
	// path 是相对于项目根目录的路径。
	// recursive 控制是否递归删除目录内容。
	Delete(ctx context.Context, projectID, path string, recursive bool) error

	// Rename 重命名或移动指定项目中的文件或目录。
	// oldPath, newPath 都是相对于项目根目录的路径。
	Rename(ctx context.Context, projectID, oldPath, newPath string) error

	// Exists 检查指定项目中的文件或目录是否存在。
	// path 是相对于项目根目录的路径。
	Exists(ctx context.Context, projectID, path string) (bool, error)

	// Stat 获取指定项目文件或目录的元信息。
	// path 是相对于项目根目录的路径。
	// 返回一个 FileInfo 结构体。
	Stat(ctx context.Context, projectID, path string) (FileInfo, error)

	// --- 工作区管理 ---

	// InitProjectWorkspace 初始化一个新的项目工作区。
	// (在这个本地实现中，可能只是创建项目根目录)
	InitProjectWorkspace(ctx context.Context, projectID string) error

	// DeleteProjectWorkspace 彻底删除一个项目的工作区。
	DeleteProjectWorkspace(ctx context.Context, projectID string) error

	// GetProjectRootPath 获取指定项目在物理存储上的绝对根路径。
	// 注意：这个方法暴露了底层细节，主要用于 FileService 内部协调，
	// 可能需要谨慎考虑是否放在公共接口中，或者返回一个更抽象的句柄。
	// 但对于本地实现，直接返回路径是最简单的。
	GetProjectRootPath(ctx context.Context, projectID string) (string, error)
}

// --- 辅助函数 (可选) ---

// ConvertFsFileInfo 将标准的 io/fs.FileInfo 转换为我们自定义的 FileInfo
func ConvertFsFileInfo(projectRoot, relativeDir string, entry fs.FileInfo) FileInfo {
	// 确保生成的路径使用 POSIX 风格的斜杠 '/'
	// filepath.Join 在不同系统上行为不同，我们需要统一
	fullRelativePath := filepath.Join(relativeDir, entry.Name())
	// 转换为 POSIX 路径分隔符
	posixPath := filepath.ToSlash(fullRelativePath)
	return FileInfo{
		Name:    entry.Name(),
		Path:    posixPath,
		IsDir:   entry.IsDir(),
		Size:    entry.Size(),
		ModTime: entry.ModTime(),
	}
}
