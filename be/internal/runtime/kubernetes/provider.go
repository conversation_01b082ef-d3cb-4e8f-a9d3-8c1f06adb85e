package kubernetes

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net"
	"strings"
	"time"

	"bytes"

	"github.com/docker/docker/api/types"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/kubectl/pkg/scheme"

	"git.nevint.com/fota3/t-rex/config"
	R "git.nevint.com/fota3/t-rex/domain/runtime"
)

const (
	defaultNamespace = "user-workspaces"
	defaultWorkspace = "/workspace"
	defaultShell     = "/bin/bash"
	labelProject     = "t-rex.io/project"
	labelApp         = "t-rex.io/app"
)

// k8sProvider implements the runtime.Provider interface using Kubernetes
type k8sProvider struct {
	client        kubernetes.Interface
	dynamicClient dynamic.Interface
	config        *rest.Config
	logger        *zap.Logger
	namespace     string
	traefikConfig config.TraefikConfig
	storageConfig config.StorageConfig
}

// IngressRoute GVR for dynamic client
var ingressRouteGVR = schema.GroupVersionResource{
	Group:    "traefik.io",
	Version:  "v1alpha1",
	Resource: "ingressroutes",
}

// NewK8sProvider creates a new Kubernetes runtime provider with Traefik support
func NewK8sProvider(logger *zap.Logger, k8sConfig config.KubernetesConfig) (R.Provider, error) {
	var restConfig *rest.Config
	var err error

	if k8sConfig.Kubeconfig != "" {
		restConfig, err = clientcmd.BuildConfigFromFlags("", k8sConfig.Kubeconfig)
	} else {
		restConfig, err = rest.InClusterConfig()
		if err != nil {
			restConfig, err = clientcmd.BuildConfigFromFlags("", clientcmd.RecommendedHomeFile)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes config: %w", err)
	}

	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes client: %w", err)
	}

	dynamicClient, err := dynamic.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create dynamic kubernetes client: %w", err)
	}

	namespace := k8sConfig.Namespace
	if namespace == "" {
		namespace = defaultNamespace
	}

	provider := &k8sProvider{
		client:        client,
		dynamicClient: dynamicClient,
		config:        restConfig,
		logger:        logger.Named("k8s_provider"),
		namespace:     namespace,
		traefikConfig: k8sConfig.Traefik,
		storageConfig: k8sConfig.Storage,
	}

	// Ensure namespace exists
	if err := provider.ensureNamespace(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ensure namespace: %w", err)
	}

	return provider, nil
}

// EnsureImage ensures that the specified image is available (K8s will pull automatically)
func (p *k8sProvider) EnsureImage(ctx context.Context, imageName string) error {
	p.logger.Info("Image availability will be ensured by Kubernetes during pod creation",
		zap.String("imageName", imageName))
	return nil
}

// StartContainer creates and starts a new pod for the given project
func (p *k8sProvider) StartContainer(ctx context.Context, projectID string, projectHostPath string, imageName string, containerName string) (string, error) {
	p.logger.Info("Creating Kubernetes resources for project",
		zap.String("projectID", projectID),
		zap.String("projectHostPath", projectHostPath),
		zap.String("imageName", imageName))

	// Create Pod
	podName := fmt.Sprintf("pod-%s", projectID)

	// Determine volume configuration based on storage strategy
	var volumes []corev1.Volume
	var volumeMounts []corev1.VolumeMount

	// For local development with Rancher Desktop, use hostPath for direct access to project files
	// This allows real-time file editing and immediate synchronization
	volumes = []corev1.Volume{
		{
			Name: "workspace",
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: projectHostPath,
					Type: &[]corev1.HostPathType{corev1.HostPathDirectory}[0],
				},
			},
		},
	}

	volumeMounts = []corev1.VolumeMount{
		{
			Name:      "workspace",
			MountPath: defaultWorkspace,
		},
	}

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: p.namespace,
			Labels: map[string]string{
				labelProject: projectID,
				labelApp:     "t-rex-runtime",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:            "runtime",
					Image:           imageName,
					ImagePullPolicy: corev1.PullNever, // 使用本地镜像，不从远程拉取
					TTY:             true,
					Stdin:           true,
					WorkingDir:      defaultWorkspace,
					Env: []corev1.EnvVar{
						{Name: "PROJECT_ID", Value: projectID},
					},
					VolumeMounts: volumeMounts,
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("256Mi"),
						},
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("2"),
							corev1.ResourceMemory: resource.MustParse("4Gi"),
						},
					},
				},
			},
			Volumes:       volumes,
			RestartPolicy: corev1.RestartPolicyAlways,
		},
	}

	createdPod, err := p.client.CoreV1().Pods(p.namespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to create pod: %w", err)
	}

	// Create Service
	if err := p.createService(ctx, projectID); err != nil {
		p.logger.Warn("Failed to create service", zap.Error(err))
		return "", fmt.Errorf("failed to create service: %w", err)
	}

	// Create Traefik IngressRoutes for all default ports
	if p.traefikConfig.Enabled {
		if err := p.createIngressRoutes(ctx, projectID); err != nil {
			p.logger.Warn("Failed to create ingress routes", zap.Error(err))
			// Continue without external access
		}
	}

	p.logger.Info("Kubernetes resources created successfully",
		zap.String("podName", createdPod.Name),
		zap.String("namespace", createdPod.Namespace))

	return createdPod.Name, nil
}

// StopContainer deletes pod and all associated resources
func (p *k8sProvider) StopContainer(ctx context.Context, containerID string) error {
	podName := containerID

	// Extract projectID from pod name (assuming format "pod-{projectID}")
	projectID := strings.TrimPrefix(podName, "pod-")

	p.logger.Info("Deleting Kubernetes resources",
		zap.String("podName", podName),
		zap.String("projectID", projectID))

	// Delete all IngressRoutes for this project
	if p.traefikConfig.Enabled {
		if err := p.deleteIngressRoutes(ctx, projectID); err != nil {
			p.logger.Warn("Failed to delete some ingress routes", zap.Error(err))
		}
	}

	// Delete Service
	serviceName := fmt.Sprintf("service-for-%s", projectID)
	err := p.client.CoreV1().Services(p.namespace).Delete(ctx, serviceName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		p.logger.Warn("Failed to delete service", zap.Error(err))
	}

	// Delete Pod
	err = p.client.CoreV1().Pods(p.namespace).Delete(ctx, podName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to delete pod: %w", err)
	}

	// Note: We no longer delete PVC since we're using hostPath volumes for direct project file access

	p.logger.Info("Kubernetes resources deleted successfully", zap.String("projectID", projectID))
	return nil
}

// GetContainerTerminalStream creates an exec session in the pod
func (p *k8sProvider) GetContainerTerminalStream(ctx context.Context, containerID string, cmd []string, workDir string) (types.HijackedResponse, string, error) {
	podName := containerID

	if len(cmd) == 0 {
		cmd = []string{defaultShell}
	}

	p.logger.Info("Creating exec session in pod",
		zap.String("podName", podName),
		zap.Strings("cmd", cmd),
		zap.String("workDir", workDir))

	req := p.client.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(p.namespace).
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Command: cmd,
			Stdin:   true,
			Stdout:  true,
			Stderr:  true,
			TTY:     true,
		}, scheme.ParameterCodec)

	executor, err := remotecommand.NewSPDYExecutor(p.config, "POST", req.URL())
	if err != nil {
		return types.HijackedResponse{}, "", fmt.Errorf("failed to create executor: %w", err)
	}

	// Create context with cancel for the exec session
	execCtx, cancel := context.WithCancel(ctx)

	// Create a stream wrapper to bridge K8s exec to Docker-like HijackedResponse
	streamWrapper := &k8sStreamWrapper{
		executor:  executor,
		logger:    p.logger,
		ctx:       execCtx,
		cancel:    cancel,
		execError: make(chan error, 1),
	}

	// Create pipes for bidirectional communication
	streamWrapper.stdinReader, streamWrapper.stdinWriter = io.Pipe()
	streamWrapper.stdoutReader, streamWrapper.stdoutWriter = io.Pipe()

	// Start the exec session
	if err := streamWrapper.startExecSession(); err != nil {
		return types.HijackedResponse{}, "", fmt.Errorf("failed to start exec session: %w", err)
	}

	execID := fmt.Sprintf("k8s-exec-%s-%d", podName, time.Now().Unix())

	p.logger.Info("Exec session created", zap.String("execID", execID))

	// Create a bufio.Reader from the stream wrapper
	reader := bufio.NewReader(streamWrapper)

	return types.HijackedResponse{
		Conn:   streamWrapper,
		Reader: reader,
	}, execID, nil
}

// ResizeContainerTerminal resizes the PTY of an exec instance
func (p *k8sProvider) ResizeContainerTerminal(ctx context.Context, execID string, rows uint, cols uint) error {
	p.logger.Debug("Resizing terminal",
		zap.String("execID", execID),
		zap.Uint("rows", rows),
		zap.Uint("cols", cols))

	// For K8s, the resize is typically handled by the terminal client
	// This is a placeholder implementation
	return nil
}

// GetContainerStatus retrieves the status of the specified pod
func (p *k8sProvider) GetContainerStatus(ctx context.Context, containerID string) (string, error) {
	podName := containerID

	pod, err := p.client.CoreV1().Pods(p.namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "not_found", nil
		}
		return "unknown", fmt.Errorf("failed to get pod status: %w", err)
	}

	// Convert K8s pod phase to Docker-like status
	switch pod.Status.Phase {
	case corev1.PodRunning:
		return "running", nil
	case corev1.PodPending:
		return "starting", nil
	case corev1.PodSucceeded:
		return "exited", nil
	case corev1.PodFailed:
		return "error", nil
	default:
		return "unknown", nil
	}
}

// GetEnvironmentInfo executes commands within the pod to determine its environment details
func (p *k8sProvider) GetEnvironmentInfo(ctx context.Context, containerID string) (*R.EnvironmentInfo, error) {
	p.logger.Info("Getting environment info for pod", zap.String("podName", containerID))

	// For K8s, we'll return some default values since environment detection
	// is more complex with the exec API
	envInfo := &R.EnvironmentInfo{
		OS:      "linux",
		Distro:  "Unknown",
		Shell:   defaultShell,
		WorkDir: defaultWorkspace,
		HomeDir: "/root",
	}

	return envInfo, nil
}

// ExecuteCommand executes a command in the pod and returns the result
func (p *k8sProvider) ExecuteCommand(ctx context.Context, containerID string, command string, workDir string) (*R.CommandResult, error) {
	podName := containerID

	if workDir == "" {
		workDir = defaultWorkspace
	}

	p.logger.Info("Executing command in pod",
		zap.String("podName", podName),
		zap.String("command", command),
		zap.String("workDir", workDir))

	// Prepare the command to be executed
	// We wrap the user's command in `sh -c '...'` to handle complex commands with pipes, redirection, etc.
	// This also ensures the command respects the specified working directory.
	fullCommand := []string{
		"sh",
		"-c",
		fmt.Sprintf("cd %s && %s", workDir, command),
	}

	// Create the request
	req := p.client.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(p.namespace).
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Command: fullCommand,
			Stdin:   false, // No input needed for non-interactive command
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
		}, scheme.ParameterCodec)

	executor, err := remotecommand.NewSPDYExecutor(p.config, "POST", req.URL())
	if err != nil {
		return nil, fmt.Errorf("failed to create executor for command execution: %w", err)
	}

	// Buffers to capture stdout and stderr
	var stdout, stderr bytes.Buffer

	// Stream the execution
	err = executor.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})

	result := &R.CommandResult{
		Command:  command,
		Stdout:   stdout.String(),
		Stderr:   stderr.String(),
		ExitCode: 0, // Default to 0 (success)
	}

	if err != nil {
		// Try to extract the exit code from the error
		if exitErr, ok := err.(interface{ ExitStatus() int }); ok {
			result.ExitCode = exitErr.ExitStatus()
		} else {
			// For other errors (e.g., network issues), set a generic non-zero exit code
			result.ExitCode = -1
			result.Stderr += "\n---\nExecutor stream error: " + err.Error()
		}
	}

	return result, nil
}

// Helper methods

func (p *k8sProvider) ensureNamespace(ctx context.Context) error {
	_, err := p.client.CoreV1().Namespaces().Get(ctx, p.namespace, metav1.GetOptions{})
	if errors.IsNotFound(err) {
		namespace := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: p.namespace,
			},
		}
		_, err = p.client.CoreV1().Namespaces().Create(ctx, namespace, metav1.CreateOptions{})
		return err
	}
	return err
}

func (p *k8sProvider) createService(ctx context.Context, projectID string) error {
	serviceName := fmt.Sprintf("service-for-%s", projectID)

	// Build service ports from configured default ports
	var servicePorts []corev1.ServicePort
	for _, port := range p.traefikConfig.DefaultPorts {
		servicePorts = append(servicePorts, corev1.ServicePort{
			Name:       fmt.Sprintf("port-%d", port),
			Port:       port,
			TargetPort: intstr.FromInt(int(port)),
			Protocol:   corev1.ProtocolTCP,
		})
	}

	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceName,
			Namespace: p.namespace,
			Labels: map[string]string{
				labelProject: projectID,
			},
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				labelProject: projectID,
			},
			Ports: servicePorts,
			Type:  corev1.ServiceTypeClusterIP,
		},
	}

	_, err := p.client.CoreV1().Services(p.namespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return err
	}
	return nil
}

func (p *k8sProvider) createIngressRoutes(ctx context.Context, projectID string) error {
	serviceName := fmt.Sprintf("service-for-%s", projectID)

	for _, port := range p.traefikConfig.DefaultPorts {
		if err := p.createIngressRoute(ctx, projectID, serviceName, port); err != nil {
			p.logger.Error("Failed to create ingress route for port",
				zap.String("projectID", projectID),
				zap.Int32("port", port),
				zap.Error(err))
			// Continue with other ports
		}
	}
	return nil
}

func (p *k8sProvider) createIngressRoute(ctx context.Context, projectID, serviceName string, port int32) error {
	ingressName := fmt.Sprintf("ingress-for-%s-port-%d", projectID, port)
	host := fmt.Sprintf("%d-%s.%s", port, projectID, p.traefikConfig.Domain)

	// Create IngressRoute as unstructured object for dynamic client
	ingressRoute := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "traefik.io/v1alpha1",
			"kind":       "IngressRoute",
			"metadata": map[string]interface{}{
				"name":      ingressName,
				"namespace": p.namespace,
				"labels": map[string]interface{}{
					labelProject: projectID,
				},
			},
			"spec": map[string]interface{}{
				"entryPoints": p.traefikConfig.EntryPoints,
				"routes": []interface{}{
					map[string]interface{}{
						"match": fmt.Sprintf("Host(`%s`)", host),
						"kind":  "Rule",
						"services": []interface{}{
							map[string]interface{}{
								"name": serviceName,
								"port": port,
							},
						},
					},
				},
			},
		},
	}

	_, err := p.dynamicClient.Resource(ingressRouteGVR).Namespace(p.namespace).Create(
		ctx, ingressRoute, metav1.CreateOptions{})

	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("failed to create IngressRoute for port %d: %w", port, err)
	}

	p.logger.Info("Created IngressRoute",
		zap.String("name", ingressName),
		zap.String("host", host),
		zap.Int32("port", port))

	return nil
}

func (p *k8sProvider) deleteIngressRoutes(ctx context.Context, projectID string) error {
	// List all IngressRoutes with the project label
	listOptions := metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", labelProject, projectID),
	}

	ingressRoutes, err := p.dynamicClient.Resource(ingressRouteGVR).Namespace(p.namespace).List(ctx, listOptions)
	if err != nil {
		return fmt.Errorf("failed to list ingress routes: %w", err)
	}

	// Delete each IngressRoute
	for _, item := range ingressRoutes.Items {
		name := item.GetName()
		err := p.dynamicClient.Resource(ingressRouteGVR).Namespace(p.namespace).Delete(
			ctx, name, metav1.DeleteOptions{})
		if err != nil && !errors.IsNotFound(err) {
			p.logger.Warn("Failed to delete IngressRoute",
				zap.String("name", name),
				zap.Error(err))
		}
	}

	return nil
}

// GetExposedURLs returns the external URLs for a project's services
func (p *k8sProvider) GetExposedURLs(ctx context.Context, projectID string) (map[int32]string, error) {
	urls := make(map[int32]string)

	if !p.traefikConfig.Enabled {
		return urls, nil
	}

	protocol := "http"
	if p.traefikConfig.TLS.Enabled {
		protocol = "https"
	}

	for _, port := range p.traefikConfig.DefaultPorts {
		host := fmt.Sprintf("%d-%s.%s", port, projectID, p.traefikConfig.Domain)
		urls[port] = fmt.Sprintf("%s://%s", protocol, host)
	}

	return urls, nil
}

// k8sStreamWrapper implements io.ReadWriteCloser to bridge K8s exec with Docker API
type k8sStreamWrapper struct {
	executor remotecommand.Executor
	logger   *zap.Logger

	// Pipes for bidirectional communication
	stdinReader  *io.PipeReader
	stdinWriter  *io.PipeWriter
	stdoutReader *io.PipeReader
	stdoutWriter *io.PipeWriter

	// Context for cancellation
	ctx    context.Context
	cancel context.CancelFunc

	// Error channel to capture exec errors
	execError chan error
}

func (w *k8sStreamWrapper) Read(p []byte) (n int, err error) {
	// Read from stdout pipe (K8s exec output → terminal)
	return w.stdoutReader.Read(p)
}

func (w *k8sStreamWrapper) Write(p []byte) (n int, err error) {
	// Write to stdin pipe (terminal input → K8s exec)
	return w.stdinWriter.Write(p)
}

func (w *k8sStreamWrapper) Close() error {
	// Cancel context and close pipes
	if w.cancel != nil {
		w.cancel()
	}

	var errs []error
	if w.stdinWriter != nil {
		errs = append(errs, w.stdinWriter.Close())
	}
	if w.stdinReader != nil {
		errs = append(errs, w.stdinReader.Close())
	}
	if w.stdoutWriter != nil {
		errs = append(errs, w.stdoutWriter.Close())
	}
	if w.stdoutReader != nil {
		errs = append(errs, w.stdoutReader.Close())
	}

	// Return first error if any
	for _, err := range errs {
		if err != nil {
			return err
		}
	}
	return nil
}

// startExecSession starts the K8s exec session in a goroutine
func (w *k8sStreamWrapper) startExecSession() error {
	// Create stream options
	streamOptions := remotecommand.StreamOptions{
		Stdin:  w.stdinReader,
		Stdout: w.stdoutWriter,
		Stderr: w.stdoutWriter, // Combine stderr with stdout
		Tty:    true,
	}

	// Start exec in a goroutine
	go func() {
		defer func() {
			w.stdoutWriter.Close()
			w.stdinReader.Close()
		}()

		w.logger.Debug("Starting K8s exec session")
		err := w.executor.StreamWithContext(w.ctx, streamOptions)
		if err != nil && err != context.Canceled {
			w.logger.Error("K8s exec session failed", zap.Error(err))
			select {
			case w.execError <- err:
			default:
			}
		} else {
			w.logger.Debug("K8s exec session completed normally")
		}
	}()

	return nil
}

// k8sAddr implements net.Addr for K8s exec connections
type k8sAddr struct {
	addr string
}

func (a *k8sAddr) Network() string { return "k8s-exec" }
func (a *k8sAddr) String() string  { return a.addr }

func (w *k8sStreamWrapper) LocalAddr() net.Addr {
	return &k8sAddr{addr: "k8s-exec-local"}
}

func (w *k8sStreamWrapper) RemoteAddr() net.Addr {
	return &k8sAddr{addr: "k8s-pod-remote"}
}

func (w *k8sStreamWrapper) SetDeadline(t time.Time) error {
	return nil
}

func (w *k8sStreamWrapper) SetReadDeadline(t time.Time) error {
	return nil
}

func (w *k8sStreamWrapper) SetWriteDeadline(t time.Time) error {
	return nil
}

// Implement net.Conn interface methods that are needed
func (w *k8sStreamWrapper) CloseConn() error {
	return w.Close()
}

func (w *k8sStreamWrapper) SetHeader(key, value string) {}
func (w *k8sStreamWrapper) Flush() error                { return nil }
