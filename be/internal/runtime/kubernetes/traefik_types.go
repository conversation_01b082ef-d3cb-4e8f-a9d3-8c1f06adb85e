package kubernetes

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// IngressRoute represents a Traefik IngressRoute CRD
type IngressRoute struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              IngressRouteSpec `json:"spec"`
}

// IngressRouteSpec defines the desired state of IngressRoute
type IngressRouteSpec struct {
	EntryPoints []string           `json:"entryPoints,omitempty"`
	Routes      []IngressRouteRule `json:"routes"`
	TLS         *IngressRouteTLS   `json:"tls,omitempty"`
}

// IngressRouteRule holds the rule configuration for an IngressRoute
type IngressRouteRule struct {
	Match       string                   `json:"match"`
	Kind        string                   `json:"kind"`
	Services    []IngressRouteService    `json:"services"`
	Middlewares []IngressRouteMiddleware `json:"middlewares,omitempty"`
}

// IngressRouteService defines a service backend for IngressRoute
type IngressRouteService struct {
	Name   string `json:"name"`
	Port   int32  `json:"port"`
	Weight int32  `json:"weight,omitempty"`
}

// IngressRouteMiddleware defines middleware to apply to the route
type IngressRouteMiddleware struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace,omitempty"`
}

// IngressRouteTLS holds TLS configuration for IngressRoute
type IngressRouteTLS struct {
	SecretName string                  `json:"secretName,omitempty"`
	Domains    []IngressRouteTLSDomain `json:"domains,omitempty"`
}

// IngressRouteTLSDomain holds domain configuration for TLS
type IngressRouteTLSDomain struct {
	Main string   `json:"main"`
	SANs []string `json:"sans,omitempty"`
}

// IngressRouteList contains a list of IngressRoute
type IngressRouteList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []IngressRoute `json:"items"`
}

// DeepCopyInto copies all properties of this object into another object of the same type
func (in *IngressRoute) DeepCopyInto(out *IngressRoute) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy returns a deep copy of the IngressRoute
func (in *IngressRoute) DeepCopy() *IngressRoute {
	if in == nil {
		return nil
	}
	out := new(IngressRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject returns a deep copy of the IngressRoute as runtime.Object
func (in *IngressRoute) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto copies all properties of IngressRouteSpec into another object
func (in *IngressRouteSpec) DeepCopyInto(out *IngressRouteSpec) {
	*out = *in
	if in.EntryPoints != nil {
		in, out := &in.EntryPoints, &out.EntryPoints
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Routes != nil {
		in, out := &in.Routes, &out.Routes
		*out = make([]IngressRouteRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(IngressRouteTLS)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopyInto copies all properties of IngressRouteRule into another object
func (in *IngressRouteRule) DeepCopyInto(out *IngressRouteRule) {
	*out = *in
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make([]IngressRouteService, len(*in))
		copy(*out, *in)
	}
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make([]IngressRouteMiddleware, len(*in))
		copy(*out, *in)
	}
}

// DeepCopyInto copies all properties of IngressRouteTLS into another object
func (in *IngressRouteTLS) DeepCopyInto(out *IngressRouteTLS) {
	*out = *in
	if in.Domains != nil {
		in, out := &in.Domains, &out.Domains
		*out = make([]IngressRouteTLSDomain, len(*in))
		for i := range *in {
			in, out := &(*in)[i], &(*out)[i]
			*out = *in
			if in.SANs != nil {
				in, out := &in.SANs, &out.SANs
				*out = make([]string, len(*in))
				copy(*out, *in)
			}
		}
	}
}
