package docker

import (
	"context"
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/mount"
	"github.com/docker/docker/client"
	"go.uber.org/zap"

	R "git.nevint.com/fota3/t-rex/domain/runtime"
)

const (
	defaultContainerWorkspace = "/workspace"
	defaultShell              = "/bin/bash"
)

// dockerProvider implements the runtime.Provider interface using Docker.
type dockerProvider struct {
	cli    *client.Client
	logger *zap.Logger
}

var _ R.Provider = &dockerProvider{}

// NewDockerProvider creates a new Docker runtime provider.
// It initializes a new Docker client, optionally using the provided dockerHost.
// If dockerHost is empty, it falls back to environment variables (DOCKER_HOST) or Docker defaults.
func NewDockerProvider(logger *zap.Logger, dockerHost string) (<PERSON><PERSON>vide<PERSON>, error) {
	opts := []client.Opt{client.FromEnv, client.WithAPIVersionNegotiation()}

	if dockerHost != "" {
		opts = append(opts, client.WithHost(dockerHost))
		logger.Info("Using explicit Docker host from config for Docker client", zap.String("dockerHost", dockerHost))
	} else {
		logger.Info("Using Docker host from environment variables (DOCKER_HOST) or default socket for Docker client.")
	}

	cli, err := client.NewClientWithOpts(opts...)
	if err != nil {
		logger.Error("Failed to create Docker client", zap.Error(err))
		return nil, fmt.Errorf("failed to create docker client: %w", err)
	}

	// Ping the Docker server to ensure the connection is valid.
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	_, err = cli.Ping(ctx)
	if err != nil {
		logger.Error("Failed to ping Docker server", zap.Error(err))
		_ = cli.Close() // Attempt to close client if ping fails
		return nil, fmt.Errorf("failed to ping docker server: %w", err)
	}

	logger.Info("Docker client initialized and server ping successful")
	return &dockerProvider{
		cli:    cli,
		logger: logger.Named("docker_provider"),
	}, nil
}

// EnsureImage ensures that the specified Docker image is available locally, pulling it if necessary.
func (p *dockerProvider) EnsureImage(ctx context.Context, imageName string) error {
	p.logger.Info("Ensuring image exists", zap.String("imageName", imageName))
	_, err := p.cli.ImageInspect(ctx, imageName)
	if err != nil {
		if client.IsErrNotFound(err) {
			p.logger.Info("Image not found locally, pulling image", zap.String("imageName", imageName))
			out, pullErr := p.cli.ImagePull(ctx, imageName, image.PullOptions{})
			if pullErr != nil {
				p.logger.Error("Failed to pull image", zap.Error(pullErr), zap.String("imageName", imageName))
				return fmt.Errorf("failed to pull image %s: %w", imageName, pullErr)
			}
			defer out.Close()
			// Drain the output to ensure the pull completes, can also log it
			// For now, just drain it.
			_, copyErr := io.Copy(io.Discard, out)
			if copyErr != nil {
				p.logger.Warn("Failed to read image pull output", zap.Error(copyErr), zap.String("imageName", imageName))
				// Not returning error here as pull might have succeeded.
			}
			p.logger.Info("Image pulled successfully", zap.String("imageName", imageName))
			return nil
		} else {
			p.logger.Error("Failed to inspect image", zap.Error(err), zap.String("imageName", imageName))
			return fmt.Errorf("failed to inspect image %s: %w", imageName, err)
		}
	}
	p.logger.Info("Image already exists locally", zap.String("imageName", imageName))
	return nil
}

// StartContainer creates and starts a new container for the given project.
func (p *dockerProvider) StartContainer(ctx context.Context, projectID string, projectHostPath string, imageName string, containerName string) (string, error) {
	p.logger.Info("Starting container",
		zap.String("projectID", projectID),
		zap.String("imageName", imageName),
		zap.String("containerName", containerName),
		zap.String("hostPath", projectHostPath),
	)

	config := &container.Config{
		Image:        imageName,
		Tty:          true, // Keep TTY for interactive shells later
		OpenStdin:    true, // Keep stdin open
		AttachStdin:  true,
		AttachStdout: true,
		AttachStderr: true,
		// Cmd:          []string{defaultShell}, // Command can be overridden by Exec
		WorkingDir: defaultContainerWorkspace, // Default working dir
	}

	hostConfig := &container.HostConfig{
		Mounts: []mount.Mount{
			{
				Type:   mount.TypeBind,
				Source: projectHostPath,
				Target: defaultContainerWorkspace,
			},
		},
		AutoRemove: true, // Automatically remove the container when it exits
	}

	resp, err := p.cli.ContainerCreate(ctx, config, hostConfig, nil, nil, containerName)
	if err != nil {
		// If container with that name already exists, try to remove it and retry (simple conflict resolution)
		if strings.Contains(err.Error(), "is already in use by container") {
			p.logger.Warn("Container name conflict, attempting to remove existing container", zap.String("containerName", containerName))
			// Attempt to remove the existing container by name
			// Note: This might fail if the container is running and not stoppable by a simple remove.
			// A more robust solution would involve stopping it first.
			_ = p.cli.ContainerRemove(ctx, containerName, container.RemoveOptions{Force: true})
			resp, err = p.cli.ContainerCreate(ctx, config, hostConfig, nil, nil, containerName)
		}
		if err != nil {
			p.logger.Error("Failed to create container", zap.Error(err), zap.String("containerName", containerName))
			return "", fmt.Errorf("failed to create container %s: %w", containerName, err)
		}
	}

	if err := p.cli.ContainerStart(ctx, resp.ID, container.StartOptions{}); err != nil {
		p.logger.Error("Failed to start container", zap.Error(err), zap.String("containerID", resp.ID))
		// Attempt to clean up created container if start fails
		_ = p.cli.ContainerRemove(ctx, resp.ID, container.RemoveOptions{Force: true})
		return "", fmt.Errorf("failed to start container %s: %w", resp.ID, err)
	}

	p.logger.Info("Container started successfully", zap.String("containerID", resp.ID), zap.String("containerName", containerName))
	return resp.ID, nil
}

// StopContainer stops and removes the specified container.
func (p *dockerProvider) StopContainer(ctx context.Context, containerID string) error {
	p.logger.Info("Stopping container", zap.String("containerID", containerID))
	// Use a short timeout for stop, then force remove.
	// Docker default stop timeout is 10 seconds.
	timeoutSeconds := 5
	err := p.cli.ContainerStop(ctx, containerID, container.StopOptions{Timeout: &timeoutSeconds})
	if err != nil {
		// If error is because container is already stopped, it's not a critical failure for this op's intent.
		if !strings.Contains(err.Error(), "is already stopped") {
			p.logger.Error("Failed to stop container gracefully, will attempt force remove", zap.Error(err), zap.String("containerID", containerID))
		}
	} else {
		p.logger.Info("Container stopped gracefully", zap.String("containerID", containerID))
	}

	// Always attempt to remove, even if stop failed (it might already be gone or require force)
	// The HostConfig.AutoRemove should handle this if the container exits cleanly,
	// but this is a more explicit cleanup.
	removeErr := p.cli.ContainerRemove(ctx, containerID, container.RemoveOptions{Force: true})
	if removeErr != nil {
		// If error is "No such container", it's acceptable as it's already gone.
		if !client.IsErrNotFound(removeErr) {
			p.logger.Error("Failed to remove container", zap.Error(removeErr), zap.String("containerID", containerID))
			return fmt.Errorf("failed to remove container %s: %w", containerID, removeErr)
		}
		p.logger.Info("Container was already removed or not found during removal attempt", zap.String("containerID", containerID))
	} else {
		p.logger.Info("Container removed successfully", zap.String("containerID", containerID))
	}
	return nil
}

// GetContainerTerminalStream creates an exec instance in the specified container (e.g., /bin/bash)
// and returns a HijackedResponse for interactive terminal communication and the execID.
func (p *dockerProvider) GetContainerTerminalStream(ctx context.Context, containerID string, cmd []string, workDir string) (types.HijackedResponse, string, error) {
	if len(cmd) == 0 {
		cmd = []string{defaultShell}
	}
	if workDir == "" {
		workDir = defaultContainerWorkspace // Default to the mount point
	}
	p.logger.Info("Creating exec instance for terminal",
		zap.String("containerID", containerID),
		zap.Strings("cmd", cmd),
		zap.String("workDir", workDir),
	)

	execOpts := container.ExecOptions{
		AttachStdin:  true,
		AttachStdout: true,
		AttachStderr: true,
		Cmd:          cmd,
		Tty:          true, // Crucial for interactive terminal
		WorkingDir:   workDir,
	}

	execResp, err := p.cli.ContainerExecCreate(ctx, containerID, execOpts)
	if err != nil {
		p.logger.Error("Failed to create exec instance", zap.Error(err), zap.String("containerID", containerID))
		return types.HijackedResponse{}, "", fmt.Errorf("failed to create exec in container %s: %w", containerID, err)
	}

	hijackedResp, err := p.cli.ContainerExecAttach(ctx, execResp.ID, container.ExecAttachOptions{Detach: false, Tty: true})
	if err != nil {
		p.logger.Error("Failed to attach to exec instance", zap.Error(err), zap.String("execID", execResp.ID))
		return types.HijackedResponse{}, "", fmt.Errorf("failed to attach to exec %s: %w", execResp.ID, err)
	}

	p.logger.Info("Exec instance created and attached successfully", zap.String("execID", execResp.ID))
	return hijackedResp, execResp.ID, nil
}

// ResizeContainerTerminal resizes the PTY of an exec instance in a container.
func (p *dockerProvider) ResizeContainerTerminal(ctx context.Context, execID string, rows uint, cols uint) error {
	p.logger.Debug("Resizing PTY for exec instance",
		zap.String("execID", execID),
		zap.Uint("rows", rows),
		zap.Uint("cols", cols),
	)

	if execID == "" {
		p.logger.Error("ResizeContainerTerminal called with empty execID")
		return fmt.Errorf("execID cannot be empty for resize")
	}
	if rows == 0 || cols == 0 {
		p.logger.Error("ResizeContainerTerminal called with zero rows or cols", zap.Uint("rows", rows), zap.Uint("cols", cols))
		return fmt.Errorf("rows and cols must be greater than zero for PTY resize")
	}

	resizeOpts := container.ResizeOptions{Height: rows, Width: cols}

	err := p.cli.ContainerExecResize(ctx, execID, resizeOpts)
	if err != nil {
		if ctx.Err() == context.Canceled || ctx.Err() == context.DeadlineExceeded {
			p.logger.Info("PTY resize aborted due to context cancellation", zap.String("execID", execID), zap.Error(ctx.Err()))
			return nil
		}
		p.logger.Error("Failed to resize PTY for exec instance", zap.Error(err), zap.String("execID", execID))
		return fmt.Errorf("failed to resize PTY for exec %s: %w", execID, err)
	}
	return nil
}

// GetContainerStatus retrieves the status of the specified container.
func (p *dockerProvider) GetContainerStatus(ctx context.Context, containerID string) (string, error) {
	p.logger.Debug("Getting container status", zap.String("containerID", containerID))
	inspectResp, err := p.cli.ContainerInspect(ctx, containerID)
	if err != nil {
		if client.IsErrNotFound(err) {
			p.logger.Info("Container not found while getting status", zap.String("containerID", containerID))
			return "not_found", nil // Or a more specific status/error
		}
		p.logger.Error("Failed to inspect container for status", zap.Error(err), zap.String("containerID", containerID))
		return "unknown", fmt.Errorf("failed to inspect container %s: %w", containerID, err)
	}

	if inspectResp.State == nil {
		p.logger.Warn("Container inspect response state is nil", zap.String("containerID", containerID))
		return "unknown", nil
	}
	p.logger.Info("Container status retrieved", zap.String("containerID", containerID), zap.String("status", inspectResp.State.Status))
	return inspectResp.State.Status, nil // e.g., "running", "paused", "exited"
}

// GetEnvironmentInfo executes commands within the container to determine its environment details.
func (p *dockerProvider) GetEnvironmentInfo(ctx context.Context, containerID string) (*R.EnvironmentInfo, error) {
	p.logger.Info("Getting environment info for container", zap.String("containerID", containerID))

	// Get OS and Distro from /etc/os-release
	osRelease, err := p.execInContainer(ctx, containerID, []string{"cat", "/etc/os-release"})
	if err != nil {
		p.logger.Warn("Could not read /etc/os-release, OS/Distro will be unknown.", zap.Error(err))
		osRelease = "" // Don't fail, just leave it blank
	}
	osRelease = cleanTerminalOutput(osRelease)
	distro := parseOSRelease(osRelease, "PRETTY_NAME")
	os := "linux" // Assume linux, as that's what /etc/os-release implies

	// Get default shell
	shell, err := p.execInContainer(ctx, containerID, []string{"sh", "-c", "echo $SHELL"})
	if err != nil {
		p.logger.Warn("Could not determine shell, falling back to default.", zap.Error(err), zap.String("default", defaultShell))
		shell = defaultShell // Fallback
	}
	shell = cleanTerminalOutput(shell)

	// Get current working directory
	workDir, err := p.execInContainer(ctx, containerID, []string{"pwd"})
	if err != nil {
		p.logger.Warn("Could not determine working directory, falling back to default.", zap.Error(err), zap.String("default", defaultContainerWorkspace))
		workDir = defaultContainerWorkspace // Fallback
	}
	workDir = cleanTerminalOutput(workDir)

	// Get home directory
	homeDir, err := p.execInContainer(ctx, containerID, []string{"sh", "-c", "echo $HOME"})
	if err != nil {
		p.logger.Warn("Could not determine home directory.", zap.Error(err))
		homeDir = "" // Can be blank if not found
	}
	homeDir = cleanTerminalOutput(homeDir)

	envInfo := &R.EnvironmentInfo{
		OS:      strings.TrimSpace(os),
		Distro:  strings.TrimSpace(distro),
		Shell:   strings.TrimSpace(shell),
		WorkDir: strings.TrimSpace(workDir),
		HomeDir: strings.TrimSpace(homeDir),
	}

	p.logger.Info("Successfully retrieved environment info", zap.Any("info", envInfo))
	return envInfo, nil
}

// execInContainer is a helper function to execute a command in a container and get its output.
func (p *dockerProvider) execInContainer(ctx context.Context, containerID string, cmd []string) (string, error) {
	execOpts := container.ExecOptions{
		Cmd:          cmd,
		AttachStdout: true,
		AttachStderr: true, // Also attach stderr to see errors
	}

	execID, err := p.cli.ContainerExecCreate(ctx, containerID, execOpts)
	if err != nil {
		return "", fmt.Errorf("failed to create exec for command %v: %w", cmd, err)
	}

	hijackedResp, err := p.cli.ContainerExecAttach(ctx, execID.ID, container.ExecAttachOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to attach to exec for command %v: %w", cmd, err)
	}
	defer hijackedResp.Close()

	output, err := io.ReadAll(hijackedResp.Reader)
	if err != nil {
		return "", fmt.Errorf("failed to read output from exec for command %v: %w", cmd, err)
	}

	// Inspect the exec instance to check the exit code
	inspect, err := p.cli.ContainerExecInspect(ctx, execID.ID)
	if err != nil {
		return "", fmt.Errorf("failed to inspect exec for command %v: %w", cmd, err)
	}

	if inspect.ExitCode != 0 {
		return "", fmt.Errorf("command %v exited with code %d: %s", cmd, inspect.ExitCode, string(output))
	}

	return string(output), nil
}

// parseOSRelease is a helper to parse simple key-value files like /etc/os-release.
func parseOSRelease(content, key string) string {
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, key+"=") {
			value := strings.TrimPrefix(line, key+"=")
			// Unquote if necessary
			if strings.HasPrefix(value, `"`) && strings.HasSuffix(value, `"`) {
				return value[1 : len(value)-1]
			}
			return value
		}
	}
	return ""
}

// cleanTerminalOutput removes ANSI escape sequences and non-printable characters from command output
func cleanTerminalOutput(output string) string {
	if output == "" {
		return ""
	}

	// Regular expression to match ANSI escape sequences
	// This pattern matches: ESC[ followed by any number of digits, semicolons, and ends with a letter
	ansiRegex := regexp.MustCompile(`\x1b\[[0-9;]*[a-zA-Z]`)

	// Remove ANSI escape sequences
	cleaned := ansiRegex.ReplaceAllString(output, "")

	// Remove other control characters (except newline, tab, and carriage return)
	var result strings.Builder
	for _, r := range cleaned {
		// Keep printable characters, newlines, tabs, and spaces
		if unicode.IsPrint(r) || r == '\n' || r == '\t' || r == '\r' {
			result.WriteRune(r)
		}
	}

	// Replace any remaining non-ASCII characters that might cause encoding issues
	finalResult := strings.Map(func(r rune) rune {
		if r > 127 {
			return ' '
		}
		return r
	}, result.String())

	return strings.TrimSpace(finalResult)
}

// ExecuteCommand executes a command in the container and returns stdout, stderr, and exit code
func (p *dockerProvider) ExecuteCommand(ctx context.Context, containerID string, command string, workDir string) (*R.CommandResult, error) {
	if workDir == "" {
		workDir = defaultContainerWorkspace
	}

	p.logger.Info("Executing command in container",
		zap.String("containerID", containerID),
		zap.String("command", command),
		zap.String("workDir", workDir))

	// Use bash to execute the command for better compatibility
	cmd := []string{"/bin/bash", "-c", command}
	output, exitCode, err := p.execInContainerWithExitCode(ctx, containerID, cmd, workDir)
	if err != nil {
		return nil, fmt.Errorf("failed to execute command: %w", err)
	}

	result := &R.CommandResult{
		Command:  command,
		ExitCode: exitCode,
	}

	// Clean the output and categorize as stdout or stderr based on exit code
	outputStr := cleanTerminalOutput(output)
	if exitCode == 0 {
		result.Stdout = outputStr
		result.Stderr = ""
	} else {
		// For failed commands, we put output in stderr
		// Note: Docker exec combines stdout and stderr, so this is a simplification
		result.Stdout = ""
		result.Stderr = outputStr
	}

	p.logger.Info("Command execution completed",
		zap.String("command", command),
		zap.Int("exitCode", exitCode),
		zap.Int("outputLength", len(outputStr)))

	return result, nil
}

// execInContainerWithExitCode is an enhanced version of execInContainer that returns exit code
func (p *dockerProvider) execInContainerWithExitCode(ctx context.Context, containerID string, cmd []string, workDir string) (string, int, error) {
	if workDir == "" {
		workDir = defaultContainerWorkspace
	}

	execOpts := container.ExecOptions{
		Cmd:          cmd,
		AttachStdout: true,
		AttachStderr: true,
		WorkingDir:   workDir,
	}

	execResp, err := p.cli.ContainerExecCreate(ctx, containerID, execOpts)
	if err != nil {
		return "", -1, fmt.Errorf("failed to create exec for command %v: %w", cmd, err)
	}

	hijackedResp, err := p.cli.ContainerExecAttach(ctx, execResp.ID, container.ExecAttachOptions{})
	if err != nil {
		return "", -1, fmt.Errorf("failed to attach to exec for command %v: %w", cmd, err)
	}
	defer hijackedResp.Close()

	output, err := io.ReadAll(hijackedResp.Reader)
	if err != nil {
		return "", -1, fmt.Errorf("failed to read output from exec for command %v: %w", cmd, err)
	}

	// Inspect the exec instance to check the exit code
	inspect, err := p.cli.ContainerExecInspect(ctx, execResp.ID)
	if err != nil {
		return "", -1, fmt.Errorf("failed to inspect exec for command %v: %w", cmd, err)
	}

	return string(output), inspect.ExitCode, nil
}
