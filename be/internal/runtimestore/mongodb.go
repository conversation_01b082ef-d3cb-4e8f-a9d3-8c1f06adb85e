package runtimestore

import (
	"context"
	"errors"
	"time"

	"git.nevint.com/fota3/t-rex/domain/runtime"
	model "git.nevint.com/fota3/t-rex/model/runtime"
	"git.nevint.com/fota3/t-rex/pkg/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// mongoRepository implements the runtime.Repository interface using MongoDB via pkg/mongodb.ORM.
// English: mongoRepository implements the runtime.Repository interface using MongoDB via pkg/mongodb.ORM.
type mongoRepository struct {
	orm    *mongodb.ORM[model.Runtime]
	logger *zap.Logger
}

// NewMongoRepository creates a new mongoRepository instance.
// It expects an initialized mongo.Client and the target database name.
// English: NewMongoRepository creates a new mongoRepository instance.
// It expects an initialized mongo.Client and the target database name.
func NewMongoRepository(client *mongo.Client, dbName string, logger *zap.Logger) (runtime.Repository, error) {
	ormInstance, err := mongodb.New[model.Runtime](client, dbName)
	if err != nil {
		logger.Error("Failed to create ORM for runtime store", zap.Error(err), zap.String("dbName", dbName))
		return nil, err
	}
	return &mongoRepository{
		orm:    ormInstance,
		logger: logger.Named("runtimestore"),
	}, nil
}

// Create persists a new runtime record.
// English: Create persists a new runtime record.
func (r *mongoRepository) Create(ctx context.Context, rt *model.Runtime) error {
	if rt.ID == "" {
		rt.ID = primitive.NewObjectID().Hex() // Generate ID if not provided
	}
	rt.CreatedAt = time.Now()
	rt.UpdatedAt = time.Now()

	_, err := r.orm.Insert(ctx, *rt) // Pass by value as Insert expects T, not *T
	if err != nil {
		r.logger.Error("Failed to create runtime record using ORM", zap.Error(err), zap.Any("runtime", rt))
		return err
	}
	r.logger.Info("Runtime record created using ORM", zap.String("runtimeID", rt.ID), zap.String("projectID", rt.ProjectID))
	return nil
}

// FindByProjectID retrieves a runtime record by its associated project ID.
// English: FindByProjectID retrieves a runtime record by its associated project ID.
func (r *mongoRepository) FindByProjectID(ctx context.Context, projectID string) (*model.Runtime, error) {
	rt, err := r.orm.FindOneByField(ctx, "project_id", projectID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			r.logger.Debug("Runtime record not found by projectID using ORM", zap.String("projectID", projectID))
			return nil, runtime.ErrRuntimeNotFound
		}
		r.logger.Error("Failed to find runtime by projectID using ORM", zap.Error(err), zap.String("projectID", projectID))
		return nil, err
	}
	r.logger.Debug("Runtime record found by projectID using ORM", zap.String("projectID", projectID), zap.String("runtimeID", rt.ID))
	return &rt, nil
}

// FindByID retrieves a runtime record by its unique ID.
// English: FindByID retrieves a runtime record by its unique ID.
func (r *mongoRepository) FindByID(ctx context.Context, id string) (*model.Runtime, error) {
	// Assuming ID is the MongoDB _id field.
	// The ORM's FindOneByField can be used if _id is treated as a regular field name.
	// Or, if ID is always a hex string of an ObjectID, we might need to convert it first if the ORM expects primitive.ObjectID for _id queries.
	// For simplicity, using FindOneByFilter with bson.M for _id.
	rt, err := r.orm.FindOneByFilter(ctx, bson.M{"_id": id})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			r.logger.Debug("Runtime record not found by ID using ORM", zap.String("runtimeID", id))
			return nil, nil
		}
		r.logger.Error("Failed to find runtime by ID using ORM", zap.Error(err), zap.String("runtimeID", id))
		return nil, err
	}
	r.logger.Debug("Runtime record found by ID using ORM", zap.String("runtimeID", rt.ID))
	return &rt, nil
}

// Update modifies an existing runtime record.
// English: Update modifies an existing runtime record.
func (r *mongoRepository) Update(ctx context.Context, rt *model.Runtime) error {
	rt.UpdatedAt = time.Now()

	filter := bson.M{"_id": rt.ID}
	// The ORM UpdateSet expects the fields to be set, not the whole object for $set.
	// We need to pass the rt object itself (or a BSON M representation of its fields for $set)
	// if we want to replace/update all fields except UpdatedAt which is set above.
	// For a full replacement, the ORM provides Replace. For partial update, use UpdateSet with specific fields.
	// Given current struct, and wanting to update based on its state, a simple $set of the object is fine.
	// Note: If rt contains fields that are zero-valued and shouldn't overwrite existing db values, this needs care.
	// The original approach of `update := bson.M{"$set": rt}` effectively does this.
	// Let's use orm.UpdateSet with rt, assuming it handles the $set correctly or we adapt.
	// The orm.UpdateSet method takes `fields any`. If `rt` is passed, it needs to be what $set expects.
	// A safer way with the current ORM is to explicitly build the $set document.
	// updateDoc := bson.M{} // This line should be removed or the variable used.
	// Selectively add fields to updateDoc to avoid zero-value overwrites if that's a concern
	// For now, assuming a full update of fields present in rt is intended via $set:
	// This might require converting rt to bson.M or ensuring ORM handles struct correctly in $set.
	// A simpler way if the ORM's Update expects the full update document for $set:
	// Check ORM's UpdateSet signature: `UpdateSet(ctx, filter, fields any)`
	// If `fields` is just the data for $set, not the $set operator itself:
	result, err := r.orm.UpdateSet(ctx, filter, rt) // Pass the object for $set fields
	if err != nil {
		r.logger.Error("Failed to update runtime record using ORM", zap.Error(err), zap.String("runtimeID", rt.ID))
		return err
	}

	if result.MatchedCount == 0 {
		r.logger.Warn("Attempted to update non-existent runtime record using ORM", zap.String("runtimeID", rt.ID))
		return mongo.ErrNoDocuments
	}
	r.logger.Info("Runtime record updated using ORM", zap.String("runtimeID", rt.ID))
	return nil
}

// Delete removes a runtime record by its ID.
// English: Delete removes a runtime record by its ID.
func (r *mongoRepository) Delete(ctx context.Context, id string) error {
	filter := bson.M{"_id": id}
	result, err := r.orm.Delete(ctx, filter)
	if err != nil {
		r.logger.Error("Failed to delete runtime record using ORM", zap.Error(err), zap.String("runtimeID", id))
		return err
	}
	if result.DeletedCount == 0 {
		r.logger.Warn("Attempted to delete non-existent runtime record using ORM", zap.String("runtimeID", id))
		return mongo.ErrNoDocuments
	}
	r.logger.Info("Runtime record deleted using ORM", zap.String("runtimeID", id))
	return nil
}
