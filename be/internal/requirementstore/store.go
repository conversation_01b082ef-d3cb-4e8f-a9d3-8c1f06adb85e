package requirementstore

import (
	"context"
	"errors"
	"fmt"
	"time"

	requirementdomain "git.nevint.com/fota3/t-rex/domain/requirement" // Import domain interfaces
	model "git.nevint.com/fota3/t-rex/model/requirement"              // Import requirement model
	"git.nevint.com/fota3/t-rex/pkg/mongodb"                          // Import the ORM package
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Store implements the requirement.RequirementRepository interface using MongoDB ORM.
// It handles persistence for user requirements, questions, and answers.
type Store struct {
	orm *mongodb.ORM[model.Requirement]
}

// NewStore creates a new store for requirements using the ORM.
func NewStore(cli *mongo.Client, dbName string) (*Store, error) {
	if cli == nil {
		return nil, errors.New("mongo client is nil")
	}
	if dbName == "" {
		return nil, errors.New("database name is empty")
	}
	orm, err := mongodb.New[model.Requirement](cli, dbName)
	if err != nil {
		return nil, fmt.Errorf("failed to create requirement ORM: %w", err)
	}

	// Create indexes for efficient querying
	coll := orm.Collection() // Get the collection using the collection method

	// Index on user_id for faster lookup of user's requirements
	_, err = coll.Indexes().CreateOne(context.Background(), mongo.IndexModel{
		Keys: bson.D{{Key: "user_id", Value: 1}},
	})
	if err != nil {
		// Don't fail if index creation fails (might already exist), but log it.
		fmt.Printf("Warning: Failed to create index on user_id (may already exist): %v\n", err)
	}

	// Index on status for faster status-based queries
	_, err = coll.Indexes().CreateOne(context.Background(), mongo.IndexModel{
		Keys: bson.D{{Key: "status", Value: 1}},
	})
	if err != nil {
		fmt.Printf("Warning: Failed to create index on status (may already exist): %v\n", err)
	}

	// Compound index on user_id + status for efficient combined queries
	_, err = coll.Indexes().CreateOne(context.Background(), mongo.IndexModel{
		Keys: bson.D{{Key: "user_id", Value: 1}, {Key: "status", Value: 1}},
	})
	if err != nil {
		fmt.Printf("Warning: Failed to create compound index on user_id+status (may already exist): %v\n", err)
	}

	return &Store{orm: orm}, nil
}

// Compile-time check to ensure Store implements the interface.
var _ requirementdomain.RequirementRepository = (*Store)(nil)

// Create inserts a new requirement using the ORM.
func (s *Store) Create(ctx context.Context, req *model.Requirement) error {
	if req.ID.IsZero() {
		req.ID = primitive.NewObjectID()
	}
	now := time.Now()
	if req.CreatedAt.IsZero() {
		req.CreatedAt = now
	}
	if req.UpdatedAt.IsZero() {
		req.UpdatedAt = now
	}
	// Set default status if not provided
	if req.Status == "" {
		req.Status = "pending"
	}

	_, err := s.orm.Insert(ctx, *req)
	if err != nil {
		return fmt.Errorf("%w", requirementdomain.RepositoryError(err))
	}
	return nil
}

// FindByID retrieves a requirement by its MongoDB ObjectID using the ORM.
func (s *Store) FindByID(ctx context.Context, id primitive.ObjectID) (*model.Requirement, error) {
	filter := bson.M{"_id": id}

	foundRequirement, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, requirementdomain.ErrNotFound
		}
		return nil, fmt.Errorf("%w: finding by ID", requirementdomain.RepositoryError(err))
	}

	return &foundRequirement, nil
}

// FindByUserID retrieves all requirements associated with a user ID.
func (s *Store) FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*model.Requirement, error) {
	filter := bson.M{"user_id": userID}
	// Sort by creation date, newest first
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Use the Find method provided by the ORM's Collection()
	coll := s.orm.Collection()
	cursor, err := coll.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("%w: finding by user_id (cursor error)", requirementdomain.RepositoryError(err))
	}
	defer cursor.Close(ctx)

	var requirements []*model.Requirement
	if err = cursor.All(ctx, &requirements); err != nil {
		return nil, fmt.Errorf("%w: finding by user_id (decoding error)", requirementdomain.RepositoryError(err))
	}

	// Return an empty slice if no requirements found, not an error
	if requirements == nil {
		requirements = []*model.Requirement{}
	}
	return requirements, nil
}

// FindByUserIDWithPagination retrieves requirements for a user with pagination support.
func (s *Store) FindByUserIDWithPagination(ctx context.Context, userID primitive.ObjectID, page, limit int) ([]*model.Requirement, int64, error) {
	filter := bson.M{"user_id": userID}

	// Get total count first
	coll := s.orm.Collection()
	totalCount, err := coll.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("%w: counting documents", requirementdomain.RepositoryError(err))
	}

	// Calculate skip value for pagination
	skip := (page - 1) * limit
	if skip < 0 {
		skip = 0
	}

	// Sort by creation date, newest first
	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetSkip(int64(skip)).
		SetLimit(int64(limit))

	cursor, err := coll.Find(ctx, filter, opts)
	if err != nil {
		return nil, totalCount, fmt.Errorf("%w: finding with pagination (cursor error)", requirementdomain.RepositoryError(err))
	}
	defer cursor.Close(ctx)

	var requirements []*model.Requirement
	if err = cursor.All(ctx, &requirements); err != nil {
		return nil, totalCount, fmt.Errorf("%w: finding with pagination (decoding error)", requirementdomain.RepositoryError(err))
	}

	// Return an empty slice if no requirements found, not an error
	if requirements == nil {
		requirements = []*model.Requirement{}
	}
	return requirements, totalCount, nil
}

// Update updates an existing requirement record using the ORM.
func (s *Store) Update(ctx context.Context, req *model.Requirement) error {
	if req.ID.IsZero() {
		return errors.New("cannot update requirement without ID")
	}

	filter := bson.M{"_id": req.ID}
	req.UpdatedAt = time.Now() // Always update the timestamp

	// Prepare update data - replacing the document but keeping original _id
	updateData := *req

	// Use the UpdateSet method from the ORM which automatically wraps with $set
	result, err := s.orm.UpdateSet(ctx, filter, updateData)
	if err != nil {
		return fmt.Errorf("%w: updating requirement", requirementdomain.RepositoryError(err))
	}

	if result.MatchedCount == 0 {
		return requirementdomain.ErrNotFound
	}

	return nil
}

// Delete removes a requirement record by its MongoDB ObjectID.
func (s *Store) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	// Use the DeleteOne method provided by the ORM's Collection()
	coll := s.orm.Collection()
	result, err := coll.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("%w: deleting by _id", requirementdomain.RepositoryError(err))
	}
	if result.DeletedCount == 0 {
		return requirementdomain.ErrNotFound
	}
	return nil
}

// FindByStatus retrieves requirements by their processing status.
func (s *Store) FindByStatus(ctx context.Context, status string) ([]*model.Requirement, error) {
	filter := bson.M{"status": status}
	// Sort by creation date, newest first
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Use the Find method provided by the ORM's Collection()
	coll := s.orm.Collection()
	cursor, err := coll.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("%w: finding by status (cursor error)", requirementdomain.RepositoryError(err))
	}
	defer cursor.Close(ctx)

	var requirements []*model.Requirement
	if err = cursor.All(ctx, &requirements); err != nil {
		return nil, fmt.Errorf("%w: finding by status (decoding error)", requirementdomain.RepositoryError(err))
	}

	// Return an empty slice if no requirements found, not an error
	if requirements == nil {
		requirements = []*model.Requirement{}
	}
	return requirements, nil
}

// FindByUserIDAndStatus retrieves requirements for a specific user filtered by status.
func (s *Store) FindByUserIDAndStatus(ctx context.Context, userID primitive.ObjectID, status string) ([]*model.Requirement, error) {
	filter := bson.M{"user_id": userID, "status": status}
	// Sort by creation date, newest first
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Use the Find method provided by the ORM's Collection()
	coll := s.orm.Collection()
	cursor, err := coll.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("%w: finding by user_id and status (cursor error)", requirementdomain.RepositoryError(err))
	}
	defer cursor.Close(ctx)

	var requirements []*model.Requirement
	if err = cursor.All(ctx, &requirements); err != nil {
		return nil, fmt.Errorf("%w: finding by user_id and status (decoding error)", requirementdomain.RepositoryError(err))
	}

	// Return an empty slice if no requirements found, not an error
	if requirements == nil {
		requirements = []*model.Requirement{}
	}
	return requirements, nil
}
