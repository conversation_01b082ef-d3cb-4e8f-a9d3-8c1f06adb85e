package llmstore

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	modelLLM "git.nevint.com/fota3/t-rex/model/llm" // Import the LLMConfig model
)

// LLMConfigRepository defines the persistence operations for LLMConfig.
type LLMConfigRepository interface {
	CreateLLMConfig(ctx context.Context, config *modelLLM.LLMConfig) error
	GetLLMConfigByID(ctx context.Context, id string) (*modelLLM.LLMConfig, error)
	ListLLMConfigs(ctx context.Context) ([]modelLLM.LLMConfig, error)
	UpdateLLMConfig(ctx context.Context, id string, updates map[string]interface{}) error
	DeleteLLMConfig(ctx context.Context, id string) error
	GetActiveLLMConfigID(ctx context.Context) (string, error)  // To store and retrieve the active LLM ID
	SetActiveLLMConfigID(ctx context.Context, id string) error // To set the active LLM ID
}

// store implements LLMConfigRepository using MongoDB.
type store struct {
	db                     *mongo.Database
	collection             *mongo.Collection
	activeConfigCollection *mongo.Collection // Collection to store the active LLM config ID
	logger                 *zap.Logger
}

// NewStore creates a new MongoDB store for LLMConfig.
func NewStore(client *mongo.Client, dbName string, logger *zap.Logger) (LLMConfigRepository, error) {
	if logger == nil {
		logger = zap.NewNop()
	}
	db := client.Database(dbName)
	collection := db.Collection((&modelLLM.LLMConfig{}).CollectionName())
	activeConfigCollection := db.Collection("global_settings") // A generic collection for global settings

	// Ensure unique index on name for LLMConfigs if desired
	// indexModel := mongo.IndexModel{
	// 	Keys:    bson.D{{Key: "name", Value: 1}},
	// 	Options: options.Index().SetUnique(true),
	// }
	// _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to create unique index for llm_configs: %w", err)
	// }

	logger.Info("LLMConfig store initialized", zap.String("collection", (&modelLLM.LLMConfig{}).CollectionName()))
	return &store{
		db:                     db,
		collection:             collection,
		activeConfigCollection: activeConfigCollection,
		logger:                 logger.Named("LLMStore"),
	}, nil
}

// CreateLLMConfig inserts a new LLM configuration into MongoDB.
func (s *store) CreateLLMConfig(ctx context.Context, config *modelLLM.LLMConfig) error {
	config.ID = primitive.NewObjectID()
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	_, err := s.collection.InsertOne(ctx, config)
	if err != nil {
		return fmt.Errorf("failed to create LLM config: %w", err)
	}
	s.logger.Debug("LLM config created", zap.String("id", config.ID.Hex()), zap.String("name", config.Name))
	return nil
}

// GetLLMConfigByID retrieves an LLM configuration by its ID.
func (s *store) GetLLMConfigByID(ctx context.Context, id string) (*modelLLM.LLMConfig, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid LLM config ID format: %w", err)
	}

	var config modelLLM.LLMConfig
	filter := bson.M{"_id": objID}
	err = s.collection.FindOne(ctx, filter).Decode(&config)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // Not found
		}
		return nil, fmt.Errorf("failed to get LLM config by ID %s: %w", id, err)
	}
	return &config, nil
}

// ListLLMConfigs retrieves all LLM configurations.
func (s *store) ListLLMConfigs(ctx context.Context) ([]modelLLM.LLMConfig, error) {
	var configs []modelLLM.LLMConfig
	cursor, err := s.collection.Find(ctx, bson.M{}, options.Find().SetSort(bson.M{"name": 1}))
	if err != nil {
		return nil, fmt.Errorf("failed to list LLM configs: %w", err)
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &configs); err != nil {
		return nil, fmt.Errorf("failed to decode LLM configs: %w", err)
	}
	s.logger.Debug("Listed LLM configs", zap.Int("count", len(configs)))
	return configs, nil
}

// UpdateLLMConfig updates an existing LLM configuration.
func (s *store) UpdateLLMConfig(ctx context.Context, id string, updates map[string]interface{}) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid LLM config ID format: %w", err)
	}

	updates["updated_at"] = time.Now() // Automatically update timestamp
	updateDoc := bson.M{"$set": updates}

	result, err := s.collection.UpdateByID(ctx, objID, updateDoc)
	if err != nil {
		return fmt.Errorf("failed to update LLM config %s: %w", id, err)
	}
	if result.ModifiedCount == 0 {
		// This might mean ID not found or no actual change was made
		s.logger.Warn("LLM config update: no documents modified", zap.String("id", id))
	} else {
		s.logger.Debug("LLM config updated", zap.String("id", id))
	}
	return nil
}

// DeleteLLMConfig deletes an LLM configuration by its ID.
func (s *store) DeleteLLMConfig(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid LLM config ID format: %w", err)
	}

	result, err := s.collection.DeleteOne(ctx, bson.M{"_id": objID})
	if err != nil {
		return fmt.Errorf("failed to delete LLM config %s: %w", id, err)
	}
	if result.DeletedCount == 0 {
		return fmt.Errorf("LLM config with ID %s not found", id)
	}
	s.logger.Debug("LLM config deleted", zap.String("id", id))
	return nil
}

// ActiveLLMConfigDocumentID is the fixed document ID for the global active LLM setting.
const ActiveLLMConfigDocumentID = "active_llm_config_id"

// GetActiveLLMConfigID retrieves the ID of the currently active LLM configuration.
func (s *store) GetActiveLLMConfigID(ctx context.Context) (string, error) {
	var result struct {
		ID          string `bson:"_id"`
		LLMConfigID string `bson:"llm_config_id"`
	}
	filter := bson.M{"_id": ActiveLLMConfigDocumentID}
	err := s.activeConfigCollection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			s.logger.Info("No active LLM config ID found in settings.")
			return "", nil // No active config set yet
		}
		return "", fmt.Errorf("failed to get active LLM config ID: %w", err)
	}
	return result.LLMConfigID, nil
}

// SetActiveLLMConfigID sets the ID of the currently active LLM configuration.
func (s *store) SetActiveLLMConfigID(ctx context.Context, id string) error {
	updateDoc := bson.M{
		"$set": bson.M{
			"llm_config_id": id,
			"updated_at":    time.Now(),
		},
	}
	opts := options.Update().SetUpsert(true) // Create if not exists

	_, err := s.activeConfigCollection.UpdateByID(ctx, ActiveLLMConfigDocumentID, updateDoc, opts)
	if err != nil {
		return fmt.Errorf("failed to set active LLM config ID to %s: %w", id, err)
	}
	s.logger.Info("Active LLM config ID set", zap.String("id", id))
	return nil
}
