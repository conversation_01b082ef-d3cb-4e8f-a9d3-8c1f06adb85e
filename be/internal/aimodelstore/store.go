package aimodelstore

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.nevint.com/fota3/t-rex/model/aimodel"
)

// Store provides MongoDB storage operations for AI models
type Store struct {
	collection *mongo.Collection
}

// NewStore creates a new AI model store instance
func NewStore(client *mongo.Client, database string) (*Store, error) {
	if client == nil {
		return nil, fmt.Errorf("mongo client cannot be nil")
	}
	if database == "" {
		return nil, fmt.Errorf("database name cannot be empty")
	}

	collection := client.Database(database).Collection(aimodel.AIModel{}.CollectionName())

	return &Store{
		collection: collection,
	}, nil
}

// Create creates a new AI model in the database
func (s *Store) Create(ctx context.Context, model *aimodel.AIModel) error {
	if model == nil {
		return fmt.Errorf("model cannot be nil")
	}

	now := time.Now()
	model.CreatedAt = now
	model.UpdatedAt = now

	result, err := s.collection.InsertOne(ctx, model)
	if err != nil {
		return fmt.Errorf("failed to create AI model: %w", err)
	}

	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		model.ID = oid
	}

	return nil
}

// GetByID retrieves an AI model by its ID
func (s *Store) GetByID(ctx context.Context, id primitive.ObjectID) (*aimodel.AIModel, error) {
	var model aimodel.AIModel
	err := s.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("AI model not found")
		}
		return nil, fmt.Errorf("failed to get AI model: %w", err)
	}
	return &model, nil
}

// GetByModelName retrieves an AI model by its model name
func (s *Store) GetByModelName(ctx context.Context, modelName string) (*aimodel.AIModel, error) {
	var model aimodel.AIModel
	err := s.collection.FindOne(ctx, bson.M{"model_name": modelName}).Decode(&model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("AI model not found")
		}
		return nil, fmt.Errorf("failed to get AI model: %w", err)
	}
	return &model, nil
}

// List retrieves all AI models with optional filtering
func (s *Store) List(ctx context.Context, activeOnly bool) ([]*aimodel.AIModel, error) {
	filter := bson.M{}
	if activeOnly {
		filter["is_active"] = true
	}

	cursor, err := s.collection.Find(ctx, filter, options.Find().SetSort(bson.D{{"created_at", -1}}))
	if err != nil {
		return nil, fmt.Errorf("failed to list AI models: %w", err)
	}
	defer cursor.Close(ctx)

	var models []*aimodel.AIModel
	for cursor.Next(ctx) {
		var model aimodel.AIModel
		if err := cursor.Decode(&model); err != nil {
			return nil, fmt.Errorf("failed to decode AI model: %w", err)
		}
		models = append(models, &model)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return models, nil
}

// Update updates an existing AI model
func (s *Store) Update(ctx context.Context, id primitive.ObjectID, updates bson.M) error {
	updates["updated_at"] = time.Now()

	result, err := s.collection.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": updates})
	if err != nil {
		return fmt.Errorf("failed to update AI model: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("AI model not found")
	}

	return nil
}

// Delete deletes an AI model by ID
func (s *Store) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := s.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete AI model: %w", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("AI model not found")
	}

	return nil
}
