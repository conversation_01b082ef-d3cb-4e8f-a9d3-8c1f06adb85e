package localfs

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings" // 需要导入 strings 包

	"git.nevint.com/fota3/t-rex/pkg/storage" // 导入我们定义的接口包
)

// ErrPathExists indicates that a file or directory already exists at the target path.
var ErrPathExists = errors.New("path already exists")

// LocalFSStorage 实现了 ProjectStorage 接口，使用本地文件系统作为后端。
type LocalFSStorage struct {
	workspacesBasePath string // 存储所有项目工作区的根目录绝对路径
}

// NewLocalFSStorage 创建一个新的 LocalFSStorage 实例。
// basePath 是存储所有项目工作区的根目录。
func NewLocalFSStorage(basePath string) (*LocalFSStorage, error) {
	// 确保 basePath 是绝对路径
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return nil, fmt.Errorf("invalid base path '%s': %w", basePath, err)
	}

	// 确保 basePath 存在，如果不存在则尝试创建
	info, err := os.Stat(absBasePath)
	if err != nil {
		if os.IsNotExist(err) {
			// 尝试创建目录
			if mkErr := os.MkdirAll(absBasePath, 0750); mkErr != nil { // 使用 0750 权限
				return nil, fmt.Errorf("failed to create base path '%s': %w", absBasePath, mkErr)
			}
			fmt.Printf("Created workspaces base directory: %s\n", absBasePath)
		} else {
			// 其他 Stat 错误
			return nil, fmt.Errorf("failed to stat base path '%s': %w", absBasePath, err)
		}
	} else if !info.IsDir() {
		return nil, fmt.Errorf("base path '%s' is not a directory", absBasePath)
	}

	return &LocalFSStorage{
		workspacesBasePath: absBasePath,
	}, nil
}

// secureJoinPath 安全地将项目根路径和相对路径拼接起来，并进行校验。
// 返回最终的绝对路径。如果路径无效或试图逃逸项目根目录，则返回错误。
func (l *LocalFSStorage) secureJoinPath(projectRoot, relativePath string) (string, error) {
	// Prevent absolute paths from being passed as relative paths
	if filepath.IsAbs(relativePath) {
		return "", fmt.Errorf("invalid path: relativePath cannot be absolute: %s", relativePath)
	}

	// Prevent paths starting with '../' immediately
	if strings.HasPrefix(relativePath, ".."+string(filepath.Separator)) || relativePath == ".." {
		return "", fmt.Errorf("invalid path: relativePath cannot start with '..': %s", relativePath)
	}

	// Clean the relative path AFTER basic checks
	cleanRelativePath := filepath.Clean(relativePath)

	// After cleaning, double-check it doesn't start with '..' again (Clean might produce this)
	// Also check if it results in just '.' or an empty string which should resolve to projectRoot
	if strings.HasPrefix(cleanRelativePath, ".."+string(filepath.Separator)) || cleanRelativePath == ".." {
		return "", fmt.Errorf("invalid path: cleaned path attempts to traverse up: %s", relativePath)
	}
	if cleanRelativePath == "." || cleanRelativePath == "" {
		cleanRelativePath = "" // Treat as project root itself
	}

	// Join the project root with the cleaned relative path
	absolutePath := filepath.Join(projectRoot, cleanRelativePath)

	// Final crucial check: Ensure the resulting absolute path is still within the project root directory.
	// This handles complex cases like symlinks or tricky '..' combinations missed earlier.
	// We check if absolutePath starts with projectRoot + separator, OR if it's exactly projectRoot.
	if !strings.HasPrefix(absolutePath, projectRoot+string(filepath.Separator)) && absolutePath != projectRoot {
		return "", fmt.Errorf("invalid path: resulting path escapes project root: %s (resolved to %s)", relativePath, absolutePath)
	}

	return absolutePath, nil
}

// GetProjectRootPath 获取指定项目在本地文件系统上的绝对根路径。
func (l *LocalFSStorage) GetProjectRootPath(ctx context.Context, projectID string) (string, error) {
	// 简单地将 projectID 作为子目录名附加到 basePath
	// TODO: 可能需要对 projectID 进行清理或哈希以确保是有效且安全的目录名
	if projectID == "" {
		return "", errors.New("projectID cannot be empty")
	}
	// 再次使用 filepath.Join 确保路径安全
	projectRoot := filepath.Join(l.workspacesBasePath, filepath.Clean(projectID))

	// 安全校验：确保最终路径仍在 basePath 下
	if !strings.HasPrefix(projectRoot, l.workspacesBasePath) || projectRoot == l.workspacesBasePath {
		return "", fmt.Errorf("invalid projectID results in path outside base directory: %s", projectID)
	}

	return projectRoot, nil
}

// ReadFile 实现。
func (l *LocalFSStorage) ReadFile(ctx context.Context, projectID, filePath string) ([]byte, error) {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("get project root: %w", err)
	}

	absolutePath, err := l.secureJoinPath(projectRoot, filePath)
	if err != nil {
		return nil, err
	}

	content, err := os.ReadFile(absolutePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("file not found: %s (%w)", filePath, err)
		}
		return nil, fmt.Errorf("read file '%s': %w", absolutePath, err)
	}
	return content, nil
}

// WriteFile 实现。
func (l *LocalFSStorage) WriteFile(ctx context.Context, projectID, filePath string, content []byte, overwrite bool) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return fmt.Errorf("get project root: %w", err)
	}

	absolutePath, err := l.secureJoinPath(projectRoot, filePath)
	if err != nil {
		return err
	}

	// 检查文件是否存在
	_, statErr := os.Stat(absolutePath)
	fileExists := !os.IsNotExist(statErr)

	if fileExists && !overwrite {
		return fmt.Errorf("file '%s' already exists and overwrite is false", filePath)
	}
	if statErr != nil && !os.IsNotExist(statErr) {
		// 如果 Stat 失败但不是因为文件不存在，返回错误
		return fmt.Errorf("stat file '%s': %w", absolutePath, statErr)
	}

	// 确保目录存在
	dirPath := filepath.Dir(absolutePath)
	if err := os.MkdirAll(dirPath, 0750); err != nil { // 使用 0750 权限
		return fmt.Errorf("create directory '%s': %w", dirPath, err)
	}

	// 写入文件
	// 使用 WriteFile 会自动处理文件创建和截断（如果已存在）
	err = os.WriteFile(absolutePath, content, 0640) // 使用 0640 权限
	if err != nil {
		return fmt.Errorf("write file '%s': %w", absolutePath, err)
	}

	return nil
}

// ListDirectory 实现。
func (l *LocalFSStorage) ListDirectory(ctx context.Context, projectID, dirPath string) ([]storage.FileInfo, error) {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("get project root: %w", err)
	}

	absoluteDirPath, err := l.secureJoinPath(projectRoot, dirPath)
	if err != nil {
		return nil, err
	}

	// 检查目录是否存在且确实是目录
	dirInfo, err := os.Stat(absoluteDirPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("directory not found: %s (%w)", dirPath, err)
		}
		return nil, fmt.Errorf("stat directory '%s': %w", absoluteDirPath, err)
	}
	if !dirInfo.IsDir() {
		return nil, fmt.Errorf("path is not a directory: %s", dirPath)
	}

	entries, err := os.ReadDir(absoluteDirPath)
	if err != nil {
		return nil, fmt.Errorf("read directory '%s': %w", absoluteDirPath, err)
	}

	var fileInfos []storage.FileInfo
	for _, entry := range entries {
		entryInfo, err := entry.Info() // 获取完整的 fs.FileInfo
		if err != nil {
			// 记录错误但可能继续处理其他条目
			fmt.Printf("Warning: could not get info for entry '%s': %v\n", entry.Name(), err)
			continue
		}
		// 使用辅助函数转换，并传入相对目录路径
		fileInfos = append(fileInfos, storage.ConvertFsFileInfo(projectRoot, dirPath, entryInfo))
	}

	return fileInfos, nil
}

// CreateDirectory 实现。
func (l *LocalFSStorage) CreateDirectory(ctx context.Context, projectID, dirPath string) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return fmt.Errorf("get project root: %w", err)
	}

	absolutePath, err := l.secureJoinPath(projectRoot, dirPath)
	if err != nil {
		return err
	}

	// 检查路径是否已存在
	info, err := os.Stat(absolutePath)
	if err == nil {
		// 路径已存在
		if info.IsDir() {
			// 如果是目录，返回 ErrPathExists
			return ErrPathExists
		}
		// 如果是文件或其他类型，返回错误
		return fmt.Errorf("path exists but is not a directory: %s", dirPath)
	}

	// 如果错误不是 "not exist"，则返回错误
	if !os.IsNotExist(err) {
		return fmt.Errorf("stat path '%s': %w", absolutePath, err)
	}

	// 路径不存在，创建目录
	err = os.MkdirAll(absolutePath, 0750) // 使用 0750 权限
	if err != nil {
		return fmt.Errorf("create directory '%s': %w", absolutePath, err)
	}
	return nil
}

// Delete 实现。
func (l *LocalFSStorage) Delete(ctx context.Context, projectID, path string, recursive bool) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return fmt.Errorf("get project root: %w", err)
	}

	absolutePath, err := l.secureJoinPath(projectRoot, path)
	if err != nil {
		return err
	}

	// 重要的安全检查：不允许删除项目根目录本身
	if absolutePath == projectRoot {
		return fmt.Errorf("cannot delete project root directory")
	}

	if recursive {
		err = os.RemoveAll(absolutePath) // RemoveAll 会删除文件或递归删除目录
	} else {
		err = os.Remove(absolutePath) // Remove 只删除文件或空目录
	}

	if err != nil {
		if os.IsNotExist(err) {
			// 文件或目录不存在，通常不认为是错误（幂等性）
			return nil // 或者根据需要返回错误 fmt.Errorf("path not found: %s (%w)", path, err)
		}
		return fmt.Errorf("delete path '%s': %w", absolutePath, err)
	}
	return nil
}

// Rename 实现。
func (l *LocalFSStorage) Rename(ctx context.Context, projectID, oldPath, newPath string) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return fmt.Errorf("get project root: %w", err)
	}

	absoluteOldPath, err := l.secureJoinPath(projectRoot, oldPath)
	if err != nil {
		return fmt.Errorf("invalid old path: %w", err)
	}

	absoluteNewPath, err := l.secureJoinPath(projectRoot, newPath)
	if err != nil {
		return fmt.Errorf("invalid new path: %w", err)
	}

	// 检查 newPath 是否已存在
	_, err = os.Stat(absoluteNewPath)
	if err == nil {
		// newPath 已存在
		return ErrPathExists
	}
	if !os.IsNotExist(err) {
		// Stat 失败，但不是因为文件不存在
		return fmt.Errorf("stat new path '%s': %w", absoluteNewPath, err)
	}

	// 检查 oldPath 是否存在
	_, err = os.Stat(absoluteOldPath)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("old path not found: %s (%w)", oldPath, err)
		}
		return fmt.Errorf("stat old path '%s': %w", absoluteOldPath, err)
	}

	// 确保目标目录存在
	newDir := filepath.Dir(absoluteNewPath)
	if err := os.MkdirAll(newDir, 0750); err != nil {
		return fmt.Errorf("create parent directory for '%s': %w", absoluteNewPath, err)
	}

	// 执行重命名
	err = os.Rename(absoluteOldPath, absoluteNewPath)
	if err != nil {
		return fmt.Errorf("rename from '%s' to '%s': %w", absoluteOldPath, absoluteNewPath, err)
	}

	return nil
}

// Exists 实现。
func (l *LocalFSStorage) Exists(ctx context.Context, projectID, path string) (bool, error) {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		// 如果项目根目录获取失败，可以认为路径不存在，或者返回错误
		// return false, fmt.Errorf("get project root: %w", err)
		if errors.Is(err, os.ErrNotExist) || strings.Contains(err.Error(), "invalid projectID") { // 更具体的错误检查
			return false, nil // 项目根路径就可能有问题，说明路径不存在
		}
		return false, fmt.Errorf("get project root: %w", err) // 其他错误
	}

	absolutePath, err := l.secureJoinPath(projectRoot, path)
	if err != nil {
		// 如果路径拼接或校验失败，说明路径无效，可认为不存在
		return false, nil // 返回 false，不返回错误
		// 或者返回错误: return false, err
	}

	_, err = os.Stat(absolutePath)
	if err == nil {
		return true, nil // Stat 成功，说明存在
	}
	if os.IsNotExist(err) {
		return false, nil // Stat 失败且是 NotExist 错误，说明不存在
	}
	// 其他 Stat 错误（如权限问题）
	return false, fmt.Errorf("check existence of '%s': %w", absolutePath, err)
}

// Stat 实现。
func (l *LocalFSStorage) Stat(ctx context.Context, projectID, path string) (storage.FileInfo, error) {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return storage.FileInfo{}, fmt.Errorf("get project root: %w", err)
	}

	absolutePath, err := l.secureJoinPath(projectRoot, path)
	if err != nil {
		return storage.FileInfo{}, err
	}

	entryInfo, err := os.Stat(absolutePath)
	if err != nil {
		if os.IsNotExist(err) {
			return storage.FileInfo{}, fmt.Errorf("path not found: %s (%w)", path, err)
		}
		return storage.FileInfo{}, fmt.Errorf("stat path '%s': %w", absolutePath, err)
	}

	// 获取相对目录路径
	relativeDir := filepath.Dir(path)

	// 使用辅助函数转换
	return storage.ConvertFsFileInfo(projectRoot, relativeDir, entryInfo), nil
}

// InitProjectWorkspace 实现。
func (l *LocalFSStorage) InitProjectWorkspace(ctx context.Context, projectID string) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		return fmt.Errorf("get project root: %w", err)
	}

	// 只需要创建项目根目录即可
	// MkdirAll 如果目录已存在也不会报错
	err = os.MkdirAll(projectRoot, 0750) // 使用 0750 权限
	if err != nil {
		return fmt.Errorf("init project workspace '%s': %w", projectRoot, err)
	}
	return nil
}

// DeleteProjectWorkspace 实现。
func (l *LocalFSStorage) DeleteProjectWorkspace(ctx context.Context, projectID string) error {
	projectRoot, err := l.GetProjectRootPath(ctx, projectID)
	if err != nil {
		// 如果获取路径时出错（例如 projectID 无效导致路径逃逸），则不执行删除
		return fmt.Errorf("get project root path failed, cannot delete: %w", err)
	}

	// 重要的安全检查：确保我们要删除的路径确实在 basePath 下，并且不是 basePath 本身
	if !strings.HasPrefix(projectRoot, l.workspacesBasePath) || projectRoot == l.workspacesBasePath {
		return fmt.Errorf("refusing to delete path outside base workspace directory or the base directory itself: %s", projectRoot)
	}

	// 检查目录是否存在
	_, err = os.Stat(projectRoot)
	if err != nil {
		if os.IsNotExist(err) {
			// 目录不存在，认为是成功的（幂等）
			return nil
		}
		// 其他 Stat 错误
		return fmt.Errorf("stat project root '%s' before delete: %w", projectRoot, err)
	}

	// 递归删除整个项目目录
	err = os.RemoveAll(projectRoot)
	if err != nil {
		return fmt.Errorf("delete project workspace '%s': %w", projectRoot, err)
	}
	return nil
}
