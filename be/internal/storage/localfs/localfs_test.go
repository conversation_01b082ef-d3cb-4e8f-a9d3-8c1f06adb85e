package localfs

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
	"time"

	"git.nevint.com/fota3/t-rex/pkg/storage" // 导入我们定义的接口包

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestEnv 创建一个临时的测试环境
func setupTestEnv(t *testing.T) (storage.ProjectStorage, string, func()) {
	t.Helper()
	// 在系统临时目录下创建一个唯一的子目录作为 basePath
	basePath, err := os.MkdirTemp("", "localfs_test_ws_")
	require.NoError(t, err, "Failed to create temp base path")

	localStorage, err := NewLocalFSStorage(basePath)
	require.NoError(t, err, "Failed to create LocalFSStorage")

	// 返回 LocalFSStorage 实例、基础路径和一个清理函数
	cleanup := func() {
		err := os.RemoveAll(basePath)
		// 在 Windows 上，有时文件句柄可能不会立即释放，导致 RemoveAll 失败
		// 尝试等待一小段时间再试一次
		if err != nil && runtime.GOOS == "windows" {
			time.Sleep(100 * time.Millisecond)
			err = os.RemoveAll(basePath)
		}
		// 忽略清理错误通常是可以接受的，但最好还是记录一下
		if err != nil {
			t.Logf("Warning: failed to cleanup test directory %s: %v", basePath, err)
		}
	}
	// 使用 t.Cleanup 注册清理函数
	t.Cleanup(cleanup)

	return localStorage, basePath, cleanup // cleanup 仍然返回以防外部需要显式调用
}

func TestNewLocalFSStorage(t *testing.T) {
	t.Run("ValidBasePath", func(t *testing.T) {
		storage, _, _ := setupTestEnv(t)
		// defer cleanup() // t.Cleanup 会处理
		assert.NotNil(t, storage)
	})

	t.Run("BasePathIsFile", func(t *testing.T) {
		// 创建一个临时文件作为 basePath
		tmpFile, err := os.CreateTemp("", "localfs_test_file_")
		require.NoError(t, err)
		filePath := tmpFile.Name()
		tmpFile.Close()           // 关闭文件句柄
		defer os.Remove(filePath) // 清理

		_, err = NewLocalFSStorage(filePath)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "is not a directory")
	})

	t.Run("BasePathDoesNotExist_CreateSuccess", func(t *testing.T) {
		tempDir := filepath.Join(os.TempDir(), "non_existent_dir_for_test_"+t.Name())
		_, err := NewLocalFSStorage(tempDir)
		require.NoError(t, err)
		// 检查目录是否真的被创建了
		_, statErr := os.Stat(tempDir)
		assert.NoError(t, statErr)
		// 清理
		os.RemoveAll(tempDir)
	})
}

func TestGetProjectRootPath(t *testing.T) {
	storage, basePath, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "testProject123"

	expectedPath := filepath.Join(basePath, projectID)
	actualPath, err := storage.GetProjectRootPath(ctx, projectID)

	assert.NoError(t, err)
	assert.Equal(t, expectedPath, actualPath)
}

func TestSecureJoinPath(t *testing.T) {
	storage, basePath, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ls := storage.(*LocalFSStorage) // 获取具体类型以调用内部方法 (仅用于测试)
	projectRoot := filepath.Join(basePath, "projectA")

	tests := []struct {
		name         string
		relativePath string
		expectError  bool
		expectedPath string // 仅在 expectError 为 false 时检查
	}{
		{"ValidFile", "file.txt", false, filepath.Join(projectRoot, "file.txt")},
		{"ValidDir", "subdir/file.txt", false, filepath.Join(projectRoot, "subdir", "file.txt")},
		{"CurrentDir", ".", false, projectRoot},
		{"EmptyPath", "", false, projectRoot},
		{"TraverseUp", "../file.txt", true, ""},
		{"TraverseUpAbsolute", "/etc/passwd", true, ""}, // 虽然 join 会处理，但前缀检查会失败
		{"TraverseUpHidden", "subdir/../../file.txt", true, ""},
		{"StartsWithDotDot", "..", true, ""},
		{"StartsWithDotDotSlash", "../otherproject", true, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actualPath, err := ls.secureJoinPath(projectRoot, tt.relativePath)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid path")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedPath, actualPath)
			}
		})
	}
}

func TestLocalFSStorage_ReadWriteFile(t *testing.T) {
	storage, _, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectRW"
	filePath := "dir/myFile.txt"
	content := []byte("Hello LocalFS!")

	// 1. 测试写入新文件
	err := storage.WriteFile(ctx, projectID, filePath, content, false) // overwrite=false
	require.NoError(t, err, "Failed to write new file")

	// 2. 测试读取文件
	readContent, err := storage.ReadFile(ctx, projectID, filePath)
	require.NoError(t, err, "Failed to read file")
	assert.Equal(t, content, readContent, "Read content mismatch")

	// 3. 测试禁止覆盖
	err = storage.WriteFile(ctx, projectID, filePath, []byte("new content"), false) // overwrite=false
	assert.Error(t, err, "Should error when overwrite is false and file exists")
	assert.Contains(t, err.Error(), "already exists")

	// 4. 测试允许覆盖
	newContent := []byte("Overwritten Content")
	err = storage.WriteFile(ctx, projectID, filePath, newContent, true) // overwrite=true
	require.NoError(t, err, "Failed to overwrite file")

	// 5. 再次读取验证覆盖
	readContent, err = storage.ReadFile(ctx, projectID, filePath)
	require.NoError(t, err, "Failed to read overwritten file")
	assert.Equal(t, newContent, readContent, "Overwritten content mismatch")

	// 6. 测试读取不存在的文件
	_, err = storage.ReadFile(ctx, projectID, "nonexistent.txt")
	assert.Error(t, err, "Should error when reading non-existent file")
	assert.True(t, errors.Is(err, os.ErrNotExist) || strings.Contains(err.Error(), "not found"), "Error should indicate file not found")
}

func TestLocalFSStorage_ListDirectory(t *testing.T) {
	storage, _, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectList"

	// 准备测试文件和目录
	require.NoError(t, storage.WriteFile(ctx, projectID, "file1.txt", []byte("f1"), false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "subdir1"))
	require.NoError(t, storage.WriteFile(ctx, projectID, "subdir1/file2.txt", []byte("f2"), false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "subdir2"))

	// 1. 列出根目录
	files, err := storage.ListDirectory(ctx, projectID, ".") // dirPath = "." 或 ""
	require.NoError(t, err)
	assert.Len(t, files, 3, "Should list 3 items in root")

	foundFile1 := false
	foundSubdir1 := false
	foundSubdir2 := false
	for _, f := range files {
		if f.Name == "file1.txt" && !f.IsDir {
			foundFile1 = true
			assert.Equal(t, "file1.txt", f.Path) // 路径应相对于 dirPath "."
		} else if f.Name == "subdir1" && f.IsDir {
			foundSubdir1 = true
			assert.Equal(t, "subdir1", f.Path)
		} else if f.Name == "subdir2" && f.IsDir {
			foundSubdir2 = true
			assert.Equal(t, "subdir2", f.Path)
		}
	}
	assert.True(t, foundFile1, "file1.txt not found")
	assert.True(t, foundSubdir1, "subdir1 not found")
	assert.True(t, foundSubdir2, "subdir2 not found")

	// 2. 列出子目录
	files, err = storage.ListDirectory(ctx, projectID, "subdir1")
	require.NoError(t, err)
	require.Len(t, files, 1, "Should list 1 item in subdir1")
	assert.Equal(t, "file2.txt", files[0].Name)
	assert.Equal(t, "subdir1/file2.txt", files[0].Path) // 路径应相对于根
	assert.False(t, files[0].IsDir)

	// 3. 列出空目录
	files, err = storage.ListDirectory(ctx, projectID, "subdir2")
	require.NoError(t, err)
	assert.Len(t, files, 0, "Should list 0 items in subdir2")

	// 4. 列出不存在的目录
	_, err = storage.ListDirectory(ctx, projectID, "nonexistentdir")
	assert.Error(t, err)
	assert.True(t, errors.Is(err, os.ErrNotExist) || strings.Contains(err.Error(), "not found"), "Error should indicate dir not found")

	// 5. 尝试列出文件路径
	_, err = storage.ListDirectory(ctx, projectID, "file1.txt")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "path is not a directory")
}

func TestLocalFSStorage_CreateDirectory(t *testing.T) {
	storage, projectRootPath, _ := setupTestEnv(t)
	projectRootPath = filepath.Join(projectRootPath, "projectCreateDir")         // Adjust project root for clarity
	storage.(*LocalFSStorage).workspacesBasePath = filepath.Dir(projectRootPath) // Adjust storage base path
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectCreateDir"

	// 1. 创建单个目录
	dirPath1 := "newDir"
	err := storage.CreateDirectory(ctx, projectID, dirPath1)
	require.NoError(t, err)
	_, statErr := os.Stat(filepath.Join(projectRootPath, dirPath1))
	assert.NoError(t, statErr, "Directory should exist")

	// 2. 创建嵌套目录
	dirPath2 := "nested/dir/structure"
	err = storage.CreateDirectory(ctx, projectID, dirPath2)
	require.NoError(t, err)
	_, statErr = os.Stat(filepath.Join(projectRootPath, dirPath2))
	assert.NoError(t, statErr, "Nested directory structure should exist")

	// 3. 创建已存在的目录 (应报错)
	err = storage.CreateDirectory(ctx, projectID, dirPath1)
	assert.ErrorIs(t, err, ErrPathExists, "Creating existing directory should return ErrPathExists")

	// 4. 尝试使用非法路径创建
	err = storage.CreateDirectory(ctx, projectID, "../escapedDir")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid path")
}

func TestLocalFSStorage_Exists(t *testing.T) {
	storage, _, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectExists"

	require.NoError(t, storage.WriteFile(ctx, projectID, "exists.txt", []byte(""), false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "existsDir"))

	// 1. 文件存在
	exists, err := storage.Exists(ctx, projectID, "exists.txt")
	assert.NoError(t, err)
	assert.True(t, exists)

	// 2. 目录存在
	exists, err = storage.Exists(ctx, projectID, "existsDir")
	assert.NoError(t, err)
	assert.True(t, exists)

	// 3. 不存在
	exists, err = storage.Exists(ctx, projectID, "nonexistent")
	assert.NoError(t, err) // Exists 应该只在 Stat 遇到权限等问题时返回 error
	assert.False(t, exists)

	// 4. 无效路径 (试图逃逸)
	exists, err = storage.Exists(ctx, projectID, "../somefile")
	assert.NoError(t, err) // 对无效路径，Exists 返回 false, nil
	assert.False(t, exists)

	// 5. 不存在的 Project ID
	exists, err = storage.Exists(ctx, "nonExistentProject", "somefile")
	assert.NoError(t, err) // 对无效 project, Exists 返回 false, nil
	assert.False(t, exists)
}

func TestLocalFSStorage_Stat(t *testing.T) {
	storage, _, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectStat"
	content := []byte("stat content")
	now := time.Now().Truncate(time.Second) // Truncate for comparison ease

	require.NoError(t, storage.WriteFile(ctx, projectID, "stat.txt", content, false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "statDir"))
	// Ensure ModTime is roughly correct
	time.Sleep(10 * time.Millisecond)

	// 1. Stat 文件
	info, err := storage.Stat(ctx, projectID, "stat.txt")
	require.NoError(t, err)
	assert.Equal(t, "stat.txt", info.Name)
	assert.Equal(t, "stat.txt", info.Path)
	assert.False(t, info.IsDir)
	assert.Equal(t, int64(len(content)), info.Size)
	assert.WithinDuration(t, now, info.ModTime, 2*time.Second, "ModTime is unexpected")

	// 2. Stat 目录
	info, err = storage.Stat(ctx, projectID, "statDir")
	require.NoError(t, err)
	assert.Equal(t, "statDir", info.Name)
	assert.Equal(t, "statDir", info.Path)
	assert.True(t, info.IsDir)
	assert.WithinDuration(t, now, info.ModTime, 2*time.Second, "ModTime is unexpected")

	// 3. Stat 不存在路径
	_, err = storage.Stat(ctx, projectID, "nonexistent")
	assert.Error(t, err)
	assert.True(t, errors.Is(err, os.ErrNotExist) || strings.Contains(err.Error(), "not found"))

	// 4. Stat 根目录 "."
	rootInfo, err := storage.Stat(ctx, projectID, ".")
	require.NoError(t, err)
	assert.NotNil(t, rootInfo)
	// Stat(".") should return the name "." regardless of the project ID
	assert.Equal(t, projectID, rootInfo.Name, "Stat on root should return projectID as name")
	assert.True(t, rootInfo.IsDir, "Root should be a directory")
	assert.NotEmpty(t, rootInfo.ModTime, "Root should have a modification time")
}

func TestLocalFSStorage_Rename(t *testing.T) {
	storage, _, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectRename"

	require.NoError(t, storage.WriteFile(ctx, projectID, "oldfile.txt", []byte("rename me"), false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "oldDir"))
	require.NoError(t, storage.WriteFile(ctx, projectID, "oldDir/inner.txt", []byte("inner"), false))
	require.NoError(t, storage.WriteFile(ctx, projectID, "existing_target.txt", []byte("dont overwrite"), false))

	// 1. 重命名文件
	err := storage.Rename(ctx, projectID, "oldfile.txt", "newfile.txt")
	require.NoError(t, err)
	exists, _ := storage.Exists(ctx, projectID, "oldfile.txt")
	assert.False(t, exists, "Old file should not exist")
	exists, _ = storage.Exists(ctx, projectID, "newfile.txt")
	assert.True(t, exists, "New file should exist")

	// 2. 移动并重命名文件
	err = storage.Rename(ctx, projectID, "newfile.txt", "newDir/movedfile.txt")
	require.NoError(t, err)
	exists, _ = storage.Exists(ctx, projectID, "newfile.txt")
	assert.False(t, exists, "Original location should not exist")
	exists, _ = storage.Exists(ctx, projectID, "newDir/movedfile.txt")
	assert.True(t, exists, "Moved file should exist in new directory")
	_, statErr := os.Stat(filepath.Join(storage.(*LocalFSStorage).workspacesBasePath, projectID, "newDir"))
	assert.NoError(t, statErr, "Target directory should be created")

	// 3. 重命名目录
	err = storage.Rename(ctx, projectID, "oldDir", "renamedDir")
	require.NoError(t, err)
	exists, _ = storage.Exists(ctx, projectID, "oldDir")
	assert.False(t, exists, "Old directory should not exist")
	exists, _ = storage.Exists(ctx, projectID, "renamedDir")
	assert.True(t, exists, "Renamed directory should exist")
	exists, _ = storage.Exists(ctx, projectID, "renamedDir/inner.txt") // 检查内部文件是否一起移动
	assert.True(t, exists, "Inner file should exist in renamed directory")

	// 4. 尝试重命名到已存在的文件 (os.Rename 行为可能依赖系统，通常会失败)
	err = storage.Rename(ctx, projectID, "renamedDir/inner.txt", "newDir/movedfile.txt")
	assert.Error(t, err, "Should fail when renaming to an existing file path")

	// 5. 尝试重命名不存在的源
	err = storage.Rename(ctx, projectID, "nonexistent.txt", "something_else.txt")
	assert.Error(t, err)
	assert.True(t, errors.Is(err, os.ErrNotExist) || strings.Contains(err.Error(), "no such file"))

	// 6. 尝试使用非法路径
	err = storage.Rename(ctx, projectID, "renamedDir", "../escaped")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid new path")

	err = storage.Rename(ctx, projectID, "../escaped_source", "newname")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid old path")

}

func TestLocalFSStorage_Delete(t *testing.T) {
	storage, projectRootPath, _ := setupTestEnv(t)
	projectRootPath = filepath.Join(projectRootPath, "projectDelete")
	storage.(*LocalFSStorage).workspacesBasePath = filepath.Dir(projectRootPath)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectDelete"

	require.NoError(t, storage.WriteFile(ctx, projectID, "file_to_delete.txt", []byte("del"), false))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "emptyDir"))
	require.NoError(t, storage.CreateDirectory(ctx, projectID, "nonEmptyDir"))
	require.NoError(t, storage.WriteFile(ctx, projectID, "nonEmptyDir/inner.txt", []byte("in"), false))

	// 1. 删除文件
	err := storage.Delete(ctx, projectID, "file_to_delete.txt", false)
	require.NoError(t, err)
	exists, _ := storage.Exists(ctx, projectID, "file_to_delete.txt")
	assert.False(t, exists, "File should be deleted")

	// 2. 删除空目录 (recursive=false)
	err = storage.Delete(ctx, projectID, "emptyDir", false)
	require.NoError(t, err)
	exists, _ = storage.Exists(ctx, projectID, "emptyDir")
	assert.False(t, exists, "Empty directory should be deleted")

	// 3. 删除非空目录 (recursive=false) - os.Remove 会失败
	err = storage.Delete(ctx, projectID, "nonEmptyDir", false)
	assert.Error(t, err, "Should fail deleting non-empty dir with recursive=false")
	// 检查错误类型可能依赖系统，syscall.ENOTEMPTY 或类似错误
	// assert.True(t, errors.Is(err, syscall.ENOTEMPTY) || strings.Contains(err.Error(), "directory not empty"))
	exists, _ = storage.Exists(ctx, projectID, "nonEmptyDir")
	assert.True(t, exists, "Non-empty directory should still exist") // 确认没被删

	// 4. 删除非空目录 (recursive=true)
	err = storage.Delete(ctx, projectID, "nonEmptyDir", true)
	require.NoError(t, err)
	exists, _ = storage.Exists(ctx, projectID, "nonEmptyDir")
	assert.False(t, exists, "Non-empty directory should be deleted with recursive=true")

	// 5. 删除不存在的路径 (应成功，幂等)
	err = storage.Delete(ctx, projectID, "nonexistent", true)
	assert.NoError(t, err, "Deleting non-existent path should not error")

	// 6. 尝试删除项目根目录 (应失败)
	err = storage.Delete(ctx, projectID, ".", true)
	assert.Error(t, err, "Should fail deleting project root '.'")
	assert.Contains(t, err.Error(), "cannot delete project root")

	err = storage.Delete(ctx, projectID, "", true) // 也应失败
	assert.Error(t, err, "Should fail deleting project root ''")
	assert.Contains(t, err.Error(), "cannot delete project root")

	// 7. 尝试使用非法路径删除
	err = storage.Delete(ctx, projectID, "../somefile", true)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid path")
}

func TestLocalFSStorage_ProjectWorkspace(t *testing.T) {
	storage, basePath, _ := setupTestEnv(t)
	// defer cleanup() // t.Cleanup 会处理
	ctx := context.Background()
	projectID := "projectWS"
	projectRoot := filepath.Join(basePath, projectID)

	// 1. 初始化工作区
	err := storage.InitProjectWorkspace(ctx, projectID)
	require.NoError(t, err)
	_, statErr := os.Stat(projectRoot)
	assert.NoError(t, statErr, "Project root directory should exist after init")

	// 2. 再次初始化 (应成功，幂等)
	err = storage.InitProjectWorkspace(ctx, projectID)
	assert.NoError(t, err, "Initializing existing workspace should not error")

	// 3. 删除工作区
	require.NoError(t, storage.WriteFile(ctx, projectID, "dummy.txt", []byte(""), false)) // 确保非空
	err = storage.DeleteProjectWorkspace(ctx, projectID)
	require.NoError(t, err)
	_, statErr = os.Stat(projectRoot)
	assert.True(t, os.IsNotExist(statErr), "Project root directory should not exist after delete")

	// 4. 删除不存在的工作区 (应成功，幂等)
	err = storage.DeleteProjectWorkspace(ctx, projectID)
	assert.NoError(t, err, "Deleting non-existent workspace should not error")

	// 5. 尝试删除 basePath (应失败)
	// 需要模拟一个 projectID 指向 basePath，但 GetProjectRootPath 有保护
	err = storage.DeleteProjectWorkspace(ctx, "..") // GetProjectRootPath 会先失败
	assert.Error(t, err)

	// 尝试构造一个可能绕过 GetProjectRootPath 简单检查的 projectID
	// (现实中 projectID 不应包含路径分隔符，但测试防御性编程)
	trickyProjectID := "../" + filepath.Base(basePath)
	err = storage.DeleteProjectWorkspace(ctx, trickyProjectID)
	assert.Error(t, err) // GetProjectRootPath 或 Delete 本身的检查应阻止
	// 检查 basePath 是否还在
	_, statErr = os.Stat(basePath)
	assert.NoError(t, statErr, "Base path should still exist")

}
