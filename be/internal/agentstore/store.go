package agentstore

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"git.nevint.com/fota3/t-rex/domain/agent"      // Import domain interfaces
	model "git.nevint.com/fota3/t-rex/model/agent" // Import agent models
	"git.nevint.com/fota3/t-rex/pkg/mongodb"       // Import the ORM package
)

const (
	conversationCollection = "agent_conversations"
)

// Store implements the agent.ConversationRepository interface using MongoDB ORM.
// It handles persistence for agent conversations.
type Store struct {
	orm *mongodb.ORM[model.Conversation]
}

// NewStore creates a new store for agent conversations using the ORM.
func NewStore(cli *mongo.Client, dbName string) (*Store, error) {
	if cli == nil {
		return nil, errors.New("mongo client is nil")
	}
	if dbName == "" {
		return nil, errors.New("database name is empty")
	}
	orm, err := mongodb.New[model.Conversation](cli, dbName)
	if err != nil {
		return nil, fmt.Errorf("failed to create conversation ORM: %w", err)
	}
	return &Store{orm: orm}, nil
}

// Compile-time check to ensure Store implements the interface.
var _ agent.ConversationRepository = (*Store)(nil)

// CreateConversation saves a new conversation document using the ORM.
func (s *Store) CreateConversation(ctx context.Context, conversation *model.Conversation) error {
	// Ensure default values are set if not provided
	if conversation.ID.IsZero() {
		conversation.ID = primitive.NewObjectID()
	}
	if conversation.CreatedAt.IsZero() {
		conversation.CreatedAt = time.Now()
	}
	if conversation.LastUpdatedAt.IsZero() {
		conversation.LastUpdatedAt = conversation.CreatedAt
	}
	if conversation.Messages == nil {
		conversation.Messages = []model.Message{}
	}

	_, err := s.orm.Insert(ctx, *conversation)
	if err != nil {
		return fmt.Errorf("failed to insert conversation via ORM: %w", err)
	}
	return nil
}

// GetConversationByID retrieves a conversation by its unique ID using the ORM.
func (s *Store) GetConversationByID(ctx context.Context, id string) (*model.Conversation, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid conversation ID format: %w", err)
	}

	filter := bson.M{"_id": objectID}

	conversation, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			// Return nil, nil if not found, consistent with previous implementation
			return nil, nil // Or return a specific domain error
		}
		return nil, fmt.Errorf("failed to find conversation by ID via ORM: %w", err)
	}

	return &conversation, nil
}

// AppendMessage adds a new message to an existing conversation's message array using the ORM.
func (s *Store) AppendMessage(ctx context.Context, conversationID string, message *model.Message) error {
	objectID, err := primitive.ObjectIDFromHex(conversationID)
	if err != nil {
		return fmt.Errorf("invalid conversation ID format: %w", err)
	}

	// Ensure message has necessary defaults
	if message.MsgID == "" {
		message.MsgID = primitive.NewObjectID().Hex() // Or use UUID
	}
	if message.Timestamp.IsZero() {
		message.Timestamp = time.Now()
	}

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$push": bson.M{"messages": message},
		"$set":  bson.M{"last_updated_at": message.Timestamp},
	}

	// Use the generic Update method from the ORM
	result, err := s.orm.Update(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to append message via ORM: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("conversation with ID '%s' not found via ORM", conversationID) // Or return a domain error
	}

	return nil
}

// FindConversationsByUserID retrieves all conversations for a specific user.
// TODO: Implement using s.orm.GetManyByFilter or similar.
func (s *Store) FindConversationsByUserID(ctx context.Context, userID string) ([]*model.Conversation, error) {
	// Example implementation sketch:
	// filter := bson.M{"user_id": userID}
	// conversations, err := s.orm.GetManyByFilter(ctx, filter).All()
	// if err != nil {
	// 	 return nil, fmt.Errorf("failed to find conversations by user ID via ORM: %w", err)
	// }
	// return conversations, nil
	return nil, fmt.Errorf("FindConversationsByUserID not implemented")
}

// ClearConversationMessages removes all messages from a conversation but keeps the conversation record.
func (s *Store) ClearConversationMessages(ctx context.Context, conversationID string) error {
	objectID, err := primitive.ObjectIDFromHex(conversationID)
	if err != nil {
		return fmt.Errorf("invalid conversation ID format: %w", err)
	}

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$set": bson.M{
			"messages":        []model.Message{},
			"last_updated_at": time.Now(),
		},
	}

	result, err := s.orm.Update(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to clear conversation messages via ORM: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("conversation with ID '%s' not found", conversationID)
	}

	return nil
}

// GetConversationByProjectID retrieves the conversation associated with a specific project.
func (s *Store) GetConversationByProjectID(ctx context.Context, projectID string) (*model.Conversation, error) {
	filter := bson.M{"project_id": projectID}

	conversation, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to find conversation by project ID via ORM: %w", err)
	}

	return &conversation, nil
}

// CreateProjectConversation creates a new conversation for a specific project.
func (s *Store) CreateProjectConversation(ctx context.Context, projectID string, userID string) (*model.Conversation, error) {
	conversation := &model.Conversation{
		ID:            primitive.NewObjectID(),
		UserID:        userID,
		ProjectID:     projectID,
		CreatedAt:     time.Now(),
		LastUpdatedAt: time.Now(),
		Messages:      []model.Message{},
	}

	err := s.CreateConversation(ctx, conversation)
	if err != nil {
		return nil, fmt.Errorf("failed to create project conversation: %w", err)
	}

	return conversation, nil
}

// GetOrCreateProjectConversation gets existing project conversation or creates one if not exists.
func (s *Store) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*model.Conversation, error) {
	// Try to get existing conversation
	conversation, err := s.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project conversation: %w", err)
	}

	// If conversation doesn't exist, create one
	if conversation == nil {
		return s.CreateProjectConversation(ctx, projectID, userID)
	}

	return conversation, nil
}

// DeleteProjectConversation completely removes the conversation record for a specific project.
func (s *Store) DeleteProjectConversation(ctx context.Context, projectID string) error {
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}

	filter := bson.M{"project_id": projectID}

	// Use the ORM's Delete method to remove the conversation document
	result, err := s.orm.Delete(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete project conversation via ORM: %w", err)
	}

	// Log the deletion for debugging purposes
	if result.DeletedCount == 0 {
		// No conversation found for this project - this is not necessarily an error
		// since the project might not have had any conversations
		return nil
	}

	return nil
}

// Add implementations for other interface methods if/when needed.
