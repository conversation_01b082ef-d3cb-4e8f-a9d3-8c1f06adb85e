package userstore

import (
	"context"
	"errors"
	"fmt"
	"time"

	domainuser "git.nevint.com/fota3/t-rex/domain/user" // Import domain interfaces with alias
	model "git.nevint.com/fota3/t-rex/model/user"       // Import user model
	"git.nevint.com/fota3/t-rex/pkg/mongodb"            // Import the ORM package
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Store implements the user.UserRepository interface using MongoDB ORM.
// It handles persistence for user accounts.
type Store struct {
	orm *mongodb.ORM[model.User]
}

// NewStore creates a new store for users using the ORM.
func NewStore(cli *mongo.Client, dbName string) (*Store, error) {
	if cli == nil {
		return nil, errors.New("mongo client is nil")
	}
	if dbName == "" {
		return nil, errors.New("database name is empty")
	}
	orm, err := mongodb.New[model.User](cli, dbName)
	if err != nil {
		return nil, fmt.Errorf("failed to create user ORM: %w", err)
	}
	// TODO: Consider creating indexes here (e.g., unique index on username)
	// Example: _, err = orm.GetCollection().Indexes().CreateOne(context.Background(), mongo.IndexModel{Keys: bson.D{{Key: "username", Value: 1}}, Options: options.Index().SetUnique(true)})
	// Handle potential index creation errors
	return &Store{orm: orm}, nil
}

// Compile-time check to ensure Store implements the interface.
var _ domainuser.UserRepository = (*Store)(nil) // Use alias for interface check

// Create inserts a new user using the ORM.
// Note: This basic implementation doesn't explicitly check for existing username/email.
// A unique index on the database level is recommended for username.
// For email uniqueness, a pre-check or a separate mechanism might be needed if required.
func (s *Store) Create(ctx context.Context, user *model.User) error {
	if user.ID.IsZero() {
		user.ID = primitive.NewObjectID()
	}
	now := time.Now()
	if user.CreatedAt.IsZero() {
		user.CreatedAt = now
	}
	if user.LastUpdatedAt.IsZero() {
		user.LastUpdatedAt = now
	}
	user.IsActive = true // Default to active

	_, err := s.orm.Insert(ctx, *user)
	if err != nil {
		// Check for MongoDB duplicate key error (E11000)
		if mongo.IsDuplicateKeyError(err) {
			// Determine which key caused the duplicate error (e.g., username)
			// This requires parsing the error message or using specific driver features if available.
			// For simplicity, assume duplicate key means username is taken.
			return domainuser.ErrUsernameTaken // Use domain error directly
		}
		return fmt.Errorf("failed to insert user via ORM: %w", err)
	}
	return nil
}

// FindByID retrieves a user by ID using the ORM.
func (s *Store) FindByID(ctx context.Context, id string) (*model.User, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	filter := bson.M{"_id": objectID}

	foundUser, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, domainuser.ErrUserNotFound // Use domain error directly
		}
		return nil, fmt.Errorf("failed to find user by ID via ORM: %w", err)
	}

	return &foundUser, nil
}

// FindByUsername retrieves a user by username using the ORM.
func (s *Store) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	filter := bson.M{"username": username}

	foundUser, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, domainuser.ErrUserNotFound // Use domain error directly
		}
		return nil, fmt.Errorf("failed to find user by username via ORM: %w", err)
	}

	return &foundUser, nil
}

// Update updates an existing user record using the ORM.
// This implementation replaces the entire document except for fields like CreatedAt.
// Consider implementing partial updates ($set) if needed for specific fields.
func (s *Store) Update(ctx context.Context, user *model.User) error {
	if user.ID.IsZero() {
		return errors.New("cannot update user without ID")
	}

	filter := bson.M{"_id": user.ID}
	user.LastUpdatedAt = time.Now() // Always update the timestamp

	// Prepare update data - replacing the document but keeping original _id
	// Note: This overwrites the entire document matching the filter.
	// If you need to update only specific fields, construct a bson.M with $set.
	updateData := *user

	// Use the generic Update method from the ORM (assuming it handles replacement or $set)
	result, err := s.orm.Update(ctx, filter, updateData) // Use Update instead of ReplaceOne
	if err != nil {
		return fmt.Errorf("failed to update user via ORM: %w", err)
	}

	if result.MatchedCount == 0 {
		return domainuser.ErrUserNotFound // Use domain error directly
	}

	return nil
}

// CountUsers returns the total number of users in the data store.
func (s *Store) CountUsers(ctx context.Context) (int64, error) {
	count, err := s.orm.Count(ctx, bson.M{}) // Count all documents
	if err != nil {
		return 0, fmt.Errorf("failed to count users: %w", err)
	}
	return count, nil
}

// TODO: Implement other UserRepository methods (Delete, List) if needed.
