package projectstore

import (
	"context"
	"errors"
	"fmt"
	"time"

	projectdomain "git.nevint.com/fota3/t-rex/domain/project" // Import domain interfaces
	model "git.nevint.com/fota3/t-rex/model/project"          // Import project model
	"git.nevint.com/fota3/t-rex/pkg/mongodb"                  // Import the ORM package
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Store implements the project.ProjectRepository interface using MongoDB ORM.
type Store struct {
	orm *mongodb.ORM[model.Project]
}

// NewStore creates a new store for project metadata using the ORM.
func NewStore(cli *mongo.Client, dbName string) (*Store, error) {
	if cli == nil {
		return nil, errors.New("mongo client is nil")
	}
	if dbName == "" {
		return nil, errors.New("database name is empty")
	}
	orm, err := mongodb.New[model.Project](cli, dbName)
	if err != nil {
		return nil, fmt.Errorf("failed to create project ORM: %w", err)
	}

	// Create indexes for efficient querying
	coll := orm.Collection() // Get the collection using the new method

	// Unique index on project_id
	_, err = coll.Indexes().CreateOne(context.Background(), mongo.IndexModel{
		Keys:    bson.D{{Key: "project_id", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		// Don't fail if index creation fails (might already exist), but log it.
		fmt.Printf("Warning: Failed to create unique index on project_id (may already exist): %v\n", err)
	}

	// Index on user_id for faster lookup of user's projects
	_, err = coll.Indexes().CreateOne(context.Background(), mongo.IndexModel{
		Keys: bson.D{{Key: "user_id", Value: 1}},
	})
	if err != nil {
		fmt.Printf("Warning: Failed to create index on user_id (may already exist): %v\n", err)
	}

	return &Store{orm: orm}, nil
}

// Compile-time check to ensure Store implements the interface.
var _ projectdomain.ProjectRepository = (*Store)(nil)

// Create inserts a new project metadata record.
func (s *Store) Create(ctx context.Context, project *model.Project) error {
	if project.ID.IsZero() {
		project.ID = primitive.NewObjectID()
	}
	if project.CreatedAt.IsZero() {
		project.CreatedAt = time.Now()
	}

	_, err := s.orm.Insert(ctx, *project)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			// Assuming the duplicate key is on project_id based on the unique index
			return projectdomain.ErrDuplicateProjectID
		}
		return fmt.Errorf("%w", projectdomain.RepositoryError(err))
	}
	return nil
}

// FindByProjectID retrieves project metadata by the user-facing ProjectID.
func (s *Store) FindByProjectID(ctx context.Context, projectID string) (*model.Project, error) {
	filter := bson.M{"project_id": projectID}
	foundProject, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, projectdomain.ErrNotFound
		}
		return nil, fmt.Errorf("%w: finding by project_id: %v", projectdomain.RepositoryError(err), projectID)
	}
	return &foundProject, nil
}

// FindByUserID retrieves all projects associated with a user ID.
func (s *Store) FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*model.Project, error) {
	filter := bson.M{"user_id": userID}
	// Consider adding sorting, e.g., by creation date
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}}) // Sort by newest first

	// Use the Find method provided by the ORM's Collection()
	coll := s.orm.Collection()
	cursor, err := coll.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("%w: finding by user_id (cursor error)", projectdomain.RepositoryError(err))
	}
	defer cursor.Close(ctx)

	var projects []*model.Project
	if err = cursor.All(ctx, &projects); err != nil {
		return nil, fmt.Errorf("%w: finding by user_id (decoding error)", projectdomain.RepositoryError(err))
	}

	// Return an empty slice if no projects found, not an error
	if projects == nil {
		projects = []*model.Project{}
	}
	return projects, nil
}

// FindByID retrieves project metadata by its MongoDB ObjectID.
func (s *Store) FindByID(ctx context.Context, id primitive.ObjectID) (*model.Project, error) {
	filter := bson.M{"_id": id}
	foundProject, err := s.orm.GetOneByFilter(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, projectdomain.ErrNotFound
		}
		return nil, fmt.Errorf("%w: finding by _id", projectdomain.RepositoryError(err))
	}
	return &foundProject, nil
}

// Delete removes project metadata by its MongoDB ObjectID.
func (s *Store) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	// Use the DeleteOne method provided by the ORM's Collection()
	coll := s.orm.Collection()
	result, err := coll.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("%w: deleting by _id", projectdomain.RepositoryError(err))
	}
	if result.DeletedCount == 0 {
		return projectdomain.ErrNotFound
	}
	return nil
}
