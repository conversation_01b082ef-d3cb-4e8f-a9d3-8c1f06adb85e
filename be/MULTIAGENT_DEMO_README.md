# Multi-Agent Simple LLM Demo

这个demo展示了如何使用真实的LLM来测试多Agent系统的核心功能。

## 配置要求

确保你的 `config/config.yaml` 文件中包含正确的OpenAI配置：

```yaml
# OpenAI configuration for Agent LLM
openai:
  api_key: "your-api-key-here"
  api_base: "https://openrouter.ai/api/v1"  # 或其他兼容的API端点
  model_name: "moonshotai/kimi-k2:free"     # 或其他模型
```

当前配置使用的是：
- **API Base**: `https://openrouter.ai/api/v1`
- **Model**: `moonshotai/kimi-k2:free`
- **API Key**: 已在配置文件中设置

## 运行方式

### 方式1: 使用运行脚本（推荐）

```bash
cd be
./run-multiagent-demo.sh
```

脚本会提供两个选项：
1. 作为测试运行
2. 作为独立程序运行

### 方式2: 直接运行测试

```bash
cd be
SIMPLE_LLM_DEMO=true go test ./domain/multiagent -v -run TestSimpleLLMDemo
```

### 方式3: 运行独立程序

```bash
cd be
go run ./cmd/multiagent-demo/main.go
```

## Demo内容

这个demo会运行两个测试用例：

### 测试用例1: 简单问题（单Agent模式）
- **问题**: "What is Go programming language? Please give me a brief introduction."
- **预期**: 使用单Agent模式处理
- **展示**: 基础的LLM交互和流式响应

### 测试用例2: 复杂分析（多Agent模式）
- **问题**: "Analyze the advantages and disadvantages of microservices architecture, compare it with monolithic architecture, and provide detailed recommendations for when to use each approach with specific examples"
- **预期**: 触发多Agent处理模式
- **展示**: 
  - 任务复杂度检测
  - 多Agent协作（如果检测为复杂任务）
  - 结果合成

## 输出示例

```
=== Multi-Agent System Simple LLM Demo ===

--- Test Case 1: Simple Question ---
User Message: What is Go programming language? Please give me a brief introduction.
Description: This should use single agent mode
Task Detection Result: false
Task Complexity: simple
Starting LLM interaction...
Response:
---
[START] Go is a programming language developed by Google... [END]
---
Total chunks received: 15
Response length: 342 characters
Single agent mode was used for this simple task

Waiting 3 seconds before next test case...

--- Test Case 2: Complex Analysis ---
User Message: Analyze the advantages and disadvantages of microservices architecture...
Description: This should trigger multi-agent processing
Task Detection Result: true
Task Complexity: high
Starting LLM interaction...
Response:
---
[START] # Microservices vs Monolithic Architecture Analysis... [END]
---
Total chunks received: 45
Response length: 1250 characters
Multi-agent mode was used for this complex task

=== Simple LLM Demo Complete ===
```

## 功能特性

1. **智能任务检测**: 自动判断是否需要多Agent处理
2. **真实LLM交互**: 使用配置文件中的真实API
3. **流式响应**: 实时显示LLM的响应内容
4. **错误处理**: 完整的错误处理和超时控制
5. **性能监控**: 显示响应时间和chunk数量

## 故障排除

### 1. API Key错误
```
Error: LLM config API key is required for ChatCompletionStream
```
**解决**: 检查 `config/config.yaml` 中的 `openai.api_key` 配置

### 2. 网络连接问题
```
Error: failed to create LLM client: connection timeout
```
**解决**: 检查网络连接和API端点是否可访问

### 3. 配置文件未找到
```
Error: failed to load config: Config file not found
```
**解决**: 确保在 `be` 目录下运行，且 `config/config.yaml` 文件存在

### 4. 模型不支持
```
Error: model not found or not supported
```
**解决**: 检查 `config/config.yaml` 中的 `openai.model_name` 是否正确

## 自定义配置

你可以修改 `real_llm_demo.go` 中的测试用例来测试不同的场景：

```go
testCases := []struct {
    name        string
    userMessage string
    description string
}{
    {
        name:        "Your Custom Test",
        userMessage: "Your custom question here",
        description: "Description of what this test should do",
    },
}
```

## 注意事项

1. **API费用**: 使用真实API可能产生费用，请注意使用量
2. **超时设置**: 默认超时为60秒，复杂任务可能需要更长时间
3. **并发限制**: 某些API提供商有并发请求限制
4. **模型限制**: 不同模型有不同的token限制和能力

## 技术细节

- **配置加载**: 使用Viper从 `config/config.yaml` 加载配置
- **LLM客户端**: 使用 `pkg/llm/provider/openai_compatible.go`
- **多Agent架构**: 基于 `domain/multiagent/` 中的实现
- **流式处理**: 支持SSE风格的流式响应
- **错误处理**: 完整的上下文错误处理和日志记录
