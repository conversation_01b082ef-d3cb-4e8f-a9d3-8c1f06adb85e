package runtime

import "time"

// Runtime represents the runtime environment associated with a project.
// It can map to either a Docker container or a Kubernetes Pod.
type Runtime struct {
	ID        string `bson:"_id,omitempty" json:"id,omitempty"` // Unique identifier (e.g., MongoDB ObjectID or UUID)
	ProjectID string `bson:"project_id" json:"project_id"`      // ID of the associated project

	// Provider type: "docker" or "kubernetes" - added for platform migration support
	ProviderType string `bson:"provider_type" json:"provider_type"` // Provider type: "docker" or "kubernetes"

	// Docker specific fields (legacy, but maintained for backward compatibility)
	ContainerID string `bson:"container_id,omitempty" json:"container_id,omitempty"` // ID of the managed container (e.g., Docker container ID)
	HostPath    string `bson:"host_path,omitempty" json:"host_path,omitempty"`       // Path on the host machine mounted into the container (Docker only)

	// Kubernetes specific fields
	Namespace   string `bson:"namespace,omitempty" json:"namespace,omitempty"`       // K8s namespace where the pod is deployed
	PodName     string `bson:"pod_name,omitempty" json:"pod_name,omitempty"`         // Name of the managed pod
	ServiceName string `bson:"service_name,omitempty" json:"service_name,omitempty"` // Name of the associated service
	IngressHost string `bson:"ingress_host,omitempty" json:"ingress_host,omitempty"` // Base ingress host (e.g., "your-domain.com")
	PVCName     string `bson:"pvc_name,omitempty" json:"pvc_name,omitempty"`         // Name of the persistent volume claim

	// Common fields for both providers
	ImageName      string    `bson:"image_name" json:"image_name"`                                 // Name of the image used for the runtime
	Status         string    `bson:"status" json:"status"`                                         // Status, e.g., "running", "stopped", "error", "starting"
	LastKnownError string    `bson:"last_known_error,omitempty" json:"last_known_error,omitempty"` // Stores the last known error message if status is "error"
	CreatedAt      time.Time `bson:"created_at" json:"created_at"`
	LastUsedAt     time.Time `bson:"last_used_at,omitempty" json:"last_used_at,omitempty"` // Timestamp of when the runtime was last actively used
	UpdatedAt      time.Time `bson:"updated_at" json:"updated_at"`                         // Timestamp of the last update to this record

	// Network configuration for exposed services (primarily for K8s)
	ExposedPorts []ExposedPort `bson:"exposed_ports,omitempty" json:"exposed_ports,omitempty"` // List of exposed ports with their external URLs
}

// ExposedPort represents a port exposed by the runtime environment
type ExposedPort struct {
	Name        string `bson:"name" json:"name"`                                     // Port name (e.g., "web", "api", "dev")
	Port        int32  `bson:"port" json:"port"`                                     // Internal port number
	TargetPort  int32  `bson:"target_port" json:"target_port"`                       // Target port in the container/pod
	Protocol    string `bson:"protocol" json:"protocol"`                             // Protocol: "TCP" or "UDP"
	ExternalURL string `bson:"external_url,omitempty" json:"external_url,omitempty"` // External access URL (e.g., "http://8080-project1.your-domain.com")
}

// CollectionName returns the MongoDB collection name for the Runtime model.
func (rt Runtime) CollectionName() string {
	return "runtimes"
}

// IsDocker returns true if this runtime uses Docker provider
func (rt Runtime) IsDocker() bool {
	return rt.ProviderType == "docker" || rt.ProviderType == "" // Empty for backward compatibility
}

// IsKubernetes returns true if this runtime uses Kubernetes provider
func (rt Runtime) IsKubernetes() bool {
	return rt.ProviderType == "kubernetes"
}

// GetRuntimeID returns the appropriate runtime identifier based on provider type
// For Docker: returns ContainerID
// For Kubernetes: returns PodName
func (rt Runtime) GetRuntimeID() string {
	if rt.IsKubernetes() {
		return rt.PodName
	}
	return rt.ContainerID
}
