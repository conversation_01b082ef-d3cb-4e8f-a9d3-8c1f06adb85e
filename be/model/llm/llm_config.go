package llm

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LLMConfig represents the configuration for a Large Language Model client.
type LLMConfig struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`      // Unique configuration ID
	Name      string             `bson:"name" json:"name"`             // User-friendly name for this configuration (e.g., "OpenAI GPT-4", "My Custom Kimi")
	Type      string             `bson:"type" json:"type"`             // Type of LLM provider (e.g., "openai_compatible", "gemini")
	APIKey    string             `bson:"api_key" json:"api_key"`       // API Key for the LLM (sensitive)
	APIBase   string             `bson:"api_base" json:"api_base"`     // Base URL for the LLM API endpoint
	ModelName string             `bson:"model_name" json:"model_name"` // Specific model name to use (e.g., "gpt-4o", "kimi-k2")
	CreatedAt time.Time          `bson:"created_at" json:"created_at"` // Timestamp when the configuration was created
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"` // Timestamp when the configuration was last updated
}

// CollectionName returns the MongoDB collection name for LLMConfig.
func (LLMConfig) CollectionName() string {
	return "llm_configs"
}
