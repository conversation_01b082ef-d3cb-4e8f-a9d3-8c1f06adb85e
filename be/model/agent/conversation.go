package agent

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Role represents the originator of a message in a conversation.
type Role string

const (
	RoleSystem    Role = "system"
	RoleUser      Role = "user"
	RoleAssistant Role = "assistant"
)

// Conversation represents a single chat session with the AI Agent.
type Conversation struct {
	ID            primitive.ObjectID `bson:"_id,omitempty"`   // Unique conversation ID
	UserID        string             `bson:"user_id"`         // ID of the user who initiated the conversation
	ProjectID     string             `bson:"project_id"`      // ID of the project context
	Title         string             `bson:"title,omitempty"` // Optional title (e.g., summary of first message)
	CreatedAt     time.Time          `bson:"created_at"`      // Timestamp when the conversation was created
	LastUpdatedAt time.Time          `bson:"last_updated_at"` // Timestamp when the conversation was last updated
	Messages      []Message          `bson:"messages"`        // Chronological list of messages in the conversation
	// Optional: Add other metadata like model used, total tokens, etc.
}

// CollectionName returns the MongoDB collection name for Conversation.
func (Conversation) CollectionName() string {
	return "agent_conversations"
}

// Message represents a single turn or event within a conversation.
const (
	MsgTypeAssistantResponse = "assistant_response"
	MsgTypeUserInput         = "user_input"
	MsgTypeToolResult        = "tool_result"
	MsgTypeContinueContent   = "continue_content"
)

type Message struct {
	MsgID     string    `json:"msg_id" bson:"msg_id"`                     // Unique message/event ID (e.g., UUID)
	MsgType   string    `json:"msg_type" bson:"msg_type"`                 // Message type (e.g. "assistant_response","user_input", "tool_result", "continue_content")
	Role      Role      `json:"role" bson:"role"`                         // Who generated this message/event (system, user, assistant)
	Content   string    `json:"content" bson:"content"`                   // Text content of the message (user input, assistant reply)
	Images    []string  `json:"images,omitempty" bson:"images,omitempty"` // Optional: Base64 encoded images for vision models
	Timestamp time.Time `json:"timestamp" bson:"timestamp"`               // Timestamp of the message/event
	// Optional: Add fields for thinking process, citations, user feedback, etc.
}

// ToolCall represents a request made by the assistant to invoke a specific tool.
type ToolCall struct {
	CallID     string      `json:"call_id" bson:"call_id"`         // Unique ID for this specific tool call attempt
	MCPService string      `json:"mcp_service" bson:"mcp_service"` // Target MCP service name (e.g., "mcp.projectfs")
	MCPMethod  string      `json:"mcp_method" bson:"mcp_method"`   // Target MCP method name (e.g., "readFile")
	MCPParams  interface{} `json:"mcp_params" bson:"mcp_params"`   // Parameters for the MCP call (use interface{} for flexibility, consider defining specific structs later)
	// Optional: Add assistant's rationale for the call
}

// ToolResult represents the result returned from executing a tool call.
// This is embedded within a Message when Role is RoleTool.
type ToolResult struct {
	CallID  string `json:"call_id" bson:"call_id"` // The ID of the tool call this result corresponds to.
	Content string `json:"content" bson:"content"` // The textual output/result from the tool execution.
	// Potential future fields: Error *ToolError `json:"error,omitempty" bson:"error,omitempty"`
}
