package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Model represents an AI model configuration in the database
type Model struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ModelName string             `bson:"model_name" json:"model_name"` // e.g., "gemini-2.5-pro"
	APIKey    string             `bson:"api_key" json:"-"`             // Hidden from JSON response for security
	APIBase   string             `bson:"api_base" json:"api_base"`     // e.g., "https://generativelanguage.googleapis.com/v1beta/openai/"
	CreatedAt time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"`
	IsActive  bool               `bson:"is_active" json:"is_active"` // Whether this model is available for use
}

// ModelListResponse represents the response for listing models (without sensitive data)
type ModelListResponse struct {
	ID        string    `json:"id"`
	ModelName string    `json:"model_name"`
	APIBase   string    `json:"api_base"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsActive  bool      `json:"is_active"`
}

// ToListResponse converts Model to ModelListResponse (excluding sensitive data)
func (m *Model) ToListResponse() ModelListResponse {
	return ModelListResponse{
		ID:        m.ID.Hex(),
		ModelName: m.ModelName,
		APIBase:   m.APIBase,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
		IsActive:  m.IsActive,
	}
}
