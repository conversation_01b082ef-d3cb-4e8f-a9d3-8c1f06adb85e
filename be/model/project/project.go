package project

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Project represents the metadata for a user project.
type Project struct {
	ID          primitive.ObjectID `bson:"_id,omitempty"       json:"id,omitempty"`            // MongoDB primary key
	ProjectID   string             `bson:"project_id"          json:"project_id"`              // User-facing/FS-related Project ID (ensure uniqueness)
	UserID      primitive.ObjectID `bson:"user_id"             json:"-"`                       // ID of the user who owns the project (don't expose in JSON)
	Name        string             `bson:"name,omitempty"        json:"name,omitempty"`        // Optional display name for the project
	Description string             `bson:"description,omitempty" json:"description,omitempty"` // Optional description
	CreatedAt   time.Time          `bson:"created_at"          json:"created_at"`              // Timestamp of project metadata creation
	// Add other metadata fields as needed, e.g., last accessed time
}

// CollectionName returns the MongoDB collection name for Project.
func (Project) CollectionName() string {
	return "projects"
}
