package requirement

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Status constants for requirement processing states
const (
	StatusPending             = "pending"              // Requirement created, awaiting AI question generation
	StatusImageAnalyzing      = "image_analyzing"      // Uploaded images are being analyzed by AI
	StatusWebsiteAnalyzing    = "website_analyzing"    // Reference website is being analyzed by AI
	StatusProcessing          = "processing"           // AI questions generated, awaiting user answers
	StatusPlanGenerating      = "plan_generating"      // User answered questions, AI is generating development plan
	StatusWireframeGenerating = "wireframe_generating" // Development plan completed, AI is generating wireframe
	StatusCompleted           = "completed"            // User has provided answers and development plan is generated
)

// AI Model constants for requirement processing
const (
	ModelGemini25Pro   = "gemini-2.5-pro"   // Gemini 2.5 Pro - High quality model for comprehensive analysis
	ModelGemini25Flash = "gemini-2.5-flash" // Gemini 2.5 Flash - Fast response model for quick processing
)

// DefaultModel defines the default AI model to use when none is specified
const DefaultModel = ModelGemini25Pro

// Wireframe represents a single wireframe option
type Wireframe struct {
	ID          string    `bson:"id" json:"id"`                     // Unique wireframe ID within the requirement
	Title       string    `bson:"title" json:"title"`               // Descriptive title for this wireframe variant
	Description string    `bson:"description" json:"description"`   // Brief description of this wireframe's approach/style
	HTML        string    `bson:"html" json:"html"`                 // The actual HTML content of the wireframe
	GeneratedAt time.Time `bson:"generated_at" json:"generated_at"` // Timestamp when this wireframe was generated
}

// Requirement represents a user requirement that will be refined through AI-generated questions
type Requirement struct {
	ID                     primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`                        // Unique requirement ID (MongoDB ObjectID)
	UserID                 primitive.ObjectID `bson:"user_id" json:"user_id"`                               // ID of the user who created this requirement
	Title                  string             `bson:"title" json:"title"`                                   // Brief title/summary of the requirement
	Content                string             `bson:"content" json:"content"`                               // Detailed description of the requirement
	ModelName              string             `bson:"model_name" json:"model_name"`                         // AI model selected by user for processing this requirement
	ReferenceImages        []ReferenceImage   `bson:"reference_images" json:"reference_images"`             // User-uploaded reference images with AI analysis
	ReferenceWebsite       *ReferenceWebsite  `bson:"reference_website" json:"reference_website"`           // User-provided reference website with AI analysis
	LegacyReferenceWebsite *string            `bson:"legacy_reference_website,omitempty" json:"legacy_reference_website,omitempty"` // Legacy string format for backward compatibility
	Questions              []Question         `bson:"questions" json:"questions"`                           // AI-generated questions to refine the requirement
	Answers                []Answer           `bson:"answers" json:"answers"`                               // User's answers to the questions
	DevelopmentPlan        string             `bson:"development_plan" json:"development_plan"`             // AI-generated detailed development plan based on requirement and answers
	WireframeHTML          string             `bson:"wireframe_html" json:"wireframe_html"`                 // Legacy: single wireframe (for backward compatibility)
	Wireframes             []Wireframe        `bson:"wireframes" json:"wireframes"`                         // AI-generated multiple wireframe options
	Status                 string             `bson:"status" json:"status"`                                 // Current processing status (pending/processing/plan_generating/completed)
	CreatedAt              time.Time          `bson:"created_at" json:"created_at"`                         // Timestamp when requirement was created
	UpdatedAt              time.Time          `bson:"updated_at" json:"updated_at"`                         // Timestamp when requirement was last updated
}

// CollectionName returns the MongoDB collection name for Requirement.
func (Requirement) CollectionName() string {
	return "requirements"
}

// GetReferenceWebsiteURL returns the reference website URL, handling backward compatibility
func (r *Requirement) GetReferenceWebsiteURL() string {
	if r.ReferenceWebsite != nil {
		return r.ReferenceWebsite.URL
	}
	if r.LegacyReferenceWebsite != nil {
		return *r.LegacyReferenceWebsite
	}
	return ""
}

// HasReferenceWebsite returns true if the requirement has a reference website (either new or legacy format)
func (r *Requirement) HasReferenceWebsite() bool {
	return r.ReferenceWebsite != nil || r.LegacyReferenceWebsite != nil
}

// UnmarshalBSON implements custom BSON unmarshaling to handle backward compatibility
func (r *Requirement) UnmarshalBSON(data []byte) error {
	// Create a temporary struct with the same fields but without custom unmarshaling
	type TempRequirement Requirement
	temp := (*TempRequirement)(r)
	
	// First try to unmarshal normally
	if err := bson.Unmarshal(data, temp); err != nil {
		// If normal unmarshaling fails, try to handle legacy reference_website
		var rawDoc bson.M
		if err := bson.Unmarshal(data, &rawDoc); err != nil {
			return err
		}
		
		// Check if reference_website is a string
		if refWebsite, exists := rawDoc["reference_website"]; exists {
			if urlStr, ok := refWebsite.(string); ok {
				// Store the string value in legacy field
				r.LegacyReferenceWebsite = &urlStr
				// Remove reference_website from raw doc to avoid unmarshal error
				delete(rawDoc, "reference_website")
				// Re-marshal and unmarshal without the problematic field
				cleanData, err := bson.Marshal(rawDoc)
				if err != nil {
					return err
				}
				return bson.Unmarshal(cleanData, temp)
			}
		}
		return err
	}
	return nil
}

// Question represents an AI-generated question to help refine a user requirement
type Question struct {
	ID        string    `bson:"id" json:"id"`                 // Unique question ID within the requirement
	Text      string    `bson:"text" json:"text"`             // The actual question text
	Type      string    `bson:"type" json:"type"`             // Question type: "choice" for single choice questions
	Options   []string  `bson:"options" json:"options"`       // Available options for choice questions
	Category  string    `bson:"category" json:"category"`     // Category/type of question (e.g., "functional", "technical", "ui")
	CreatedAt time.Time `bson:"created_at" json:"created_at"` // Timestamp when question was generated
}

// Answer represents a user's response to an AI-generated question
type Answer struct {
	QuestionID    string    `bson:"question_id" json:"question_id"`       // ID of the question this answer corresponds to
	SelectedIndex int       `bson:"selected_index" json:"selected_index"` // Index of selected option for choice questions (0-based)
	Text          string    `bson:"text" json:"text"`                     // User's answer text (for backward compatibility)
	AnsweredAt    time.Time `bson:"answered_at" json:"answered_at"`       // Timestamp when answer was provided
}

// ReferenceImage represents a user-uploaded reference image with AI analysis
type ReferenceImage struct {
	ID         string        `bson:"id" json:"id"`                   // Unique image ID within the requirement
	FileName   string        `bson:"file_name" json:"file_name"`     // Original filename of the uploaded image
	FilePath   string        `bson:"file_path" json:"file_path"`     // Server storage path for the image file
	FileSize   int64         `bson:"file_size" json:"file_size"`     // File size in bytes
	MimeType   string        `bson:"mime_type" json:"mime_type"`     // MIME type (e.g., image/jpeg, image/png)
	Analysis   ImageAnalysis `bson:"analysis" json:"analysis"`       // AI analysis results for the image
	UploadedAt time.Time     `bson:"uploaded_at" json:"uploaded_at"` // Timestamp when image was uploaded
	AnalyzedAt *time.Time    `bson:"analyzed_at" json:"analyzed_at"` // Timestamp when AI analysis was completed (nullable)
}

// ImageAnalysis represents the AI analysis results of a reference image across multiple dimensions
type ImageAnalysis struct {
	// Basic Visual Elements - What can be seen?
	VisualElements VisualElementsAnalysis `bson:"visual_elements" json:"visual_elements"`

	// Technical and Style Aspects - How is it made?
	TechnicalStyle TechnicalStyleAnalysis `bson:"technical_style" json:"technical_style"`

	// Emotional and Atmospheric Aspects - How does it feel?
	EmotionalTone EmotionalToneAnalysis `bson:"emotional_tone" json:"emotional_tone"`

	// Narrative and Symbolic Aspects - What story does it tell?
	NarrativeSymbolic NarrativeSymbolicAnalysis `bson:"narrative_symbolic" json:"narrative_symbolic"`

	// Business Dimension - What business is it about?
	BusinessDimension BusinessDimensionAnalysis `bson:"business_dimension" json:"business_dimension"`

	// Overall summary and recommendations
	Summary string `bson:"summary" json:"summary"` // Comprehensive summary combining all analysis dimensions
}

// VisualElementsAnalysis analyzes the basic visual components of the image
type VisualElementsAnalysis struct {
	MainSubjects   []string `bson:"main_subjects" json:"main_subjects"`     // Primary subjects and focal points
	Composition    string   `bson:"composition" json:"composition"`         // Layout and compositional structure
	ColorPalette   []string `bson:"color_palette" json:"color_palette"`     // Dominant colors and color schemes
	LightingStyle  string   `bson:"lighting_style" json:"lighting_style"`   // Lighting characteristics and mood
	LinesShapes    string   `bson:"lines_shapes" json:"lines_shapes"`       // Linear elements and geometric forms
	TexturePattern string   `bson:"texture_pattern" json:"texture_pattern"` // Surface textures and patterns
}

// TechnicalStyleAnalysis analyzes the technical execution and artistic style
type TechnicalStyleAnalysis struct {
	Medium        string `bson:"medium" json:"medium"`                 // Medium and technique used
	ArtisticStyle string `bson:"artistic_style" json:"artistic_style"` // Overall artistic style and characteristics
	Technique     string `bson:"technique" json:"technique"`           // Specific techniques employed
	Quality       string `bson:"quality" json:"quality"`               // Technical quality assessment
}

// EmotionalToneAnalysis analyzes the emotional and atmospheric qualities
type EmotionalToneAnalysis struct {
	OverallMood   string `bson:"overall_mood" json:"overall_mood"`     // General atmospheric mood
	EmotionalTone string `bson:"emotional_tone" json:"emotional_tone"` // Emotional feeling conveyed
	SensoryImpact string `bson:"sensory_impact" json:"sensory_impact"` // Sensory associations and impact
	EnergyLevel   string `bson:"energy_level" json:"energy_level"`     // Energy and dynamism level
}

// NarrativeSymbolicAnalysis analyzes the story and symbolic meaning
type NarrativeSymbolicAnalysis struct {
	Narrative        string   `bson:"narrative" json:"narrative"`                 // Story or narrative elements
	SymbolsMetaphors []string `bson:"symbols_metaphors" json:"symbols_metaphors"` // Symbolic elements and metaphors
	ThematicContent  string   `bson:"thematic_content" json:"thematic_content"`   // Themes and conceptual content
	CulturalContext  string   `bson:"cultural_context" json:"cultural_context"`   // Cultural and historical context
}

// BusinessDimensionAnalysis analyzes the business dimension of the image
type BusinessDimensionAnalysis struct {
	BusinessDirection string `bson:"business_direction" json:"business_direction"` // Business direction
	BusinessFunction  string `bson:"business_function" json:"business_function"`   // Business function
	BusinessProcess   string `bson:"business_process" json:"business_process"`     // Business process
}

// ReferenceWebsite represents a user-provided reference website with AI analysis
type ReferenceWebsite struct {
	URL        string          `bson:"url" json:"url"`                 // The reference website URL
	Analysis   WebsiteAnalysis `bson:"analysis" json:"analysis"`       // AI analysis results for the website
	AnalyzedAt *time.Time      `bson:"analyzed_at" json:"analyzed_at"` // Timestamp when AI analysis was completed (nullable)
	CreatedAt  time.Time       `bson:"created_at" json:"created_at"`   // Timestamp when website reference was added
}

// LegacyReferenceWebsite represents the old string format for backward compatibility
type LegacyReferenceWebsite string

// WebsiteAnalysis represents the AI analysis results of a reference website
type WebsiteAnalysis struct {
	// Visual Design Elements - How does it look?
	VisualDesign VisualDesignAnalysis `bson:"visual_design" json:"visual_design"`

	// User Experience and Interaction - How does it work?
	UserExperience UserExperienceAnalysis `bson:"user_experience" json:"user_experience"`

	// Content and Information Architecture - What information does it contain?
	ContentStructure ContentStructureAnalysis `bson:"content_structure" json:"content_structure"`

	// Technical Implementation - How is it built?
	TechnicalAspects TechnicalAspectsAnalysis `bson:"technical_aspects" json:"technical_aspects"`

	// Brand and Emotional Tone - What feeling does it convey?
	BrandTone BrandToneAnalysis `bson:"brand_tone" json:"brand_tone"`

	// Overall summary and recommendations
	Summary string `bson:"summary" json:"summary"` // Comprehensive summary combining all analysis dimensions
}

// VisualDesignAnalysis analyzes the visual design elements of the website
type VisualDesignAnalysis struct {
	ColorScheme    []string `bson:"color_scheme" json:"color_scheme"`       // Primary and secondary colors used
	Typography     string   `bson:"typography" json:"typography"`           // Font choices and text styling
	Layout         string   `bson:"layout" json:"layout"`                   // Overall layout structure and grid system
	VisualStyle    string   `bson:"visual_style" json:"visual_style"`       // Overall visual style (modern, classic, minimalist, etc.)
	ImageryStyle   string   `bson:"imagery_style" json:"imagery_style"`     // Style of images and graphics used
	SpacingDensity string   `bson:"spacing_density" json:"spacing_density"` // Use of whitespace and content density
}

// UserExperienceAnalysis analyzes the user experience and interaction design
type UserExperienceAnalysis struct {
	Navigation       string `bson:"navigation" json:"navigation"`             // Navigation structure and usability
	InteractionStyle string `bson:"interaction_style" json:"interaction_style"` // How users interact with the site
	Responsiveness   string `bson:"responsiveness" json:"responsiveness"`     // Mobile and responsive design approach
	Accessibility    string `bson:"accessibility" json:"accessibility"`       // Accessibility features and considerations
	UserFlow         string `bson:"user_flow" json:"user_flow"`               // How users move through the site
}

// ContentStructureAnalysis analyzes the content organization and information architecture
type ContentStructureAnalysis struct {
	ContentTypes      []string `bson:"content_types" json:"content_types"`           // Types of content present
	InformationHierarchy string `bson:"information_hierarchy" json:"information_hierarchy"` // How information is organized
	ContentTone       string   `bson:"content_tone" json:"content_tone"`             // Tone and style of written content
	ContentDensity    string   `bson:"content_density" json:"content_density"`       // Amount and density of information
}

// TechnicalAspectsAnalysis analyzes the technical implementation aspects
type TechnicalAspectsAnalysis struct {
	FrameworkTech   string `bson:"framework_tech" json:"framework_tech"`     // Detected frameworks or technologies
	Performance     string `bson:"performance" json:"performance"`           // Performance characteristics
	ModernFeatures  string `bson:"modern_features" json:"modern_features"`   // Modern web features used
	CodeQuality     string `bson:"code_quality" json:"code_quality"`         // Code quality and best practices
}

// BrandToneAnalysis analyzes the brand personality and emotional tone
type BrandToneAnalysis struct {
	BrandPersonality string `bson:"brand_personality" json:"brand_personality"` // Overall brand personality
	EmotionalTone    string `bson:"emotional_tone" json:"emotional_tone"`       // Emotional feeling conveyed
	TargetAudience   string `bson:"target_audience" json:"target_audience"`     // Apparent target audience
	BusinessType     string `bson:"business_type" json:"business_type"`         // Type of business or organization
}
