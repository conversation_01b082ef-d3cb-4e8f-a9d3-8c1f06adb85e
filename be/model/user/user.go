package user

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// User represents a user account in the system.
type User struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`                                // Unique user ID (MongoDB ObjectID)
	Username      string             `bson:"username" json:"username"`                               // Unique username for login
	Email         string             `bson:"email,omitempty" json:"email,omitempty"`                 // Optional user email
	PasswordHash  string             `bson:"password_hash" json:"-"`                                 // Hashed password (do not expose to JSON)
	FullName      string             `bson:"full_name,omitempty" json:"full_name,omitempty"`         // Optional full name
	AvatarURL     string             `bson:"avatar_url,omitempty" json:"avatar_url,omitempty"`       // Optional URL to user's avatar
	IsActive      bool               `bson:"is_active" json:"is_active"`                             // Flag indicating if the account is active
	IsAdmin       bool               `bson:"is_admin" json:"is_admin"`                               // Flag indicating administrative privileges
	CreatedAt     time.Time          `bson:"created_at" json:"created_at"`                           // Timestamp of account creation
	LastUpdatedAt time.Time          `bson:"last_updated_at" json:"last_updated_at"`                 // Timestamp of last profile update
	LastLoginAt   *time.Time         `bson:"last_login_at,omitempty" json:"last_login_at,omitempty"` // Timestamp of last login (pointer to allow null)
	// Add other fields as needed, e.g., preferences, roles, etc.
}

// CollectionName returns the MongoDB collection name for User.
func (User) CollectionName() string {
	return "users"
}
