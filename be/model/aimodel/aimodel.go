package aimodel

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AIModel represents an AI model configuration stored in MongoDB
type AIModel struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"` // Unique AI model ID (MongoDB ObjectID)
	APIKey    string             `bson:"api_key" json:"api_key"`            // API key for accessing the model
	APIBase   string             `bson:"api_base" json:"api_base"`          // Base URL for the API endpoint
	ModelName string             `bson:"model_name" json:"model_name"`      // Name/identifier of the AI model
	IsActive  bool               `bson:"is_active" json:"is_active"`        // Whether this model is currently active/available
	CreatedAt time.Time          `bson:"created_at" json:"created_at"`      // Timestamp when model was created
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"`      // Timestamp when model was last updated
}

// CollectionName returns the MongoDB collection name for AIModel.
func (AIModel) CollectionName() string {
	return "aimodels"
}
