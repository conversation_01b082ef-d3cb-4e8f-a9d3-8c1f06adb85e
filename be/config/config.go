package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application.
// The fields are mapped from config.yaml.
type Config struct {
	Server       ServerConfig       `mapstructure:"server"`
	MongoDB      MongoDBConfig      `mapstructure:"mongodb"`
	Redis        RedisConfig        `mapstructure:"redis"`
	Session      SessionConfig      `mapstructure:"session"`
	Workspace    WorkspaceConfig    `mapstructure:"workspace"`
	FileUpload   FileUploadConfig   `mapstructure:"file_upload"`
	MCP          MCPConfig          `mapstructure:"mcp"`
	Logger       LoggerConfig       `mapstructure:"logger"`
	Runtime      RuntimeConfig      `mapstructure:"runtime"`
	OpenAI       OpenAIConfig       `mapstructure:"openai"`
	Tokenizer    TokenizerConfig    `mapstructure:"tokenizer"`
	GoogleSearch GoogleSearchConfig `mapstructure:"google_search"`
	History      HistoryConfig      `mapstructure:"history"`
}

type OpenAIConfig struct {
	APIKey    string `mapstructure:"api_key"`
	APIBase   string `mapstructure:"api_base"`
	ModelName string `mapstructure:"model_name"`
	Path      string `mapstructure:"path"`
}

// ServerConfig holds server specific config
type ServerConfig struct {
	Port string `mapstructure:"port"`
}

// MongoDBConfig holds MongoDB specific config
type MongoDBConfig struct {
	URI      string `mapstructure:"uri"`
	Database string `mapstructure:"database"`
}

// RedisConfig holds Redis specific config
type RedisConfig struct {
	Address  string `mapstructure:"address"`
	Password string `mapstructure:"password"`
}

// SessionConfig holds Session specific config
type SessionConfig struct {
	Secret string `mapstructure:"secret"`
}

// WorkspaceConfig holds Workspace specific config
type WorkspaceConfig struct {
	BasePath string `mapstructure:"base_path"`
}

// FileUploadConfig holds file upload specific config
type FileUploadConfig struct {
	UploadDir string `mapstructure:"upload_dir"`
	MaxSize   int64  `mapstructure:"max_size"`  // Maximum file size in bytes
	MaxCount  int    `mapstructure:"max_count"` // Maximum number of files per upload
	BaseURL   string `mapstructure:"base_url"`  // Base URL for serving uploaded files
}

// MCPConfig holds MCP specific config
type MCPConfig struct {
	ServersConfigPath string `mapstructure:"servers_config_path"`
}

// LoggerConfig holds Logger specific config
type LoggerConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

// HistoryConfig holds conversation history management specific config
type HistoryConfig struct {
	MaxMessages int `mapstructure:"max_messages"`
	MaxTokens   int `mapstructure:"max_tokens"`
}

// RuntimeConfig holds runtime specific config
type RuntimeConfig struct {
	Provider         string           `mapstructure:"provider"` // "docker" or "kubernetes"
	DefaultImageName string           `mapstructure:"default_image_name"`
	DockerHost       string           `mapstructure:"docker_host"` // Example: unix:///var/run/docker.sock or tcp://localhost:2375
	Kubernetes       KubernetesConfig `mapstructure:"kubernetes"`
}

// KubernetesConfig holds Kubernetes-specific configuration
type KubernetesConfig struct {
	Kubeconfig string        `mapstructure:"kubeconfig"` // Path to kubeconfig file (empty for in-cluster or default)
	Namespace  string        `mapstructure:"namespace"`  // Namespace for runtime pods
	Traefik    TraefikConfig `mapstructure:"traefik"`    // Traefik IngressRoute configuration
	Storage    StorageConfig `mapstructure:"storage"`    // Storage configuration
}

// TraefikConfig holds Traefik-specific configuration for IngressRoutes
type TraefikConfig struct {
	Enabled      bool      `mapstructure:"enabled"`       // Enable Traefik IngressRoute creation
	Domain       string    `mapstructure:"domain"`        // Base domain (e.g., "your-domain.com")
	EntryPoints  []string  `mapstructure:"entry_points"`  // Traefik entry points (e.g., ["web", "websecure"])
	DefaultPorts []int32   `mapstructure:"default_ports"` // Default ports to expose (e.g., [8080, 3000, 5000])
	TLS          TLSConfig `mapstructure:"tls"`           // TLS configuration
}

// TLSConfig holds TLS-specific configuration for Traefik
type TLSConfig struct {
	Enabled    bool   `mapstructure:"enabled"`     // Enable TLS/HTTPS
	SecretName string `mapstructure:"secret_name"` // Name of the TLS secret
}

// StorageConfig holds storage-specific configuration for K8s
type StorageConfig struct {
	Class string `mapstructure:"class"` // Storage class name (e.g., "local-path", "fast-ssd")
	Size  string `mapstructure:"size"`  // Default PVC size (e.g., "10Gi")
}

type TokenizerConfig struct {
	Encoding string `mapstructure:"encoding"`
}

// GoogleSearchConfig holds Google Custom Search API specific config
type GoogleSearchConfig struct {
	APIKey string `mapstructure:"api_key"`
	CX     string `mapstructure:"cx"`
}

// LoadConfig reads configuration from file or environment variables.
func LoadConfig(path string) (config Config, err error) {
	v := viper.New()

	v.AddConfigPath(path)     // Path to look for the config file in
	v.SetConfigName("config") // Name of config file (without extension)
	v.SetConfigType("yaml")   // REQUIRED if the config file does not have the extension in the name

	v.AutomaticEnv()                                   // Read in environment variables that match
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_")) // Replace dots with underscores for env var names (e.g., SERVER_PORT)

	// Attempt to read the config file
	if err = v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// Config file not found; ignore error if desired or handle otherwise
			fmt.Println("Warning: Config file not found at", path, ". Using defaults or environment variables.")
			// Optionally set defaults here if the file is optional
			// v.SetDefault("server.port", "8080")
		} else {
			// Config file was found but another error was produced
			return config, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Unmarshal the config into the Config struct
	err = v.Unmarshal(&config)
	if err != nil {
		return config, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	fmt.Println("Configuration loaded successfully.")
	// Optionally print loaded config for debugging (sensitive info should be masked)
	// fmt.Printf("Loaded config: %+v\n", config)

	return config, nil
}
