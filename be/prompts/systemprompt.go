package prompts

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"strings"
	"sync"
	"time"

	"embed"

	"github.com/mark3labs/mcp-go/mcp"
)

// --- 辅助数据结构 (从之前的讨论中复制) ---

type FileInfo struct {
	Name      string    `json:"name"`
	Path      string    `json:"path"`
	IsDir     bool      `json:"isDir"`
	Size      int64     `json:"size"`
	ModTime   time.Time `json:"modTime"`
	GitStatus string    `json:"gitStatus"`
}
type AuthorInfo struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}
type CommitInfo struct {
	Hash    string     `json:"hash"`
	Author  AuthorInfo `json:"author"`
	Date    time.Time  `json:"date"`
	Message string     `json:"message"`
}
type BranchInfo struct {
	Name      string `json:"name"`
	IsCurrent bool   `json:"isCurrent"`
}
type ToolParameter struct {
	Name           string `json:"name"`
	Description    string `json:"description"`
	IsRequired     bool   `json:"isRequired"`
	ParametersText string
}
type ToolDefinition struct {
	Name           string          `json:"name"`
	Description    string          `json:"description"`
	Parameters     []ToolParameter `json:"parameters,omitempty"`
	ParametersText string          `json:"parametersText"`
	UsageExample   string          `json:"usageExample"`
	RequiresMode   string          `json:"requiresMode,omitempty"`
}

// --- 动态数据结构 ---

type MCPInfo struct {
	Servers map[string][]mcp.Tool
}

// ProjectInfo holds project metadata information
type ProjectInfo struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

// RuntimeInfo holds runtime environment information
type RuntimeInfo struct {
	//ContainerID   string    `json:"container_id"`
	Status    string `json:"status"`
	ImageName string `json:"image_name"`
	//HostPath      string    `json:"host_path"`      // Host machine project path
	ContainerPath string    `json:"container_path"` // Container mount path (/workspace)
	CreatedAt     time.Time `json:"created_at"`
	LastUsedAt    time.Time `json:"last_used_at"`
}

// UserInfo holds user information
type UserInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type PromptDynamicData struct {
	// Path information for different contexts
	ProjectRoot       string `json:"project_root"`        // For MCP file operations: "."
	RuntimeWorkingDir string `json:"runtime_working_dir"` // For command execution: "/workspace"

	// Legacy fields (keeping for backward compatibility)
	Cwd                        string
	SystemInfoOS               string
	SystemInfoShell            string
	SystemInfoHome             string
	UserCustomInstructionsText string
	InteractionModes           struct{ PlanModeTool ToolDefinition }
	MCPServers                 MCPInfo

	// New structured information
	Project ProjectInfo `json:"project"`
	Runtime RuntimeInfo `json:"runtime"`
	User    UserInfo    `json:"user"`

	// Optional additional information
	RecentFiles      []string `json:"recent_files,omitempty"`
	ProjectStructure string   `json:"project_structure,omitempty"`
}

// --- 模板加载与执行 ---

//go:embed *.tmpl
var promptFS embed.FS

var (
	parsedTemplates *template.Template
	loadOnce        sync.Once
	loadError       error // 存储加载时的错误
)

// LoadTemplates 解析 prompts 目录下的所有 .tmpl 文件
// 这个函数现在是线程安全的，并且只会执行一次解析。
func LoadTemplates() error {
	loadOnce.Do(func() {
		// 使用 ParseFS 解析嵌入的文件系统
		// Funcs 必须在 ParseFS 之前调用
		tmpl := template.New("").Funcs(template.FuncMap{
			// 添加你可能需要的自定义函数
			"ToUpper": func(s string) string {
				return s // 示例函数
				// 注意：需要 Go 标准库 "strings"
				// import "strings"
				// return strings.ToUpper(s)
				// return s // 占位符实现
			},
			// 添加 GenerateMCPPrompt 作为模板函数
			"GenerateMCPPrompt": func(data PromptDynamicData) string {
				return generateMCPPrompt(data.MCPServers)
			},
			// 添加特殊函数来避免转义
			"unescaped": func(s string) template.HTML {
				return template.HTML(s)
			},
		})

		// 遍历并解析所有模板文件，确保不会自动HTML转义
		templates, err := promptFS.ReadDir(".")
		if err != nil {
			loadError = fmt.Errorf("failed to read embedded prompt templates: %w", err)
			return
		}

		for _, entry := range templates {
			if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".tmpl") {
				content, err := promptFS.ReadFile(entry.Name())
				if err != nil {
					loadError = fmt.Errorf("failed to read template %s: %w", entry.Name(), err)
					return
				}

				_, err = tmpl.New(entry.Name()).Parse(string(content))
				if err != nil {
					loadError = fmt.Errorf("failed to parse template %s: %w", entry.Name(), err)
					return
				}
			}
		}

		parsedTemplates = tmpl
		if loadError != nil {
			loadError = fmt.Errorf("failed to parse embedded prompt templates: %w", loadError)
			fmt.Println(loadError) // 在启动时打印错误
		} else {
			fmt.Println("Prompt templates loaded successfully.")
			// fmt.Println("Defined templates:", parsedTemplates.DefinedTemplates()) // 调试用
		}
	})
	return loadError // 返回加载时可能发生的错误
}

// GenerateSystemPrompt 使用加载的模板和动态数据生成最终的 System Prompt
// 确保 LoadTemplates 已被调用（例如在 main 函数中）。
func GenerateSystemPrompt(data PromptDynamicData) (string, error) {
	// 首先确保模板已成功加载
	initErr := LoadTemplates()
	if initErr != nil {
		return "", fmt.Errorf("failed to initialize templates: %w", initErr)
	}
	if parsedTemplates == nil {
		return "", fmt.Errorf("prompt templates are not loaded (parsedTemplates is nil)")
	}

	var buf bytes.Buffer
	// 执行名为 "system_prompt.tmpl" 的模板
	err := parsedTemplates.ExecuteTemplate(&buf, "system_prompt.tmpl", data)
	if err != nil {
		// 尝试打印已定义的模板以帮助调试
		// fmt.Println("Error executing template. Defined templates:", parsedTemplates.DefinedTemplates())
		return "", fmt.Errorf("failed to execute system prompt template 'system_prompt.tmpl': %w", err)
	}

	// 处理多余的空行，最多保留一个空行
	result := cleanExcessEmptyLines(buf.String())

	return result, nil
}

func generateMCPPrompt(mcpInfo MCPInfo) string {
	var buf bytes.Buffer

	if len(mcpInfo.Servers) == 0 {
		buf.WriteString("No MCP servers are currently connected.\n")
		return buf.String()
	}

	for serverName, tools := range mcpInfo.Servers {
		// 服务器标题
		buf.WriteString(fmt.Sprintf("## %s \n\n", serverName))

		// 工具列表
		if len(tools) > 0 {
			buf.WriteString("### Available Tools\n")
			for _, tool := range tools {
				// 工具名称和描述
				buf.WriteString(fmt.Sprintf("- %s: %s\n", tool.Name, tool.Description))

				// 工具的输入模式
				// 将InputSchema转为格式化的JSON字符串
				if inputSchema, err := json.MarshalIndent(tool.InputSchema, "    ", "  "); err == nil && len(inputSchema) > 2 {
					buf.WriteString("    Input Schema:\n")
					// 写入JSON字符串，保留引号
					// 注意：在模板中需要使用 {{unescaped (GenerateMCPPrompt .)}} 避免HTML转义
					buf.WriteString(fmt.Sprintf("    %s\n\n", string(inputSchema)))
				}
			}
		} else {
			buf.WriteString("No tools available on this server.\n\n")
		}

		// 直接资源部分可能需要另外添加
		// 当前MCPInfo结构中没有资源相关字段
	}

	return buf.String()
}

// cleanExcessEmptyLines 清理字符串中多余的空行，最多保留一个空行
func cleanExcessEmptyLines(input string) string {
	// 将输入文本按行分割
	lines := strings.Split(input, "\n")

	// 创建一个新的字符串切片用于存储处理后的行
	var result []string
	// 记录连续空行的数量
	consecutiveEmptyLines := 0

	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			// 当前行是空行
			consecutiveEmptyLines++
			// 如果这是第一个空行，则添加到结果中
			if consecutiveEmptyLines <= 1 {
				result = append(result, "")
			}
			// 如果是第二个或更多的连续空行，则忽略
		} else {
			// 当前行非空，重置计数器并添加到结果中
			consecutiveEmptyLines = 0
			result = append(result, line)
		}
	}

	// 将处理后的行重新连接成字符串
	return strings.Join(result, "\n")
}

// PrepareDynamicData 是一个示例函数，用于准备模板所需的数据
// 在实际应用中，这些数据会来自运行时环境和配置
func PrepareDynamicData( /* ... runtime parameters ... */ ) PromptDynamicData {
	planModeTool := ToolDefinition{Name: "plan_mode_response", Description: "Respond conversationally in PLAN MODE.", ParametersText: "response (required)", UsageExample: "<plan_mode_response><response>...</response></plan_mode_response>"}

	return PromptDynamicData{
		Cwd:                        "/Users/<USER>/project/be",
		SystemInfoOS:               "macOS Sonoma",
		SystemInfoShell:            "/bin/zsh",
		SystemInfoHome:             "/Users/<USER>",
		UserCustomInstructionsText: "- Always use Go generics where applicable.\n- Keep functions concise.",
		InteractionModes:           struct{ PlanModeTool ToolDefinition }{PlanModeTool: planModeTool},
	}
}
