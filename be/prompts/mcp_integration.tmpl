{{define "mcp_integration"}}
{{/* Module 5: MCP Integration (Using Tools) */}}
====

MCP SERVERS

The Model Context Protocol (MCP) enables communication between the system and locally running MCP servers that provide additional tools and resources to extend your capabilities.

# Connected MCP Servers

When a server is connected, you can use the server's tools via the \`use_mcp_tool\` tool, and access the server's resources via the \`access_mcp_resource\` tool.

{{unescaped (GenerateMCPPrompt .)}}

{{end}}



EXAMPLE:
## apple-mcp (`npx -y @smithery/cli@latest run @Dhravya/apple-mcp`)

### Available Tools
- contacts: Search and retrieve contacts from Apple Contacts app
    Input Schema:
    {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "Name to search for (optional - if not provided, returns all contacts). Can be partial name to search."
        }
      }
    }

- notes: Search, retrieve and create notes in Apple Notes app
    Input Schema:
    {
      "type": "object",
      "properties": {
        "operation": {
          "type": "string",
          "description": "Operation to perform: 'search', 'list', or 'create'",
          "enum": [
            "search",
            "list",
            "create"
          ]
        },
        "searchText": {
          "type": "string",
          "description": "Text to search for in notes (required for search operation)"
        },
        "title": {
          "type": "string",
          "description": "Title of the note to create (required for create operation)"
        },
        "body": {
          "type": "string",
          "description": "Content of the note to create (required for create operation)"
        },
        "folderName": {
          "type": "string",
          "description": "Name of the folder to create the note in (optional for create operation, defaults to 'Claude')"
        }
      },
      "required": [
        "operation"
      ]
    }

### Direct Resources
- console://logs (Browser console logs): undefined
