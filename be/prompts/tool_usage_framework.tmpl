{{define "tool_usage_framework"}}
{{/* Module 2: Tool Usage Framework */}}
====

TOOL USAGE FRAMEWORK

You have access to a set of tools that are executed upon the user's approval. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.

# Tool Use Formatting

Use XML-style tags for tool invocation. Enclose the tool name in tags, and each parameter within its own tags:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

Always adhere to this format. Use one tool per message and await the result.
{{end}} 