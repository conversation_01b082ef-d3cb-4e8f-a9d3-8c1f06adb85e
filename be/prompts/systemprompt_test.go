package prompts

import (
	"fmt"
	"strings"
	"testing"

	"github.com/mark3labs/mcp-go/mcp"
)

// TestLoadTemplates 测试模板加载功能
func TestLoadTemplates(t *testing.T) {
	// 测试模板加载
	err := LoadTemplates()
	if err != nil {
		t.Fatalf("LoadTemplates 应该成功加载模板，但报错: %v", err)
	}

	// 确保模板已正确加载
	if parsedTemplates == nil {
		t.Fatal("LoadTemplates 成功执行后 parsedTemplates 不应为 nil")
	}

	// 测试模板是否包含预期的模板
	templates := parsedTemplates.DefinedTemplates()
	if !strings.Contains(templates, "system_prompt.tmpl") {
		t.<PERSON>rrorf("已加载的模板中应该包含 system_prompt.tmpl，但实际内容是: %s", templates)
	}

	// 测试重复加载
	originalTemplates := parsedTemplates
	err = LoadTemplates()
	if err != nil {
		t.Fatalf("重复调用 LoadTemplates 不应返回错误，但报错: %v", err)
	}
	if originalTemplates != parsedTemplates {
		t.Error("LoadTemplates 在重复调用时应返回相同的模板实例，但返回了不同的实例")
	}
}

// TestGenerateSystemPrompt 测试系统提示生成功能
func TestGenerateSystemPrompt(t *testing.T) {
	// 准备测试数据
	testData := PromptDynamicData{
		Cwd:                        "/test/path",
		SystemInfoOS:               "TestOS",
		SystemInfoShell:            "/bin/test",
		SystemInfoHome:             "/home/<USER>",
		UserCustomInstructionsText: "- Test instruction.",
		MCPServers: MCPInfo{
			Servers: map[string][]mcp.Tool{
				"test_server": {
					{
						Name:        "test_tool",
						Description: "Test tool description.",
						InputSchema: mcp.ToolInputSchema{
							Type: "object",
							Properties: map[string]interface{}{
								"name": map[string]string{
									"type":        "string",
									"description": "Name to search for (optional - if not provided, returns all contacts). Can be partial name to search.",
								},
							},
						},
					},
				},
			},
		},
	}

	// 生成提示
	prompt, err := GenerateSystemPrompt(testData)
	if err != nil {
		t.Fatalf("GenerateSystemPrompt 应该成功生成提示，但报错: %v", err)
	}

	// 基本验证：确保生成的提示符不为空
	if strings.TrimSpace(prompt) == "" {
		t.Fatal("GenerateSystemPrompt 返回了空字符串")
	}

	fmt.Println("生成的提示词:", prompt)

	// // 验证提示中包含一些预期的内容
	// expectedContents := []string{
	// 	"/test/path",             // Cwd
	// 	"TestOS",                 // SystemInfoOS
	// 	"/bin/test",              // SystemInfoShell
	// 	"test_tool",              // Tool name
	// 	"Test tool description.", // Tool description
	// }

	// for _, expected := range expectedContents {
	// 	if !strings.Contains(prompt, expected) {
	// 		t.Errorf("生成的提示中应包含 %q，但未找到", expected)
	// 	}
	// }
}

// TestPrepareDynamicData 测试数据准备函数
func TestPrepareDynamicData(t *testing.T) {
	data := PrepareDynamicData()

	// 验证返回的数据结构包含预期字段
	if data.Cwd == "" {
		t.Error("PrepareDynamicData 返回的 Cwd 不应为空")
	}

	if data.SystemInfoOS == "" {
		t.Error("PrepareDynamicData 返回的 SystemInfoOS 不应为空")
	}
}

// TestErrorHandling 测试错误处理场景
func TestErrorHandling(t *testing.T) {
	// 创建临时备份并在测试后恢复
	origTemplates := parsedTemplates
	defer func() { parsedTemplates = origTemplates }()

	// 模拟模板未加载的情况
	parsedTemplates = nil

	_, err := GenerateSystemPrompt(PromptDynamicData{})
	if err == nil {
		t.Error("当模板未加载时，GenerateSystemPrompt 应返回错误，但未返回")
	}

	if !strings.Contains(err.Error(), "templates are not loaded") {
		t.Errorf("错误消息应包含 'templates are not loaded'，但得到: %v", err)
	}
}
