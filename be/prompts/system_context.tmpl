{{define "system_context"}}
{{/* Module 7: System & Environment Context */}}
====

SYSTEM & ENVIRONMENT CONTEXT

Operating System: {{.SystemInfoOS}}
Default Shell: {{.SystemInfoShell}}
Home Directory: {{.SystemInfoHome}}

====

PROJECT CONTEXT

Project ID: {{.Project.ID}}
Project Root Directory: {{.ProjectRoot}}
Created: {{.Project.CreatedAt.Format "2006-01-02 15:04:05"}}

====

RUNTIME ENVIRONMENT

Container Status: {{.Runtime.Status}}
Docker Image: {{.Runtime.ImageName}}
Runtime Working Directory: {{.RuntimeWorkingDir}}
Container Mount Path: {{.Runtime.ContainerPath}}
Runtime Created: {{.Runtime.CreatedAt.Format "2006-01-02 15:04:05"}}
Last Used: {{.Runtime.LastUsedAt.Format "2006-01-02 15:04:05"}}

====

PATH USAGE GUIDELINES

CRITICAL: Use different path concepts for different operations:

1. **For File System Operations (MCP tools like read_file, write_file, list_files, etc.)**:
   - Use project-relative paths starting from "{{.ProjectRoot}}"
   - Examples: "./src/main.go", "./config/app.yaml", "./README.md"
   - DO NOT use absolute container paths like "{{.RuntimeWorkingDir}}/src/main.go"

2. **For Command Execution (terminal commands, shell operations)**:
   - Commands execute in the runtime working directory: {{.RuntimeWorkingDir}}
   - You can use relative paths in commands: "ls ./src", "cd ./build && make"
   - The working directory is automatically set to {{.RuntimeWorkingDir}}

3. **Path Mapping Reference**:
   - Container mount path: {{.Runtime.ContainerPath}}
   - Project root for file operations: {{.ProjectRoot}}

This separation ensures that MCP file system tools work with clean project-relative paths,
while runtime operations work correctly within the containerized environment.
The containerized runtime path "{{.RuntimeWorkingDir}}" should NEVER be exposed to MCP file system tools.

{{end}} 