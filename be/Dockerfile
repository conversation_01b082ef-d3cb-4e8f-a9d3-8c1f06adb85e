# TODO: Add Docker build instructions
FROM golang:1.21-alpine AS builder

WORKDIR /app

# TODO: Copy go.mod and go.sum and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# TODO: Copy the rest of the source code
COPY . .

# TODO: Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -v -o server ./cmd/server

# TODO: Create final small image
FROM alpine:latest
WORKDIR /app
COPY --from=builder /app/server .
# TODO: Copy necessary config files or assets

# TODO: Expose port and define entrypoint
EXPOSE 8080
ENTRYPOINT ["./server"] 