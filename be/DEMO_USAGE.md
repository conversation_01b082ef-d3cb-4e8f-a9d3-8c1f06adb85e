# 🚀 Multi-Agent LLM Demo 使用指南

## 📋 概述

我已经为您创建了一个完整的多Agent系统真实LLM演示程序，可以使用您配置文件中的真实API来测试多Agent架构。

## 📁 文件结构

```
be/
├── domain/multiagent/
│   ├── simple_llm_demo.go          # 简化版LLM演示程序
│   └── simple_llm_demo_test.go     # 演示测试
├── cmd/multiagent-demo/
│   └── main.go                     # 独立可执行程序
├── run-multiagent-demo.sh          # 运行脚本
├── MULTIAGENT_DEMO_README.md       # 详细说明文档
└── DEMO_USAGE.md                   # 本文件
```

## 🔧 配置要求

确保您的 `config/config.yaml` 文件包含正确的OpenAI配置：

```yaml
# OpenAI configuration for Agent LLM
openai:
  api_key: "your-api-key-here"
  api_base: "https://openrouter.ai/api/v1"
  model_name: "moonshotai/kimi-k2:free"
```

## 🎯 运行方式

### 方式1: 使用运行脚本（推荐）

```bash
cd be
./run-multiagent-demo.sh
```

脚本会提供两个选项：
1. 作为测试运行
2. 作为独立程序运行

### 方式2: 直接运行测试

```bash
cd be
SIMPLE_LLM_DEMO=true go test ./domain/multiagent -v -run TestSimpleLLMDemo
```

### 方式3: 运行独立程序

```bash
cd be
go run ./cmd/multiagent-demo/main.go
```

## 🎪 Demo功能展示

### 1. 智能任务检测
- **简单问题**: "What is Go programming language?"
  - 检测结果: `false` (单Agent模式)
  - 复杂度: `simple`

- **复杂分析**: "Analyze microservices vs monolithic architecture..."
  - 检测结果: `true` (多Agent模式)
  - 复杂度: `high`
  - 推荐Agent数量: `3`

### 2. 真实LLM交互
- 使用配置文件中的真实API
- 流式响应显示
- 实时chunk计数和响应长度统计

### 3. 资源监控演示
- Agent使用情况跟踪
- Token使用统计
- 工具调用计数
- 执行时间监控

### 4. 权限验证演示
- 允许的工具调用验证
- 禁止的工具调用拦截
- 资源限制检查

## 📊 预期输出示例

```
=== Multi-Agent System Simple LLM Demo ===

--- Test Case 1: Simple Question ---
User Message: What is Go programming language? Please give me a brief introduction.
Description: This should use single agent mode
Task Detection Result: false
Task Complexity: simple
Starting LLM interaction...
Response:
---
Go is a programming language developed by Google in 2007...
---
Total chunks received: 12
Response length: 285 characters
This task is suitable for single agent processing

Waiting 3 seconds before next test case...

--- Test Case 2: Complex Analysis ---
User Message: Analyze the advantages and disadvantages of microservices architecture...
Description: This should trigger multi-agent processing
Task Detection Result: true
Task Complexity: high
Recommended Agent Count: 3
Starting LLM interaction...
Response:
---
# Microservices vs Monolithic Architecture Analysis

## Advantages of Microservices:
1. Scalability...
2. Technology diversity...
...
---
Total chunks received: 35
Response length: 1150 characters
This task would benefit from multi-agent processing

--- Resource Monitoring Demo ---
Agent Usage: Tokens=150, ToolCalls=2
Final Usage: Tokens=150, ToolCalls=2, Duration=1.234ms
Global Usage: TotalAgents=1, TotalTokens=150, TotalToolCalls=2

--- Permission Validation Demo ---
Allowed tool validation passed
Forbidden tool validation correctly failed: tool not allowed: mcp.forbidden.dangerous_operation

=== Simple LLM Demo Complete ===
```

## ⚠️ 注意事项

1. **API费用**: 使用真实API可能产生费用，请注意使用量
2. **网络连接**: 确保能访问配置的API端点
3. **API Key**: 确保API Key有效且有足够的配额
4. **超时设置**: 默认超时60秒，复杂任务可能需要更长时间

## 🔍 故障排除

### 配置文件问题
```
Error: failed to load config: Config file not found
```
**解决**: 确保在 `be` 目录下运行，且 `config/config.yaml` 存在

### API Key问题
```
Error: LLM config API key is required
```
**解决**: 检查 `config/config.yaml` 中的 `openai.api_key` 配置

### 网络连接问题
```
Error: connection timeout
```
**解决**: 检查网络连接和API端点是否可访问

## 🎨 自定义测试

您可以修改 `simple_llm_demo.go` 中的测试用例：

```go
testCases := []struct {
    name        string
    userMessage string
    description string
}{
    {
        name:        "Your Custom Test",
        userMessage: "Your custom question here",
        description: "Description of what this test should do",
    },
}
```

## 🚀 下一步

1. **运行demo**: 使用上述方式运行demo
2. **观察输出**: 查看任务检测和LLM响应
3. **测试不同问题**: 尝试不同复杂度的问题
4. **集成到主系统**: 将多Agent架构集成到主应用中

这个demo完美展示了多Agent系统的核心功能，包括智能任务检测、真实LLM交互、资源监控和权限验证！🎉
