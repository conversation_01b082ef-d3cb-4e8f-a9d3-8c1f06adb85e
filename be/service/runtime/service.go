package runtime

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	// "errors" // Standard library 'errors' - will be commented out
	"io"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	projectDomain "git.nevint.com/fota3/t-rex/domain/project" // Alias for project domain
	"git.nevint.com/fota3/t-rex/domain/projectfs"
	domainRuntime "git.nevint.com/fota3/t-rex/domain/runtime" // Alias for runtime domain
	"git.nevint.com/fota3/t-rex/model/runtime"
	"git.nevint.com/fota3/t-rex/pkg/middleware"
	// modelRuntime "git.nevint.com/fota3/t-rex/model/runtime" // If needed for runtime model directly
)

const (
	// Time allowed to write a message to the peer.
	writeWait = 10 * time.Second
	// Time allowed to read the next pong message from the peer.
	pongWait = 15 * 60 * time.Second // 15 minutes
	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = (pongWait * 9) / 10
	// Maximum message size allowed from peer.
	maxMessageSize = 2048 // Adjusted to a more reasonable default like 2KB
)

// wsMessage defines the structure for incoming WebSocket messages (e.g., for resize).
type wsMessage struct {
	Type string `json:"type"`
	Rows uint   `json:"rows,omitempty"`
	Cols uint   `json:"cols,omitempty"`
	// Data string `json:"data,omitempty"` // If we expect textual data frames
}

// Service handles HTTP requests for runtime functionalities, primarily WebSocket terminal.
type Service struct {
	logger          *zap.Logger
	runtimeDomain   *domainRuntime.Domain
	projectDomain   *projectDomain.Domain
	projectfsDomain *projectfs.Domain
	upgrader        websocket.Upgrader
}

// NewService creates a new runtime Service instance.
func NewService(logger *zap.Logger, rtDomain *domainRuntime.Domain, projDomain *projectDomain.Domain, projfsDomain *projectfs.Domain) *Service {
	return &Service{
		logger:          logger.Named("runtime_http_service"),
		runtimeDomain:   rtDomain,
		projectDomain:   projDomain,
		projectfsDomain: projfsDomain,
		upgrader: websocket.Upgrader{
			ReadBufferSize:  maxMessageSize, // Use maxMessageSize for buffer consistency
			WriteBufferSize: maxMessageSize,
			CheckOrigin: func(r *http.Request) bool {
				// TODO: Implement proper origin check for production
				// For development, allow all origins.
				return true
			},
		},
	}
}

// HandleTerminalConnection handles WebSocket requests for project terminals.
func (s *Service) HandleTerminalConnection(c *gin.Context) {
	projectID := c.Param("projectID")
	if projectID == "" {
		s.logger.Error("Project ID is missing from request path")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Parse initial rows and cols from query parameters
	initialRowsStr := c.DefaultQuery("rows", "24") // Default to 24 rows if not provided
	initialColsStr := c.DefaultQuery("cols", "80") // Default to 80 cols if not provided

	initialRows, errRows := strconv.ParseUint(initialRowsStr, 10, 16)
	if errRows != nil {
		s.logger.Warn("Invalid 'rows' query parameter, using default", zap.String("projectID", projectID), zap.Error(errRows))
		initialRows = 24 // Default
	}
	initialCols, errCols := strconv.ParseUint(initialColsStr, 10, 16)
	if errCols != nil {
		s.logger.Warn("Invalid 'cols' query parameter, using default", zap.String("projectID", projectID), zap.Error(errCols))
		initialCols = 80 // Default
	}

	s.logger.Info("Attempting to establish WebSocket connection",
		zap.String("projectID", projectID),
		zap.Uint64("initialRows", initialRows),
		zap.Uint64("initialCols", initialCols),
	)

	// Check project access permission
	if err := s.checkProjectAccess(c, projectID); err != nil {
		s.logger.Warn("User not authorized to access project terminal",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: You don't have permission to access this project's terminal"})
		return
	}

	// For now, assume the user is authorized for the projectID.

	wsConn, err := s.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Error("Failed to upgrade WebSocket connection", zap.String("projectID", projectID), zap.Error(err))
		// Upgrade() already sends an HTTP error response, so no further c.JSON is needed.
		return
	}
	defer wsConn.Close()
	s.logger.Info("WebSocket connection established", zap.String("projectID", projectID), zap.String("remoteAddr", wsConn.RemoteAddr().String()))

	// Use Gin's request context for the initial GetOrCreateRuntime call,
	// but create a new cancellable context for the lifetime of the WebSocket stream.
	opCtx, opCancel := context.WithCancel(context.Background()) // opCtx for "operation context" of the stream
	defer opCancel()                                            // Ensure cancellation on exit

	// Get or create the runtime environment and terminal stream
	// The returned hijackedResp.Conn is an io.ReadWriteCloser for the PTY
	_, hijackedResp, execID, err := s.runtimeDomain.GetOrCreateTerminalStream(c.Request.Context(), projectID, uint(initialRows), uint(initialCols))
	if err != nil {
		s.logger.Error("Failed to get or create runtime", zap.String("projectID", projectID), zap.Error(err))

		// Check if this is a container not found error - send more specific error
		errorMsg := "Failed to initialize terminal session."
		if strings.Contains(err.Error(), "not running") && strings.Contains(err.Error(), "not_found") {
			errorMsg = "Runtime container was not found. Please rebuild the runtime environment."
		}

		// Send error details via WebSocket text message before closing
		errorResponse := map[string]interface{}{
			"type":       "error",
			"error":      errorMsg,
			"canRebuild": strings.Contains(err.Error(), "not_found"),
		}
		if errorData, marshalErr := json.Marshal(errorResponse); marshalErr == nil {
			wsConn.WriteMessage(websocket.TextMessage, errorData)
		}

		// Then send close message
		wsConn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInternalServerErr, errorMsg))
		return
	}
	defer hijackedResp.Close() // Ensure the hijacked connection to Docker is closed

	s.logger.Info("Runtime stream acquired for project", zap.String("projectID", projectID), zap.String("execID", execID))

	var wg sync.WaitGroup
	wg.Add(3) // For three goroutines: ws->pty, pty->ws, and ping->ws

	// Goroutine: WebSocket to PTY (reads from client, writes to Docker exec, handles resize)
	go func() {
		defer wg.Done()
		// Ensure opCancel is called if this goroutine exits, to signal the other goroutine.
		// This is crucial for shutting down the PTY-to-WebSocket stream if the client disconnects.
		defer opCancel()

		for {
			select {
			case <-opCtx.Done(): // Context cancelled, likely by PTY-to-WS goroutine or main handler exit
				s.logger.Info("Shutting down WebSocket to PTY goroutine (context canceled)", zap.String("projectID", projectID), zap.String("execID", execID))
				return
			default:
				// Read incoming message from WebSocket client
				msgType, msgBytes, err := wsConn.ReadMessage()
				if err != nil {
					// Check if it's a normal closure or an unexpected error
					if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure, websocket.CloseNoStatusReceived) {
						s.logger.Warn("WebSocket to PTY: Unexpected close error", zap.Error(err), zap.String("projectID", projectID), zap.String("execID", execID))
					} else if err == io.EOF || strings.Contains(err.Error(), "forcibly closed by the remote host") || strings.Contains(err.Error(), "connection reset by peer") {
						s.logger.Info("WebSocket to PTY: Connection closed by client (EOF or reset)", zap.String("projectID", projectID), zap.String("execID", execID))
					} else {
						// Log other errors, could be temporary network issues if not a close error type
						s.logger.Error("WebSocket to PTY: Error reading message", zap.Error(err), zap.String("projectID", projectID), zap.String("execID", execID))
					}

					// Attempt to gracefully close the shell in the PTY before opCancel
					s.logger.Info("WebSocket to PTY: Client connection closed or error. Attempting to send 'exit' to PTY.",
						zap.String("projectID", projectID), zap.String("execID", execID))

					// Set a short deadline for writing exit commands
					if conn, ok := hijackedResp.Conn.(interface{ SetWriteDeadline(time.Time) error }); ok {
						if err := conn.SetWriteDeadline(time.Now().Add(500 * time.Millisecond)); err != nil {
							s.logger.Warn("WebSocket to PTY: Failed to set write deadline on PTY for exit command", zap.Error(err), zap.String("projectID", projectID))
							// Continue anyway, the write might still work or fail quickly
						}
					}

					// Try sending "exit\r\n"
					exitCmd := []byte("exit\r\n")
					_, writeErr := hijackedResp.Conn.Write(exitCmd)
					if writeErr != nil {
						s.logger.Warn("WebSocket to PTY: Failed to write 'exit' command to PTY", zap.Error(writeErr), zap.String("projectID", projectID))
					} else {
						s.logger.Info("WebSocket to PTY: Successfully wrote 'exit' command to PTY", zap.String("projectID", projectID))
					}

					// Optionally, also try sending Ctrl+D (EOF)
					eofCmd := []byte{0x04} // EOF character
					_, writeErrEOF := hijackedResp.Conn.Write(eofCmd)
					if writeErrEOF != nil {
						s.logger.Warn("WebSocket to PTY: Failed to write EOF (Ctrl+D) to PTY", zap.Error(writeErrEOF), zap.String("projectID", projectID))
					} else {
						s.logger.Info("WebSocket to PTY: Successfully wrote EOF (Ctrl+D) to PTY", zap.String("projectID", projectID))
					}

					// Reset the deadline if it was set
					if conn, ok := hijackedResp.Conn.(interface{ SetWriteDeadline(time.Time) error }); ok {
						// Setting a zero time value for deadline means no deadline.
						if err := conn.SetWriteDeadline(time.Time{}); err != nil {
							s.logger.Warn("WebSocket to PTY: Failed to reset write deadline on PTY", zap.Error(err), zap.String("projectID", projectID))
						}
					}

					// Now, allow the deferred opCancel to run and this goroutine to exit.
					return
				}

				if msgType == websocket.TextMessage {
					var msg wsMessage
					if err := json.Unmarshal(msgBytes, &msg); err == nil {
						switch msg.Type {
						case "resize":
							if msg.Rows > 0 && msg.Cols > 0 {
								s.logger.Info("Resizing terminal PTY",
									zap.String("projectID", projectID),
									zap.String("execID", execID),
									zap.Uint("rows", msg.Rows),
									zap.Uint("cols", msg.Cols))
								// Use a short-lived context for the resize operation
								resizeCtx, resizeCancel := context.WithTimeout(opCtx, 5*time.Second)
								if resizeErr := s.runtimeDomain.ResizeRuntimeTerminal(resizeCtx, execID, msg.Rows, msg.Cols); resizeErr != nil {
									s.logger.Error("Failed to resize PTY", zap.String("projectID", projectID), zap.String("execID", execID), zap.Error(resizeErr))
									// Optionally notify client of resize failure
								}
								resizeCancel()
							} else {
								s.logger.Warn("Invalid resize parameters", zap.Any("message", msg))
							}
						// case "stdin": // If you plan to send textual stdin data wrapped in JSON
						// 	if _, writeErr := hijackedResp.Conn.Write([]byte(msg.Data)); writeErr != nil {
						// 		s.logger.Error("Error writing textual data to PTY", zap.Error(writeErr))
						// 		return
						// 	}
						default:
							s.logger.Warn("Received unhandled text message type", zap.String("type", msg.Type))
							// Potentially treat as raw input if not a known command type
							// For now, only "resize" is handled as a command.
							// If other text messages should go to PTY, write them:
							// if _, writeErr := hijackedResp.Conn.Write(payload); writeErr != nil { ... }
						}
					} else {
						//s.logger.Warn("Failed to unmarshal text message from WebSocket, treating as raw input", zap.Error(err), zap.ByteString("payload", msgBytes))
						// If text messages that aren't valid JSON commands should still go to PTY:
						if _, writeErr := hijackedResp.Conn.Write(msgBytes); writeErr != nil {
							s.logger.Error("Error writing raw text message to PTY", zap.String("projectID", projectID), zap.String("execID", execID), zap.Error(writeErr))
							return
						}
					}
				}
				// Reset read deadline after successful read
				wsConn.SetReadDeadline(time.Now().Add(pongWait))
			}
		}
	}()

	// Goroutine: PTY to WebSocket (reads from Docker exec, writes to client)
	go func() {
		defer wg.Done()
		defer opCancel() // If this goroutine exits, cancel the other two
		// No need to defer wsConn.Close() here as the main function defer does it.

		buffer := make([]byte, 1024) // Reusable buffer for reading from PTY
		for {
			select {
			case <-opCtx.Done():
				s.logger.Info("Shutting down PTY to WebSocket goroutine (context canceled)", zap.String("projectID", projectID), zap.String("execID", execID))
				return
			default:
				// Set a read deadline on the PTY connection to allow context cancellation to be checked
				// This depends on the net.Conn implementation supporting deadlines.
				// Docker's hijacked connection should support this.
				if connWithDeadline, ok := hijackedResp.Conn.(interface{ SetReadDeadline(time.Time) error }); ok {
					connWithDeadline.SetReadDeadline(time.Now().Add(1 * time.Second))
				}

				n, readErr := hijackedResp.Conn.Read(buffer)
				if n > 0 {
					wsConn.SetWriteDeadline(time.Now().Add(writeWait))
					if err := wsConn.WriteMessage(websocket.BinaryMessage, buffer[:n]); err != nil {
						s.logger.Error("Error writing to WebSocket", zap.String("projectID", projectID), zap.String("execID", execID), zap.Error(err))
						return // Exit goroutine
					}
				}

				if readErr != nil {
					// If it's an EOF, it means the process in the container exited.
					if readErr == io.EOF {
						s.logger.Info("PTY stream closed (EOF)", zap.String("projectID", projectID), zap.String("execID", execID))
						// Send a close message to the client indicating normal closure from PTY side.
						wsConn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "Terminal session ended."))
					} else if netErr, ok := readErr.(net.Error); ok && netErr.Timeout() {
						// This is an expected error if SetReadDeadline was used. Continue to check opCtx.Done().
						continue
					} else {
						s.logger.Error("Error reading from PTY", zap.String("projectID", projectID), zap.String("execID", execID), zap.Error(readErr))
					}
					return // Exit goroutine
				}
			}
		}
	}()

	// Keep alive pings
	// This is a common pattern but might be redundant if PTY activity itself keeps the ws alive.
	// However, if PTY is idle, this ensures WebSocket doesn't time out on network level.
	ticker := time.NewTicker(pingPeriod)
	defer ticker.Stop()
	go func() {
		defer wg.Done() // This goroutine should also count towards the WaitGroup
		for {
			select {
			case <-ticker.C:
				// Need to lock a mutex if wsConn can be closed by other goroutines concurrently
				// For gorilla/websocket, SetWriteDeadline and WriteMessage are safe for concurrent use
				// as long as only one goroutine writes data messages at a time.
				// Control messages (Ping, Pong, Close) can be interleaved by other goroutines.
				// To be absolutely safe, especially if opCtx can be cancelled causing wsConn to be closed,
				// check opCtx here too.
				select {
				case <-opCtx.Done():
					s.logger.Info("Shutting down WebSocket ping goroutine (context canceled before ping)", zap.String("projectID", projectID), zap.String("execID", execID))
					return
				default:
				}

				if err := wsConn.SetWriteDeadline(time.Now().Add(writeWait)); err != nil {
					s.logger.Warn("Failed to set write deadline for ping", zap.Error(err))
					return
				}
				if err := wsConn.WriteMessage(websocket.PingMessage, nil); err != nil {
					// Check if the error is due to the connection being closed
					if websocket.IsCloseError(err) || errors.Is(err, net.ErrClosed) || strings.Contains(err.Error(), "use of closed network connection") {
						s.logger.Info("WebSocket connection closed, stopping ping goroutine.", zap.String("projectID", projectID), zap.String("execID", execID), zap.Error(err))
					} else {
						s.logger.Warn("Failed to send ping to WebSocket", zap.Error(err))
					}
					return // Stop pinging if error
				}
				s.logger.Debug("Sent ping to WebSocket client", zap.String("projectID", projectID), zap.String("execID", execID))
			case <-opCtx.Done():
				s.logger.Info("Shutting down WebSocket ping goroutine (context canceled)", zap.String("projectID", projectID), zap.String("execID", execID))
				return
			}
		}
	}()

	wg.Wait() // Wait for all goroutines to finish (ws->pty, pty->ws, ping->ws)

	// Removed the attempt to write exit/EOF as it was too late and causing broken pipe errors.
	// We will rely on the deferred hijackedResp.Close() to handle PTY and process termination.
	s.logger.Info("All WebSocket I/O and ping goroutines finished. Proceeding to close PTY via deferred Close().",
		zap.String("projectID", projectID),
		zap.String("execID", execID))

	s.logger.Info("WebSocket connection handler finished. Deferred functions (opCancel, hijackedResp.Close, wsConn.Close) will now execute.",
		zap.String("projectID", projectID),
		zap.String("execID", execID),
		zap.String("remoteAddr", wsConn.RemoteAddr().String()))
}

// Helper to get net.Error if underlying error is one
// func isNetError(err error) (net.Error, bool) { // Commented out as errors.As is used directly and net.Error type check is in PTY read loop
//     if err == nil {
//         return nil, false
//     }
//     var ne net.Error
//     if errors.As(err, &ne) {
//         return ne, true
//     }
//     return nil, false
// }
// Import net for isNetError, and errors for errors.As // These lines are now removed as imports are moved to the top

// checkProjectAccess verifies if the current user has access to the specified project.
func (s *Service) checkProjectAccess(c *gin.Context, projectID string) error {
	// Get UserID from context
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("UserID not found in context for runtime project access check", zap.String("projectID", projectID))
		return errors.New("unauthorized")
	}

	userIDStr, ok := userIDRaw.(string)
	if !ok || userIDStr == "" {
		s.logger.Error("UserID in context is not a valid string for runtime project access check",
			zap.Any("userIDRaw", userIDRaw), zap.String("projectID", projectID))
		return errors.New("internal server error processing user identity")
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse UserID from context for runtime project access check",
			zap.String("userIDStr", userIDStr), zap.Error(err), zap.String("projectID", projectID))
		return errors.New("internal server error processing user identity")
	}

	// Check if user has access to the project
	authorized, err := s.projectDomain.CheckUserAuthorization(c.Request.Context(), userID, projectID)
	if err != nil {
		s.logger.Error("Error checking project authorization for runtime access",
			zap.String("projectID", projectID), zap.String("userID", userIDStr), zap.Error(err))
		return errors.New("failed to verify project access")
	}

	if !authorized {
		s.logger.Warn("User not authorized to access project runtime",
			zap.String("projectID", projectID), zap.String("userID", userIDStr))
		return errors.New("access denied: you don't have permission to access this project")
	}

	s.logger.Info("User authorized for project runtime access",
		zap.String("projectID", projectID), zap.String("userID", userIDStr))

	return nil
}

// HandleRebuildRuntime handles HTTP requests to rebuild a project's runtime environment
func (s *Service) HandleRebuildRuntime(c *gin.Context) {
	projectID := c.Param("projectID")
	if projectID == "" {
		s.logger.Error("Project ID is missing from rebuild runtime request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Check project access permission
	if err := s.checkProjectAccess(c, projectID); err != nil {
		s.logger.Warn("User not authorized to rebuild project runtime",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: You don't have permission to rebuild this project's runtime"})
		return
	}

	s.logger.Info("Rebuilding runtime for project", zap.String("projectID", projectID))

	// Get project domain to get project details
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("UserID not found in context for runtime rebuild")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userIDStr, ok := userIDRaw.(string)
	if !ok {
		s.logger.Error("UserID in context is not a valid string")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse UserID", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Verify user has access to this project
	authorized, err := s.projectDomain.CheckUserAuthorization(c.Request.Context(), userID, projectID)
	if err != nil {
		s.logger.Error("Failed to check project authorization for runtime rebuild",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify project access"})
		return
	}

	if !authorized {
		s.logger.Warn("User not authorized to rebuild runtime for project",
			zap.String("projectID", projectID),
			zap.String("userID", userIDStr))
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: You don't have permission to rebuild this project's runtime"})
		return
	}

	// Rebuild the runtime for this project
	newRuntime, err := s.rebuildRuntimeForProject(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Error("Failed to create new runtime",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rebuild runtime: " + err.Error()})
		return
	}

	s.logger.Info("Successfully rebuilt runtime",
		zap.String("projectID", projectID),
		zap.String("runtimeID", newRuntime.ID))

	c.JSON(http.StatusOK, gin.H{
		"message": "Runtime rebuilt successfully",
		"runtime": map[string]interface{}{
			"id":           newRuntime.ID,
			"project_id":   newRuntime.ProjectID,
			"container_id": newRuntime.ContainerID,
			"status":       newRuntime.Status,
			"created_at":   newRuntime.CreatedAt,
		},
	})
}

// rebuildRuntimeForProject is a helper method that stops the existing runtime and creates a new one
func (s *Service) rebuildRuntimeForProject(ctx context.Context, projectID string) (*runtime.Runtime, error) {
	s.logger.Info("Rebuilding runtime for project", zap.String("projectID", projectID))

	// Delete existing runtime completely (both container and DB record)
	// This ensures we start fresh without any stale container IDs
	if deleteErr := s.runtimeDomain.DeleteRuntime(ctx, projectID); deleteErr != nil {
		s.logger.Warn("Failed to delete existing runtime, continuing with rebuild",
			zap.String("projectID", projectID),
			zap.Error(deleteErr))
	}

	// Get the project details to verify it exists
	_, err := s.projectDomain.GetProjectByID(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project details: %w", err)
	}

	// Get the correct project host path from projectfsDomain
	// This is the same approach used in project domain's CreateProjectWithRuntime
	projectHostPath, err := s.getProjectHostPath(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project host path: %w", err)
	}

	// Create new runtime
	newRuntime, err := s.runtimeDomain.Create(ctx, projectID, projectHostPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create new runtime: %w", err)
	}

	s.logger.Info("Successfully rebuilt runtime",
		zap.String("projectID", projectID),
		zap.String("runtimeID", newRuntime.ID),
		zap.String("hostPath", projectHostPath))

	return newRuntime, nil
}

// getProjectHostPath retrieves the host path for a project using projectfsDomain
func (s *Service) getProjectHostPath(ctx context.Context, projectID string) (string, error) {
	// Use projectfsDomain to get the project root path
	// This is the same approach used in project domain's CreateProjectWithRuntime
	projectHostPath, err := s.projectfsDomain.GetProjectRoot(ctx, projectID)
	if err != nil {
		s.logger.Error("Failed to get project root path from projectfsDomain",
			zap.String("projectID", projectID),
			zap.Error(err))
		return "", fmt.Errorf("failed to get project root path: %w", err)
	}

	s.logger.Debug("Retrieved project host path",
		zap.String("projectID", projectID),
		zap.String("hostPath", projectHostPath))

	return projectHostPath, nil
}

// HandleGetProjectURLs handles requests to get external access URLs for a project
func (s *Service) HandleGetProjectURLs(c *gin.Context) {
	projectID := c.Param("projectID")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Check project access permission
	if err := s.checkProjectAccess(c, projectID); err != nil {
		s.logger.Warn("User not authorized to access project URLs",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: You don't have permission to access this project's URLs"})
		return
	}

	// Get runtime for the project
	runtime, err := s.runtimeDomain.GetByProjectID(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Error("Failed to get runtime for project",
			zap.String("projectID", projectID),
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Runtime not found for this project"})
		return
	}

	// Check if runtime uses Kubernetes provider and supports URL exposure
	var urls map[int32]string = make(map[int32]string)

	if runtime.IsKubernetes() {
		// Try to get URLs from the provider if it supports GetExposedURLs
		if k8sProvider, ok := s.runtimeDomain.GetProvider().(interface {
			GetExposedURLs(context.Context, string) (map[int32]string, error)
		}); ok {
			if providerURLs, err := k8sProvider.GetExposedURLs(c.Request.Context(), projectID); err == nil {
				urls = providerURLs
			} else {
				s.logger.Warn("Failed to get URLs from K8s provider",
					zap.String("projectID", projectID),
					zap.Error(err))
			}
		}

		// Also get URLs from runtime record's ExposedPorts
		for _, port := range runtime.ExposedPorts {
			if port.ExternalURL != "" {
				urls[port.Port] = port.ExternalURL
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"project_id":    projectID,
		"provider_type": runtime.ProviderType,
		"urls":          urls,
		"exposed_ports": runtime.ExposedPorts,
	})
}
