package llm

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	llmDomain "git.nevint.com/fota3/t-rex/domain/llm" // Import LLM domain
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"   // Import LLM model
)

// Service provides HTTP handlers for LLM configuration operations.
type Service struct {
	llmDomain *llmDomain.Domain
	logger    *zap.SugaredLogger
}

// NewService creates a new instance of the LLM configuration service.
func NewService(llmDomain *llmDomain.Domain, logger *zap.SugaredLogger) *Service {
	if logger == nil {
		logger = zap.NewNop().Sugar()
	}
	return &Service{
		llmDomain: llmDomain,
		logger:    logger.Named("LLMService"),
	}
}

// --- Request/Response DTOs --- 为了前后端一致性，这里字段名将和json tag一样，而不是mapstructure

// CreateLLMConfigRequest defines the request body for creating a new LLM config.
type CreateLLMConfigRequest struct {
	Name      string `json:"name" binding:"required"`
	Type      string `json:"type" binding:"required"`
	APIKey    string `json:"api_key" binding:"required"`
	APIBase   string `json:"api_base" binding:"required"`
	ModelName string `json:"model_name" binding:"required"`
}

// UpdateLLMConfigRequest defines the request body for updating an LLM config.
type UpdateLLMConfigRequest struct {
	Name      *string `json:"name,omitempty"`
	Type      *string `json:"type,omitempty"`
	APIKey    *string `json:"api_key,omitempty"`
	APIBase   *string `json:"api_base,omitempty"`
	ModelName *string `json:"model_name,omitempty"`
}

// LLMConfigResponse defines the response structure for LLM config details.
type LLMConfigResponse struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Type      string    `json:"type"`
	APIKey    string    `json:"api_key"` // Should be masked or omitted in real responses for security
	APIBase   string    `json:"api_base"`
	ModelName string    `json:"model_name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsActive  bool      `json:"is_active"` // Indicates if this is the currently active config
}

// mapLLMConfigToResponse converts a modelLLM.LLMConfig to LLMConfigResponse.
func mapLLMConfigToResponse(config modelLLM.LLMConfig, isActive bool) LLMConfigResponse {
	return LLMConfigResponse{
		ID:        config.ID.Hex(),
		Name:      config.Name,
		Type:      config.Type,
		APIKey:    config.APIKey, // NOTE: For production, this should be masked or omitted!
		APIBase:   config.APIBase,
		ModelName: config.ModelName,
		CreatedAt: config.CreatedAt,
		UpdatedAt: config.UpdatedAt,
		IsActive:  isActive,
	}
}

// --- HTTP Handlers ---

// HandleCreateLLMConfig handles the creation of a new LLM configuration.
func (s *Service) HandleCreateLLMConfig(c *gin.Context) {
	var req CreateLLMConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err)})
		return
	}

	config := &modelLLM.LLMConfig{
		Name:      req.Name,
		Type:      req.Type,
		APIKey:    req.APIKey,
		APIBase:   req.APIBase,
		ModelName: req.ModelName,
	}

	err := s.llmDomain.CreateLLMConfig(c.Request.Context(), config)
	if err != nil {
		s.logger.Errorw("Failed to create LLM config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create LLM configuration"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "LLM configuration created successfully", "id": config.ID.Hex()})
}

// HandleListLLMConfigs lists all LLM configurations.
func (s *Service) HandleListLLMConfigs(c *gin.Context) {
	configs, err := s.llmDomain.ListLLMConfigs(c.Request.Context())
	if err != nil {
		s.logger.Errorw("Failed to list LLM configs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve LLM configurations"})
		return
	}

	activeConfigID, err := s.llmDomain.GetActiveLLMConfig(c.Request.Context())
	var activeIDStr string
	if err != nil && err.Error() != "no active LLM config has been set" {
		s.logger.Warnw("Failed to get active LLM config during list", zap.Error(err))
		// Continue but activeIDStr remains empty, meaning no config will be marked active
	} else if activeConfigID != nil {
		activeIDStr = activeConfigID.ID.Hex()
	}

	responses := make([]LLMConfigResponse, len(configs))
	for i, config := range configs {
		responses[i] = mapLLMConfigToResponse(config, config.ID.Hex() == activeIDStr)
	}

	c.JSON(http.StatusOK, responses)
}

// HandleGetLLMConfig retrieves a specific LLM configuration by ID.
func (s *Service) HandleGetLLMConfig(c *gin.Context) {
	id := c.Param("id")
	config, err := s.llmDomain.GetLLMConfigByID(c.Request.Context(), id)
	if err != nil {
		s.logger.Errorw("Failed to get LLM config by ID", zap.String("id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve LLM configuration"})
		return
	}
	if config == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "LLM configuration not found"})
		return
	}

	activeConfig, err := s.llmDomain.GetActiveLLMConfig(c.Request.Context())
	var isActive bool
	if err == nil && activeConfig != nil {
		isActive = (activeConfig.ID.Hex() == id)
	}

	c.JSON(http.StatusOK, mapLLMConfigToResponse(*config, isActive))
}

// HandleUpdateLLMConfig updates an existing LLM configuration.
func (s *Service) HandleUpdateLLMConfig(c *gin.Context) {
	id := c.Param("id")
	var req UpdateLLMConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err)})
		return
	}

	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.APIKey != nil {
		updates["api_key"] = *req.APIKey
	}
	if req.APIBase != nil {
		updates["api_base"] = *req.APIBase
	}
	if req.ModelName != nil {
		updates["model_name"] = *req.ModelName
	}

	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	err := s.llmDomain.UpdateLLMConfig(c.Request.Context(), id, updates)
	if err != nil {
		s.logger.Errorw("Failed to update LLM config", zap.String("id", id), zap.Error(err))
		if err.Error() == fmt.Sprintf("LLM config with ID %s not found", id) { // Specific error from store
			c.JSON(http.StatusNotFound, gin.H{"error": "LLM configuration not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update LLM configuration"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "LLM configuration updated successfully"})
}

// HandleDeleteLLMConfig deletes an LLM configuration.
func (s *Service) HandleDeleteLLMConfig(c *gin.Context) {
	id := c.Param("id")
	err := s.llmDomain.DeleteLLMConfig(c.Request.Context(), id)
	if err != nil {
		s.logger.Errorw("Failed to delete LLM config", zap.String("id", id), zap.Error(err))
		if err.Error() == fmt.Sprintf("LLM config with ID %s not found", id) || err.Error() == fmt.Sprintf("cannot delete the currently active LLM config. Please set another config as active first") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()}) // Use 409 Conflict for specific business rules
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete LLM configuration"})
		}
		return
	}

	c.JSON(http.StatusNoContent, nil) // 204 No Content for successful deletion
}

// HandleSetActiveLLMConfig sets a specific LLM configuration as active.
func (s *Service) HandleSetActiveLLMConfig(c *gin.Context) {
	id := c.Param("id")
	err := s.llmDomain.SetActiveLLMConfig(c.Request.Context(), id)
	if err != nil {
		s.logger.Errorw("Failed to set active LLM config", zap.String("id", id), zap.Error(err))
		if err.Error() == fmt.Sprintf("LLM config with ID %s not found, cannot set as active", id) {
			c.JSON(http.StatusNotFound, gin.H{"error": "LLM configuration not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set active LLM configuration"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "LLM configuration " + id + " set as active"})
}
