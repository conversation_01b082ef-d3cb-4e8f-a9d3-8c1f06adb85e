package projectfs

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	projectdomain "git.nevint.com/fota3/t-rex/domain/project" // Import project domain
	"git.nevint.com/fota3/t-rex/domain/projectfs"             // Updated import path, should refer to the package containing projectfs.Domain

	// Import project model
	"git.nevint.com/fota3/t-rex/pkg/middleware" // Import middleware for UserIDKey
	"git.nevint.com/fota3/t-rex/pkg/storage"    // Corrected import path

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// --- Request/Response Structs (Moved from Handler) ---

type CreateProjectRequest struct {
	ProjectID string `json:"project_id" binding:"required"`
	Name      string `json:"name,omitempty"` // Optional project name
}

type CreateFSObjectRequest struct {
	Path    string `json:"path" binding:"required"` // Relative path within the project
	IsDir   bool   `json:"is_dir"`                  // Defaults to false (file)
	Content string `json:"content"`                 // Base64 encoded content for files (optional)
}

type RenameFSObjectRequest struct {
	OldPath string `json:"old_path" binding:"required"`
	NewPath string `json:"new_path" binding:"required"`
}

type WriteFileRequest struct {
	Content string `json:"content" binding:"required"` // Base64 encoded content
}

// IProjectFSService 定义了合并后的 Service 层接口
// 注意：方法现在直接处理 Gin 上下文
type IProjectFSService interface {
	CreateProject(c *gin.Context)
	ListProjects(c *gin.Context)  // Added method to list projects for the user
	DeleteProject(c *gin.Context) // Added method to delete project completely
	ListDirectory(c *gin.Context)
	CreateFSObject(c *gin.Context)
	ReadFile(c *gin.Context)
	WriteFile(c *gin.Context)
	DeleteFSObject(c *gin.Context)
	RenameFSObject(c *gin.Context)
	WatchFileChanges(c *gin.Context) // Added method for file change watching via SSE
	// Stat method might still be useful internally or for a dedicated API
	// Stat(c *gin.Context)
}

// Service 实现了 IProjectFSService
type Service struct {
	fsDomain      *projectfs.Domain     // Dependency on domain layer concrete type for FS operations
	projectDomain *projectdomain.Domain // Dependency on project domain for auth and metadata
	logger        *zap.Logger
}

// NewService 创建 Service 实例
func NewService(fsDomain *projectfs.Domain, projectDomain *projectdomain.Domain, logger *zap.Logger) *Service {
	if logger == nil {
		logger = zap.NewNop()
	}
	if fsDomain == nil {
		// Handle error: fsDomain is required
		panic("fsDomain cannot be nil") // Or return error
	}
	if projectDomain == nil {
		// Handle error: projectRepo is required
		panic("projectDomain cannot be nil") // Or return error
	}
	return &Service{
		fsDomain:      fsDomain,
		projectDomain: projectDomain,
		logger:        logger.Named("ProjectFSService"), // Renamed logger
	}
}

// checkProjectAccess verifies if the current user has access to the specified project.
// Returns the userID if authorized, or responds with appropriate HTTP error.
func (s *Service) checkProjectAccess(c *gin.Context, projectID string) (primitive.ObjectID, bool) {
	// Get UserID from context
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("UserID not found in context for project access check", zap.String("projectID", projectID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return primitive.NilObjectID, false
	}

	userIDStr, ok := userIDRaw.(string)
	if !ok || userIDStr == "" {
		s.logger.Error("UserID in context is not a valid string for project access check",
			zap.Any("userIDRaw", userIDRaw), zap.String("projectID", projectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return primitive.NilObjectID, false
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse UserID from context for project access check",
			zap.String("userIDStr", userIDStr), zap.Error(err), zap.String("projectID", projectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return primitive.NilObjectID, false
	}

	// Check if user has access to the project
	authorized, err := s.projectDomain.CheckUserAuthorization(c.Request.Context(), userID, projectID)
	if err != nil {
		if errors.Is(err, projectdomain.ErrNotFound) {
			s.logger.Info("Project not found during access check", zap.String("projectID", projectID), zap.String("userID", userIDStr))
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		} else {
			s.logger.Error("Error checking project authorization", zap.String("projectID", projectID), zap.String("userID", userIDStr), zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify project access"})
		}
		return primitive.NilObjectID, false
	}

	if !authorized {
		s.logger.Warn("User not authorized to access project", zap.String("projectID", projectID), zap.String("userID", userIDStr))
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: You don't have permission to access this project"})
		return primitive.NilObjectID, false
	}

	return userID, true
}

// --- Service Methods (Now handle HTTP directly) ---

// CreateProject godoc
// @Summary Create a new project workspace and metadata
// @Description Initializes the workspace for a new project ID and saves its metadata, linking it to the current user.
// @Tags projects
// @Accept  json
// @Produce json
// @Param   body body CreateProjectRequest true "Project ID and optional Name"
// @Success 201 {object} model.Project "Project created successfully"
// @Failure 400 {object} map[string]string "Invalid request body or project ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 409 {object} map[string]string "Project ID already exists"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects [post]
func (s *Service) CreateProject(c *gin.Context) {
	var req CreateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Failed to bind CreateProject request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	if !isValidProjectID(req.ProjectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Get UserID from context (set by AuthRequired middleware)
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("UserID not found in context, AuthRequired middleware might be missing", zap.String("projectID", req.ProjectID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	userIDStr, ok := userIDRaw.(string)
	if !ok || userIDStr == "" {
		s.logger.Error("UserID in context is not a valid string", zap.Any("userIDRaw", userIDRaw), zap.String("projectID", req.ProjectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return
	}
	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse UserID from context", zap.String("userIDStr", userIDStr), zap.Error(err), zap.String("projectID", req.ProjectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return
	}

	s.logger.Info("Service: Handling CreateProject request", zap.String("projectID", req.ProjectID), zap.String("userID", userIDStr))

	// Call the project domain to handle creation logic
	newProject, err := s.projectDomain.CreateProjectWithRuntime(c.Request.Context(), req.ProjectID, req.Name, userID)
	if err != nil {
		if errors.Is(err, projectdomain.ErrDuplicateProjectID) {
			s.logger.Warn("Project ID already exists in metadata", zap.String("projectID", req.ProjectID), zap.Error(err))
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			s.logger.Error("Failed to create project via domain", zap.String("projectID", req.ProjectID), zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
		}
		return
	}

	// Return the created project metadata (ID is populated by the repo)
	c.JSON(http.StatusCreated, newProject)
}

// ListProjects godoc
// @Summary List projects for the current user
// @Description Retrieves a list of projects associated with the currently logged-in user.
// @Tags projects
// @Produce json
// @Security BearerAuth
// @Success 200 {array} model.Project "List of user's projects"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects [get]
func (s *Service) ListProjects(c *gin.Context) {
	// Get UserID from context
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("UserID not found in context for ListProjects")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	userIDStr, ok := userIDRaw.(string)
	if !ok || userIDStr == "" {
		s.logger.Error("UserID in context is not a valid string for ListProjects", zap.Any("userIDRaw", userIDRaw))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return
	}
	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse UserID from context for ListProjects", zap.String("userIDStr", userIDStr), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error processing user identity"})
		return
	}

	s.logger.Info("Service: Handling ListProjects request", zap.String("userID", userIDStr))

	// Call domain logic
	projects, err := s.projectDomain.ListProjectsByUserID(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to find projects by user ID via domain", zap.String("userID", userIDStr), zap.Error(err))
		// Don't expose detailed repo errors
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve projects"})
		return
	}

	// Return empty list if no projects found (repo already handles this)
	c.JSON(http.StatusOK, projects)
}

// DeleteProject godoc
// @Summary Delete a project completely
// @Description Deletes a project and all its associated resources including runtime, filesystem, and metadata.
// @Tags projects
// @Param   projectID path string true "Project ID"
// @Success 200 {object} map[string]string "Project deleted successfully"
// @Failure 400 {object} map[string]string "Invalid project ID"
// @Failure 403 {object} map[string]string "Access denied"
// @Failure 404 {object} map[string]string "Project not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID} [delete]
func (s *Service) DeleteProject(c *gin.Context) {
	projectID := c.Param("projectID")

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Check project access permission
	userID, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	s.logger.Info("Service: Handling DeleteProject request",
		zap.String("projectID", projectID),
		zap.String("userID", userID.Hex()))

	// Call domain logic to perform complete project deletion
	err := s.projectDomain.DeleteProjectComplete(c.Request.Context(), projectID)
	if err != nil {
		if errors.Is(err, projectdomain.ErrNotFound) {
			s.logger.Info("Project not found for deletion", zap.String("projectID", projectID))
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		} else {
			s.logger.Error("Failed to delete project via domain",
				zap.String("projectID", projectID),
				zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project"})
		}
		return
	}

	s.logger.Info("Project deleted successfully", zap.String("projectID", projectID))
	c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

// ListDirectory godoc
// @Summary List files and directories within a project path
// @Description Retrieves a list of items within a specified directory of a project. Defaults to the project root if path is empty or ".".
// @Tags projects
// @Produce json
// @Param   projectID path string true "Project ID"
// @Param   path query string false "Directory path relative to project root (e.g., 'src/components')" default(".")
// @Success 200 {array} storage.FileInfo "List of files and directories"
// @Failure 400 {object} map[string]string "Invalid project ID or path"
// @Failure 404 {object} map[string]string "Project or path not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files [get]
func (s *Service) ListDirectory(c *gin.Context) {
	projectID := c.Param("projectID")
	path := c.DefaultQuery("path", ".")

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	s.logger.Debug("Service: Handling ListDirectory request", zap.String("projectId", projectID), zap.String("path", path))

	// Call domain logic
	files, err := s.fsDomain.ListDirectory(c.Request.Context(), projectID, path)

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, path)
		return
	}

	if files == nil {
		files = []storage.FileInfo{}
	}

	c.JSON(http.StatusOK, files)
}

// CreateFSObject godoc
// @Summary Create a file or directory
// @Description Creates a new file or directory at the specified path within a project.
// @Tags projects
// @Accept  json
// @Produce json
// @Param   projectID path string true "Project ID"
// @Param   body body CreateFSObjectRequest true "File/Directory details (path, isDir, optional base64 content for file)"
// @Success 201 {object} storage.FileInfo "Details of the created file/directory"
// @Failure 400 {object} map[string]string "Invalid request body, project ID, or path"
// @Failure 409 {object} map[string]string "Path already exists"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files [post]
func (s *Service) CreateFSObject(c *gin.Context) {
	projectID := c.Param("projectID")
	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	var req CreateFSObjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Failed to bind CreateFSObject request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	s.logger.Info("Service: Handling CreateFSObject request",
		zap.String("projectId", projectID),
		zap.String("path", req.Path),
		zap.Bool("isDir", req.IsDir))

	var info storage.FileInfo
	var err error

	// Call domain logic based on request type
	if req.IsDir {
		info, err = s.fsDomain.CreateDirectory(c.Request.Context(), projectID, req.Path)
	} else {
		var content []byte
		if req.Content != "" {
			content, err = base64.StdEncoding.DecodeString(req.Content)
			if err != nil {
				s.logger.Warn("Failed to decode base64 content for file creation", zap.Error(err))
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid base64 content"})
				return
			}
		}
		info, err = s.fsDomain.CreateFile(c.Request.Context(), projectID, req.Path, content)
	}

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, req.Path)
		return
	}

	c.JSON(http.StatusCreated, info)
}

// ReadFile godoc
// @Summary Read file content
// @Description Reads the content of a specified file within a project.
// @Tags projects
// @Produce json
// @Param   projectID path string true "Project ID"
// @Param   path query string true "File path relative to project root"
// @Success 200 {object} map[string]string "File content (base64 encoded)"
// @Failure 400 {object} map[string]string "Invalid project ID or path"
// @Failure 404 {object} map[string]string "Project or file not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files/content [get]
func (s *Service) ReadFile(c *gin.Context) {
	projectID := c.Param("projectID")
	path := c.Query("path")

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}
	if path == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Query parameter 'path' is required"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	s.logger.Debug("Service: Handling ReadFile request", zap.String("projectId", projectID), zap.String("path", path))

	// Call domain logic
	content, err := s.fsDomain.ReadFile(c.Request.Context(), projectID, path)

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, path)
		return
	}

	encodedContent := base64.StdEncoding.EncodeToString(content)
	c.JSON(http.StatusOK, gin.H{"content": encodedContent})
}

// WriteFile godoc
// @Summary Write file content
// @Description Writes (or overwrites) the content of a specified file within a project.
// @Tags projects
// @Accept  json
// @Produce json
// @Param   projectID path string true "Project ID"
// @Param   path query string true "File path relative to project root"
// @Param   body body WriteFileRequest true "Base64 encoded file content"
// @Success 204 "File written successfully"
// @Failure 400 {object} map[string]string "Invalid request, project ID, path, or content"
// @Failure 404 {object} map[string]string "Project not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files/content [put]
func (s *Service) WriteFile(c *gin.Context) {
	projectID := c.Param("projectID")
	path := c.Query("path")

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}
	if path == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Query parameter 'path' is required"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	var req WriteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Failed to bind WriteFile request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	content, err := base64.StdEncoding.DecodeString(req.Content)
	if err != nil {
		s.logger.Warn("Failed to decode base64 content for file writing", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid base64 content"})
		return
	}

	s.logger.Info("Service: Handling WriteFile request", zap.String("projectId", projectID), zap.String("path", path))

	// Call domain logic
	err = s.fsDomain.WriteFile(c.Request.Context(), projectID, path, content)

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, path)
		return
	}

	c.Status(http.StatusNoContent) // 204 No Content
}

// DeleteFSObject godoc
// @Summary Delete a file or directory
// @Description Deletes a file or directory at the specified path within a project.
// @Tags projects
// @Param   projectID path string true "Project ID"
// @Param   path query string true "Path relative to project root to delete"
// @Success 204 "Deleted successfully"
// @Failure 400 {object} map[string]string "Invalid project ID or path"
// @Failure 404 {object} map[string]string "Project or path not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files [delete]
func (s *Service) DeleteFSObject(c *gin.Context) {
	projectID := c.Param("projectID")
	path := c.Query("path") // Use Query, not DefaultQuery

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}
	// Add check for empty path
	if path == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Query parameter 'path' is required"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	s.logger.Info("Service: Handling DeleteFSObject request", zap.String("projectId", projectID), zap.String("path", path))

	// Call domain logic
	err := s.fsDomain.Delete(c.Request.Context(), projectID, path)

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, path)
		return
	}

	// Success
	c.Status(http.StatusNoContent)
}

// RenameFSObject godoc
// @Summary Rename or move a file/directory
// @Description Renames or moves a file/directory from an old path to a new path within a project.
// @Tags projects
// @Accept  json
// @Param   projectID path string true "Project ID"
// @Param   body body RenameFSObjectRequest true "Old and new paths"
// @Success 204 "Renamed/moved successfully"
// @Failure 400 {object} map[string]string "Invalid request body, project ID, or paths"
// @Failure 404 {object} map[string]string "Old path not found"
// @Failure 409 {object} map[string]string "New path already exists"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/files/rename [put]
func (s *Service) RenameFSObject(c *gin.Context) {
	projectID := c.Param("projectID")
	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	var req RenameFSObjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Failed to bind RenameFSObject request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	s.logger.Info("Service: Handling RenameFSObject request",
		zap.String("projectId", projectID),
		zap.String("oldPath", req.OldPath),
		zap.String("newPath", req.NewPath))

	// Call domain logic
	err := s.fsDomain.Rename(c.Request.Context(), projectID, req.OldPath, req.NewPath)

	// Handle response
	if err != nil {
		handleFSError(c, s.logger, err, projectID, fmt.Sprintf("from '%s' to '%s'", req.OldPath, req.NewPath))
		return
	}

	// Success
	c.Status(http.StatusNoContent)
}

// WatchFileChanges godoc
// @Summary Watch for file changes in a project via Server-Sent Events
// @Description Establishes an SSE connection to stream real-time file change events for a specific project.
// @Tags projects
// @Produce text/event-stream
// @Param   projectID path string true "Project ID"
// @Success 200 {string} string "SSE stream of file change events"
// @Failure 400 {object} map[string]string "Invalid project ID"
// @Failure 403 {object} map[string]string "Access denied"
// @Failure 404 {object} map[string]string "Project not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/projects/{projectID}/watch [get]
func (s *Service) WatchFileChanges(c *gin.Context) {
	projectID := c.Param("projectID")

	if !isValidProjectID(projectID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Check project access permission
	_, authorized := s.checkProjectAccess(c, projectID)
	if !authorized {
		return // Response already sent by checkProjectAccess
	}

	s.logger.Info("Service: Handling WatchFileChanges SSE request", zap.String("projectId", projectID))

	// Set SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	// c.Header("Access-Control-Allow-Origin", "*") // This is handled by global CORS middleware
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// Generate unique subscriber ID
	subscriberID := uuid.New().String()
	s.logger.Debug("Generated subscriber ID", zap.String("subscriberID", subscriberID), zap.String("projectID", projectID))

	// Get file watcher manager from domain
	watcherManager := s.fsDomain.GetFileWatcherManager()

	// Subscribe to file change events
	subscriber, err := watcherManager.Subscribe(c.Request.Context(), projectID, subscriberID)
	if err != nil {
		s.logger.Error("Failed to subscribe to file changes",
			zap.String("projectID", projectID),
			zap.String("subscriberID", subscriberID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start file watching"})
		return
	}

	// Ensure cleanup on disconnect
	defer func() {
		s.logger.Debug("Cleaning up file watcher subscription",
			zap.String("projectID", projectID),
			zap.String("subscriberID", subscriberID))
		watcherManager.Unsubscribe(projectID, subscriberID)
	}()

	// Get response writer
	w := c.Writer

	// Send keepalive every 30 seconds
	keepAliveTicker := time.NewTicker(30 * time.Second)
	defer keepAliveTicker.Stop()

	// Event streaming loop
	for {
		select {
		case event, ok := <-subscriber.Channel:
			if !ok {
				s.logger.Debug("Subscriber channel closed",
					zap.String("projectID", projectID),
					zap.String("subscriberID", subscriberID))
				return
			}

			// Serialize event to JSON
			eventData, err := json.Marshal(event.Data)
			if err != nil {
				s.logger.Error("Failed to marshal event data",
					zap.String("projectID", projectID),
					zap.Error(err))
				continue
			}

			// Send SSE event
			_, err = fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Type, eventData)
			if err != nil {
				s.logger.Error("Failed to write SSE event",
					zap.String("projectID", projectID),
					zap.Error(err))
				return
			}

			// Flush the response
			if flusher, ok := w.(http.Flusher); ok {
				flusher.Flush()
			}

			s.logger.Debug("Sent SSE event",
				zap.String("projectID", projectID),
				zap.String("eventType", string(event.Type)))

		case <-keepAliveTicker.C:
			// Send keepalive
			_, err := fmt.Fprintf(w, "event: keepalive\ndata: {\"timestamp\":\"%s\"}\n\n",
				time.Now().Format(time.RFC3339))
			if err != nil {
				s.logger.Error("Failed to write keepalive",
					zap.String("projectID", projectID),
					zap.Error(err))
				return
			}

			if flusher, ok := w.(http.Flusher); ok {
				flusher.Flush()
			}

		case <-c.Request.Context().Done():
			s.logger.Debug("Client disconnected",
				zap.String("projectID", projectID),
				zap.String("subscriberID", subscriberID))
			return

		case <-subscriber.Ctx.Done():
			s.logger.Debug("Subscriber context cancelled",
				zap.String("projectID", projectID),
				zap.String("subscriberID", subscriberID))
			return
		}
	}
}

// --- Helper Functions (Moved from Handler) ---

// isValidProjectID performs basic validation on project ID format.
func isValidProjectID(projectID string) bool {
	if projectID == "" || strings.Contains(projectID, "/") || strings.Contains(projectID, "..") {
		return false
	}
	// Add more checks if needed (e.g., length, allowed characters)
	return true
}

// handleFSError maps common filesystem errors from the domain layer to HTTP status codes.
func handleFSError(c *gin.Context, logger *zap.Logger, err error, projectID, pathInfo string) {
	switch {
	case errors.Is(err, os.ErrNotExist):
		logger.Info("Resource not found", zap.String("projectID", projectID), zap.String("pathInfo", pathInfo), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
	case errors.Is(err, storage.ErrPathExists):
		logger.Info("Resource already exists", zap.String("projectID", projectID), zap.String("pathInfo", pathInfo), zap.Error(err))
		c.JSON(http.StatusConflict, gin.H{"error": "Resource already exists"})
	case errors.Is(err, projectfs.ErrCannotOperateRoot): // Specific check for CannotOperateRoot
		logger.Warn("Operation on root denied", zap.String("projectID", projectID), zap.String("pathInfo", pathInfo), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": projectfs.ErrCannotOperateRoot.Error()}) // Use specific error message
	case errors.Is(err, storage.ErrInvalidPath): // Specific check for ErrInvalidPath
		logger.Warn("Invalid path detected", zap.String("projectID", projectID), zap.String("pathInfo", pathInfo), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": storage.ErrInvalidPath.Error()}) // Use specific error message
	default:
		logger.Error("Internal server error during filesystem operation", zap.String("projectID", projectID), zap.String("pathInfo", pathInfo), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
	}
}
