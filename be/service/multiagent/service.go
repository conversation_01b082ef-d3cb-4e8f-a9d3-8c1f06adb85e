package multiagent

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	multiagentDomain "git.nevint.com/fota3/t-rex/domain/multiagent"
	"git.nevint.com/fota3/t-rex/model/agent"
)

// Service handles HTTP requests for multi-agent conversations
// This service is compatible with the original agent service interface
type Service struct {
	domain multiagentDomain.MultiAgentDomain
	logger *zap.SugaredLogger
}

// NewService creates a new multi-agent service instance
func NewService(domain multiagentDomain.MultiAgentDomain, logger *zap.SugaredLogger) *Service {
	return &Service{
		domain: domain,
		logger: logger.Named("MultiAgentService"),
	}
}

// SendMessageAndStream handles streaming conversation messages (compatible with original agent service)
func (s *Service) SendMessageAndStream(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Parse request body
	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Errorw("Failed to parse request body", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Get user ID from context (assuming it's set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	// Create user message
	userMessage := agent.Message{
		MsgID:     GenerateMsgID(),
		MsgType:   "user_message",
		Role:      agent.RoleUser,
		Content:   req.Content,
		Timestamp: time.Now(),
	}

	// Set up SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Send connected event
	s.writeSseEvent(c, "connected", map[string]interface{}{
		"status": "connected",
	})

	// Start streaming interaction
	domainStreamChan, err := s.domain.HandleProjectStreamingInteraction(
		c.Request.Context(),
		projectID,
		userIDStr,
		userMessage,
	)
	if err != nil {
		s.logger.Errorw("Failed to start streaming interaction", "error", err, "projectID", projectID)
		s.writeSseEvent(c, "error", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	// Process the stream from the domain layer
	for chunk := range domainStreamChan {
		// Wrap the domain chunk in SSEContentDeltaData
		sseContentDelta := SSEContentDeltaData{
			Chunk: chunk,
		}
		if err := s.writeSseEventInternal(c, "content_delta", sseContentDelta); err != nil {
			return // Connection might be closed by client
		}
	}

	s.logger.Infow("Streaming interaction completed", "projectID", projectID)
}

// GetConversationHistory retrieves conversation history for a project
func (s *Service) GetConversationHistory(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	messages, err := s.domain.ExportProjectConversation(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Errorw("Failed to get conversation history", "error", err, "projectID", projectID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve conversation history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"messages": messages,
	})
}

// ClearConversationHistory clears conversation history for a project
func (s *Service) ClearConversationHistory(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	err := s.domain.ClearProjectConversation(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Errorw("Failed to clear conversation history", "error", err, "projectID", projectID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear conversation history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Conversation history cleared successfully",
	})
}

// writeSseEvent writes an SSE event to the response
func (s *Service) writeSseEvent(c *gin.Context, eventType string, data interface{}) error {
	return s.writeSseEventInternal(c, eventType, data)
}

// writeSseEventInternal writes an SSE event with proper formatting
func (s *Service) writeSseEventInternal(c *gin.Context, eventType string, data interface{}) error {
	// Format SSE event
	eventData := fmt.Sprintf("event: %s\ndata: %v\n\n", eventType, data)

	// Write to response
	_, err := c.Writer.WriteString(eventData)
	if err != nil {
		s.logger.Errorw("Failed to write SSE event", "error", err, "eventType", eventType)
		return err
	}

	// Flush the response
	if flusher, ok := c.Writer.(http.Flusher); ok {
		flusher.Flush()
	}

	return nil
}

// SendMessageRequest represents the request body for sending a message
type SendMessageRequest struct {
	Content string `json:"content" binding:"required"`
}

// SSEContentDeltaData represents the data structure for content delta SSE events
type SSEContentDeltaData struct {
	Chunk multiagentDomain.DomainStreamChunk `json:"chunk"`
}

// GenerateMsgID generates a unique message ID
func GenerateMsgID() string {
	return fmt.Sprintf("msg_%d", time.Now().UnixNano())
}

// RegisterRoutes registers the multi-agent service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	// Project-level conversation routes (compatible with original agent service)
	router.POST("/projects/:projectId/conversation/messages", s.SendMessageAndStream)
	router.GET("/projects/:projectId/conversation/history", s.GetConversationHistory)
	router.DELETE("/projects/:projectId/conversation/history", s.ClearConversationHistory)

	// Additional multi-agent specific routes
	router.GET("/projects/:projectId/multiagent/status", s.GetMultiAgentStatus)
	router.POST("/projects/:projectId/multiagent/abort", s.AbortMultiAgentExecution)
	router.POST("/projects/:projectId/conversation/messages/:messageId/retry", s.RetryMessage)

	// System-level routes
	router.GET("/multiagent/health", s.HealthCheck)
	router.GET("/multiagent/metrics", s.GetMetrics)
}

// GetMultiAgentStatus returns the current status of multi-agent execution
func (s *Service) GetMultiAgentStatus(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// This would require extending the domain interface to provide status information
	// For now, return a basic status
	c.JSON(http.StatusOK, gin.H{
		"project_id": projectID,
		"status":     "ready",
		"message":    "Multi-agent system is ready for requests",
		"timestamp":  time.Now().Unix(),
	})
}

// AbortMultiAgentExecution aborts any running multi-agent execution
func (s *Service) AbortMultiAgentExecution(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// This would require extending the domain interface to support abortion
	// For now, return a success response
	c.JSON(http.StatusOK, gin.H{
		"project_id": projectID,
		"message":    "Multi-agent execution abort requested",
		"timestamp":  time.Now().Unix(),
	})
}

// HealthCheck endpoint
func (s *Service) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   "multi-agent",
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0",
	})
}

// GetMetrics returns multi-agent system metrics
func (s *Service) GetMetrics(c *gin.Context) {
	// This would require extending the domain interface to provide metrics
	// For now, return basic metrics
	c.JSON(http.StatusOK, gin.H{
		"total_requests":        0,
		"active_agents":         0,
		"completed_requests":    0,
		"failed_requests":       0,
		"average_response_time": "0ms",
		"uptime":                "0s",
		"timestamp":             time.Now().Unix(),
	})
}

// RetryMessage retries a failed message
func (s *Service) RetryMessage(c *gin.Context) {
	projectID := c.Param("projectId")
	messageID := c.Param("messageId")

	if projectID == "" || messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID and Message ID are required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	// Get conversation history to find the message to retry
	messages, err := s.domain.ExportProjectConversation(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Errorw("Failed to get conversation history for retry", "error", err, "projectID", projectID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve conversation history"})
		return
	}

	// Find the message to retry
	var messageToRetry *agent.Message
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].MsgID == messageID && messages[i].Role == agent.RoleUser {
			messageToRetry = &messages[i]
			break
		}
	}

	if messageToRetry == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Message not found"})
		return
	}

	// Set up SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Send connected event
	s.writeSseEvent(c, "connected", map[string]interface{}{
		"status": "connected",
		"retry":  true,
	})

	// Start streaming interaction with retry flag
	domainStreamChan, err := s.domain.HandleProjectStreamingInteraction(
		c.Request.Context(),
		projectID,
		userIDStr,
		*messageToRetry,
	)
	if err != nil {
		s.logger.Errorw("Failed to start retry streaming interaction", "error", err, "projectID", projectID, "messageID", messageID)
		s.writeSseEvent(c, "error", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	// Process the stream from the domain layer
	for chunk := range domainStreamChan {
		sseContentDelta := SSEContentDeltaData{
			Chunk: chunk,
		}
		if err := s.writeSseEventInternal(c, "content_delta", sseContentDelta); err != nil {
			return
		}
	}

	s.logger.Infow("Retry streaming interaction completed", "projectID", projectID, "messageID", messageID)
}
