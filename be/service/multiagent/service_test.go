package multiagent

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"

	multiagentDomain "git.nevint.com/fota3/t-rex/domain/multiagent"
	"git.nevint.com/fota3/t-rex/model/agent"
)

// MockMultiAgentDomain implements MultiAgentDomain for testing
type MockMultiAgentDomain struct{}

func (m *MockMultiAgentDomain) HandleStreamingInteraction(ctx context.Context, conversation *agent.Conversation, userMessage agent.Message, systemPrompt string, isRetry bool) (<-chan multiagentDomain.DomainStreamChunk, error) {
	responseChan := make(chan multiagentDomain.DomainStreamChunk, 1)
	go func() {
		defer close(responseChan)
		content := "Mock response"
		responseChan <- multiagentDomain.DomainStreamChunk{
			Delta:          &content,
			ConversationID: "test-conv",
			MessageID:      "test-msg",
		}
	}()
	return responseChan, nil
}

func (m *MockMultiAgentDomain) HandleProjectStreamingInteraction(ctx context.Context, projectID string, userID string, userMessage agent.Message) (<-chan multiagentDomain.DomainStreamChunk, error) {
	return m.HandleStreamingInteraction(ctx, nil, userMessage, "", false)
}

func (m *MockMultiAgentDomain) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	return nil, nil
}

func (m *MockMultiAgentDomain) GetProjectConversation(ctx context.Context, projectID string) (*agent.Conversation, error) {
	return nil, nil
}

func (m *MockMultiAgentDomain) ClearProjectConversation(ctx context.Context, projectID string) error {
	return nil
}

func (m *MockMultiAgentDomain) DeleteProjectConversation(ctx context.Context, projectID string) error {
	return nil
}

func (m *MockMultiAgentDomain) ExportProjectConversation(ctx context.Context, projectID string) ([]agent.Message, error) {
	return []agent.Message{}, nil
}

// TestServiceCreation tests the service creation
func TestServiceCreation(t *testing.T) {
	logger := zap.NewNop().Sugar()
	mockDomain := &MockMultiAgentDomain{}

	service := NewService(mockDomain, logger)
	assert.NotNil(t, service, "Service should be created successfully")
	assert.NotNil(t, service.domain, "Service should have domain")
	assert.NotNil(t, service.logger, "Service should have logger")
}

// TestServiceMethods tests that all service methods exist
func TestServiceMethods(t *testing.T) {
	logger := zap.NewNop().Sugar()
	mockDomain := &MockMultiAgentDomain{}

	service := NewService(mockDomain, logger)

	// Test that all required methods exist
	assert.NotNil(t, service.SendMessageAndStream, "SendMessageAndStream method should exist")
	assert.NotNil(t, service.GetConversationHistory, "GetConversationHistory method should exist")
	assert.NotNil(t, service.ClearConversationHistory, "ClearConversationHistory method should exist")
	assert.NotNil(t, service.GetMultiAgentStatus, "GetMultiAgentStatus method should exist")
	assert.NotNil(t, service.AbortMultiAgentExecution, "AbortMultiAgentExecution method should exist")
	assert.NotNil(t, service.HealthCheck, "HealthCheck method should exist")
	assert.NotNil(t, service.GetMetrics, "GetMetrics method should exist")
	assert.NotNil(t, service.RetryMessage, "RetryMessage method should exist")
}

// TestGenerateMsgID tests the message ID generation
func TestGenerateMsgID(t *testing.T) {
	id1 := GenerateMsgID()
	id2 := GenerateMsgID()

	assert.NotEmpty(t, id1, "Generated ID should not be empty")
	assert.NotEmpty(t, id2, "Generated ID should not be empty")
	assert.NotEqual(t, id1, id2, "Generated IDs should be unique")
	assert.Contains(t, id1, "msg_", "Generated ID should contain msg_ prefix")
	assert.Contains(t, id2, "msg_", "Generated ID should contain msg_ prefix")
}
