package user

import (
	"errors"
	"fmt"
	"net/http"

	userdomain "git.nevint.com/fota3/t-rex/domain/user" // Import domain entry point
	model "git.nevint.com/fota3/t-rex/model/user"
	"git.nevint.com/fota3/t-rex/pkg/middleware" // Import middleware for constants
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// Service holds the dependencies for user-related HTTP handlers.
type Service struct {
	userDomain *userdomain.Domain
	logger     *zap.Logger
}

// NewService creates a new handler for user endpoints.
func NewService(userDomain *userdomain.Domain, logger *zap.Logger) *Service {
	return &Service{
		userDomain: userDomain,
		logger:     logger.Named("user_service"),
	}
}

// HandleRegister godoc
// @Summary Register a new user
// @Description Creates a new user account.
// @Tags auth
// @Accept  json
// @Produce json
// @Param   body body RegisterRequest true "User registration details"
// @Success 201 {object} map{message=string, user=model.User} "User registered successfully (user object excludes password hash)"
// @Failure 400 {object} map[string]string "Invalid registration data or username taken"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/auth/register [post]
func (h *Service) HandleRegister(c *gin.Context) {
	h.logger.Info("Registration request received",
		zap.String("remote_addr", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")))

	var req RegisterRequest // Assuming RegisterRequest is defined here or imported

	// Bind JSON request body
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid registration data received",
			zap.Error(err),
			zap.String("remote_addr", c.ClientIP()))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid registration data: " + err.Error()})
		return
	}

	h.logger.Info("Processing registration request",
		zap.String("username", req.Username),
		zap.String("email", req.Email),
		zap.String("full_name", req.FullName),
		zap.String("remote_addr", c.ClientIP()))

	// Call the domain logic directly
	newUser, err := h.userDomain.RegisterUser(c.Request.Context(), req.Username, req.Password, req.Email, req.FullName)
	if err != nil {
		// Map domain errors to HTTP status codes
		status := http.StatusInternalServerError
		msg := "Registration failed"
		if errors.Is(err, userdomain.ErrUsernameTaken) {
			status = http.StatusBadRequest
			msg = err.Error()
			h.logger.Warn("Registration failed: username already taken",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
		} else if errors.Is(err, userdomain.ErrHashingFailed) {
			// Log internal error, return generic message
			h.logger.Error("Password hashing failed during registration",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
			msg = "An internal error occurred during registration"
		} else {
			// Log other unexpected errors
			h.logger.Error("Unexpected registration error",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
			msg = "An unexpected error occurred"
		}
		c.JSON(status, gin.H{"error": msg})
		return
	}

	h.logger.Info("User registered successfully",
		zap.String("user_id", newUser.ID.Hex()),
		zap.String("username", newUser.Username),
		zap.String("email", newUser.Email),
		zap.String("remote_addr", c.ClientIP()))

	// Registration successful - Return limited user info (DTO recommended)
	respUser := model.User{
		ID:       newUser.ID,
		Username: newUser.Username,
		Email:    newUser.Email,
		FullName: newUser.FullName,
		IsAdmin:  newUser.IsAdmin, // NEW: Include IsAdmin in response
	}

	// --- Session Management (NEW: Added for Register) ---
	session := sessions.Default(c)
	session.Set(middleware.UserIDKey, newUser.ID.Hex())
	session.Set(middleware.UsernameKey, newUser.Username)
	session.Set(middleware.IsAdminKey, newUser.IsAdmin) // NEW: Save IsAdmin to session
	if err := session.Save(); err != nil {
		h.logger.Error("Failed to save session after registration",
			zap.String("user_id", newUser.ID.Hex()),
			zap.String("username", newUser.Username),
			zap.String("remote_addr", c.ClientIP()),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save session after registration"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "User registered successfully", "user": respUser})
}

// HandleLogin godoc
// @Summary Log in a user
// @Description Authenticates a user and creates a session.
// @Tags auth
// @Accept  json
// @Produce json
// @Param   body body LoginRequest true "User login credentials"
// @Success 200 {object} map{message=string, user=model.User} "Login successful (user object excludes password hash)"
// @Failure 400 {object} map[string]string "Invalid login data"
// @Failure 401 {object} map[string]string "Login failed: Invalid credentials or inactive account"
// @Failure 500 {object} map[string]string "Internal server error (e.g., session save failed)"
// @Router /api/v1/auth/login [post]
func (h *Service) HandleLogin(c *gin.Context) {
	h.logger.Info("Login request received",
		zap.String("remote_addr", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")))

	var req LoginRequest // Assuming LoginRequest is defined here or imported

	// Bind JSON request body
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid login data received",
			zap.Error(err),
			zap.String("remote_addr", c.ClientIP()))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid login data: " + err.Error()})
		return
	}

	h.logger.Info("Processing login request",
		zap.String("username", req.Username),
		zap.String("remote_addr", c.ClientIP()))

	// Call the domain logic directly
	user, err := h.userDomain.AuthenticateUser(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		// Map domain errors to HTTP status codes
		status := http.StatusUnauthorized // Default for login failures
		msg := "Login failed: Invalid credentials or inactive account"
		if errors.Is(err, userdomain.ErrInactiveAccount) {
			msg = userdomain.ErrInactiveAccount.Error()
			h.logger.Warn("Login failed: inactive account",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
		} else if errors.Is(err, userdomain.ErrAuthentication) {
			h.logger.Warn("Login failed: invalid credentials",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
		} else {
			// Log unexpected internal errors
			status = http.StatusInternalServerError
			msg = "An internal error occurred during login"
			h.logger.Error("Unexpected login error",
				zap.String("username", req.Username),
				zap.String("remote_addr", c.ClientIP()),
				zap.Error(err))
		}
		c.JSON(status, gin.H{"error": msg})
		return
	}

	h.logger.Info("User authentication successful",
		zap.String("user_id", user.ID.Hex()),
		zap.String("username", user.Username),
		zap.String("remote_addr", c.ClientIP()))

	// --- Session Management ---
	session := sessions.Default(c)
	// Use keys defined in the middleware package
	session.Set(middleware.UserIDKey, user.ID.Hex())
	session.Set(middleware.UsernameKey, user.Username)
	session.Set(middleware.IsAdminKey, user.IsAdmin)
	if err := session.Save(); err != nil {
		h.logger.Error("Failed to save session after login",
			zap.String("user_id", user.ID.Hex()),
			zap.String("username", user.Username),
			zap.String("remote_addr", c.ClientIP()),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save session after login"})
		return
	}

	h.logger.Info("Login successful, session created",
		zap.String("user_id", user.ID.Hex()),
		zap.String("username", user.Username),
		zap.String("remote_addr", c.ClientIP()))

	// Login successful - Return limited user info (DTO recommended)
	respUser := model.User{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		FullName: user.FullName,
		IsAdmin:  user.IsAdmin,
	}
	c.JSON(http.StatusOK, gin.H{"message": "Login successful", "user": respUser})
}

// HandleGetProfile godoc
// @Summary Get current user profile
// @Description Retrieves the profile information of the currently logged-in user.
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.User "User profile retrieved successfully (excludes password hash)"
// @Failure 401 {object} map[string]string "Unauthorized: Invalid or missing session"
// @Failure 500 {object} map[string]string "Internal server error (e.g., failed to parse user ID or fetch user)"
// @Router /api/v1/auth/profile [get]
func (h *Service) HandleGetProfile(c *gin.Context) {
	// UserID is set by the AuthRequired middleware
	userIDRaw, exists := c.Get(middleware.UserIDKey)
	if !exists {
		// This should ideally not happen if AuthRequired middleware is used correctly
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: User ID not found in context"})
		return
	}

	userIDStr, ok := userIDRaw.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error: User ID has unexpected type in context"})
		return
	}

	// Convert hex string to ObjectID
	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error: Failed to parse user ID"})
		return
	}

	// Call domain logic to get user by ID
	user, err := h.userDomain.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if errors.Is(err, userdomain.ErrUserNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "User profile not found"}) // Or 401 if user magically disappeared?
		} else {
			fmt.Printf("Error getting user profile: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve user profile"})
		}
		return
	}

	// Return user profile (DTO without password hash is handled by GetUserByID)
	c.JSON(http.StatusOK, user)
}

// HandleLogout godoc
// @Summary Log out the current user
// @Description Clears the user's session.
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map{message=string} "Logout successful"
// @Failure 500 {object} map[string]string "Internal server error (e.g., session save failed)"
// @Router /api/v1/auth/logout [post]
func (h *Service) HandleLogout(c *gin.Context) {
	session := sessions.Default(c)
	// Clear session data
	session.Clear()
	// Set options to expire cookie immediately
	session.Options(sessions.Options{MaxAge: -1})

	if err := session.Save(); err != nil {
		fmt.Printf("Error clearing session during logout: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process logout"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Logout successful"})
}

// DTOs for request binding
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=8,max=100"`
	Email    string `json:"email,omitempty" binding:"omitempty,email"`
	FullName string `json:"full_name,omitempty"`
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}
