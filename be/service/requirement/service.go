package requirement

import (
	"errors"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	requirementdomain "git.nevint.com/fota3/t-rex/domain/requirement"
	requirementmodel "git.nevint.com/fota3/t-rex/model/requirement"
	"git.nevint.com/fota3/t-rex/pkg/fileupload"
	"git.nevint.com/fota3/t-rex/pkg/middleware"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// FileUploadService defines the interface for file upload operations
type FileUploadService interface {
	UploadImage(file *multipart.FileHeader) (*fileupload.UploadedFile, error)
	DeleteFile(relativePath string) error
}

// Service holds the dependencies for requirement-related HTTP handlers.
type Service struct {
	requirementDomain *requirementdomain.Domain
	fileUploadService FileUploadService
	logger            *zap.Logger
}

// NewService creates a new service instance for requirement endpoints.
func NewService(requirementDomain *requirementdomain.Domain, fileUploadService FileUploadService, logger *zap.Logger) *Service {
	return &Service{
		requirementDomain: requirementDomain,
		fileUploadService: fileUploadService,
		logger:            logger.Named("requirement_service"),
	}
}

// CreateRequirementRequest represents the request payload for creating a new requirement
type CreateRequirementRequest struct {
	Title      string `json:"title" binding:"required,min=1,max=200"`
	Content    string `json:"content" binding:"required,min=3,max=5000"`
	ModelName  string `json:"model_name"`
	WebsiteURL string `json:"website_url,omitempty"` // Optional reference website URL
}

// UpdateRequirementRequest represents the request payload for updating a requirement
type UpdateRequirementRequest struct {
	Title           *string `json:"title,omitempty"`
	Content         *string `json:"content,omitempty"`
	DevelopmentPlan *string `json:"development_plan,omitempty"`
}

// SubmitAnswersRequest represents the request payload for submitting answers to questions
type SubmitAnswersRequest struct {
	Answers []AnswerSubmission `json:"answers" binding:"required,dive"`
}

// AnswerSubmission represents a single answer submission
type AnswerSubmission struct {
	QuestionID    string `json:"question_id" binding:"required"`
	SelectedIndex int    `json:"selected_index"` // Index of selected option for choice questions (0-based)
	Text          string `json:"text" binding:"required,min=1,max=2000"`
}

// CreateProjectFromRequirementRequest represents the request payload for creating a project from a requirement
type CreateProjectFromRequirementRequest struct {
	ProjectID   string `json:"project_id" binding:"required,min=1,max=100"`
	ProjectName string `json:"project_name" binding:"required,min=1,max=200"`
	WireframeID string `json:"wireframe_id" binding:"required"`
}

// HandleCreateRequirement godoc
// @Summary Create a new requirement
// @Description Creates a new requirement for the authenticated user with optional AI model selection, reference images, and/or reference website
// @Tags requirements
// @Accept  multipart/form-data
// @Produce json
// @Param   title formData string true "Requirement title"
// @Param   content formData string true "Requirement content"
// @Param   model_name formData string false "AI model to use (gemini-2.5-pro or gemini-2.5-flash, defaults to gemini-2.5-pro)"
// @Param   website_url formData string false "Reference website URL"
// @Param   images formData file false "Reference images (multiple files allowed)"
// @Success 201 {object} map{message=string, requirement=requirementmodel.Requirement} "Requirement created successfully"
// @Failure 400 {object} map[string]string "Invalid request data"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements [post]
func (s *Service) HandleCreateRequirement(c *gin.Context) {
	s.logger.Info("Create requirement request received",
		zap.String("remote_addr", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")))

	// Extract user ID from context (set by auth middleware)
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse form data
	title := c.PostForm("title")
	content := c.PostForm("content")
	modelName := c.PostForm("model_name")
	websiteURL := c.PostForm("website_url")

	// Validate required fields
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Title is required"})
		return
	}
	if content == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Content is required"})
		return
	}

	// Set default model if not provided
	if modelName == "" {
		modelName = requirementmodel.DefaultModel
	}

	// Handle file uploads
	form, err := c.MultipartForm()
	var imageFiles []*multipart.FileHeader
	if err == nil && form.File["images"] != nil {
		imageFiles = form.File["images"]
	}

	// Determine which creation method to use based on provided data
	hasImages := len(imageFiles) > 0
	hasWebsite := websiteURL != ""

	s.logger.Info("Processing requirement creation request",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName),
		zap.Bool("has_images", hasImages),
		zap.Bool("has_website", hasWebsite))

	var requirement *requirementmodel.Requirement

	if hasImages && hasWebsite {
		// Both images and website
		requirement, err = s.requirementDomain.CreateRequirementWithImagesAndWebsite(c.Request.Context(), userID, title, content, modelName, websiteURL, imageFiles)
		if err != nil {
			status := http.StatusInternalServerError
			msg := "Failed to create requirement with images and website"
			if errors.Is(err, requirementdomain.ErrInvalidInput) {
				status = http.StatusBadRequest
				msg = err.Error()
			}
			s.logger.Error("Failed to create requirement with images and website",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
			c.JSON(status, gin.H{"error": msg})
			return
		}
	} else if hasImages {
		// Only images
		requirement, err = s.requirementDomain.CreateRequirementWithImages(c.Request.Context(), userID, title, content, modelName, imageFiles)
		if err != nil {
			status := http.StatusInternalServerError
			msg := "Failed to create requirement with images"
			if errors.Is(err, requirementdomain.ErrInvalidInput) {
				status = http.StatusBadRequest
				msg = err.Error()
			}
			s.logger.Error("Failed to create requirement with images",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
			c.JSON(status, gin.H{"error": msg})
			return
		}
	} else if hasWebsite {
		// Only website
		requirement, err = s.requirementDomain.CreateRequirementWithWebsite(c.Request.Context(), userID, title, content, modelName, websiteURL)
		if err != nil {
			status := http.StatusInternalServerError
			msg := "Failed to create requirement with website"
			if errors.Is(err, requirementdomain.ErrInvalidInput) {
				status = http.StatusBadRequest
				msg = err.Error()
			}
			s.logger.Error("Failed to create requirement with website",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
			c.JSON(status, gin.H{"error": msg})
			return
		}
	} else {
		// Neither images nor website
		requirement, err = s.requirementDomain.CreateRequirement(c.Request.Context(), userID, title, content, modelName)
		if err != nil {
			status := http.StatusInternalServerError
			msg := "Failed to create requirement"
			if errors.Is(err, requirementdomain.ErrInvalidInput) {
				status = http.StatusBadRequest
				msg = err.Error()
			}
			s.logger.Error("Failed to create requirement",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
			c.JSON(status, gin.H{"error": msg})
			return
		}
	}

	s.logger.Info("Requirement created successfully",
		zap.String("requirement_id", requirement.ID.Hex()),
		zap.String("user_id", userID.Hex()))

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Requirement created successfully",
		"requirement": requirement,
	})
}

// HandleGetRequirements godoc
// @Summary Get user requirements with pagination
// @Description Retrieves requirements for the authenticated user with pagination support
// @Tags requirements
// @Produce json
// @Param   page query int false "Page number (default: 1)"
// @Param   limit query int false "Items per page (default: 10, max: 100)"
// @Param   status query string false "Filter by status (pending/processing/completed)"
// @Success 200 {object} map{requirements=[]requirementmodel.Requirement, pagination=map[string]interface{}} "Requirements retrieved successfully"
// @Failure 400 {object} map[string]string "Invalid query parameters"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements [get]
func (s *Service) HandleGetRequirements(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	status := c.Query("status") // Optional status filter

	s.logger.Info("Get requirements request",
		zap.String("user_id", userID.Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.String("status", status))

	// Call domain layer
	var requirements []*requirementmodel.Requirement
	var total int64

	if status != "" {
		requirements, err = s.requirementDomain.GetUserRequirementsByStatus(c.Request.Context(), userID, status)
		if err != nil {
			s.logger.Error("Failed to retrieve requirements by status",
				zap.String("user_id", userID.Hex()),
				zap.String("status", status),
				zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve requirements"})
			return
		}
		total = int64(len(requirements))

		// Manual pagination for status filter
		start := (page - 1) * limit
		end := start + limit
		if start > len(requirements) {
			requirements = []*requirementmodel.Requirement{}
		} else {
			if end > len(requirements) {
				end = len(requirements)
			}
			requirements = requirements[start:end]
		}
	} else {
		requirements, total, err = s.requirementDomain.GetUserRequirementsWithPagination(c.Request.Context(), userID, page, limit)
		if err != nil {
			s.logger.Error("Failed to retrieve requirements with pagination",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve requirements"})
			return
		}
	}

	// Calculate pagination info
	totalPages := (total + int64(limit) - 1) / int64(limit)

	c.JSON(http.StatusOK, gin.H{
		"requirements": requirements,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// HandleGetRequirement godoc
// @Summary Get a specific requirement
// @Description Retrieves a specific requirement by ID for the authenticated user
// @Tags requirements
// @Produce json
// @Param   id path string true "Requirement ID"
// @Success 200 {object} map{requirement=requirementmodel.Requirement} "Requirement retrieved successfully"
// @Failure 400 {object} map[string]string "Invalid requirement ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id} [get]
func (s *Service) HandleGetRequirement(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	s.logger.Info("Get requirement request",
		zap.String("user_id", userID.Hex()),
		zap.String("requirement_id", requirementID.Hex()))

	// Call domain layer
	requirement, err := s.requirementDomain.GetRequirementByID(c.Request.Context(), requirementID, userID)
	if err != nil {
		if errors.Is(err, requirementdomain.ErrNotFound) {
			s.logger.Warn("Requirement not found",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
			c.JSON(http.StatusNotFound, gin.H{"error": "Requirement not found"})
			return
		} else if errors.Is(err, requirementdomain.ErrUnauthorized) {
			s.logger.Warn("Unauthorized access to requirement",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized access to requirement"})
			return
		}

		s.logger.Error("Failed to retrieve requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve requirement"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"requirement": requirement})
}

// HandleGenerateQuestions godoc
// @Summary Generate AI questions for a requirement
// @Description Generates AI questions to help refine a requirement
// @Tags requirements
// @Produce json
// @Param   id path string true "Requirement ID"
// @Success 200 {object} map{message=string, requirement=requirementmodel.Requirement} "Questions generated successfully"
// @Failure 400 {object} map[string]string "Invalid requirement ID or requirement state"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id}/questions [post]
func (s *Service) HandleGenerateQuestions(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	s.logger.Info("Generate questions request",
		zap.String("user_id", userID.Hex()),
		zap.String("requirement_id", requirementID.Hex()))

	// Call domain layer
	requirement, err := s.requirementDomain.GenerateQuestions(c.Request.Context(), requirementID, userID)
	if err != nil {
		status := http.StatusInternalServerError
		msg := "Failed to generate questions"

		if errors.Is(err, requirementdomain.ErrNotFound) {
			status = http.StatusNotFound
			msg = "Requirement not found"
		} else if errors.Is(err, requirementdomain.ErrUnauthorized) {
			status = http.StatusUnauthorized
			msg = "Unauthorized access to requirement"
		} else if errors.Is(err, requirementdomain.ErrQuestionGenerationFailed) {
			status = http.StatusBadRequest
			msg = err.Error()
		} else if errors.Is(err, requirementdomain.ErrInvalidInput) {
			status = http.StatusBadRequest
			msg = err.Error()
		}

		s.logger.Error("Question generation failed",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))

		c.JSON(status, gin.H{"error": msg})
		return
	}

	s.logger.Info("Questions generated successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Int("question_count", len(requirement.Questions)))

	c.JSON(http.StatusOK, gin.H{
		"message":     "Questions generated successfully",
		"requirement": requirement,
	})
}

// HandleSubmitAnswers godoc
// @Summary Submit answers to requirement questions
// @Description Submits user answers to AI-generated questions for a requirement
// @Tags requirements
// @Accept  json
// @Produce json
// @Param   id path string true "Requirement ID"
// @Param   body body SubmitAnswersRequest true "Answer submission data"
// @Success 200 {object} map{message=string, requirement=requirementmodel.Requirement} "Answers submitted successfully"
// @Failure 400 {object} map[string]string "Invalid request data or requirement state"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id}/answers [post]
func (s *Service) HandleSubmitAnswers(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	var req SubmitAnswersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Invalid answer submission data received",
			zap.Error(err),
			zap.String("user_id", userID.Hex()),
			zap.String("requirement_id", requirementID.Hex()))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	s.logger.Info("Submit answers request",
		zap.String("user_id", userID.Hex()),
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("answer_count", len(req.Answers)))

	// Convert request answers to domain model
	answers := make([]requirementmodel.Answer, 0, len(req.Answers))
	for _, answer := range req.Answers {
		answers = append(answers, requirementmodel.Answer{
			QuestionID:    answer.QuestionID,
			SelectedIndex: answer.SelectedIndex, // 添加选中索引
			Text:          answer.Text,
			AnsweredAt:    time.Now(),
		})
	}

	// Call domain layer
	requirement, err := s.requirementDomain.SubmitAnswers(c.Request.Context(), requirementID, userID, answers)
	if err != nil {
		status := http.StatusInternalServerError
		msg := "Failed to submit answers"

		if errors.Is(err, requirementdomain.ErrNotFound) {
			status = http.StatusNotFound
			msg = "Requirement not found"
		} else if errors.Is(err, requirementdomain.ErrUnauthorized) {
			status = http.StatusUnauthorized
			msg = "Unauthorized access to requirement"
		} else if errors.Is(err, requirementdomain.ErrInvalidInput) {
			status = http.StatusBadRequest
			msg = err.Error()
		}

		s.logger.Error("Answer submission failed",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))

		c.JSON(status, gin.H{"error": msg})
		return
	}

	s.logger.Info("Answers submitted successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	c.JSON(http.StatusOK, gin.H{
		"message":     "Answers submitted successfully",
		"requirement": requirement,
	})
}

// HandleUpdateRequirement godoc
// @Summary Update a requirement
// @Description Updates the title and/or content of a requirement
// @Tags requirements
// @Accept  json
// @Produce json
// @Param   id path string true "Requirement ID"
// @Param   body body UpdateRequirementRequest true "Requirement update data"
// @Success 200 {object} map{message=string, requirement=requirementmodel.Requirement} "Requirement updated successfully"
// @Failure 400 {object} map[string]string "Invalid request data or requirement ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id} [put]
func (s *Service) HandleUpdateRequirement(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	var req UpdateRequirementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Invalid requirement update data received",
			zap.Error(err),
			zap.String("user_id", userID.Hex()),
			zap.String("requirement_id", requirementID.Hex()))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.DevelopmentPlan != nil {
		updates["development_plan"] = *req.DevelopmentPlan
	}

	// At least one field must be provided
	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one field to update must be provided"})
		return
	}

	s.logger.Info("Update requirement request",
		zap.String("user_id", userID.Hex()),
		zap.String("requirement_id", requirementID.Hex()),
		zap.Any("updates", updates))

	// Call domain layer to update the requirement
	updatedRequirement, err := s.requirementDomain.UpdateRequirement(c.Request.Context(), requirementID, userID, updates)
	if err != nil {
		status := http.StatusInternalServerError
		msg := "Failed to update requirement"

		if errors.Is(err, requirementdomain.ErrNotFound) {
			status = http.StatusNotFound
			msg = "Requirement not found"
		} else if errors.Is(err, requirementdomain.ErrUnauthorized) {
			status = http.StatusUnauthorized
			msg = "Unauthorized access to requirement"
		} else if errors.Is(err, requirementdomain.ErrInvalidInput) {
			status = http.StatusBadRequest
			msg = err.Error()
		}

		s.logger.Error("Requirement update failed",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))

		c.JSON(status, gin.H{"error": msg})
		return
	}

	s.logger.Info("Requirement updated successfully",
		zap.String("requirement_id", updatedRequirement.ID.Hex()),
		zap.String("user_id", userID.Hex()))

	c.JSON(http.StatusOK, gin.H{
		"message":     "Requirement updated successfully",
		"requirement": updatedRequirement,
	})
}

// HandleDeleteRequirement godoc
// @Summary Delete a requirement
// @Description Deletes a requirement for the authenticated user
// @Tags requirements
// @Produce json
// @Param   id path string true "Requirement ID"
// @Success 200 {object} map{message=string} "Requirement deleted successfully"
// @Failure 400 {object} map[string]string "Invalid requirement ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id} [delete]
func (s *Service) HandleDeleteRequirement(c *gin.Context) {
	// Extract user ID from context
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	s.logger.Info("Delete requirement request",
		zap.String("user_id", userID.Hex()),
		zap.String("requirement_id", requirementID.Hex()))

	// Call domain layer
	err = s.requirementDomain.DeleteRequirement(c.Request.Context(), requirementID, userID)
	if err != nil {
		if errors.Is(err, requirementdomain.ErrNotFound) {
			s.logger.Warn("Requirement not found for deletion",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
			c.JSON(http.StatusNotFound, gin.H{"error": "Requirement not found"})
			return
		} else if errors.Is(err, requirementdomain.ErrUnauthorized) {
			s.logger.Warn("Unauthorized deletion attempt",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized access to requirement"})
			return
		}

		s.logger.Error("Failed to delete requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete requirement"})
		return
	}

	s.logger.Info("Requirement deleted successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	c.JSON(http.StatusOK, gin.H{"message": "Requirement deleted successfully"})
}

// HandleGenerateWireframe godoc
// @Summary Generate wireframe for a requirement
// @Description Generates an interactive HTML wireframe based on the requirement's development plan
// @Tags requirements
// @Produce json
// @Param   id path string true "Requirement ID"
// @Success 200 {object} map{message=string, requirement=requirementmodel.Requirement} "Wireframe generated successfully"
// @Failure 400 {object} map[string]string "Invalid request or missing development plan"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id}/wireframe [post]
func (s *Service) HandleGenerateWireframe(c *gin.Context) {
	s.logger.Info("Generate wireframe request received",
		zap.String("remote_addr", c.ClientIP()),
		zap.String("requirement_id", c.Param("id")))

	// Extract user ID from context (set by auth middleware)
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse requirement ID
	requirementIDStr := c.Param("id")
	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format",
			zap.String("requirement_id", requirementIDStr),
			zap.String("user_id", userID.Hex()),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	s.logger.Info("Processing wireframe generation request",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	// Call domain layer to generate wireframe
	requirement, err := s.requirementDomain.GenerateWireframe(c.Request.Context(), requirementID, userID)
	if err != nil {
		status := http.StatusInternalServerError
		msg := "Failed to generate wireframe"

		if errors.Is(err, requirementdomain.ErrUnauthorized) {
			status = http.StatusForbidden
			msg = "Access denied"
			s.logger.Warn("Wireframe generation access denied",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
		} else if errors.Is(err, requirementdomain.ErrNotFound) {
			status = http.StatusNotFound
			msg = "Requirement not found"
			s.logger.Warn("Wireframe generation failed: requirement not found",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
		} else if err.Error() == "development plan is required to generate wireframe" {
			status = http.StatusBadRequest
			msg = "Development plan is required to generate wireframe"
			s.logger.Warn("Wireframe generation failed: missing development plan",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()))
		} else {
			s.logger.Error("Unexpected error during wireframe generation",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
		}

		c.JSON(status, gin.H{"error": msg})
		return
	}

	s.logger.Info("Wireframe generated successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Int("wireframe_length", len(requirement.WireframeHTML)))

	c.JSON(http.StatusOK, gin.H{
		"message":     "Wireframe generated successfully",
		"requirement": requirement,
	})
}

// HandleCreateRequirementWithImages godoc
// @Summary Create a new requirement with reference images
// @Description Creates a new requirement for the authenticated user with optional reference images and AI model selection
// @Tags requirements
// @Accept  multipart/form-data
// @Produce json
// @Param   title formData string true "Requirement title"
// @Param   content formData string true "Requirement content"
// @Param   model_name formData string false "AI model to use (gemini-2.5-pro or gemini-2.5-flash, defaults to gemini-2.5-pro)"
// @Param   images formData file false "Reference images (multiple files allowed)"
// @Success 201 {object} map{message=string, requirement=requirementmodel.Requirement} "Requirement created successfully"
// @Failure 400 {object} map[string]string "Invalid request data"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/with-images [post]
func (s *Service) HandleCreateRequirementWithImages(c *gin.Context) {
	s.logger.Info("Create requirement with images request received",
		zap.String("remote_addr", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")))

	// Extract user ID from context (set by auth middleware)
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse form data
	title := c.PostForm("title")
	content := c.PostForm("content")
	modelName := c.PostForm("model_name")

	// Validate required fields
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Title is required"})
		return
	}
	if content == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Content is required"})
		return
	}

	// Set default model if not provided
	if modelName == "" {
		modelName = requirementmodel.DefaultModel
	}

	// Parse uploaded files
	form, err := c.MultipartForm()
	if err != nil {
		s.logger.Error("Failed to parse multipart form", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
		return
	}

	var imageFiles []*multipart.FileHeader
	if files, exists := form.File["images"]; exists {

		imageFiles = files
	}

	s.logger.Info("Processing requirement creation request with images",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName),
		zap.Int("image_count", len(imageFiles)))

	// Call domain layer to create requirement with images
	requirement, err := s.requirementDomain.CreateRequirementWithImages(c.Request.Context(), userID, title, content, modelName, imageFiles)
	if err != nil {
		status := http.StatusInternalServerError
		msg := "Failed to create requirement"

		if errors.Is(err, requirementdomain.ErrInvalidInput) {
			status = http.StatusBadRequest
			msg = err.Error()
			s.logger.Warn("Requirement creation with images failed: invalid input",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
		} else {
			s.logger.Error("Unexpected error during requirement creation with images",
				zap.String("user_id", userID.Hex()),
				zap.Error(err))
		}

		c.JSON(status, gin.H{"error": msg})
		return
	}

	s.logger.Info("Requirement with images created successfully",
		zap.String("requirement_id", requirement.ID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Int("image_count", len(requirement.ReferenceImages)))

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Requirement with images created successfully",
		"requirement": requirement,
	})
}

// HandleCreateProjectFromRequirement godoc
// @Summary Create a project from a requirement and selected wireframe
// @Description Creates a new project based on a completed requirement and a selected wireframe. The project will include the development plan and wireframe HTML as initial files.
// @Tags requirements
// @Accept  json
// @Produce json
// @Param   id path string true "Requirement ID"
// @Param   body body CreateProjectFromRequirementRequest true "Project creation details"
// @Success 201 {object} map{message=string, project_id=string} "Project created successfully"
// @Failure 400 {object} map[string]string "Invalid request data"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Requirement not found"
// @Failure 409 {object} map[string]string "Project ID already exists"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/v1/requirements/{id}/create-project [post]
func (s *Service) HandleCreateProjectFromRequirement(c *gin.Context) {
	requirementIDStr := c.Param("id")
	if requirementIDStr == "" {
		s.logger.Warn("Missing requirement ID in request path")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Requirement ID is required"})
		return
	}

	requirementID, err := primitive.ObjectIDFromHex(requirementIDStr)
	if err != nil {
		s.logger.Warn("Invalid requirement ID format", zap.String("requirement_id", requirementIDStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid requirement ID format"})
		return
	}

	// Extract user ID from context (set by auth middleware)
	userIDStr, exists := c.Get(middleware.UserIDKey)
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		s.logger.Error("Invalid user ID format", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	var req CreateProjectFromRequirementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warn("Failed to bind CreateProjectFromRequirement request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	s.logger.Info("Create project from requirement request received",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("project_id", req.ProjectID),
		zap.String("project_name", req.ProjectName),
		zap.String("wireframe_id", req.WireframeID))

	// Call domain logic to create project from requirement
	_, err = s.requirementDomain.CreateProjectFromRequirement(
		c.Request.Context(),
		requirementID,
		req.WireframeID,
		req.ProjectID,
		req.ProjectName,
		userID,
	)

	if err != nil {
		s.logger.Error("Failed to create project from requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("project_id", req.ProjectID),
			zap.Error(err))

		// Handle specific error types
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if strings.Contains(err.Error(), "already exists") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "access denied") {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		} else if strings.Contains(err.Error(), "must be completed") || strings.Contains(err.Error(), "wireframe") || strings.Contains(err.Error(), "development plan") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project from requirement"})
		}
		return
	}

	s.logger.Info("Project created successfully from requirement",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("project_id", req.ProjectID),
		zap.String("project_name", req.ProjectName),
		zap.String("wireframe_id", req.WireframeID))

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Project created successfully from requirement",
		"project_id": req.ProjectID,
	})
}
