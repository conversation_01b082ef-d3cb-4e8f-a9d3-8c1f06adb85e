package aimodel

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	aimodelDomain "git.nevint.com/fota3/t-rex/domain/aimodel"
	"git.nevint.com/fota3/t-rex/model/aimodel"
)

// Service provides HTTP handlers for AI model operations
type Service struct {
	domain *aimodelDomain.Domain
	logger *zap.SugaredLogger
}

// NewService creates a new AI model service instance
func NewService(domain *aimodelDomain.Domain, logger *zap.Logger) *Service {
	return &Service{
		domain: domain,
		logger: logger.Sugar(),
	}
}

// CreateAIModelRequest represents the request payload for creating an AI model
type CreateAIModelRequest struct {
	APIKey    string `json:"api_key" binding:"required"`
	APIBase   string `json:"api_base" binding:"required"`
	ModelName string `json:"model_name" binding:"required"`
	IsActive  *bool  `json:"is_active,omitempty"`
}

// UpdateAIModelRequest represents the request payload for updating an AI model
type UpdateAIModelRequest struct {
	APIKey    *string `json:"api_key,omitempty"`
	APIBase   *string `json:"api_base,omitempty"`
	ModelName *string `json:"model_name,omitempty"`
	IsActive  *bool   `json:"is_active,omitempty"`
}

// HandleCreateAIModel handles POST /api/v1/aimodels
func (s *Service) HandleCreateAIModel(c *gin.Context) {
	var req CreateAIModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warnf("Invalid request payload: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	model := &aimodel.AIModel{
		APIKey:    req.APIKey,
		APIBase:   req.APIBase,
		ModelName: req.ModelName,
		IsActive:  true, // Default to active
	}

	// Override default if explicitly provided
	if req.IsActive != nil {
		model.IsActive = *req.IsActive
	}

	if err := s.domain.CreateAIModel(c.Request.Context(), model); err != nil {
		s.logger.Errorf("Failed to create AI model: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create AI model", "details": err.Error()})
		return
	}

	s.logger.Infof("AI model created successfully: %s", model.ModelName)
	c.JSON(http.StatusCreated, gin.H{
		"message": "AI model created successfully",
		"model":   model,
	})
}

// HandleGetAIModels handles GET /api/v1/aimodels
func (s *Service) HandleGetAIModels(c *gin.Context) {
	// Parse query parameter for active filter
	activeOnlyStr := c.Query("active_only")
	activeOnly := false
	if activeOnlyStr != "" {
		if parsed, err := strconv.ParseBool(activeOnlyStr); err == nil {
			activeOnly = parsed
		}
	}

	models, err := s.domain.ListAIModels(c.Request.Context(), activeOnly)
	if err != nil {
		s.logger.Errorf("Failed to list AI models: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list AI models", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"models": models,
		"count":  len(models),
	})
}

// HandleGetAIModel handles GET /api/v1/aimodels/:id
func (s *Service) HandleGetAIModel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		s.logger.Warnf("Invalid AI model ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid AI model ID"})
		return
	}

	model, err := s.domain.GetAIModel(c.Request.Context(), id)
	if err != nil {
		s.logger.Errorf("Failed to get AI model: %v", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "AI model not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"model": model})
}

// HandleUpdateAIModel handles PUT /api/v1/aimodels/:id
func (s *Service) HandleUpdateAIModel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		s.logger.Warnf("Invalid AI model ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid AI model ID"})
		return
	}

	var req UpdateAIModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Warnf("Invalid request payload: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.APIKey != nil {
		updates["api_key"] = *req.APIKey
	}
	if req.APIBase != nil {
		updates["api_base"] = *req.APIBase
	}
	if req.ModelName != nil {
		updates["model_name"] = *req.ModelName
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	if err := s.domain.UpdateAIModel(c.Request.Context(), id, updates); err != nil {
		s.logger.Errorf("Failed to update AI model: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update AI model", "details": err.Error()})
		return
	}

	s.logger.Infof("AI model updated successfully: %s", idStr)
	c.JSON(http.StatusOK, gin.H{"message": "AI model updated successfully"})
}

// HandleDeleteAIModel handles DELETE /api/v1/aimodels/:id
func (s *Service) HandleDeleteAIModel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		s.logger.Warnf("Invalid AI model ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid AI model ID"})
		return
	}

	if err := s.domain.DeleteAIModel(c.Request.Context(), id); err != nil {
		s.logger.Errorf("Failed to delete AI model: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete AI model", "details": err.Error()})
		return
	}

	s.logger.Infof("AI model deleted successfully: %s", idStr)
	c.JSON(http.StatusOK, gin.H{"message": "AI model deleted successfully"})
}

// HandleToggleAIModelStatus handles POST /api/v1/aimodels/:id/toggle
func (s *Service) HandleToggleAIModelStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		s.logger.Warnf("Invalid AI model ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid AI model ID"})
		return
	}

	if err := s.domain.ToggleAIModelStatus(c.Request.Context(), id); err != nil {
		s.logger.Errorf("Failed to toggle AI model status: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to toggle AI model status", "details": err.Error()})
		return
	}

	s.logger.Infof("AI model status toggled successfully: %s", idStr)
	c.JSON(http.StatusOK, gin.H{"message": "AI model status toggled successfully"})
}
