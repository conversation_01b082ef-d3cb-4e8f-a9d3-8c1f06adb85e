package agent

import (
	"context"
	"fmt"
	"log"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	appConfig "git.nevint.com/fota3/t-rex/config"
	"git.nevint.com/fota3/t-rex/domain/agent"
	"git.nevint.com/fota3/t-rex/internal/agentstore"
	agentModel "git.nevint.com/fota3/t-rex/model/agent"
	"git.nevint.com/fota3/t-rex/pkg/llm/provider"
	"git.nevint.com/fota3/t-rex/pkg/mcp_hub"
	"git.nevint.com/fota3/t-rex/pkg/tokenizer"
	"git.nevint.com/fota3/t-rex/prompts"
)

func TestAgentService_HandleStreamingInteraction(t *testing.T) {
	cfg, err := appConfig.LoadConfig("../../config")
	if err != nil {
		log.Fatalf("Failed to load application configuration: %v", err)
	}
	logger, err := zap.NewDevelopment()
	if err != nil {
		log.Fatalf("can't initialize zap logger: %v", err)
	}
	defer logger.Sync()
	sugar := logger.Sugar()

	// --- Initialize MongoDB Connection ---
	sugar.Info("Connecting to MongoDB...")
	clientOptions := options.Client().ApplyURI(cfg.MongoDB.URI)
	mongoClient, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		sugar.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	err = mongoClient.Ping(context.TODO(), nil)
	if err != nil {
		sugar.Fatalf("Failed to ping MongoDB: %v", err)
	}
	sugar.Info("Connected to MongoDB!")
	defer func() {
		if err = mongoClient.Disconnect(context.TODO()); err != nil {
			sugar.Errorf("Error disconnecting from MongoDB: %v", err)
		}
		sugar.Info("Disconnected from MongoDB.")
	}()

	sugar.Info("Initializing Project Store...")
	repo, err := agentstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize repo: %v", err)
	}

	sugar.Info("Initializing LLM Client...")
	llmClient, err := provider.NewOpenAICompatibleClient(provider.OpenAICompatibleConfig{
		APIKey:  cfg.OpenAI.APIKey,
		BaseURL: cfg.OpenAI.APIBase,
	})

	if err != nil {
		log.Fatalf("Failed to create OpenAI compatible client: %v", err)
	}

	mcpServersConfig, err := mcp_hub.LoadConfigFromFile("../../config/mcp_servers.json")
	if err != nil {
		log.Fatalf("Failed to load MCP server configuration: %v", err)
	}
	mcpClient, err := mcp_hub.NewClientManager(mcpServersConfig, logger)
	if err != nil {
		log.Fatalf("Failed to load MCP client: %v", err)
	}
	executer := mcp_hub.NewMCPToolExecutorImpl(mcpClient)

	tokenizerService, err := tokenizer.NewService(cfg.Tokenizer.Encoding)
	if err != nil {
		log.Fatalf("Failed to initialize Tokenizer Service: %v", err)
	}

	historyManager := agent.NewSimpleHistoryManager(
		sugar,
		tokenizerService,
		cfg.History.MaxMessages,
		cfg.History.MaxTokens,
	)

	reActDomain := agent.NewAgentReActDomain(repo, llmClient, executer, historyManager, sugar, cfg.OpenAI.ModelName)
	ctx := context.Background()
	projectID := "test-project-id"

	// Generate a valid system prompt for the test
	systemPrompt, err := prompts.GenerateSystemPrompt(prompts.PromptDynamicData{})
	if err != nil {
		log.Fatalf("Failed to generate system prompt: %v", err)
	}

	conversation := &agentModel.Conversation{
		ID:        primitive.NewObjectID(),
		ProjectID: projectID,
		Messages:  make([]agentModel.Message, 0),
	}

	userMsg := agentModel.Message{
		MsgID:     primitive.NewObjectID().Hex(),
		Role:      agentModel.RoleUser,
		Content:   "帮我查看一下/Users/<USER>",
		Timestamp: time.Now(),
	}
	conversation.Messages = append(conversation.Messages, userMsg)

	stream, err := reActDomain.HandleStreamingInteraction(ctx, conversation, userMsg, systemPrompt)
	if err != nil {
		log.Fatalf("初始化对话失败: %v", err)
	}

	// 逐步读取AI流式回复
	for resp := range stream {
		fmt.Print(resp) // 可以推送到前端、WebSocket、SSE等
	}
}
