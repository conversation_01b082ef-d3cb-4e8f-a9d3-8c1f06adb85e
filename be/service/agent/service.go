package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.nevint.com/fota3/t-rex/domain/runtime"
	"git.nevint.com/fota3/t-rex/prompts"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/domain/agent"
	projectDomain "git.nevint.com/fota3/t-rex/domain/project"
	agentModel "git.nevint.com/fota3/t-rex/model/agent"
	mcpHub "git.nevint.com/fota3/t-rex/pkg/mcp_hub"
	"git.nevint.com/fota3/t-rex/pkg/middleware"
)

// Service provides HTTP handlers for agent-related operations
type Service struct {
	agentDomain   *agent.AgentReActDomain
	projectDomain *projectDomain.Domain
	runtimeDomain *runtime.Domain
	logger        *zap.SugaredLogger
	mcpHub        *mcpHub.ClientManager
}

// NewService creates a new instance of the agent service
func NewService(
	agentDomain *agent.AgentReActDomain,
	projectDomain *projectDomain.Domain,
	runtimeDomain *runtime.Domain,
	logger *zap.SugaredLogger,
	mcpHub *mcpHub.ClientManager,
) *Service {
	return &Service{
		agentDomain:   agentDomain,
		projectDomain: projectDomain,
		runtimeDomain: runtimeDomain,
		logger:        logger.Named("AgentService"),
		mcpHub:        mcpHub,
	}
}

// Request/Response types

type GetConversationResponse struct {
	ConversationID string               `json:"conversation_id"`
	ProjectID      string               `json:"project_id"`
	Messages       []agentModel.Message `json:"messages"`
	CreatedAt      time.Time            `json:"created_at"`
	UpdatedAt      time.Time            `json:"updated_at"`
	MessageCount   int                  `json:"message_count"`
}

type SendMessageRequest struct {
	Message        string       `json:"message" binding:"required"`
	Context        *ChatContext `json:"context,omitempty"`
	RetryMessageID string       `json:"retry_message_id,omitempty"`
}

type ChatContext struct {
	CurrentFile string     `json:"current_file,omitempty"`
	Selection   *Selection `json:"selection,omitempty"`
	OpenFiles   []string   `json:"open_files,omitempty"`
}

type Selection struct {
	FilePath  string `json:"file_path"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	Content   string `json:"content"`
}

type SendMessageResponse struct {
	MessageID string `json:"message_id"`
	StreamURL string `json:"stream_url"`
}

type ClearConversationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// --- New SSE Event Data Structures ---

// SSEMessageStartData is the data for the 'message_start' SSE event.
type SSEMessageStartData struct {
	MessageID      string `json:"message_id"`      // Service-level ID for the entire assistant's response stream
	ConversationID string `json:"conversation_id"` // ID of the conversation
}

// SSEContentDeltaData is the data for the 'content_delta' SSE event.
// It wraps the DomainStreamChunk which contains the actual delta, domain-level message_id, and markers.
type SSEContentDeltaData struct {
	Chunk agent.DomainStreamChunk `json:"chunk"` // The chunk from the domain layer
}

// SSEMessageCompleteData is the data for the 'message_complete' SSE event.
type SSEMessageCompleteData struct {
	MessageID string `json:"message_id"` // Service-level ID for the entire assistant's response stream
}

// SSEErrorData is the data for the 'error' SSE event.
type SSEErrorData struct {
	Message   string `json:"message"`
	Code      string `json:"code,omitempty"`       // Optional error code
	MessageID string `json:"message_id,omitempty"` // Optional: associates error with a specific service-level message stream
}

// --- End of New SSE Event Data Structures ---

// HTTP Handlers

// GetProjectConversation retrieves or creates the conversation for a project
func (s *Service) GetProjectConversation(c *gin.Context) {
	projectID := c.Param("projectID")

	if err := s.validateProjectAccess(c, projectID); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	userID := s.getUserIDFromContext(c)
	conversation, err := s.agentDomain.GetOrCreateProjectConversation(c, projectID, userID)
	if err != nil {
		s.logger.Error("Failed to get or create project conversation",
			zap.String("projectId", projectID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		return
	}

	// Check if conversation is nil
	if conversation == nil {
		s.logger.Error("Conversation is nil after GetOrCreateProjectConversation",
			zap.String("projectID", projectID),
			zap.String("userID", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建对话失败"})
		return
	}

	c.JSON(http.StatusOK, GetConversationResponse{
		ConversationID: conversation.ID.Hex(),
		ProjectID:      projectID,
		Messages:       conversation.Messages,
		CreatedAt:      conversation.CreatedAt,
		UpdatedAt:      conversation.LastUpdatedAt,
		MessageCount:   len(conversation.Messages),
	})
}

// SendMessageToProject sends a message to the project conversation
func (s *Service) SendMessageToProject(c *gin.Context) {
	projectID := c.Param("projectID")

	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := s.validateProjectAccess(c, projectID); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: " + err.Error()})
		return
	}

	userID := s.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	conversation, err := s.agentDomain.GetOrCreateProjectConversation(c.Request.Context(), projectID, userID)
	if err != nil {
		s.logger.Errorw("Failed to get or create project conversation in SendMessageToProject",
			zap.String("projectID", projectID), zap.String("userID", userID), zap.Error(err))
		// Unlike the old stream endpoint, we can't easily send an SSE error here if the conversation fails before streaming starts.
		// The HTTP connection for SSE hasn't been fully established in that mode yet.
		// So, we return a normal HTTP error.
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize conversation: " + err.Error()})
		return
	}
	if conversation == nil { // Should ideally be caught by the error above, but as a safeguard.
		s.logger.Errorw("Conversation is nil after GetOrCreateProjectConversation in SendMessageToProject",
			zap.String("projectID", projectID), zap.String("userID", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve conversation object"})
		return
	}

	// ---> START: Dynamic System Prompt Generation <---
	systemPrompt, err := s.generateDynamicSystemPrompt(c.Request.Context(), projectID)
	if err != nil {
		s.logger.Errorw("Failed to generate dynamic system prompt",
			zap.String("projectID", projectID),
			zap.Error(err))
		_ = s.writeSseEventInternal(c, "error", SSEErrorData{
			Message: "Failed to prepare agent environment: " + err.Error(),
			Code:    "SYSTEM_PROMPT_GENERATION_FAILED",
		})
		return
	}
	// ---> END: Dynamic System Prompt Generation <---

	var userMessage agentModel.Message
	if req.RetryMessageID != "" {
		var found bool
		for i := len(conversation.Messages) - 1; i >= 0; i-- {
			if conversation.Messages[i].MsgID == req.RetryMessageID {
				userMessage = conversation.Messages[i]
				found = true
				break
			}
		}
		if !found {
			c.JSON(http.StatusNotFound, gin.H{"error": "message to retry not found"})
			return
		}
	} else {
		userMessage = agentModel.Message{
			MsgID:     agent.GenerateMsgID(),
			MsgType:   agentModel.MsgTypeUserInput,
			Role:      agentModel.RoleUser,
			Content:   req.Message,
			Timestamp: time.Now(),
		}
	}

	// Set headers for SSE
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	// c.Writer.Header().Set("Access-Control-Allow-Origin", "*") // Adjust as needed for CORS

	var firstMessageIDProcessed string
	firstChunkProcessed := false

	s.logger.Infow("Preparing SSE stream for new message",
		zap.String("projectID", projectID),
		zap.String("userID", userID),
		zap.String("userMessageID", userMessage.MsgID),
		zap.String("conversationID", conversation.ID.Hex()))

	// Call the domain layer to handle the streaming interaction.
	domainStreamChan, err := s.agentDomain.HandleStreamingInteraction(
		c.Request.Context(),
		conversation,
		userMessage,
		systemPrompt,
		req.RetryMessageID != "", // Pass isRetry flag
	)
	if err != nil {
		s.logger.Errorw("Failed to start streaming interaction with domain layer",
			zap.String("projectID", projectID),
			zap.Error(err))
		// Try to send an error event over SSE if headers haven't been fully committed by a successful writeSseEventInternal yet.
		// However, if GetOrCreateProjectConversation failed earlier, we'd have sent an HTTP error.
		// This path is for errors from HandleStreamingInteraction itself.
		_ = s.writeSseEventInternal(c, "error", SSEErrorData{
			Message: "Failed to start AI interaction: " + err.Error(),
			Code:    "DOMAIN_INTERACTION_FAILED",
			// MessageID might not be known yet if no chunk was processed.
		})
		return
	}

	// Process the stream from the domain layer
	for chunk := range domainStreamChan {
		if !firstChunkProcessed {
			firstMessageIDProcessed = chunk.MessageID // Capture the first MessageID from domain
			firstChunkProcessed = true

			s.logger.Infow("First domain chunk processed, sending message_start",
				zap.String("projectID", projectID),
				zap.String("firstMessageID", firstMessageIDProcessed),
				zap.String("conversationID", conversation.ID.Hex()))

			// Send message_start event using the MessageID from the first domain chunk
			sseMessageStart := SSEMessageStartData{
				MessageID:      firstMessageIDProcessed,
				ConversationID: conversation.ID.Hex(),
			}
			if err := s.writeSseEventInternal(c, "message_start", sseMessageStart); err != nil {
				s.logger.Errorw("Failed to write message_start SSE event", zap.Error(err), zap.String("projectID", projectID))
				return // Stop if we can't even send message_start
			}
		}

		// Wrap the domain chunk in SSEContentDeltaData
		sseContentDelta := SSEContentDeltaData{
			Chunk: chunk, // Chunk now directly contains its own MessageID, Marker, etc.
		}
		if err := s.writeSseEventInternal(c, "content_delta", sseContentDelta); err != nil {
			s.logger.Errorw("Failed to write content_delta SSE event", zap.Error(err), zap.String("projectID", projectID))
			return // Connection might be closed by client
		}

		if chunk.Error != nil {
			if errVal, ok := (*chunk.Error).(error); ok {
				s.logger.Warnw("Error received in domain chunk, sending SSE error event",
					zap.String("projectID", projectID),
					zap.String("domainMessageID", chunk.MessageID),
					zap.Error(errVal))
				_ = s.writeSseEventInternal(c, "error", SSEErrorData{
					Message:   "AI processing error: " + errVal.Error(),
					Code:      "DOMAIN_CHUNK_ERROR",
					MessageID: firstMessageIDProcessed, // Relate to the overall message stream using the first ID
				})
			}
		}
	}

	// Send message_complete event
	if firstChunkProcessed { // Only send message_complete if message_start was sent
		sseMessageComplete := SSEMessageCompleteData{
			MessageID: firstMessageIDProcessed,
		}
		if err := s.writeSseEventInternal(c, "message_complete", sseMessageComplete); err != nil {
			s.logger.Errorw("Failed to write message_complete SSE event", zap.Error(err), zap.String("projectID", projectID))
		}
	} else {
		// This case means domainStreamChan was empty or closed immediately.
		// We might not have sent message_start. Consider if an error or a specific completion event is needed.
		s.logger.Warnw("Domain stream was empty, no message_start was sent. Consider if a specific SSE event is needed here.",
			zap.String("projectID", projectID),
			zap.String("conversationID", conversation.ID.Hex()))
		// Optionally send a generic error or a specific 'empty_response' event if useful for client.
		// For now, we just log and the connection will close.
	}

	s.logger.Infow("SSE stream completed for service message",
		zap.String("projectID", projectID),
		zap.String("firstMessageIDOfStream", firstMessageIDProcessed))
}

// ClearProjectConversation clears all messages from the project conversation
func (s *Service) ClearProjectConversation(c *gin.Context) {
	projectID := c.Param("projectID")

	if err := s.validateProjectAccess(c, projectID); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	err := s.agentDomain.ClearProjectConversation(c, projectID)
	if err != nil {
		s.logger.Error("Failed to clear project conversation",
			zap.String("projectId", projectID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清空对话失败"})
		return
	}

	c.JSON(http.StatusOK, ClearConversationResponse{
		Success: true,
		Message: "对话已清空",
	})
}

// ExportProjectConversation exports the project conversation in specified format
func (s *Service) ExportProjectConversation(c *gin.Context) {
	projectID := c.Param("projectID")
	format := c.DefaultQuery("format", "markdown")

	if err := s.validateProjectAccess(c, projectID); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	content, err := s.agentDomain.ExportProjectConversation(c, projectID, format)
	if err != nil {
		s.logger.Error("Failed to export project conversation",
			zap.String("projectId", projectID),
			zap.String("format", format),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "导出对话失败"})
		return
	}

	filename := fmt.Sprintf("conversation_%s_%d.%s", projectID, time.Now().Unix(), format)

	// Set appropriate content type
	var contentType string
	switch format {
	case "json":
		contentType = "application/json"
	case "markdown":
		contentType = "text/markdown"
	case "txt":
		contentType = "text/plain"
	default:
		contentType = "text/plain"
	}

	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Data(http.StatusOK, contentType, []byte(content))
}

// Helper methods

func (s *Service) validateProjectAccess(c *gin.Context, projectID string) error {
	s.logger.Info("validateProjectAccess called",
		zap.String("projectID", projectID),
		zap.Int("projectIDLen", len(projectID)),
		zap.Bool("projectDomainIsNil", s.projectDomain == nil))

	if s.projectDomain == nil {
		s.logger.Error("Project domain is nil in validateProjectAccess")
		return fmt.Errorf("project domain is not initialized")
	}

	userIDStr := s.getUserIDFromContext(c)
	if userIDStr == "" {
		s.logger.Error("User not authenticated in validateProjectAccess")
		return fmt.Errorf("user not authenticated")
	}
	s.logger.Info("User authenticated", zap.String("userID", userIDStr))

	// Convert userID string to ObjectID
	userID, err := primitive.ObjectIDFromHex(userIDStr)
	if err != nil {
		s.logger.Error("Failed to parse userID", zap.String("userIDStr", userIDStr), zap.Error(err))
		return fmt.Errorf("invalid user ID format")
	}

	// Check if user has access to this project using the domain's authorization method
	s.logger.Info("Checking user authorization for project",
		zap.String("projectID", projectID),
		zap.String("userID", userIDStr))

	authorized, err := s.projectDomain.CheckUserAuthorization(c.Request.Context(), userID, projectID)
	if err != nil {
		s.logger.Error("Failed to check project authorization",
			zap.String("projectID", projectID),
			zap.String("userID", userIDStr),
			zap.Error(err))
		return fmt.Errorf("failed to verify project access: %w", err)
	}

	if !authorized {
		s.logger.Warn("User not authorized to access project",
			zap.String("projectID", projectID),
			zap.String("userID", userIDStr))
		return fmt.Errorf("access denied: you don't have permission to access this project")
	}

	s.logger.Info("User authorized for project access",
		zap.String("projectID", projectID),
		zap.String("userID", userIDStr))

	return nil
}

func (s *Service) getUserIDFromContext(c *gin.Context) string {
	// TODO: Extract user ID from JWT token or session
	// This is a placeholder implementation
	if userID, exists := c.Get(middleware.UserIDKey); exists {
		if uid, ok := userID.(string); ok {
			return uid
		}
	}

	// Fallback: try to get from header (for testing)
	if userID := c.GetHeader("X-User-ID"); userID != "" {
		return userID
	}

	// Default user ID for testing
	return "default_user_id"
}

// generateDynamicSystemPrompt creates a system prompt tailored to the project's runtime environment.
func (s *Service) generateDynamicSystemPrompt(ctx context.Context, projectID string) (string, error) {
	s.logger.Infow("Generating dynamic system prompt", zap.String("projectID", projectID))

	// 1. Get the runtime for the project.
	// We don't need the full project object, just the runtime.
	// The runtime domain should be able to find the runtime by projectID.
	run, err := s.runtimeDomain.GetByProjectID(ctx, projectID)
	if err != nil {
		return "", fmt.Errorf("could not get runtime for project %s: %w", projectID, err)
	}

	// 2. Get the environment info from the runtime.
	envInfo, err := s.runtimeDomain.GetEnvironmentInfo(ctx, run.ID)
	if err != nil {
		return "", fmt.Errorf("could not get environment info for runtime %s: %w", run.ID, err)
	}

	// 3. Get available MCP tools
	mcpServers := s.mcpHub.ListMCPServers()

	// 4. Prepare data for the prompt template
	promptData := prompts.PromptDynamicData{
		// 项目文件操作使用相对路径（对 MCP 工具隐藏容器路径）
		ProjectRoot: ".",

		// 命令执行使用容器内实际路径
		RuntimeWorkingDir: envInfo.WorkDir, // 这是 /workspace

		// 系统环境信息
		SystemInfoOS:               envInfo.OS,
		SystemInfoShell:            envInfo.Shell,
		SystemInfoHome:             envInfo.HomeDir,
		UserCustomInstructionsText: "请使用中文回答用户的问题。",

		// 项目详细信息
		Project: prompts.ProjectInfo{
			ID:        run.ProjectID,
			CreatedAt: run.CreatedAt,
		},

		// 运行时详细信息
		Runtime: prompts.RuntimeInfo{
			//ContainerID:   run.ContainerID,
			Status:    run.Status,
			ImageName: run.ImageName,
			//HostPath:      run.HostPath,
			ContainerPath: envInfo.WorkDir, // /workspace
			CreatedAt:     run.CreatedAt,
			LastUsedAt:    run.LastUsedAt,
		},

		// MCP 服务器
		MCPServers: prompts.MCPInfo{
			Servers: mcpServers,
		},
	}

	// 5. Generate the system prompt string.
	systemPrompt, err := prompts.GenerateSystemPrompt(promptData)
	if err != nil {
		s.logger.Error("Failed to generate system prompt", zap.String("projectID", projectID), zap.Error(err))
		return "", fmt.Errorf("could not generate system prompt: %w", err)
	}

	s.logger.Info("Generated system prompt for request", zap.String("projectID", projectID), zap.String("prompt", systemPrompt))

	return systemPrompt, nil
}

// This is a new internal helper for writing SSE events within the Service methods
func (s *Service) writeSseEventInternal(c *gin.Context, eventType string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		s.logger.Errorw("Failed to marshal SSE data for internal write",
			zap.String("eventType", eventType),
			zap.Error(err))
		return fmt.Errorf("failed to marshal SSE data for event %s: %w", eventType, err)
	}

	if _, err := c.Writer.WriteString(fmt.Sprintf("event: %s\n", eventType)); err != nil {
		s.logger.Errorw("Failed to write SSE event type to writer", zap.Error(err))
		return err
	}
	if _, err := c.Writer.WriteString(fmt.Sprintf("data: %s\n\n", string(jsonData))); err != nil {
		s.logger.Errorw("Failed to write SSE event data to writer", zap.Error(err))
		return err
	}

	if flusher, ok := c.Writer.(http.Flusher); ok {
		flusher.Flush()
	} else {
		s.logger.Warn("http.Flusher not available on gin.Context.Writer, SSE events might be buffered.")
		// For non-streaming writers (like in tests), Flush might not be available.
		// In a real HTTP scenario, gin's writer should support http.Flusher.
	}
	return nil
}

// --- End of Helper methods ---
