package user

// PasswordHasher defines the interface for hashing and verifying passwords.
// This allows the domain logic to be independent of the specific hashing algorithm implementation.
type PasswordHasher interface {
	// Hash generates a secure hash for the given plain text password.
	Hash(password string) (string, error)

	// Check compares a plain text password against a stored hash.
	// It returns true if the password matches the hash, false otherwise.
	Check(password, hash string) bool
}
