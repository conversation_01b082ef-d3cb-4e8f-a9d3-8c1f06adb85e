package user

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	model "git.nevint.com/fota3/t-rex/model/user"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo" // For checking specific DB errors
	"go.uber.org/zap"
)

// Domain specific errors
// ... (Errors remain the same)
var (
	ErrUserNotFound    = errors.New("user not found")
	ErrUsernameTaken   = errors.New("username is already taken")
	ErrAuthentication  = errors.New("invalid username or password")
	ErrHashingFailed   = errors.New("failed to hash password")
	ErrInactiveAccount = errors.New("user account is inactive")
	ErrRepository      = errors.New("user repository error") // Generic repo error
)

// Domain is the single entry point for user domain logic.
type Domain struct { // Renamed from DomainService
	repo   UserRepository
	hasher PasswordHasher
	logger *zap.Logger
}

// New creates a new user domain entry point.
func New(repo UserRepository, hasher PasswordHasher, logger *zap.Logger) (*Domain, error) { // Renamed from NewDomainService
	if repo == nil {
		return nil, errors.New("user repository is required")
	}
	if hasher == nil {
		return nil, errors.New("password hasher is required")
	}
	if logger == nil {
		return nil, errors.New("logger is required")
	}
	return &Domain{repo: repo, hasher: hasher, logger: logger.Named("user_domain")}, nil
}

// RegisterUser handles the core logic for registering a new user.
func (s *Domain) RegisterUser(ctx context.Context, username, password, email, fullName string) (*model.User, error) { // Receiver changed to *Domain
	s.logger.Info("Starting user registration process",
		zap.String("username", username),
		zap.String("email", email),
		zap.String("full_name", fullName))

	// 1. Validate input (basic validation here, more complex rules could be added)
	if strings.TrimSpace(username) == "" || len(username) < 3 {
		s.logger.Warn("Registration validation failed: username too short",
			zap.String("username", username),
			zap.Int("username_length", len(username)))
		return nil, errors.New("username must be at least 3 characters long")
	}
	if len(password) < 8 {
		s.logger.Warn("Registration validation failed: password too short",
			zap.String("username", username),
			zap.Int("password_length", len(password)))
		return nil, errors.New("password must be at least 8 characters long")
	}

	s.logger.Debug("Input validation passed",
		zap.String("username", username))

	// 2. Check if username already exists
	_, err := s.repo.FindByUsername(ctx, username)
	if err == nil {
		// User found, username already exists
		s.logger.Warn("Registration failed: username already exists",
			zap.String("username", username))
		return nil, ErrUsernameTaken
	} else {
		// Check if the error is NOT ErrUserNotFound. Any other error is unexpected.
		if !errors.Is(err, ErrUserNotFound) {
			// Log the original error for debugging
			s.logger.Error("Error checking username availability during registration",
				zap.String("username", username),
				zap.Error(err))
			return nil, fmt.Errorf("%w: checking username availability", ErrRepository)
		}
		// If error IS ErrUserNotFound, we can proceed.
		s.logger.Debug("Username availability confirmed",
			zap.String("username", username))
	}

	// 3. Hash the password
	s.logger.Debug("Hashing password",
		zap.String("username", username))
	hashedPassword, err := s.hasher.Hash(password)
	if err != nil {
		// Log the original error
		s.logger.Error("Error hashing password during registration",
			zap.String("username", username),
			zap.Error(err))
		return nil, ErrHashingFailed
	}

	// 4. Determine if this user should be an admin (first registered user is admin)
	userCount, err := s.repo.CountUsers(ctx)
	if err != nil {
		s.logger.Error("Failed to count users during registration", zap.Error(err))
		return nil, fmt.Errorf("%w: failed to count users", ErrRepository)
	}
	isAdmin := userCount == 0
	if isAdmin {
		s.logger.Info("First user registering, setting as admin", zap.String("username", username))
	}

	// 5. Create the user model
	newUser := &model.User{
		Username:      username,
		PasswordHash:  hashedPassword,
		Email:         email,
		FullName:      fullName,
		IsActive:      true,    // Activate user immediately by default
		IsAdmin:       isAdmin, // Set admin status based on user count
		CreatedAt:     time.Now(),
		LastUpdatedAt: time.Now(),
	}

	s.logger.Debug("User model created, saving to repository",
		zap.String("username", username))

	// 6. Save the user to the database
	err = s.repo.Create(ctx, newUser)
	if err != nil {
		// Check if the specific error from the repo is ErrUsernameTaken
		if errors.Is(err, ErrUsernameTaken) {
			s.logger.Warn("Registration failed: username taken (from repository)",
				zap.String("username", username))
			return nil, ErrUsernameTaken
		}
		// Handle other repository errors
		s.logger.Error("Error creating user in repository",
			zap.String("username", username),
			zap.Error(err))
		return nil, fmt.Errorf("%w: creating user", ErrRepository)
	}

	s.logger.Info("User registration completed successfully",
		zap.String("user_id", newUser.ID.Hex()),
		zap.String("username", username))

	// Return user without hash
	newUser.PasswordHash = ""
	return newUser, nil
}

// AuthenticateUser handles the core logic for user login.
func (s *Domain) AuthenticateUser(ctx context.Context, username, password string) (*model.User, error) { // Receiver changed to *Domain
	s.logger.Info("Starting user authentication process",
		zap.String("username", username))

	// 1. Find the user by username
	s.logger.Debug("Looking up user by username",
		zap.String("username", username))
	user, err := s.repo.FindByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, ErrUserNotFound) {
			s.logger.Warn("Authentication failed: user not found",
				zap.String("username", username))
			return nil, ErrAuthentication // Return generic auth error for not found
		}
		// Log unexpected repository errors
		s.logger.Error("Error finding user by username during login",
			zap.String("username", username),
			zap.Error(err))
		return nil, fmt.Errorf("%w: finding user by username", ErrRepository)
	}

	s.logger.Debug("User found, checking account status",
		zap.String("username", username),
		zap.String("user_id", user.ID.Hex()),
		zap.Bool("is_active", user.IsActive))

	// 2. Check if the user account is active
	if !user.IsActive {
		s.logger.Warn("Authentication failed: inactive account",
			zap.String("username", username),
			zap.String("user_id", user.ID.Hex()))
		return nil, ErrInactiveAccount
	}

	// 3. Compare the provided password with the stored hash
	s.logger.Debug("Verifying password",
		zap.String("username", username))
	if !s.hasher.Check(password, user.PasswordHash) {
		s.logger.Warn("Authentication failed: invalid password",
			zap.String("username", username),
			zap.String("user_id", user.ID.Hex()))
		return nil, ErrAuthentication // Generic auth error for wrong password
	}

	s.logger.Info("User authentication successful",
		zap.String("username", username),
		zap.String("user_id", user.ID.Hex()))

	// 4. Authentication successful - Update LastLoginAt (optional)
	now := time.Now()
	user.LastLoginAt = &now
	err = s.repo.Update(ctx, user)
	if err != nil {
		// Log the error but don't fail authentication because of it
		s.logger.Warn("Failed to update last login time",
			zap.String("username", username),
			zap.String("user_id", user.ID.Hex()),
			zap.Error(err))
	}

	// 5. Return user details (without hash)
	user.PasswordHash = ""
	return user, nil
}

// GetUserByID retrieves a user by their ID.
func (s *Domain) GetUserByID(ctx context.Context, userID primitive.ObjectID) (*model.User, error) {
	// Convert ObjectID back to string for the repository method
	userIDStr := userID.Hex()

	user, err := s.repo.FindByID(ctx, userIDStr)
	if err != nil {
		// The repository implementation should ideally return domain-specific errors.
		// If it returns ErrUserNotFound or wraps mongo.ErrNoDocuments, this check is good.
		// If FindByID in store.go returns a different error string for not found,
		// we might need to adjust error checking here or standardize errors in the repo.
		if errors.Is(err, ErrUserNotFound) || errors.Is(err, mongo.ErrNoDocuments) || strings.Contains(err.Error(), "not found by ID") { // Added check for store's error string
			return nil, ErrUserNotFound // Return the consistent domain error
		}
		// Log unexpected repository errors
		fmt.Printf("Error finding user by ID in domain: %v\n", err)
		return nil, fmt.Errorf("%w: finding user by ID", ErrRepository)
	}

	// Return user details (without hash)
	user.PasswordHash = ""
	return user, nil
}

// TODO: Add UpdateProfile, ChangePassword domain logic methods
