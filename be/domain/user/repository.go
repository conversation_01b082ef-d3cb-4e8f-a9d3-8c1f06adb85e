package user

import (
	"context"

	model "git.nevint.com/fota3/t-rex/model/user"
)

// UserRepository defines the interface for data persistence operations related to users.
// Implementations of this interface will handle the actual database interactions.
type UserRepository interface {
	// Create inserts a new user record into the data store.
	// It should return an error if the username or email already exists (depending on constraints).
	Create(ctx context.Context, user *model.User) error

	// FindByID retrieves a user by their unique ID.
	// It should return a specific error (e.g., ErrUserNotFound) if the user doesn't exist.
	FindByID(ctx context.Context, id string) (*model.User, error)

	// FindByUsername retrieves a user by their username.
	// It should return a specific error (e.g., ErrUserNotFound) if the user doesn't exist.
	FindByUsername(ctx context.Context, username string) (*model.User, error)

	// Update updates an existing user record.
	// It should likely take the user ID and the updated user object or specific fields to update.
	Update(ctx context.Context, user *model.User) error

	// CountUsers returns the total number of users in the data store.
	CountUsers(ctx context.Context) (int64, error)

	// Delete removes a user record by ID (consider soft delete vs hard delete).
	// Delete(ctx context.Context, id string) error

	// List retrieves a list of users, potentially with pagination and filtering.
	// List(ctx context.Context, options ListOptions) ([]*model.User, int64, error) // Returns users and total count
}

/*
// Example ListOptions struct (if List method is added)
type ListOptions struct {
	Page     int
	PageSize int
	Filter   map[string]interface{} // Simple filter example
	SortBy   string
	SortDesc bool
}
*/
