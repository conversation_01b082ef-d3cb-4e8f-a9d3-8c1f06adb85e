package runtime

import (
	"context"

	"github.com/docker/docker/api/types"
)

// EnvironmentInfo holds key information about the runtime environment inside a container.
type EnvironmentInfo struct {
	OS      string // e.g., "linux"
	Distro  string // e.g., "Ubuntu 22.04.3 LTS"
	Shell   string // e.g., "/bin/bash"
	WorkDir string // e.g., "/app"
	HomeDir string // e.g., "/root"
}

// CommandResult represents the result of executing a command in a container
type CommandResult struct {
	Stdout   string `json:"stdout"`
	Stderr   string `json:"stderr"`
	ExitCode int    `json:"exit_code"`
	Command  string `json:"command"`
}

// Provider defines the interface for interacting with a runtime environment (e.g., Docker).
// English: Provider defines the interface for interacting with a runtime environment (e.g., Docker).
type Provider interface {
	// EnsureImage ensures that the specified Docker image is available locally, pulling it if necessary.
	// English: EnsureImage ensures that the specified Docker image is available locally, pulling it if necessary.
	EnsureImage(ctx context.Context, imageName string) error

	// StartContainer creates and starts a new container for the given project.
	// It mounts the projectHostPath to a specific working directory within the container (e.g., /workspace).
	// Returns the container ID on success.
	// English: StartContainer creates and starts a new container for the given project.
	// It mounts the projectHostPath to a specific working directory within the container (e.g., /workspace).
	// Returns the container ID on success.
	StartContainer(ctx context.Context, projectID string, projectHostPath string, imageName string, containerName string) (containerID string, err error)

	// StopContainer stops and removes the specified container.
	// English: StopContainer stops and removes the specified container.
	StopContainer(ctx context.Context, containerID string) error

	// GetContainerTerminalStream creates an exec instance in the specified container (e.g., /bin/bash)
	// and returns a HijackedResponse for interactive terminal communication and the execID.
	// The workDir specifies the working directory for the command within the container.
	// English: GetContainerTerminalStream creates an exec instance in the specified container (e.g., /bin/bash)
	// and returns a HijackedResponse for interactive terminal communication and the execID.
	// The workDir specifies the working directory for the command within the container.
	GetContainerTerminalStream(ctx context.Context, containerID string, cmd []string, workDir string) (types.HijackedResponse, string, error)

	// ResizeContainerTerminal resizes the PTY of an exec instance in a container.
	// English: ResizeContainerTerminal resizes the PTY of an exec instance in a container.
	ResizeContainerTerminal(ctx context.Context, execID string, rows uint, cols uint) error

	// GetContainerStatus retrieves the status of the specified container.
	// English: GetContainerStatus retrieves the status of the specified container.
	GetContainerStatus(ctx context.Context, containerID string) (string, error)

	// GetEnvironmentInfo executes commands within the container to determine its environment details.
	GetEnvironmentInfo(ctx context.Context, containerID string) (*EnvironmentInfo, error)

	// ExecuteCommand executes a command in the container and returns the result
	ExecuteCommand(ctx context.Context, containerID string, command string, workDir string) (*CommandResult, error)
}
