package runtime

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/runtime"
)

var (
	// ErrRuntimeNotFound indicates that a runtime record was not found.
	ErrRuntimeNotFound = errors.New("runtime not found")
	// ErrProjectFS indicates an error interacting with the project file system service.
	ErrProjectFS = errors.New("project file system service error")
	// ErrProjectOperation indicates an error with project operations (e.g., not found, not authorized).
	ErrProjectOperation = errors.New("project operation error")
)

// Domain provides the core business logic for managing project runtimes.
type Domain struct {
	repo             Repository
	provider         Provider
	logger           *zap.Logger
	defaultImageName string // Default image name from config
	providerType     string // Provider type: "docker" or "kubernetes"
}

// New creates a new runtime Domain instance.
func New(
	r Repository,
	p Provider,
	l *zap.Logger,
	defaultImageName string,
) (*Domain, error) { // Added error return for New constructor
	if r == nil {
		return nil, errors.New("runtime repository is required")
	}
	if p == nil {
		return nil, errors.New("runtime provider is required")
	}
	if l == nil {
		return nil, errors.New("logger is required")
	}
	if defaultImageName == "" {
		return nil, errors.New("default image name is required")
	}

	// Detect provider type based on the actual implementation
	providerType := "docker" // default fallback
	switch p.(type) {
	case interface {
		GetExposedURLs(context.Context, string) (map[int32]string, error)
	}:
		providerType = "kubernetes"
	default:
		providerType = "docker"
	}

	return &Domain{
		repo:             r,
		provider:         p,
		logger:           l.Named("runtime_domain"),
		defaultImageName: defaultImageName,
		providerType:     providerType,
	}, nil
}

// Create creates a new runtime environment for a given project.
// It's called by the project domain when a new project is created.
func (d *Domain) Create(ctx context.Context, projectID, projectHostPath string) (*runtime.Runtime, error) {
	d.logger.Info("Creating new runtime for project", zap.String("projectID", projectID))

	if err := d.provider.EnsureImage(ctx, d.defaultImageName); err != nil {
		d.logger.Error("Failed to ensure Docker image", zap.String("imageName", d.defaultImageName), zap.Error(err))
		return nil, fmt.Errorf("failed to ensure image %s: %w", d.defaultImageName, err)
	}

	containerName := fmt.Sprintf("trex-runtime-%s-%s", strings.ReplaceAll(projectID, "_", "-"), uuid.New().String()[:8])

	runtimeID, startErr := d.provider.StartContainer(ctx, projectID, projectHostPath, d.defaultImageName, containerName)
	if startErr != nil {
		d.logger.Error("Failed to start runtime for new project", zap.String("projectID", projectID), zap.Error(startErr))
		return nil, fmt.Errorf("failed to start runtime: %w", startErr)
	}

	// Create runtime record with provider-specific fields
	newRuntime := &runtime.Runtime{
		ProjectID:    projectID,
		ProviderType: d.providerType,
		ImageName:    d.defaultImageName,
		Status:       "running",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		LastUsedAt:   time.Now(),
	}

	// Set provider-specific fields
	switch d.providerType {
	case "docker":
		newRuntime.ContainerID = runtimeID
		newRuntime.HostPath = projectHostPath
	case "kubernetes":
		newRuntime.PodName = runtimeID
		newRuntime.Namespace = "t-rex-workspaces" // 修正为实际使用的namespace
		newRuntime.ServiceName = fmt.Sprintf("service-for-%s", projectID)
		// Note: No longer using PVC since we switched to hostPath volumes for direct file access
		// Get exposed URLs if K8s provider supports it
		if k8sProvider, ok := d.provider.(interface {
			GetExposedURLs(context.Context, string) (map[int32]string, error)
		}); ok {
			if urls, err := k8sProvider.GetExposedURLs(ctx, projectID); err == nil {
				for port, url := range urls {
					newRuntime.ExposedPorts = append(newRuntime.ExposedPorts, runtime.ExposedPort{
						Name:        fmt.Sprintf("port-%d", port),
						Port:        port,
						TargetPort:  port,
						Protocol:    "TCP",
						ExternalURL: url,
					})
				}
			}
		}
	}

	if err := d.repo.Create(ctx, newRuntime); err != nil {
		d.logger.Error("Failed to create runtime record in DB, attempting to clean up runtime",
			zap.String("projectID", projectID),
			zap.String("runtimeID", runtimeID),
			zap.Error(err),
		)
		// Best-effort cleanup
		_ = d.provider.StopContainer(ctx, runtimeID)
		return nil, fmt.Errorf("failed to save runtime record to DB: %w", err)
	}

	d.logger.Info("Successfully created runtime and saved record",
		zap.String("projectID", projectID),
		zap.String("runtimeID", newRuntime.ID),
		zap.String("providerType", d.providerType),
		zap.String("runtimeIdentifier", runtimeID),
	)
	return newRuntime, nil
}

// GetOrCreateTerminalStream retrieves an existing terminal stream for a project or creates a new one if none exists.
func (d *Domain) GetOrCreateTerminalStream(ctx context.Context, projectIDStr string, initialRows uint, initialCols uint) (rt *runtime.Runtime, stream types.HijackedResponse, execID string, err error) {
	d.logger.Info("GetOrCreateTerminalStream called",
		zap.String("projectID", projectIDStr),
		zap.Uint("initialRows", initialRows),
		zap.Uint("initialCols", initialCols),
	)

	// 1. Find runtime record. This is now the source of truth.
	rt, err = d.repo.FindByProjectID(ctx, projectIDStr)
	if err != nil {
		d.logger.Error("Failed to find runtime for project", zap.String("projectID", projectIDStr), zap.Error(err))
		if errors.Is(err, ErrRuntimeNotFound) {
			return nil, types.HijackedResponse{}, "", fmt.Errorf("runtime for project %s has not been created; this should not happen if project was created correctly", projectIDStr)
		}
		return nil, types.HijackedResponse{}, "", fmt.Errorf("database error finding runtime: %w", err)
	}

	// 2. Check runtime status
	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier == "" {
		return nil, types.HijackedResponse{}, "", fmt.Errorf("runtime record for project %s has no runtime identifier", projectIDStr)
	}

	status, statusErr := d.provider.GetContainerStatus(ctx, runtimeIdentifier)
	if statusErr != nil {
		// If we can't get status, we can't proceed.
		d.logger.Error("Failed to get runtime status, cannot create terminal",
			zap.String("runtimeID", runtimeIdentifier),
			zap.Error(statusErr),
		)
		return nil, types.HijackedResponse{}, "", fmt.Errorf("could not get runtime status for %s: %w", runtimeIdentifier, statusErr)
	}

	if status != "running" {
		// TODO: Implement logic to restart a stopped/exited runtime.
		// For now, we fail if it's not running.
		d.logger.Error("Runtime is not running", zap.String("runtimeID", runtimeIdentifier), zap.String("status", status))
		return nil, types.HijackedResponse{}, "", fmt.Errorf("runtime for project %s is not running (status: %s)", projectIDStr, status)
	}

	// 3. Create a new terminal stream in the running runtime
	stream, execID, attachErr := d.provider.GetContainerTerminalStream(ctx, runtimeIdentifier, nil, "")
	if attachErr != nil {
		d.logger.Error("Failed to attach to running container", zap.String("containerID", runtimeIdentifier), zap.Error(attachErr))
		return nil, types.HijackedResponse{}, "", fmt.Errorf("failed to attach to container %s: %w", runtimeIdentifier, attachErr)
	}

	d.logger.Info("Successfully attached to container's new exec instance",
		zap.String("containerID", runtimeIdentifier), zap.String("execID", execID))

	// 4. Set initial terminal size
	if initialRows > 0 && initialCols > 0 {
		if resizeErr := d.ResizeRuntimeTerminal(ctx, execID, initialRows, initialCols); resizeErr != nil {
			d.logger.Warn("Failed to set initial PTY size for new exec", zap.String("execID", execID), zap.Error(resizeErr))
			// Non-fatal, proceed anyway.
		}
	}

	// 5. Update runtime usage stats
	rt.LastUsedAt = time.Now()
	if updateErr := d.repo.Update(ctx, rt); updateErr != nil {
		d.logger.Warn("Failed to update runtime LastUsedAt", zap.Error(updateErr))
		// Non-critical, proceed.
	}

	return rt, stream, execID, nil
}

// GetByProjectID finds a runtime by its project ID.
func (d *Domain) GetByProjectID(ctx context.Context, projectID string) (*runtime.Runtime, error) {
	return d.repo.FindByProjectID(ctx, projectID)
}

// GetEnvironmentInfo gets the environment details from the runtime provider.
func (d *Domain) GetEnvironmentInfo(ctx context.Context, runtimeID string) (*EnvironmentInfo, error) {
	// For this call, we assume runtimeID is the database runtime ID.
	rt, err := d.repo.FindByID(ctx, runtimeID)
	if err != nil {
		return nil, fmt.Errorf("failed to find runtime by id %s: %w", runtimeID, err)
	}

	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier == "" {
		return nil, fmt.Errorf("runtime %s has no runtime identifier", runtimeID)
	}
	return d.provider.GetEnvironmentInfo(ctx, runtimeIdentifier)
}

// StopRuntime stops the runtime (container/pod) associated with a project's runtime.
func (d *Domain) StopRuntime(ctx context.Context, projectIDStr string) error {
	d.logger.Info("Stopping runtime for project", zap.String("projectID", projectIDStr))
	rt, err := d.repo.FindByProjectID(ctx, projectIDStr)
	if err != nil {
		if errors.Is(err, ErrRuntimeNotFound) {
			d.logger.Warn("No runtime found for project, nothing to stop", zap.String("projectID", projectIDStr))
			return nil // Not an error if it doesn't exist
		}
		d.logger.Error("Failed to find runtime for project to stop it", zap.String("projectID", projectIDStr), zap.Error(err))
		return err
	}

	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier == "" {
		d.logger.Warn("Runtime record found but no runtime identifier, nothing to stop", zap.String("projectID", projectIDStr))
		rt.Status = "stopped"
		_ = d.repo.Update(ctx, rt)
		return nil
	}

	err = d.provider.StopContainer(ctx, runtimeIdentifier)
	if err != nil {
		d.logger.Error("Failed to stop runtime via provider", zap.String("runtimeID", runtimeIdentifier), zap.Error(err))
		// Even if provider fails, update status in DB
		rt.Status = "error_stopping"
		rt.LastKnownError = err.Error()
		updateErr := d.repo.Update(ctx, rt)
		return fmt.Errorf("provider failed to stop runtime: %w (db update err: %v)", err, updateErr)
	}

	rt.Status = "stopped"
	rt.LastKnownError = ""
	if updateErr := d.repo.Update(ctx, rt); updateErr != nil {
		d.logger.Error("Failed to update runtime status to stopped in DB", zap.String("runtimeID", rt.ID), zap.Error(updateErr))
		return updateErr // Return DB error as it's the final step
	}

	d.logger.Info("Runtime stopped successfully", zap.String("projectID", projectIDStr), zap.String("runtimeID", runtimeIdentifier))
	return nil
}

// DeleteRuntime completely removes a project's runtime record and stops its runtime.
// This is used when rebuilding a runtime to clean up the old one completely.
func (d *Domain) DeleteRuntime(ctx context.Context, projectIDStr string) error {
	d.logger.Info("Deleting runtime for project", zap.String("projectID", projectIDStr))

	rt, err := d.repo.FindByProjectID(ctx, projectIDStr)
	if err != nil {
		if errors.Is(err, ErrRuntimeNotFound) {
			d.logger.Warn("No runtime found for project, nothing to delete", zap.String("projectID", projectIDStr))
			return nil // Not an error if it doesn't exist
		}
		d.logger.Error("Failed to find runtime for project to delete it", zap.String("projectID", projectIDStr), zap.Error(err))
		return err
	}

	// Stop and remove the runtime if it exists
	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier != "" {
		d.logger.Info("Stopping and removing runtime for runtime deletion",
			zap.String("projectID", projectIDStr),
			zap.String("runtimeID", runtimeIdentifier))

		// Best effort to stop the runtime
		if stopErr := d.provider.StopContainer(ctx, runtimeIdentifier); stopErr != nil {
			d.logger.Warn("Failed to stop runtime during deletion, continuing with DB cleanup",
				zap.String("runtimeID", runtimeIdentifier),
				zap.Error(stopErr))
		}
	}

	// Delete the runtime record from database
	if deleteErr := d.repo.Delete(ctx, rt.ID); deleteErr != nil {
		d.logger.Error("Failed to delete runtime record from DB",
			zap.String("projectID", projectIDStr),
			zap.String("runtimeID", rt.ID),
			zap.Error(deleteErr))
		return fmt.Errorf("failed to delete runtime record: %w", deleteErr)
	}

	d.logger.Info("Runtime deleted successfully",
		zap.String("projectID", projectIDStr),
		zap.String("runtimeID", rt.ID),
		zap.String("runtimeIdentifier", runtimeIdentifier))
	return nil
}

// GetRuntimeStatus retrieves the current status of a project's runtime.
func (d *Domain) GetRuntimeStatus(ctx context.Context, projectIDStr string) (*runtime.Runtime, error) {
	d.logger.Debug("Getting runtime status for project", zap.String("projectID", projectIDStr))
	rt, err := d.repo.FindByProjectID(ctx, projectIDStr)
	if err != nil {
		return nil, err // Propagate ErrRuntimeNotFound or other DB errors
	}

	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier != "" {
		status, err := d.provider.GetContainerStatus(ctx, runtimeIdentifier)
		if err != nil {
			d.logger.Warn("Could not get live status from provider, returning last known DB status",
				zap.String("runtimeID", runtimeIdentifier), zap.Error(err))
			rt.Status = "unknown_provider_error"
			rt.LastKnownError = err.Error()
			// Return the DB record with an updated transient status
			return rt, nil
		}
		// Update DB status if it differs from live status
		if rt.Status != status {
			rt.Status = status
			rt.LastKnownError = ""
			if updateErr := d.repo.Update(ctx, rt); updateErr != nil {
				d.logger.Error("Failed to update runtime status in DB", zap.String("runtimeID", rt.ID), zap.Error(updateErr))
				// Non-fatal, return the live status anyway
			}
		}
	} else {
		rt.Status = "no_container"
	}

	return rt, nil
}

// GetProvider returns the runtime provider instance
func (d *Domain) GetProvider() Provider {
	return d.provider
}

// ResizeRuntimeTerminal resizes the PTY of a running terminal session.
func (d *Domain) ResizeRuntimeTerminal(ctx context.Context, execID string, rows uint, cols uint) error {
	// The execID is sufficient to resize the terminal; we don't need projectID here.
	d.logger.Debug("Resizing runtime terminal", zap.String("execID", execID), zap.Uint("rows", rows), zap.Uint("cols", cols))
	if execID == "" {
		return errors.New("execID is required to resize terminal")
	}
	return d.provider.ResizeContainerTerminal(ctx, execID, rows, cols)
}

// ExecuteCommand executes a bash command in the project's runtime (container/pod)
func (d *Domain) ExecuteCommand(ctx context.Context, projectID string, command string, workDir string, timeout time.Duration) (*CommandResult, error) {
	d.logger.Info("Executing command in project runtime",
		zap.String("projectID", projectID),
		zap.String("command", command),
		zap.String("workDir", workDir))

	// 1. Find runtime record for the project
	rt, err := d.repo.FindByProjectID(ctx, projectID)
	if err != nil {
		if errors.Is(err, ErrRuntimeNotFound) {
			return nil, fmt.Errorf("runtime for project %s not found", projectID)
		}
		return nil, fmt.Errorf("failed to find runtime: %w", err)
	}

	// 2. Check runtime status using provider-agnostic method
	runtimeIdentifier := rt.GetRuntimeID()
	if runtimeIdentifier == "" {
		return nil, fmt.Errorf("no runtime identifier found for project %s (provider: %s)", projectID, rt.ProviderType)
	}

	status, err := d.provider.GetContainerStatus(ctx, runtimeIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get runtime status: %w", err)
	}
	if status != "running" {
		return nil, fmt.Errorf("runtime is not running (status: %s)", status)
	}

	// 3. Set timeout context if specified
	if timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	// 4. Execute command through provider
	result, err := d.provider.ExecuteCommand(ctx, runtimeIdentifier, command, workDir)
	if err != nil {
		d.logger.Error("Failed to execute command",
			zap.String("projectID", projectID),
			zap.String("command", command),
			zap.String("providerType", rt.ProviderType),
			zap.String("runtimeIdentifier", runtimeIdentifier),
			zap.Error(err))
		return nil, fmt.Errorf("failed to execute command: %w", err)
	}

	// 5. Update runtime usage stats
	rt.LastUsedAt = time.Now()
	if updateErr := d.repo.Update(ctx, rt); updateErr != nil {
		d.logger.Warn("Failed to update runtime LastUsedAt", zap.Error(updateErr))
		// Non-critical, proceed with result
	}

	d.logger.Info("Command executed successfully",
		zap.String("projectID", projectID),
		zap.String("command", command),
		zap.Int("exitCode", result.ExitCode))

	return result, nil
}
