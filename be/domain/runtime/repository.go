package runtime

import (
	"context"

	"git.nevint.com/fota3/t-rex/model/runtime"
)

// Repository defines the interface for runtime data persistence.
// It handles CRUD operations for Runtime entities.
// English: Repository defines the interface for runtime data persistence.
// It handles CRUD operations for Runtime entities.
type Repository interface {
	// Create persists a new runtime record.
	// English: Create persists a new runtime record.
	Create(ctx context.Context, rt *runtime.Runtime) error

	// FindByProjectID retrieves a runtime record by its associated project ID.
	// Returns nil, nil if not found.
	// English: FindByProjectID retrieves a runtime record by its associated project ID.
	// Returns nil, nil if not found.
	FindByProjectID(ctx context.Context, projectID string) (*runtime.Runtime, error)

	// FindByID retrieves a runtime record by its unique ID.
	// Returns nil, nil if not found.
	// English: FindByID retrieves a runtime record by its unique ID.
	// Returns nil, nil if not found.
	FindByID(ctx context.Context, id string) (*runtime.Runtime, error)

	// Update modifies an existing runtime record.
	// English: Update modifies an existing runtime record.
	Update(ctx context.Context, rt *runtime.Runtime) error

	// Delete removes a runtime record by its ID.
	// English: Delete removes a runtime record by its ID.
	Delete(ctx context.Context, id string) error
}
