package aimodel

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/aimodel"
)

// Store defines the interface for AI model storage operations
type Store interface {
	Create(ctx context.Context, model *aimodel.AIModel) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*aimodel.AIModel, error)
	GetByModelName(ctx context.Context, modelName string) (*aimodel.AIModel, error)
	List(ctx context.Context, activeOnly bool) ([]*aimodel.AIModel, error)
	Update(ctx context.Context, id primitive.ObjectID, updates bson.M) error
	Delete(ctx context.Context, id primitive.ObjectID) error
}

// Domain provides business logic for AI model operations
type Domain struct {
	store  Store
	logger *zap.SugaredLogger
}

// New creates a new AI model domain instance
func New(store Store, logger *zap.Logger) (*Domain, error) {
	if store == nil {
		return nil, fmt.Errorf("store cannot be nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	return &Domain{
		store:  store,
		logger: logger.Sugar(),
	}, nil
}

// CreateAIModel creates a new AI model with validation
func (d *Domain) CreateAIModel(ctx context.Context, model *aimodel.AIModel) error {
	if model == nil {
		return fmt.Errorf("model cannot be nil")
	}

	// Validate required fields
	if model.APIKey == "" {
		return fmt.Errorf("API key is required")
	}
	if model.APIBase == "" {
		return fmt.Errorf("API base URL is required")
	}
	if model.ModelName == "" {
		return fmt.Errorf("model name is required")
	}

	// Check if model with same name already exists
	existing, err := d.store.GetByModelName(ctx, model.ModelName)
	if err == nil && existing != nil {
		return fmt.Errorf("AI model with name '%s' already exists", model.ModelName)
	}

	// Set default active status if not specified
	if !model.IsActive {
		model.IsActive = true
	}

	d.logger.Infof("Creating AI model: %s", model.ModelName)
	return d.store.Create(ctx, model)
}

// GetAIModel retrieves an AI model by ID
func (d *Domain) GetAIModel(ctx context.Context, id primitive.ObjectID) (*aimodel.AIModel, error) {
	return d.store.GetByID(ctx, id)
}

// GetAIModelByName retrieves an AI model by name
func (d *Domain) GetAIModelByName(ctx context.Context, modelName string) (*aimodel.AIModel, error) {
	return d.store.GetByModelName(ctx, modelName)
}

// ListAIModels retrieves all AI models, optionally filtering by active status
func (d *Domain) ListAIModels(ctx context.Context, activeOnly bool) ([]*aimodel.AIModel, error) {
	return d.store.List(ctx, activeOnly)
}

// UpdateAIModel updates an existing AI model
func (d *Domain) UpdateAIModel(ctx context.Context, id primitive.ObjectID, updates map[string]interface{}) error {
	// Validate that the model exists
	existing, err := d.store.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// If updating model name, check for conflicts
	if newName, ok := updates["model_name"].(string); ok && newName != existing.ModelName {
		conflicting, err := d.store.GetByModelName(ctx, newName)
		if err == nil && conflicting != nil && conflicting.ID != id {
			return fmt.Errorf("AI model with name '%s' already exists", newName)
		}
	}

	d.logger.Infof("Updating AI model: %s", existing.ModelName)
	return d.store.Update(ctx, id, bson.M(updates))
}

// DeleteAIModel deletes an AI model by ID
func (d *Domain) DeleteAIModel(ctx context.Context, id primitive.ObjectID) error {
	// Validate that the model exists
	existing, err := d.store.GetByID(ctx, id)
	if err != nil {
		return err
	}

	d.logger.Infof("Deleting AI model: %s", existing.ModelName)
	return d.store.Delete(ctx, id)
}

// ToggleAIModelStatus toggles the active status of an AI model
func (d *Domain) ToggleAIModelStatus(ctx context.Context, id primitive.ObjectID) error {
	existing, err := d.store.GetByID(ctx, id)
	if err != nil {
		return err
	}

	newStatus := !existing.IsActive
	d.logger.Infof("Toggling AI model status: %s -> %v", existing.ModelName, newStatus)

	return d.store.Update(ctx, id, bson.M{"is_active": newStatus})
}
