package llm

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/internal/llmstore" // Import the llmstore package
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"
)

// Domain encapsulates the business logic for LLM configurations.
type Domain struct {
	repo   llmstore.LLMConfigRepository // Dependency on the LLM config repository
	logger *zap.Logger
}

// NewDomain creates a new instance of the LLM domain.
func NewDomain(repo llmstore.LLMConfigRepository, logger *zap.Logger) *Domain {
	if logger == nil {
		logger = zap.NewNop()
	}
	return &Domain{
		repo:   repo,
		logger: logger.Named("LLMDomain"),
	}
}

// CreateLLMConfig creates a new LLM configuration.
func (d *Domain) CreateLLMConfig(ctx context.Context, config *modelLLM.LLMConfig) error {
	d.logger.Info("Creating LLM config", zap.String("name", config.Name), zap.String("type", config.Type))
	// Basic validation
	if config.Name == "" || config.Type == "" || config.APIKey == "" || config.APIBase == "" || config.ModelName == "" {
		return fmt.Errorf("LLM config name, type, api_key, api_base, and model_name are required")
	}

	return d.repo.CreateLLMConfig(ctx, config)
}

// GetLLMConfigByID retrieves an LLM configuration by its ID.
func (d *Domain) GetLLMConfigByID(ctx context.Context, id string) (*modelLLM.LLMConfig, error) {
	d.logger.Debug("Getting LLM config by ID", zap.String("id", id))
	return d.repo.GetLLMConfigByID(ctx, id)
}

// ListLLMConfigs retrieves all LLM configurations.
func (d *Domain) ListLLMConfigs(ctx context.Context) ([]modelLLM.LLMConfig, error) {
	d.logger.Debug("Listing all LLM configs")
	return d.repo.ListLLMConfigs(ctx)
}

// UpdateLLMConfig updates an existing LLM configuration.
func (d *Domain) UpdateLLMConfig(ctx context.Context, id string, updates map[string]interface{}) error {
	d.logger.Info("Updating LLM config", zap.String("id", id))
	// Basic validation for updates can be added here if needed
	// For example, disallow updating 'id' or 'created_at'

	// Ensure updated_at is set correctly in the store, not here.
	return d.repo.UpdateLLMConfig(ctx, id, updates)
}

// DeleteLLMConfig deletes an LLM configuration by its ID.
func (d *Domain) DeleteLLMConfig(ctx context.Context, id string) error {
	d.logger.Info("Deleting LLM config", zap.String("id", id))
	// Optional: Add logic to prevent deleting the active config without first setting another one.
	activeID, err := d.repo.GetActiveLLMConfigID(ctx)
	if err != nil {
		d.logger.Warn("Failed to get active LLM config ID during delete check", zap.Error(err))
		// Continue with delete, but log the issue
	} else if activeID == id {
		return fmt.Errorf("cannot delete the currently active LLM config. Please set another config as active first")
	}

	return d.repo.DeleteLLMConfig(ctx, id)
}

// GetActiveLLMConfig retrieves the currently active LLM configuration.
func (d *Domain) GetActiveLLMConfig(ctx context.Context) (*modelLLM.LLMConfig, error) {
	d.logger.Debug("Getting active LLM config")
	activeID, err := d.repo.GetActiveLLMConfigID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve active LLM config ID: %w", err)
	}
	if activeID == "" {
		return nil, fmt.Errorf("no active LLM config has been set")
	}
	config, err := d.repo.GetLLMConfigByID(ctx, activeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active LLM config by ID %s: %w", activeID, err)
	}
	if config == nil {
		// This means the active ID points to a non-existent config, which is an inconsistency.
		d.logger.Warn("Active LLM config ID points to a non-existent config", zap.String("activeID", activeID))
		return nil, fmt.Errorf("active LLM config not found (ID: %s)", activeID)
	}
	return config, nil
}

// SetActiveLLMConfig sets the specified LLM configuration as active.
func (d *Domain) SetActiveLLMConfig(ctx context.Context, llmConfigID string) error {
	d.logger.Info("Setting active LLM config", zap.String("id", llmConfigID))
	// Ensure the config actually exists before setting it as active
	config, err := d.repo.GetLLMConfigByID(ctx, llmConfigID)
	if err != nil {
		return fmt.Errorf("failed to check existence of LLM config %s: %w", llmConfigID, err)
	}
	if config == nil {
		return fmt.Errorf("LLM config with ID %s not found, cannot set as active", llmConfigID)
	}

	return d.repo.SetActiveLLMConfigID(ctx, llmConfigID)
}

// InitialSeedDefaultLLMConfig seeds a default LLM config if none exist and no active config is set.
// This is typically called on application startup.
func (d *Domain) InitialSeedDefaultLLMConfig(ctx context.Context, defaultLLMConfig modelLLM.LLMConfig) error {
	configs, err := d.repo.ListLLMConfigs(ctx)
	if err != nil {
		return fmt.Errorf("failed to list LLM configs during initial seed: %w", err)
	}

	if len(configs) == 0 {
		// No configs exist, create the default one
		defaultLLMConfig.Name = "Default LLM (from config)" // Give it a clear name
		defaultLLMConfig.CreatedAt = time.Now()
		defaultLLMConfig.UpdatedAt = time.Now()
		defaultLLMConfig.ID = primitive.NewObjectID() // Ensure it has a new ID

		err := d.repo.CreateLLMConfig(ctx, &defaultLLMConfig)
		if err != nil {
			return fmt.Errorf("failed to create initial default LLM config: %w", err)
		}
		d.logger.Info("Created initial default LLM config", zap.String("id", defaultLLMConfig.ID.Hex()), zap.String("name", defaultLLMConfig.Name))

		// Set it as active
		err = d.repo.SetActiveLLMConfigID(ctx, defaultLLMConfig.ID.Hex())
		if err != nil {
			return fmt.Errorf("failed to set initial default LLM config as active: %w", err)
		}
		d.logger.Info("Set initial default LLM config as active", zap.String("id", defaultLLMConfig.ID.Hex()))
	} else {
		// Check if an active config is already set
		activeID, err := d.repo.GetActiveLLMConfigID(ctx)
		if err != nil {
			return fmt.Errorf("failed to check active LLM config during initial seed: %w", err)
		}
		if activeID == "" {
			// No active config set, but configs exist. Set the first one as active.
			err = d.repo.SetActiveLLMConfigID(ctx, configs[0].ID.Hex())
			if err != nil {
				return fmt.Errorf("failed to set first existing LLM config as active: %w", err)
			}
			d.logger.Info("Set first existing LLM config as active", zap.String("id", configs[0].ID.Hex()), zap.String("name", configs[0].Name))
		}
	}
	return nil
}
