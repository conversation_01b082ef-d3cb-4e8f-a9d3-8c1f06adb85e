package project

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.nevint.com/fota3/t-rex/domain/agent"
	"git.nevint.com/fota3/t-rex/domain/projectfs"
	"git.nevint.com/fota3/t-rex/domain/runtime"
	model "git.nevint.com/fota3/t-rex/model/project"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// Domain provides core business logic for project metadata.
// It now also orchestrates the creation of a runtime environment for the project.
// Errors used by this domain are defined in errors.go
type Domain struct {
	repo            ProjectRepository       // Dependency on the existing repository interface
	runtimeDomain   *runtime.Domain         // Dependency on the runtime domain
	agentDomain     *agent.AgentReActDomain // Dependency on the agent domain
	logger          *zap.Logger
	projectfsDomain *projectfs.Domain // Dependency on the FSStorage interface
}

// New creates a new project Domain instance.
func New(repo ProjectRepository, runtimeDomain *runtime.Domain, agentDomain *agent.AgentReActDomain, logger *zap.Logger, projectfsDomain *projectfs.Domain) (*Domain, error) {
	if repo == nil {
		return nil, errors.New("project repository is required for domain")
	}
	if runtimeDomain == nil {
		return nil, errors.New("runtime domain is required for project domain")
	}
	if agentDomain == nil {
		return nil, errors.New("agent domain is required for project domain")
	}
	if logger == nil {
		return nil, errors.New("logger is required for project domain")
	}
	if projectfsDomain == nil {
		return nil, errors.New("projectfsDomain is required for project domain")
	}
	return &Domain{
		repo:            repo,
		runtimeDomain:   runtimeDomain,
		agentDomain:     agentDomain,
		logger:          logger.Named("project_domain"),
		projectfsDomain: projectfsDomain,
	}, nil
}

// CreateProjectWithRuntime handles the core logic for creating a project,
// including its metadata and its associated runtime environment.
// It ensures that a project is not left without a runtime.
func (d *Domain) CreateProjectWithRuntime(ctx context.Context, projectID, name string, userID primitive.ObjectID) (*model.Project, error) {
	d.logger.Info("Attempting to create project with runtime",
		zap.String("projectID", projectID),
		zap.String("userID", userID.Hex()),
		zap.String("name", name))

	// 1. Validate input
	if projectID == "" || userID.IsZero() {
		d.logger.Warn("Invalid input for project creation",
			zap.String("projectID", projectID),
			zap.Bool("userIDIsZero", userID.IsZero()))
		return nil, ErrInvalidInput
	}

	// 2. Create the project's physical directory on the host first.
	// This is the source for the bind mount and must exist before the container is created.
	// We use GetProjectRoot to get the path, then EnsureProjectExists to create it.
	projectHostPath, err := d.projectfsDomain.GetProjectRoot(ctx, projectID)
	if err != nil {
		d.logger.Error("Failed to determine project root path, aborting creation.", zap.String("projectID", projectID), zap.Error(err))
		return nil, fmt.Errorf("failed to determine project root path: %w", err)
	}

	if err := d.projectfsDomain.EnsureProjectExists(ctx, projectID); err != nil {
		d.logger.Error("Failed to create project root directory, aborting creation.", zap.String("projectID", projectID), zap.Error(err))
		// No rollback needed as nothing has been created yet.
		return nil, fmt.Errorf("failed to create project filesystem: %w", err)
	}
	d.logger.Info("Project root directory created successfully", zap.String("projectID", projectID), zap.String("path", projectHostPath))

	// 3. Create the associated runtime environment now that the host path exists.
	_, err = d.runtimeDomain.Create(ctx, projectID, projectHostPath)
	if err != nil {
		d.logger.Error("Failed to create runtime for new project. Rolling back project directory creation.",
			zap.String("projectID", projectID),
			zap.Error(err))

		// Attempt to roll back the directory creation.
		rollbackErr := d.projectfsDomain.Delete(ctx, projectID, ".")
		if rollbackErr != nil {
			d.logger.Error("CRITICAL: Failed to roll back project directory after runtime failure. Orphan directory may exist.",
				zap.String("projectID", projectID),
				zap.String("path", projectHostPath),
				zap.Error(rollbackErr))
			return nil, fmt.Errorf("runtime creation failed (%v) and directory rollback also failed (%v)", err, rollbackErr)
		}

		return nil, fmt.Errorf("failed to create runtime: %w", err)
	}
	d.logger.Info("Runtime created successfully for project", zap.String("projectID", projectID))

	// 4. Create project metadata in the database now that all physical resources are ready.
	newProject := &model.Project{
		ProjectID: projectID,
		UserID:    userID,
		Name:      name,
		CreatedAt: time.Now(),
	}

	err = d.repo.Create(ctx, newProject)
	if err != nil {
		d.logger.Error("Failed to create project metadata in repository after runtime and directory were created. Rolling back.", zap.String("projectID", projectID), zap.Error(err))
		// Attempt to roll back both runtime and directory.
		// We log errors but continue, prioritizing the primary error message.
		if rollbackErr := d.runtimeDomain.StopRuntime(ctx, projectID); rollbackErr != nil {
			d.logger.Error("CRITICAL: Failed to rollback runtime during project metadata creation failure.", zap.String("projectID", projectID), zap.Error(rollbackErr))
		}
		if rollbackErr := d.projectfsDomain.Delete(ctx, projectID, "."); rollbackErr != nil {
			d.logger.Error("CRITICAL: Failed to rollback directory during project metadata creation failure.", zap.String("projectID", projectID), zap.Error(rollbackErr))
		}

		if errors.Is(err, ErrDuplicateProjectID) {
			return nil, ErrDuplicateProjectID
		}
		return nil, fmt.Errorf("%w: %v", ErrOperationFailed, RepositoryError(err))
	}

	d.logger.Info("Project metadata created successfully, completing project creation", zap.String("dbID", newProject.ID.Hex()), zap.String("projectID", newProject.ProjectID))

	// Return the successfully created project
	return newProject, nil
}

// GetProjectByID retrieves project metadata by its user-facing ProjectID.
func (d *Domain) GetProjectByID(ctx context.Context, projectID string) (*model.Project, error) {
	d.logger.Info("GetProjectByID called",
		zap.String("projectID", projectID),
		zap.Int("projectIDLen", len(projectID)),
		zap.Bool("repoIsNil", d.repo == nil))

	if projectID == "" {
		d.logger.Warn("Empty projectID in GetProjectByID")
		return nil, ErrInvalidInput
	}

	if d.repo == nil {
		d.logger.Error("Repository is nil in GetProjectByID")
		return nil, fmt.Errorf("%w: repository is nil", ErrOperationFailed)
	}

	d.logger.Info("About to call repo.FindByProjectID", zap.String("projectID", projectID))
	proj, err := d.repo.FindByProjectID(ctx, projectID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			d.logger.Info("Project not found by repository", zap.String("projectID", projectID))
			return nil, ErrNotFound // Return standard ErrNotFound
		}
		d.logger.Error("Repository error getting project by ProjectID",
			zap.String("projectID", projectID),
			zap.Error(err),
			zap.String("errorType", fmt.Sprintf("%T", err)))
		return nil, fmt.Errorf("%w: %v", ErrOperationFailed, RepositoryError(err))
	}

	d.logger.Info("Project found successfully by repository",
		zap.String("projectID", projectID),
		zap.String("foundProjectName", proj.Name))

	return proj, nil
}

// GetProjectByObjectID retrieves project metadata by its MongoDB ObjectID.
func (d *Domain) GetProjectByObjectID(ctx context.Context, objectID primitive.ObjectID) (*model.Project, error) {
	d.logger.Debug("Getting project by ObjectID", zap.String("objectID", objectID.Hex()))
	if objectID.IsZero() {
		return nil, ErrInvalidInput // ObjectID zero is an invalid input
	}
	proj, err := d.repo.FindByID(ctx, objectID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			return nil, ErrNotFound // Return standard ErrNotFound
		}
		d.logger.Error("Repository error getting project by ObjectID", zap.String("objectID", objectID.Hex()), zap.Error(err))
		return nil, fmt.Errorf("%w: %v", ErrOperationFailed, RepositoryError(err))
	}
	return proj, nil
}

// ListProjectsByUserID retrieves all projects for a given user.
func (d *Domain) ListProjectsByUserID(ctx context.Context, userID primitive.ObjectID) ([]*model.Project, error) {
	d.logger.Debug("Listing projects by UserID", zap.String("userID", userID.Hex()))
	if userID.IsZero() {
		return nil, ErrInvalidInput // UserID zero is an invalid input
	}
	projects, err := d.repo.FindByUserID(ctx, userID)
	if err != nil {
		// FindByUserID should return an empty list, not ErrNotFound, if no projects. Error here means operational failure.
		d.logger.Error("Repository error listing projects by UserID", zap.String("userID", userID.Hex()), zap.Error(err))
		return nil, fmt.Errorf("%w: %v", ErrOperationFailed, RepositoryError(err))
	}
	if projects == nil {
		projects = []*model.Project{} // Ensure non-nil slice
	}
	return projects, nil
}

// DeleteProjectMetadata deletes project metadata by its MongoDB ObjectID.
func (d *Domain) DeleteProjectMetadata(ctx context.Context, objectID primitive.ObjectID) error {
	d.logger.Info("Deleting project metadata", zap.String("objectID", objectID.Hex()))
	if objectID.IsZero() {
		return ErrInvalidInput // ObjectID zero is an invalid input
	}

	// TODO: We also need to delete the associated runtime here.
	// This requires getting the projectID from the objectID first.
	// For now, this only deletes the metadata.

	err := d.repo.Delete(ctx, objectID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			return ErrNotFound // Return standard ErrNotFound
		}
		d.logger.Error("Repository error deleting project metadata", zap.String("objectID", objectID.Hex()), zap.Error(err))
		return fmt.Errorf("%w: %v", ErrOperationFailed, RepositoryError(err))
	}
	return nil
}

// CheckUserAuthorization verifies if a user has permission to access a project.
// Returns true if authorized, false otherwise, or an error if something went wrong.
func (d *Domain) CheckUserAuthorization(ctx context.Context, userID primitive.ObjectID, projectID string) (bool, error) {
	d.logger.Debug("Checking user authorization", zap.String("userID", userID.Hex()), zap.String("projectID", projectID))

	if userID.IsZero() || projectID == "" {
		d.logger.Warn("Invalid input for authorization check", zap.String("userID", userID.Hex()), zap.String("projectID", projectID))
		return false, ErrInvalidInput
	}

	proj, err := d.GetProjectByID(ctx, projectID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			d.logger.Info("Project not found during authorization check", zap.String("projectID", projectID))
			return false, ErrNotFound
		}
		d.logger.Error("Failed to get project for authorization check", zap.String("projectID", projectID), zap.Error(err))
		return false, fmt.Errorf("%w: failed to get project for auth check: %v", ErrOperationFailed, err)
	}

	if proj.UserID != userID {
		d.logger.Warn("User not authorized for project",
			zap.String("userID", userID.Hex()),
			zap.String("projectOwnerID", proj.UserID.Hex()),
			zap.String("projectID", projectID))
		return false, nil // Not authorized, but no error
	}

	d.logger.Debug("User authorized for project", zap.String("userID", userID.Hex()), zap.String("projectID", projectID))
	return true, nil
}

// DeleteProjectComplete handles the complete deletion of a project,
// including its runtime environment, filesystem, and metadata.
// It ensures all resources are properly cleaned up.
func (d *Domain) DeleteProjectComplete(ctx context.Context, projectID string) error {
	d.logger.Info("Starting complete project deletion",
		zap.String("projectID", projectID))

	// 1. Validate input
	if projectID == "" {
		d.logger.Warn("Empty projectID in DeleteProjectComplete")
		return ErrInvalidInput
	}

	// 2. Get project metadata first to validate existence
	project, err := d.GetProjectByID(ctx, projectID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			d.logger.Info("Project not found for deletion", zap.String("projectID", projectID))
			return ErrNotFound
		}
		d.logger.Error("Failed to get project for deletion", zap.String("projectID", projectID), zap.Error(err))
		return fmt.Errorf("failed to get project for deletion: %w", err)
	}

	d.logger.Info("Project found for deletion",
		zap.String("projectID", projectID),
		zap.String("projectName", project.Name),
		zap.String("projectDBID", project.ID.Hex()))

	// 3. Delete runtime environment (containers and runtime records)
	// This will stop and remove the Docker container and delete runtime DB records
	d.logger.Info("Deleting runtime environment", zap.String("projectID", projectID))
	if runtimeErr := d.runtimeDomain.DeleteRuntime(ctx, projectID); runtimeErr != nil {
		d.logger.Warn("Failed to delete runtime environment, continuing with filesystem deletion",
			zap.String("projectID", projectID),
			zap.Error(runtimeErr))
		// We continue with deletion even if runtime cleanup fails
		// since we want to clean up as much as possible
	} else {
		d.logger.Info("Runtime environment deleted successfully", zap.String("projectID", projectID))
	}

	// 4. Delete project filesystem (all project files and directories)
	d.logger.Info("Deleting project filesystem", zap.String("projectID", projectID))
	if fsErr := d.projectfsDomain.DeleteProjectWorkspace(ctx, projectID); fsErr != nil {
		d.logger.Error("Failed to delete project filesystem",
			zap.String("projectID", projectID),
			zap.Error(fsErr))
		// Filesystem deletion failure is serious, but we still try to clean up metadata
		// to avoid orphaned database records
	} else {
		d.logger.Info("Project filesystem deleted successfully", zap.String("projectID", projectID))
	}

	// 5. Delete agent conversations associated with this project
	d.logger.Info("Deleting project agent conversations", zap.String("projectID", projectID))
	if agentErr := d.agentDomain.DeleteProjectConversation(ctx, projectID); agentErr != nil {
		d.logger.Warn("Failed to delete project agent conversations, continuing with metadata deletion",
			zap.String("projectID", projectID),
			zap.Error(agentErr))
		// We continue with deletion even if agent conversation cleanup fails
		// since we want to clean up as much as possible
	} else {
		d.logger.Info("Project agent conversations deleted successfully", zap.String("projectID", projectID))
	}

	// 6. Delete project metadata from database
	d.logger.Info("Deleting project metadata", zap.String("projectID", projectID))
	if metadataErr := d.repo.Delete(ctx, project.ID); metadataErr != nil {
		d.logger.Error("Failed to delete project metadata",
			zap.String("projectID", projectID),
			zap.String("projectDBID", project.ID.Hex()),
			zap.Error(metadataErr))

		if errors.Is(metadataErr, ErrNotFound) {
			// Metadata already deleted, this is ok
			d.logger.Info("Project metadata was already deleted", zap.String("projectID", projectID))
		} else {
			return fmt.Errorf("failed to delete project metadata: %w", metadataErr)
		}
	} else {
		d.logger.Info("Project metadata deleted successfully", zap.String("projectID", projectID))
	}

	d.logger.Info("Project deletion completed successfully",
		zap.String("projectID", projectID),
		zap.String("projectName", project.Name))

	return nil
}

// WriteProjectFile writes content to a file in the specified project.
// This is a convenience method that delegates to the projectfs domain.
func (d *Domain) WriteProjectFile(ctx context.Context, projectID string, filePath string, content []byte) error {
	return d.projectfsDomain.WriteFile(ctx, projectID, filePath, content)
}

// ReadProjectFile reads content from a file in the specified project.
// This is a convenience method that delegates to the projectfs domain.
func (d *Domain) ReadProjectFile(ctx context.Context, projectID string, filePath string) ([]byte, error) {
	return d.projectfsDomain.ReadFile(ctx, projectID, filePath)
}
