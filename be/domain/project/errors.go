package project

import (
	"errors"
	"fmt"
)

// General project domain errors
var (
	// ErrNotFound indicates that a requested project entity was not found.
	// This can be returned by repository or domain logic.
	ErrNotFound = errors.New("project not found")

	// ErrDuplicateProjectID indicates an attempt to create a project with a ProjectID
	// that already exists in the system.
	// This can be returned by repository on unique constraint violation, or by domain pre-checks.
	ErrDuplicateProjectID = errors.New("project ID already exists")

	// ErrInvalidInput indicates that the input provided for a project operation was invalid.
	// Examples: empty project ID, invalid name format.
	ErrInvalidInput = errors.New("invalid input for project operation")

	// ErrUserQuotaExceeded indicates that a user has exceeded their allowed project quota.
	// This is a domain-specific business rule.
	ErrUserQuotaExceeded = errors.New("user project quota exceeded")

	// ErrOperationFailed is a more generic error for domain-level operation failures
	// not covered by more specific errors. It can wrap underlying causes.
	ErrOperationFailed = errors.New("project operation failed")
)

// RepositoryError wraps an error originating from the project repository layer.
// It helps in providing context or checking for generic repository issues.
func RepositoryError(err error) error {
	if err == nil {
		return nil
	}
	// Avoid double wrapping if it's already a well-known repository error that we might expose directly
	if errors.Is(err, ErrNotFound) || errors.Is(err, ErrDuplicateProjectID) {
		return err
	}
	return fmt.Errorf("project repository: %w", err)
}
