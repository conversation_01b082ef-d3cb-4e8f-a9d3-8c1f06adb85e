package project

import (
	"context"
	// "errors" // No longer needed here, errors are in errors.go

	model "git.nevint.com/fota3/t-rex/model/project"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Domain specific errors for project operations
// var (
// 	ErrProjectNotFound    = errors.New("project not found")
// 	ErrProjectIDExists    = errors.New("project ID already exists") // For user-facing project ID
// 	ErrProjectRepoFailure = errors.New("project repository operation failed")
// ) // Errors moved to errors.go

// ProjectRepository defines the interface for data persistence operations related to project metadata.
// Implementations should return errors defined in errors.go (e.g., ErrNotFound, ErrDuplicateProjectID).
type ProjectRepository interface {
	// Create inserts a new project metadata record.
	// Should return ErrDuplicateProjectID if a project with the same ProjectID already exists.
	// Can return other errors like ErrInvalidInput or a wrapped error via RepositoryError() for general failures.
	Create(ctx context.Context, project *model.Project) error

	// FindByProjectID retrieves project metadata by the user-facing ProjectID.
	// Should return ErrNotFound if no project matches the given ProjectID.
	FindByProjectID(ctx context.Context, projectID string) (*model.Project, error)

	// FindByUserID retrieves all project metadata records associated with a specific user ID.
	// Returns an empty slice if no projects are found, not an error.
	FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*model.Project, error)

	// FindByID retrieves project metadata by its MongoDB ObjectID.
	// Should return ErrNotFound if no project matches the given ID.
	FindByID(ctx context.Context, id primitive.ObjectID) (*model.Project, error)

	// Delete removes project metadata by its MongoDB ObjectID.
	// Should return ErrNotFound if no project matches the given ID to delete.
	Delete(ctx context.Context, id primitive.ObjectID) error

	// TODO: Add Update method if project metadata needs to be updated (e.g., renaming)
	// Update(ctx context.Context, project *model.Project) error
}
