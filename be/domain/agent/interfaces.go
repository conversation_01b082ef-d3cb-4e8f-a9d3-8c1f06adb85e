package agent

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"

	// Import the concrete model types defined in be/model/agent
	"git.nevint.com/fota3/t-rex/model/agent"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm" // Import LLMConfig model
)

// ConversationRepository defines the persistence operations required for conversations.
// Implementations will handle the actual storage (e.g., in MongoDB).
type ConversationRepository interface {
	// CreateConversation saves a new conversation.
	CreateConversation(ctx context.Context, conversation *agent.Conversation) error

	// GetConversationByID retrieves a conversation by its unique ID.
	GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error)

	// AppendMessage adds a new message to an existing conversation.
	// Implementations should ensure atomicity and update LastUpdatedAt.
	AppendMessage(ctx context.Context, conversationID string, message *agent.Message) error

	// UpdateMessage updates specific fields of a message within a conversation.
	// Implementations should ensure atomicity and update LastUpdatedAt.
	// Note: Using map[string]interface{} for updates offers flexibility but lacks type safety.
	// Consider more specific update methods if needed.
	// UpdateMessage(ctx context.Context, conversationID string, messageID string, updates map[string]interface{}) error

	// ListConversations retrieves a list of conversation metadata (e.g., ID, Title, Timestamp) for a user/project.
	// Returns lightweight representations to avoid loading full message history.
	// ListConversations(ctx context.Context, userID string, projectID string, limit int, offset int) ([]agent.ConversationMeta, error) // ConversationMeta needs definition

	// FindConversationsByUserID retrieves all conversations for a specific user.
	// Adjust signature based on actual needs (e.g., add pagination, filtering).
	FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error)

	// Project-level conversation operations
	// GetConversationByProjectID retrieves the conversation associated with a specific project.
	GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error)

	// CreateProjectConversation creates a new conversation for a specific project.
	CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error)

	// GetOrCreateProjectConversation gets existing project conversation or creates one if not exists.
	GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error)

	// ClearConversationMessages removes all messages from a conversation but keeps the conversation record.
	ClearConversationMessages(ctx context.Context, conversationID string) error

	// DeleteProjectConversation completely removes the conversation record for a specific project.
	DeleteProjectConversation(ctx context.Context, projectID string) error

	// Add other methods as needed, e.g., DeleteConversation, UpdateTitle, etc.
}

// LLMClient defines the interface for interacting with a Large Language Model.
// Implementations will wrap specific LLM APIs (e.g., OpenAI, Gemini).
type LLMClient interface {
	// ChatCompletionStream sends a request to the LLM for chat completion and receives a stream of responses.
	// It should handle mapping internal message formats to the LLM's expected format
	// and parsing the LLM's tool call requests if applicable.
	// The responseChan streams back partial message content and potential tool calls.
	ChatCompletionStream(
		ctx context.Context,
		llmConfig modelLLM.LLMConfig, // Directly pass dynamic config to underlying client
		req *ChatCompletionRequest,
	) (<-chan ChatCompletionStreamResponse, error)

	// ChatCompletionNonStreaming sends a request to the LLM for chat completion and returns the complete response.
	// This method is suitable for scenarios where the complete response is needed at once,
	// such as generating structured questions or getting final answers.
	ChatCompletionNonStreaming(ctx context.Context, req *ChatCompletionRequest) (*ChatCompletionResponse, error)
}

// ChatCompletionRequest encapsulates the data needed for an LLM chat completion request.
// This structure is designed to be compatible with OpenAI's API structure.
type ChatCompletionRequest struct {
	Model    string           `json:"model"`           // The model identifier (e.g., "gpt-4o")
	Messages []agent.Message  `json:"messages"`        // The conversation history (needs mapping to LLM format by implementation)
	Stream   bool             `json:"stream"`          // Always true for ChatCompletionStream method
	Tools    []ToolDefinition `json:"tools,omitempty"` // Definitions of tools the LLM can request
	// Add other parameters like Temperature, MaxTokens, User, etc. as needed
	Temperature *float32 `json:"temperature,omitempty"`
	MaxTokens   *int     `json:"max_tokens,omitempty"`
}

// ToolDefinition describes a tool that the LLM can call.
// Compatible with OpenAI's tool definition structure.
type ToolDefinition struct {
	Type     string             `json:"type"` // Typically "function"
	Function FunctionDefinition `json:"function"`
}

// FunctionDefinition describes the function signature for a tool.
type FunctionDefinition struct {
	Name        string      `json:"name"`                  // Format: "mcp_service.mcp_method"
	Description string      `json:"description,omitempty"` // Description for the LLM
	Parameters  interface{} `json:"parameters,omitempty"`  // JSON Schema describing the parameters
}

// ChatCompletionStreamResponse represents a single chunk received from the LLM stream.
type ChatCompletionStreamResponse struct {
	ContentDelta  string          `json:"content_delta,omitempty"`   // A chunk of the response text
	ToolCallDelta []ToolCallDelta `json:"tool_call_delta,omitempty"` // Partial information about tool calls
	IsFinal       bool            `json:"is_final,omitempty"`        // Indicates if this is the last chunk for a message part
	Error         error           `json:"error,omitempty"`           // Any error encountered during streaming
	// Add FinishReason, Usage stats if needed from the final chunk
}

// ChatCompletionResponse represents the complete response from a non-streaming LLM call.
type ChatCompletionResponse struct {
	Content      string           `json:"content"`                 // The complete response text
	ToolCalls    []agent.ToolCall `json:"tool_calls,omitempty"`    // Complete tool calls if any
	FinishReason string           `json:"finish_reason,omitempty"` // Reason why the model stopped generating
	// Add Usage stats if needed
}

// ToolCallDelta represents partial information about a tool call received during streaming.
// Needs to be aggregated by the caller to form complete agent.ToolCall objects.
type ToolCallDelta struct {
	Index      int    `json:"index"`                 // Index of the tool call in the list
	ID         string `json:"id,omitempty"`          // Call ID (usually appears early)
	MCPService string `json:"mcp_service,omitempty"` // Derived from Function.Name
	MCPMethod  string `json:"mcp_method,omitempty"`  // Derived from Function.Name
	ArgChunk   string `json:"arg_chunk,omitempty"`   // A chunk of the JSON arguments string
}

// PromptBuilder defines the interface for constructing the final prompt/messages to send to the LLM.
type PromptBuilder interface {
	// BuildPrompt takes context (like conversation history, system message, user customization)
	// and returns the structured list of messages ready for the LLMClient.
	BuildPrompt(ctx context.Context, data PromptData) ([]agent.Message, error)
}

// PromptData holds the necessary information for the PromptBuilder.
type PromptData struct {
	SystemPrompt string              // Base system prompt template
	UserContext  map[string]string   // User-specific customizations or info
	AppContext   map[string]string   // Application-level context (e.g., available tools)
	Conversation *agent.Conversation // Current conversation history
	UserQuery    agent.Message       // The latest user message
}

// MCPToolExecutor defines the interface for executing MCP tool calls requested by the LLM.
type MCPToolExecutor interface {
	// ExecuteToolCall performs the actual MCP call based on the assistant's request.
	// It should return the result (or error) in a structured way.
	ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error)

	ListMCPServers(ctx context.Context) map[string][]mcp.Tool
}

// HistoryManager defines the interface for managing conversation history.
// This allows for different strategies (e.g., simple truncation, summarization)
// to be implemented and swapped easily.
type HistoryManager interface {
	// Prune takes a full conversation and returns a pruned list of messages
	// that should be sent to the LLM, based on the implementation's strategy
	// (e.g., token limit, message count).
	Prune(ctx context.Context, conversation *agent.Conversation) ([]agent.Message, error)
}
