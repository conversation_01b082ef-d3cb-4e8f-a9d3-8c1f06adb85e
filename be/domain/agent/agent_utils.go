package agent

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// generateMsgID generates a unique message ID
func GenerateMsgID() string {
	// format: mid_{mongodb_object_id}
	return fmt.Sprintf("mid_%s", primitive.NewObjectID().Hex())
}

// GenerateCallID generates a unique call ID for tool calls
func GenerateCallID() string {
	// format: tcid_{mongodb_object_id}
	return fmt.Sprintf("tcid_%s", primitive.NewObjectID().Hex())
}

// float32Ptr returns a pointer to the float32 value
func float32Ptr(val float32) *float32 {
	return &val
}

// intPtr returns a pointer to the int value
func intPtr(val int) *int {
	return &val
}

// stringPtr returns a pointer to the string value.
func stringPtr(s string) *string {
	return &s
}

// errorPtr returns a pointer to the error value.
// Note: Go's type system doesn't directly allow *error to be an error type.
// This is intended for embedding in structs where a nil pointer means "no error".
func errorPtr(e error) *error {
	if e == nil {
		return nil
	}
	return &e
}

// streamEventMarkerPtr returns a pointer to the StreamEventMarker value.
func streamEventMarkerPtr(m StreamEventMarker) *StreamEventMarker {
	return &m
}
