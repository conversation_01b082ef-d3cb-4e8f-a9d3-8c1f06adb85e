package agent

import (
	"context"
	"fmt"

	"git.nevint.com/fota3/t-rex/model/agent"
	"git.nevint.com/fota3/t-rex/prompts"
)

// buildSystemPrompt builds the system prompt using the template system
func (s *AgentReActDomain) buildSystemPrompt(conversation *agent.Conversation) (string, error) {
	// Prepare dynamic data for the prompt template
	dynamicData := prompts.PromptDynamicData{
		// Fill in with appropriate values from context
		Cwd:                        getCWD(),
		SystemInfoOS:               getOS(),
		SystemInfoShell:            getShellPath(),
		SystemInfoHome:             getHomeDir(),
		UserCustomInstructionsText: "请使用中文回答用户的问题。",
		// Add other required fields
	}
	toolRes := s.executor.ListMCPServers(context.Background())
	s.logger.Debugw("MCPServers", "MCPServers", toolRes)
	dynamicData.MCPServers = prompts.MCPInfo{
		Servers: toolRes,
	}

	// Generate the system prompt
	systemPrompt, err := prompts.GenerateSystemPrompt(dynamicData)
	if err != nil {
		return "", fmt.Errorf("failed to generate system prompt: %w", err)
	}

	return systemPrompt, nil
}

// buildMessagesForLLM builds the messages array for the LLM request
func (s *AgentReActDomain) buildMessagesForLLM(
	conversation *agent.Conversation,
	systemPrompt string,
) []agent.Message {
	// Start with the system message
	messages := []agent.Message{
		{
			Role:    agent.RoleSystem,
			Content: systemPrompt,
		},
	}

	// Add conversation history
	messages = append(messages, conversation.Messages...)

	// Debug logging
	s.logger.Debugw("Built messages for LLM",
		"totalMessages", len(messages),
		"conversationMessages", len(conversation.Messages),
		"systemPromptLength", len(systemPrompt))

	/*
		for i, msg := range messages {
			s.logger.Debugw("Message details",
				"index", i,
				"role", string(msg.Role),
				"contentLength", len(msg.Content),
				"content", msg.Content)
		}	*/

	return messages
}
