package agent

import (
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"runtime"
)

func getOS() string {
	return runtime.GOOS
}

func getCWD() string {
	cwd, _ := os.Getwd()
	return cwd
}

func getHomeDir() string {
	if home := os.Getenv("HOME"); home != "" {
		return home
	}

	u, err := user.Current()
	if err != nil {
		return ""
	}
	return u.HomeDir
}

// 获取 bash 的绝对路径
func getShellPath() string {
	// 方法1: 通过 which 命令查找
	if path, err := exec.LookPath("bash"); err == nil {
		if absPath, err := filepath.Abs(path); err == nil {
			return absPath
		}
		return path
	}

	// 方法2: 常见默认路径
	defaultPaths := []string{
		"/bin/bash",              // Linux 标准路径
		"/usr/bin/bash",          // 某些 Linux 发行版
		"/opt/homebrew/bin/bash", // M1 Mac Homebrew
		"/usr/local/bin/bash",    // Intel Mac Homebrew
	}

	for _, path := range defaultPaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return "" // 未找到
}
