package agent

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/agent"
	"github.com/mark3labs/mcp-go/mcp"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockMCPToolExecutor is a mock implementation of MCPToolExecutor for testing.
// It needs to satisfy the MCPToolExecutor interface defined in interfaces.go
type MockMCPToolExecutor struct{}

// ExecuteToolCall mocks the execution of an MCP tool.
// It now matches the MCPToolExecutor interface.
func (m *MockMCPToolExecutor) ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error) {
	// Combine MCPService and MCPMethod to form a logical tool name for the mock result
	logicalToolName := toolCall.MCPService + "." + toolCall.MCPMethod
	return &agent.ToolResult{
		CallID:  toolCall.CallID,                                                   // Use CallID from toolCall
		Content: fmt.Sprintf("mock tool execution result for %s", logicalToolName), // Use Content field
	}, nil
}

// ListMCPServers mocks listing MCP servers.
// It now returns map[string][]mcp.Tool to match the MCPToolExecutor interface.
func (m *MockMCPToolExecutor) ListMCPServers(ctx context.Context) map[string][]mcp.Tool {
	// Return a fixed map for testing system prompt generation.
	return map[string][]mcp.Tool{
		"mock_fs": {
			{Name: "readFile", Description: "Reads a file"},
		},
		"mock_code": {
			{Name: "execute", Description: "Executes a command"},
		},
	}
}

// MockConversationRepository is a mock implementation of ConversationRepository for testing.
type MockConversationRepository struct {
	Conversations map[string]*agent.Conversation
	Errors        map[string]error // Optional: to simulate errors for specific methods/IDs
}

func NewMockConversationRepository() *MockConversationRepository {
	return &MockConversationRepository{
		Conversations: make(map[string]*agent.Conversation),
		Errors:        make(map[string]error),
	}
}

func (m *MockConversationRepository) CreateConversation(ctx context.Context, conversation *agent.Conversation) error {
	if err, ok := m.Errors["CreateConversation"]; ok {
		return err
	}
	m.Conversations[conversation.ID.Hex()] = conversation
	return nil
}

func (m *MockConversationRepository) GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error) {
	if err, ok := m.Errors["GetConversationByID"]; ok {
		return nil, err
	}
	conv, ok := m.Conversations[id]
	if !ok {
		return nil, nil // Simulate not found
	}
	return conv, nil
}

func (m *MockConversationRepository) AppendMessage(ctx context.Context, id string, message *agent.Message) error {
	if err, ok := m.Errors["AppendMessage"]; ok {
		return err
	}
	conv, ok := m.Conversations[id]
	if !ok {
		return fmt.Errorf("conversation not found: %s", id)
	}
	conv.Messages = append(conv.Messages, *message)
	conv.LastUpdatedAt = time.Now() // Simulate update
	return nil
}

func (m *MockConversationRepository) GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error) {
	if err, ok := m.Errors["GetConversationByProjectID"]; ok {
		return nil, err
	}
	for _, conv := range m.Conversations {
		if conv.ProjectID == projectID {
			return conv, nil
		}
	}
	return nil, nil
}

// CreateProjectConversation adds the missing method to satisfy the interface.
func (m *MockConversationRepository) CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	if err, ok := m.Errors["CreateProjectConversation"]; ok {
		return nil, err
	}
	// Check if one already exists for this projectID (simple check)
	existingConv, _ := m.GetConversationByProjectID(ctx, projectID)
	if existingConv != nil {
		return existingConv, fmt.Errorf("conversation for project %s already exists", projectID) // Or return existing one
	}
	newConv := &agent.Conversation{
		ID:            primitive.NewObjectID(),
		ProjectID:     projectID,
		UserID:        userID,
		Messages:      []agent.Message{},
		CreatedAt:     time.Now(),
		LastUpdatedAt: time.Now(),
	}
	m.Conversations[newConv.ID.Hex()] = newConv
	return newConv, nil
}

func (m *MockConversationRepository) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	if err, ok := m.Errors["GetOrCreateProjectConversation"]; ok {
		return nil, err
	}
	conv, _ := m.GetConversationByProjectID(ctx, projectID)
	if conv != nil {
		return conv, nil
	}
	// If not found, create it using the CreateProjectConversation logic (or similar)
	return m.CreateProjectConversation(ctx, projectID, userID) // Delegate or replicate
}

func (m *MockConversationRepository) ClearConversationMessages(ctx context.Context, id string) error {
	if err, ok := m.Errors["ClearConversationMessages"]; ok {
		return err
	}
	conv, ok := m.Conversations[id]
	if !ok {
		return fmt.Errorf("conversation not found: %s", id)
	}
	conv.Messages = []agent.Message{}
	conv.LastUpdatedAt = time.Now()
	return nil
}

// FindConversationsByUserID adds the missing method to satisfy the interface.
func (m *MockConversationRepository) FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error) {
	if err, ok := m.Errors["FindConversationsByUserID"]; ok {
		return nil, err
	}
	var userConversations []*agent.Conversation
	for _, conv := range m.Conversations {
		if conv.UserID == userID {
			userConversations = append(userConversations, conv)
		}
	}
	return userConversations, nil
}

// TestAgentReactService_processResponseDeltaWithAttemptCompletion tests the parsing of LLM responses,
// including thinking blocks, tool calls, attempt_completion, and ask_followup_question tags.
func TestAgentReactService_processResponseDeltaWithAttemptCompletion(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	service := &AgentReActDomain{
		logger: logger.Sugar(),
	}

	tests := []struct {
		name                         string
		inputContent                 string
		expectedThinkingContent      string
		expectedServerName           string
		expectedToolName             string
		expectedToolParams           map[string]interface{}
		expectedAttemptCompletion    bool
		expectedDidAskFollowup       bool
		expectedFollowupQuestion     string
		expectedFollowupQuestionOpts []string
		expectError                  bool
	}{
		{
			name:                    "Simple thinking block",
			inputContent:            "<thinking>This is a thought.</thinking>",
			expectedThinkingContent: "This is a thought.",
			expectedToolParams:      make(map[string]interface{}),
		},
		{
			name:               "Simple tool call",
			inputContent:       "<use_mcp_tool><server_name>local</server_name><tool_name>file_reader</tool_name><arguments>{\"path\":\"/test.txt\"}</arguments></use_mcp_tool>",
			expectedServerName: "local",
			expectedToolName:   "file_reader",
			expectedToolParams: map[string]interface{}{"path": "/test.txt"},
		},
		{
			name: "Tool call with thinking", // Thinking is cleared if followed by tool call
			inputContent: "<thinking>I should read the file.</thinking>" +
				"<use_mcp_tool><server_name>t-rex/projectfs</server_name><tool_name>file_reader</tool_name><arguments>{\"path\":\"/test.txt\"}</arguments></use_mcp_tool>",
			expectedThinkingContent: "", // Cleared by tool call
			expectedServerName:      "t-rex/projectfs",
			expectedToolName:        "file_reader",
			expectedToolParams:      map[string]interface{}{"path": "/test.txt"},
		},
		{
			name:                      "Attempt completion tag",
			inputContent:              "This is the final answer. <attempt_completion>true</attempt_completion>",
			expectedAttemptCompletion: true,
			expectedToolParams:        make(map[string]interface{}),
		},
		{
			name:                      "Attempt completion with result tag",
			inputContent:              "<attempt_completion><result>系统已准备就绪，请提供具体任务指令</result></attempt_completion>",
			expectedAttemptCompletion: true,
			expectedToolParams:        make(map[string]interface{}),
		},
		{
			name:                     "Ask followup question simple",
			inputContent:             "<ask_followup_question>What is your name?</ask_followup_question>",
			expectedDidAskFollowup:   true,
			expectedFollowupQuestion: "What is your name?",
			expectedToolParams:       make(map[string]interface{}),
		},
		{
			name:                     "Ask followup question with explicit question tag",
			inputContent:             "<ask_followup_question><question>What is your project's primary goal?</question></ask_followup_question>",
			expectedDidAskFollowup:   true,
			expectedFollowupQuestion: "What is your project's primary goal?",
			expectedToolParams:       make(map[string]interface{}),
		},
		{
			name: "Ask followup question with question and options",
			inputContent: "<ask_followup_question>" +
				"<question>Which color do you prefer?</question>" +
				"<options>[\"Red\", \"Green\", \"Blue\"]</options>" +
				"</ask_followup_question>",
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Which color do you prefer?",
			expectedFollowupQuestionOpts: []string{"Red", "Green", "Blue"},
			expectedToolParams:           make(map[string]interface{}),
		},
		{
			name:                         "Ask followup question with options only (question inferred from main content if parser supports)",
			inputContent:                 "<ask_followup_question>Your preference? <options>[\"Yes\", \"No\"]</options></ask_followup_question>",
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Your preference?",
			expectedFollowupQuestionOpts: []string{"Yes", "No"},
			expectedToolParams:           make(map[string]interface{}),
		},
		{
			name: "Ask followup question with malformed options (should parse question, ignore options, no error)",
			inputContent: "<ask_followup_question>" +
				"<question>Is this correct?</question>" +
				"<options>Not A JSON Array</options>" +
				"</ask_followup_question>",
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Is this correct?",
			expectedFollowupQuestionOpts: nil,
			expectedToolParams:           make(map[string]interface{}),
			expectError:                  false, // Expect no error from main parser if question is fine
		},
		{
			name: "Ask followup question with empty options array",
			inputContent: "<ask_followup_question>" +
				"<question>Any comments?</question>" +
				"<options>[]</options>" +
				"</ask_followup_question>",
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Any comments?",
			expectedFollowupQuestionOpts: []string{},
			expectedToolParams:           make(map[string]interface{}),
		},
		{
			name: "Thinking, then ask followup with question and options", // Thinking is cleared
			inputContent: "<thinking>I need to clarify.</thinking>" +
				"<ask_followup_question>" +
				"<question>Which feature first?</question>" +
				"<options>[\"A\", \"B\"]</options>" +
				"</ask_followup_question>",
			expectedThinkingContent:      "", // Cleared by ask_followup
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Which feature first?",
			expectedFollowupQuestionOpts: []string{"A", "B"},
			expectedToolParams:           make(map[string]interface{}),
		},
		{
			name:                     "Malformed XML (unclosed ask_followup_question)", // Should be an error
			inputContent:             "<ask_followup_question><question>Test",
			expectedDidAskFollowup:   false, // Unclosed block leads to error, fields cleared
			expectedFollowupQuestion: "",
			expectedToolParams:       make(map[string]interface{}),
			expectError:              true,
		},
		{
			name:                         "Attempt completion after thinking and asking a question (ask should take precedence)",
			inputContent:                 "<thinking>Pondering.</thinking><ask_followup_question><question>Sure?</question><options>[\"Yes\"]</options></ask_followup_question><attempt_completion>true</attempt_completion>",
			expectedThinkingContent:      "", // Cleared by ask_followup
			expectedDidAskFollowup:       true,
			expectedFollowupQuestion:     "Sure?",
			expectedFollowupQuestionOpts: []string{"Yes"},
			expectedAttemptCompletion:    false,
			expectedToolParams:           make(map[string]interface{}),
			expectError:                  false, // Expect no error for valid sequence where ask takes precedence
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			var (
				inThinkingBlock   bool
				inToolBlock       bool
				thinkingContent   string
				serverName        string
				toolName          string
				attemptCompletion bool
				didAskFollowup    bool
				followupQuestion  string
				followupOptions   []string
			)
			toolParams := make(map[string]interface{})

			err := service.processResponseDeltaWithAttemptCompletion(
				tc.inputContent,
				&inThinkingBlock,
				&inToolBlock,
				&thinkingContent,
				&serverName,
				&toolName,
				toolParams,
				&attemptCompletion,
				&didAskFollowup,
				&followupQuestion,
				&followupOptions,
			)

			if tc.expectError {
				assert.Error(t, err, "Expected an error for input: %s", tc.inputContent)
			} else {
				assert.NoError(t, err, "Did not expect an error for input: %s. Error: %v", tc.inputContent, err)
			}

			assert.Equal(t, tc.expectedThinkingContent, thinkingContent, "Thinking content mismatch")
			assert.Equal(t, tc.expectedServerName, serverName, "Server name mismatch")
			assert.Equal(t, tc.expectedToolName, toolName, "Tool name mismatch")

			if len(tc.expectedToolParams) == 0 && len(toolParams) == 0 {
				// This is fine, both are effectively empty maps (make(map[string]interface{}) vs nil map treated as empty)
			} else {
				assert.Equal(t, tc.expectedToolParams, toolParams, "Tool params mismatch")
			}

			assert.Equal(t, tc.expectedAttemptCompletion, attemptCompletion, "Attempt completion flag mismatch")
			assert.Equal(t, tc.expectedDidAskFollowup, didAskFollowup, "Did ask followup flag mismatch")
			assert.Equal(t, tc.expectedFollowupQuestion, followupQuestion, "Followup question mismatch")
			assert.Equal(t, tc.expectedFollowupQuestionOpts, followupOptions, "Followup question options mismatch")

			// Verify that flags are mutually exclusive as expected by the parser logic, only if no error was expected
			if !tc.expectError {
				if didAskFollowup {
					assert.False(t, attemptCompletion, "If didAskFollowup is true, attemptCompletion should be false")
					assert.Empty(t, toolName, "If didAskFollowup is true, toolName should be empty")
					assert.Empty(t, serverName, "If didAskFollowup is true, serverName should be empty")
				}
				if toolName != "" {
					assert.False(t, attemptCompletion, "If toolName is not empty, attemptCompletion should be false")
					assert.False(t, didAskFollowup, "If toolName is not empty, didAskFollowup should be false")
					assert.NotEmpty(t, serverName, "If toolName is not empty, serverName should not be empty")
				}
				if attemptCompletion {
					assert.Empty(t, toolName, "If attemptCompletion is true, toolName should be empty")
					assert.Empty(t, serverName, "If attemptCompletion is true, serverName should be empty")
					assert.False(t, didAskFollowup, "If attemptCompletion is true, didAskFollowup should be false")
				}
			}
		})
	}
}

// TestAgentReactService_buildMessagesForLLM tests the message building for LLM requests.
func TestAgentReactService_buildMessagesForLLM(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	mockExecutor := &MockMCPToolExecutor{}
	mockRepo := NewMockConversationRepository()

	service := &AgentReActDomain{
		logger:    logger.Sugar(),
		executor:  mockExecutor,
		repo:      mockRepo,
		modelName: "test-model",
	}

	systemPrompt := "这是系统提示"
	conversation := &agent.Conversation{
		Messages: []agent.Message{
			{
				Role:    agent.RoleUser,
				Content: "你好",
			},
			{
				Role:    agent.RoleAssistant,
				Content: "你好，有什么可以帮助你的？",
			},
		},
	}

	messages := service.buildMessagesForLLM(conversation, systemPrompt)

	assert.Equal(t, 3, len(messages))
	assert.Equal(t, agent.RoleSystem, messages[0].Role)
	assert.Equal(t, systemPrompt, messages[0].Content)
	assert.Equal(t, agent.RoleUser, messages[1].Role)
	assert.Equal(t, "你好", messages[1].Content)
	assert.Equal(t, agent.RoleAssistant, messages[2].Role)
}

// TestHelperFunctions remains the same as it tests standalone functions.
