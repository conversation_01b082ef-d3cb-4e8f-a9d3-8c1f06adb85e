package agent

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
)

// Regular expressions for parsing LLM response tags
var (
	thinkingTagRegex          = regexp.MustCompile(`(?s)<thinking>(.*?)</thinking>`)
	toolTagRegex              = regexp.MustCompile(`(?s)<use_mcp_tool>(.*?)</use_mcp_tool>`)
	serverNameTagRegex        = regexp.MustCompile(`(?s)<server_name>(.*?)</server_name>`)
	toolNameTagRegex          = regexp.MustCompile(`(?s)<tool_name>(.*?)</tool_name>`)
	argumentsTagRegex         = regexp.MustCompile(`(?s)<arguments>(.*?)</arguments>`)
	attemptCompletionTagRegex = regexp.MustCompile(`(?s)<attempt_completion>(.*?)</attempt_completion>`)
	// Updated regex to be non-greedy for the content within ask_followup_question
	// and to capture the entire block for further sub-parsing.
	askFollowupQuestionBlockRegex = regexp.MustCompile(`(?s)<ask_followup_question>(.*?)</ask_followup_question>`)
	questionTagRegex              = regexp.MustCompile(`(?s)<question>(.*?)</question>`)
	optionsTagRegex               = regexp.MustCompile(`(?s)<options>(.*?)</options>`)
)

// Constants for block types during parsing
const (
	blockTypeNone = iota
	blockTypeThinking
	blockTypeTool
	blockTypeAttemptCompletion
	blockTypeAskFollowup
)

// Errors related to parsing
var (
	errToolNotClosed              = errors.New("use_mcp_tool block was not properly closed by the end of LLM response")
	errThinkingNotClosed          = errors.New("thinking block was not properly closed by the end of LLM response")
	errAttemptCompletionNotClosed = errors.New("attempt_completion block was not properly closed by the end of LLM response")
	errAskFollowupNotClosed       = errors.New("ask_followup_question block was not properly closed by the end of LLM response")
)

// parseJSONStringToMap parses a JSON string into a map[string]interface{}.
func parseJSONStringToMap(jsonStr string, out *map[string]interface{}) error {
	jsonStr = strings.TrimSpace(jsonStr)
	if jsonStr == "" {
		*out = make(map[string]interface{}) // Ensure it's an empty map, not nil, if jsonStr is empty
		return nil
	}
	return json.Unmarshal([]byte(jsonStr), out)
}

// parseAskFollowupQuestionBlock is a helper function to parse the content within <ask_followup_question> tags.
// It extracts the question text and options. If <options> parsing fails, it logs a warning and sets optionsOut to nil,
// but does not return an error for the options parsing itself.
// Returns an error only if the overall block structure is problematic for extracting the question.
func (s *AgentReActDomain) parseAskFollowupQuestionBlock(askFollowupBlockContent string, questionOut *string, optionsOut *[]string) error {
	*questionOut = ""
	*optionsOut = nil

	extractedQuestionText := ""
	questionMatches := questionTagRegex.FindStringSubmatch(askFollowupBlockContent)
	if len(questionMatches) > 1 {
		extractedQuestionText = strings.TrimSpace(questionMatches[1])
	} else {
		optionsIdx := strings.Index(askFollowupBlockContent, "<options>")
		if optionsIdx != -1 {
			contentBeforeOptions := strings.TrimSpace(askFollowupBlockContent[:optionsIdx])
			if contentBeforeOptions != "" { // Only use it if it's not just whitespace before options
				extractedQuestionText = contentBeforeOptions
				s.logger.Debugf("No <question> tag found; using content before <options> as question: %s", extractedQuestionText)
			}
		} else {
			extractedQuestionText = strings.TrimSpace(askFollowupBlockContent)
			if extractedQuestionText != "" {
				s.logger.Debugf("No <question> or <options> tag found; treating entire block content as question: %s", extractedQuestionText)
			}
		}
	}
	*questionOut = extractedQuestionText

	optionsMatches := optionsTagRegex.FindStringSubmatch(askFollowupBlockContent)
	if len(optionsMatches) > 1 {
		optionsStr := strings.TrimSpace(optionsMatches[1])
		if optionsStr != "" {
			var parsedOptions []string
			if err := json.Unmarshal([]byte(optionsStr), &parsedOptions); err != nil {
				s.logger.Warnf("Failed to parse <options> content as JSON array: %s. Error: %v. Options will be ignored.", optionsStr, err)
				// *optionsOut remains nil, no error returned for this part.
			} else {
				*optionsOut = parsedOptions
			}
		} else {
			*optionsOut = []string{} // Empty options tag means empty array
		}
	}

	// Return error only if the question itself could not be determined and the block wasn't just empty space.
	if *questionOut == "" && strings.TrimSpace(askFollowupBlockContent) != "" {
		// This case could mean an <ask_followup_question> tag with only an <options> tag and no preceding text,
		// or completely empty/malformed content. If options were parsed, question can be empty.
		// If both are empty but block wasn't, it is ambiguous.
		// For now, if question is empty, we allow it. The caller (recursivelyMakeRequests) might provide a default.
	}
	return nil // No fatal error from this function regarding parsing options or question presence.
}

// processResponseDeltaWithAttemptCompletion processes the full accumulated response of an LLM turn
// to detect thinking blocks, tool calls, <attempt_completion>, and <ask_followup_question> tags.
func (s *AgentReActDomain) processResponseDeltaWithAttemptCompletion(
	accumulator string,
	inThinkingBlock *bool,
	inToolBlock *bool,
	thinking *string,
	serverName *string,
	toolName *string,
	toolParams map[string]interface{},
	attemptCompletion *bool,
	didAskFollowup *bool,
	followupQuestionText *string,
	followupQuestionOptions *[]string,
) error {
	*inThinkingBlock = false
	*inToolBlock = false
	*thinking = ""
	*toolName = ""
	*serverName = ""
	for k := range toolParams {
		delete(toolParams, k)
	}
	*attemptCompletion = false
	*didAskFollowup = false
	*followupQuestionText = ""
	*followupQuestionOptions = nil

	remainingResponse := accumulator
	var lastError error
	currentBlockType := blockTypeNone
	var lastSuccessfullyParsedThinking string // Store thinking that is not immediately followed by an action

	for len(remainingResponse) > 0 {
		processedSomethingInIteration := false

		if askFollowupMatches := askFollowupQuestionBlockRegex.FindStringIndex(remainingResponse); askFollowupMatches != nil {
			startIdx, endIdx := askFollowupMatches[0], askFollowupMatches[1]
			blockContentInner := askFollowupQuestionBlockRegex.FindStringSubmatch(remainingResponse[startIdx:endIdx])[1]

			var tempQuestion string
			var tempOptions []string
			// parseAskFollowupQuestionBlock now only returns error for truly unrecoverable situations (currently never)
			if parseErr := s.parseAskFollowupQuestionBlock(blockContentInner, &tempQuestion, &tempOptions); parseErr != nil {
				s.logger.Errorf("Critical error parsing <ask_followup_question> block: %v. Content: '%s'", parseErr, blockContentInner)
				lastError = fmt.Errorf("critically failed to parse <ask_followup_question> content: %w. Block: '%s'", parseErr, blockContentInner)
				// If even the lenient sub-parser fails, this block is bad.
				*didAskFollowup = false // Ensure it's false
			} else {
				// Even if options parsing failed internally in parseAskFollowupQuestionBlock (logged as WARN there),
				// as long as we get a question (or it's permissibly empty), we mark it as a followup action.
				*followupQuestionText = tempQuestion
				*followupQuestionOptions = tempOptions
				*didAskFollowup = true
				*attemptCompletion = false
				*toolName = ""
				*serverName = ""
				lastSuccessfullyParsedThinking = "" // Action taken, clear preceding thinking
			}
			remainingResponse = remainingResponse[endIdx:]
			processedSomethingInIteration = true
			currentBlockType = blockTypeNone
			continue
		}

		if !*didAskFollowup {
			if toolMatches := toolTagRegex.FindStringIndex(remainingResponse); toolMatches != nil {
				startIdx, endIdx := toolMatches[0], toolMatches[1]
				// TODO: Add multiple tool calls support
				toolBlockContent := toolTagRegex.FindStringSubmatch(remainingResponse[startIdx:endIdx])[1]
				serverNameMatches := serverNameTagRegex.FindStringSubmatch(toolBlockContent)
				toolNameMatches := toolNameTagRegex.FindStringSubmatch(toolBlockContent)
				argumentsMatches := argumentsTagRegex.FindStringSubmatch(toolBlockContent)

				if len(toolNameMatches) > 1 && len(argumentsMatches) > 1 && len(serverNameMatches) > 1 {
					parsedToolName := strings.TrimSpace(toolNameMatches[1])
					argumentsStr := strings.TrimSpace(argumentsMatches[1])
					var tempToolParams map[string]interface{}
					if err := parseJSONStringToMap(argumentsStr, &tempToolParams); err != nil {
						s.logger.Errorf("Failed to parse tool arguments JSON: %v. Arguments string: '%s'", err, argumentsStr)
						lastError = fmt.Errorf("failed to parse tool arguments for '%s': %w", parsedToolName, err)
						*serverName = ""
						*toolName = ""
					} else {
						*serverName = strings.TrimSpace(serverNameMatches[1])
						*toolName = parsedToolName
						for k, v := range tempToolParams {
							toolParams[k] = v
						}
						*attemptCompletion = false
						lastSuccessfullyParsedThinking = "" // Action taken
					}
				} else {
					s.logger.Warnf("Malformed use_mcp_tool block: missing server_name, tool_name, or arguments. Content: %s", toolBlockContent)
					lastError = fmt.Errorf("malformed use_mcp_tool block: %s", toolBlockContent)
					*toolName = ""
					*serverName = ""
				}
				remainingResponse = remainingResponse[endIdx:]
				processedSomethingInIteration = true
				currentBlockType = blockTypeNone
				continue
			}
		}

		if !*didAskFollowup && *toolName == "" {
			if completionMatches := attemptCompletionTagRegex.FindStringIndex(remainingResponse); completionMatches != nil {
				_, endIdx := completionMatches[0], completionMatches[1]
				// The presence of the tag is sufficient to mark completion.
				// We no longer inspect the content inside the tag.
				*attemptCompletion = true
				lastSuccessfullyParsedThinking = "" // Action taken

				remainingResponse = remainingResponse[endIdx:]
				processedSomethingInIteration = true
				currentBlockType = blockTypeNone
				continue
			}
		}

		if thinkingMatches := thinkingTagRegex.FindStringIndex(remainingResponse); thinkingMatches != nil {
			startIdx, endIdx := thinkingMatches[0], thinkingMatches[1]
			lastSuccessfullyParsedThinking = strings.TrimSpace(thinkingTagRegex.FindStringSubmatch(remainingResponse[startIdx:endIdx])[1])
			remainingResponse = remainingResponse[endIdx:]
			processedSomethingInIteration = true
			currentBlockType = blockTypeNone
			continue
		}

		if !processedSomethingInIteration {
			if strings.HasPrefix(remainingResponse, "<ask_followup_question>") {
				currentBlockType = blockTypeAskFollowup
			} else if strings.HasPrefix(remainingResponse, "<use_mcp_tool>") {
				currentBlockType = blockTypeTool
			} else if strings.HasPrefix(remainingResponse, "<attempt_completion>") {
				currentBlockType = blockTypeAttemptCompletion
			} else if strings.HasPrefix(remainingResponse, "<thinking>") {
				currentBlockType = blockTypeThinking
			}
			break
		}
	}

	// Assign the last valid thinking content if no higher-priority action followed it directly.
	if !*didAskFollowup && *toolName == "" && !*attemptCompletion {
		*thinking = lastSuccessfullyParsedThinking
	} else {
		*thinking = "" // Clear if an action was taken
	}

	// Handle errors for unclosed tags, but only if a higher-priority action wasn't already taken.
	switch currentBlockType {
	case blockTypeAskFollowup:
		// This case implies the *entire remainingResponse* started with <ask_followup_question> but wasn't a full match from regex,
		// AND no other action (*didAskFollowup, *toolName, *attemptCompletion) was set to true.
		if !*didAskFollowup && *toolName == "" && !*attemptCompletion {
			*didAskFollowup = false
			*followupQuestionText = ""
			*followupQuestionOptions = nil
			*thinking = ""
			*serverName = ""
			lastError = errAskFollowupNotClosed
		}
	case blockTypeTool:
		if !*didAskFollowup && *toolName == "" && !*attemptCompletion {
			*inToolBlock = true
			*toolName = ""
			*serverName = ""
			clear(toolParams)
			*thinking = ""
			lastError = errToolNotClosed
		}
	case blockTypeAttemptCompletion:
		if !*didAskFollowup && *toolName == "" && !*attemptCompletion {
			*attemptCompletion = false
			*thinking = ""
			*serverName = ""
			lastError = errAttemptCompletionNotClosed
		}
	case blockTypeThinking:
		if !*didAskFollowup && *toolName == "" && !*attemptCompletion {
			*inThinkingBlock = true
			lastError = errThinkingNotClosed
		}
	}

	return lastError
}
