package agent

import (
	"context"

	"git.nevint.com/fota3/t-rex/model/agent"
	"git.nevint.com/fota3/t-rex/pkg/tokenizer"
	"go.uber.org/zap"
)

// SimpleHistoryManager implements a basic history management strategy.
// It truncates the conversation history based on a maximum number of messages
// and a maximum number of tokens.
type SimpleHistoryManager struct {
	logger      *zap.SugaredLogger
	tokenizer   *tokenizer.Service
	maxMessages int
	maxTokens   int
}

// NewSimpleHistoryManager creates a new SimpleHistoryManager.
func NewSimpleHistoryManager(
	logger *zap.SugaredLogger,
	tokenizer *tokenizer.Service,
	maxMessages int,
	maxTokens int,
) *SimpleHistoryManager {
	if logger == nil {
		logger = zap.NewNop().Sugar()
	}
	return &SimpleHistoryManager{
		logger:      logger.Named("SimpleHistoryManager"),
		tokenizer:   tokenizer,
		maxMessages: maxMessages,
		maxTokens:   maxTokens,
	}
}

// Prune trims the conversation history according to the configured limits.
// It prioritizes keeping the most recent messages.
// It iterates messages from newest to oldest, adding them to the result
// until either the message limit or the token limit is reached.
func (m *SimpleHistoryManager) Prune(ctx context.Context, conversation *agent.Conversation) ([]agent.Message, error) {
	if conversation == nil || len(conversation.Messages) == 0 {
		return []agent.Message{}, nil
	}

	prunedMessages := make([]agent.Message, 0, m.maxMessages)
	currentMessageCount := 0
	currentTokenCount := 0

	// Iterate backwards from the most recent message.
	for i := len(conversation.Messages) - 1; i >= 0; i-- {
		message := conversation.Messages[i]

		// Stop if we've reached the maximum number of messages.
		if currentMessageCount >= m.maxMessages {
			m.logger.Debugf("Message limit reached (%d), stopping history pruning.", m.maxMessages)
			break
		}

		// Calculate tokens for the current message content.
		tokenCount, err := m.tokenizer.CountTokens(message.Content)
		if err != nil {
			m.logger.Errorf("Failed to count tokens for message, skipping: %v", err)
			continue // Skip this message if token counting fails.
		}

		// Stop if adding this message would exceed the token limit.
		if currentTokenCount+tokenCount > m.maxTokens {
			m.logger.Debugf("Token limit reached (%d + %d > %d), stopping history pruning.", currentTokenCount, tokenCount, m.maxTokens)
			break
		}

		// Add the message to our result list and update counts.
		prunedMessages = append(prunedMessages, message)
		currentMessageCount++
		currentTokenCount += tokenCount
	}

	// The `prunedMessages` list is currently in reverse chronological order.
	// We need to reverse it back to the correct chronological order for the LLM.
	for i, j := 0, len(prunedMessages)-1; i < j; i, j = i+1, j-1 {
		prunedMessages[i], prunedMessages[j] = prunedMessages[j], prunedMessages[i]
	}

	m.logger.Infof("Pruned conversation history from %d to %d messages (%d tokens).", len(conversation.Messages), len(prunedMessages), currentTokenCount)

	return prunedMessages, nil
}
