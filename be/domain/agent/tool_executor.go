package agent

import (
	"context"
	"fmt"
	"strings"

	"git.nevint.com/fota3/t-rex/model/agent"
)

// executeToolCall executes a tool call through the MCP executor
func (s *AgentReActDomain) executeToolCall(
	ctx context.Context,
	toolName string,
	params map[string]interface{},
) (string, error) {
	// Create a ToolCall object
	toolCall := &agent.ToolCall{
		CallID:     GenerateCallID(),
		MCPService: toolName, // Assuming toolName is in the format "service.method"
		MCPMethod:  "",       // Will be parsed from toolName if needed
		MCPParams:  params,
	}

	// Parse service and method from toolName if needed
	parts := strings.Split(toolName, ".")
	if len(parts) > 1 {
		toolCall.MCPService = parts[0]
		toolCall.MCPMethod = parts[1]
	}

	// Execute the tool call
	result, err := s.executor.ExecuteToolCall(ctx, toolCall)
	if err != nil {
		return "", fmt.Errorf("failed to execute tool %s: %w", toolName, err)
	}

	return result.Content, nil
}
