package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	llmDomain "git.nevint.com/fota3/t-rex/domain/llm" // Import LLM domain
	"git.nevint.com/fota3/t-rex/model/agent"
	// Import LLM model for ChatCompletionStream
)

// DomainStreamChunk carries data from the agent's core ReAct logic (domain layer)
// to the service layer. The service layer will then transform these chunks
// into appropriate SSE events for the frontend.
type DomainStreamChunk struct {
	Delta          *string            // Raw text delta from the LLM (e.g., "Hel", "lo Wor", "<thinki", "ng>...</think", "ing>").
	ConversationID string             // The ID of the current conversation.
	MessageID      string             // The ID of the message (assistant's turn) being streamed.
	Role           agent.Role         // The role of the message (e.g., "user", "assistant", "system").
	MsgType        string             // The type of the message (e.g., "assistant_response", "user_input", "tool_result", "continue_content").
	Marker         *StreamEventMarker // Optional marker for specific stream events (e.g., start/end of this message turn).
	Error          *error             // Optional error that occurred during domain processing.
}

// StreamEventMarker denotes specific points or events in the stream originating from the domain logic.
type StreamEventMarker string

const (
	// MarkerStart signals the beginning of a new turn/message from the domain's ReAct cycle.
	// This helps the service layer know when to send its `message_start` SSE event.
	MarkerStart StreamEventMarker = "start"

	// MarkerEnd signals the end of the current turn/message from the domain's ReAct cycle.
	// This helps the service layer know when to send its `message_complete` SSE event.
	MarkerEnd StreamEventMarker = "end"

	// MarkerError signals an error encountered within the domain logic
	// that the service layer should be aware of. The actual error details
	// would be in the `Error` field of the DomainStreamChunk.
	MarkerError StreamEventMarker = "error"
)

// AgentReActDomain implements a streaming conversation service based on the ReACT framework
type AgentReActDomain struct {
	repo           ConversationRepository
	llm            LLMClient
	executor       MCPToolExecutor
	historyManager HistoryManager
	logger         *zap.SugaredLogger
	maxCycles      int               // 最大递归周期，防止无限循环
	llmDomain      *llmDomain.Domain // NEW: Dependency on LLM domain
	// modelName      string // This will now come from active LLM config
}

// NewAgentReActDomain creates a new instance of AgentReActDomain
func NewAgentReActDomain(
	repo ConversationRepository,
	llm LLMClient,
	executor MCPToolExecutor,
	historyManager HistoryManager,
	logger *zap.SugaredLogger,
	llmDomain *llmDomain.Domain, // NEW: Accept llmDomain
	// modelName string, // This will be dynamic
) *AgentReActDomain {
	if logger == nil {
		logger = zap.NewNop().Sugar()
	}

	return &AgentReActDomain{
		repo:           repo,
		llm:            llm,
		executor:       executor,
		historyManager: historyManager,
		logger:         logger.Named("AgentReActDomain"),
		maxCycles:      30,        // 默认最大递归次数
		llmDomain:      llmDomain, // NEW: Store llmDomain
		// modelName:      modelName,
	}
}

// func (s *AgentReActDomain) SetModelName(ctx context.Context, modelName string) *AgentReActDomain {
// 	s.modelName = modelName
// 	return s
// }

// Helper struct for sending structured question to the frontend
type FollowupQuestionPayload struct {
	Type     string   `json:"type"` // e.g., "ask_followup_question"
	Question string   `json:"question"`
	Options  []string `json:"options,omitempty"`
}

// HandleStreamingInteraction handles a streaming conversation using the ReACT framework
// It returns a channel that streams DomainStreamChunk instances back to the caller (service layer).
func (s *AgentReActDomain) HandleStreamingInteraction(
	ctx context.Context,
	conversation *agent.Conversation,
	userMessage agent.Message,
	systemPrompt string, // The dynamically generated system prompt is now passed in.
	isRetry bool,
) (<-chan DomainStreamChunk, error) {
	responseChan := make(chan DomainStreamChunk)

	go func() {
		defer close(responseChan)
		var err error

		if !isRetry {
			s.repo.AppendMessage(ctx, conversation.ID.Hex(), &userMessage)
			conversation, err = s.repo.GetConversationByID(ctx, conversation.ID.Hex())
			if err != nil {
				s.logger.Error("Failed to get conversation by ID", zap.Error(err), zap.String("conversationID", conversation.ID.Hex()))
				return
			}
			if conversation == nil {
				s.logger.Error("Conversation is nil after initial refresh")
				return
			}
		}

		cycleCount := 0

		err = s.recursivelyMakeRequests(
			ctx,
			conversation,
			responseChan,
			&cycleCount,
			systemPrompt, // Pass the system prompt down to the core loop.
		)

		// Error handling after recursivelyMakeRequests returns
		if err != nil {
			s.logger.Error("Error from ReACT framework process", zap.Error(err), zap.String("conversationID", conversation.ID.Hex()))
		}
	}()

	return responseChan, nil
}

func (s *AgentReActDomain) recursivelyMakeRequests(
	ctx context.Context,
	conversation *agent.Conversation,
	responseChan chan<- DomainStreamChunk,
	cycleCount *int,
	systemPrompt string, // Receive the system prompt here.
) error {

	// TODO: Need to add a pesudo message to the conversation, and send it to the frontend when error occurs.
	pesudoMsg := &agent.Message{}
	currentMessageID := GenerateMsgID()
	assistantMessage := &agent.Message{
		MsgID:   currentMessageID,
		MsgType: agent.MsgTypeAssistantResponse,
		Role:    agent.RoleAssistant,
		//Timestamp: time.Now(),
	}
	firstChunkSentInThisTurn := false
	s.logger.Debugw("Starting new assistant turn", "attemptAssistantMsgID", currentMessageID, "cycle", *cycleCount+1)

	send := func(msg *agent.Message, delta *string, marker *StreamEventMarker, errVal *error) {
		// Safety check to prevent panic
		if conversation == nil || conversation.ID.IsZero() {
			s.logger.Error("Cannot send chunk: conversation is nil or has invalid ID",
				zap.String("assistantMsgID", currentMessageID))
			return
		}

		chunk := DomainStreamChunk{
			ConversationID: conversation.ID.Hex(),
			MessageID:      currentMessageID,
			MsgType:        msg.MsgType,
			Role:           msg.Role,
			Delta:          delta,
			Marker:         marker,
			Error:          errVal,
		}

		if !firstChunkSentInThisTurn {
			startMarker := MarkerStart
			chunk.Marker = &startMarker
			firstChunkSentInThisTurn = true
			s.logger.Debugw("Sent MarkerDomainTurnStart", "assistantMsgID", currentMessageID, "conversationID", conversation.ID.Hex())
		}
		responseChan <- chunk
	}

	// TODO: need to return a specific warning to the frontend
	if *cycleCount >= s.maxCycles {
		s.logger.Warn("Exceeded maximum ReACT cycles", zap.Int("maxCycles", s.maxCycles), zap.String("conversationID", conversation.ID.Hex()), zap.String("assistantMsgID", currentMessageID))
		deltaContent := "\n已达到最大思考次数限制，结束当前对话。"
		send(pesudoMsg, stringPtr(deltaContent), streamEventMarkerPtr(MarkerEnd), nil)
		return nil
	}

	*cycleCount++
	s.logger.Debugw("Starting ReACT cycle", "cycle", *cycleCount, "conversationID", conversation.ID.Hex(), "assistantMsgID", currentMessageID)

	// Prune conversation history before building messages for LLM
	prunedMessages, err := s.historyManager.Prune(ctx, conversation)
	if err != nil {
		historyErr := fmt.Errorf("failed to prune conversation history: %w", err)
		s.logger.Error(historyErr.Error(), zap.String("assistantMsgID", currentMessageID))
		send(pesudoMsg, nil, streamEventMarkerPtr(MarkerEnd), errorPtr(historyErr))
		return historyErr
	}

	// Create a temporary conversation object with pruned messages for building the LLM request
	tempConversation := &agent.Conversation{
		Messages: prunedMessages,
	}
	messages := s.buildMessagesForLLM(tempConversation, systemPrompt)

	// Get the active LLM config dynamically
	activeLLMConfig, err := s.llmDomain.GetActiveLLMConfig(ctx)
	if err != nil {
		llmConfigErr := fmt.Errorf("failed to get active LLM configuration: %w", err)
		s.logger.Error(llmConfigErr.Error(), zap.String("assistantMsgID", currentMessageID))
		send(pesudoMsg, nil, streamEventMarkerPtr(MarkerEnd), errorPtr(llmConfigErr))
		return llmConfigErr
	}

	llmReq := &ChatCompletionRequest{
		Model:       activeLLMConfig.ModelName, // Use dynamic model name
		Messages:    messages,
		Stream:      true,
		Temperature: float32Ptr(0.7),
		MaxTokens:   intPtr(10000),
	}

	// Pass the activeLLMConfig directly to the LLM client
	llmStreamChan, err := s.llm.ChatCompletionStream(ctx, *activeLLMConfig, llmReq)
	if err != nil {
		llmCallErr := fmt.Errorf("failed to call LLM: %w", err)
		s.logger.Error(llmCallErr.Error(), zap.String("assistantMsgID", currentMessageID))
		send(pesudoMsg, nil, streamEventMarkerPtr(MarkerEnd), errorPtr(llmCallErr))
		return llmCallErr
	}
	s.logger.Infow("Successfully called LLM, streaming response...", "model", activeLLMConfig.ModelName, "assistantMsgID", currentMessageID)

	var fullResponseBuilder strings.Builder
	var llmStreamErr error

	for llmDelta := range llmStreamChan {
		//s.logger.Debugw("LLM stream delta", "delta", llmDelta.ContentDelta, "isFinal", llmDelta.IsFinal, "error", llmDelta.Error)
		if llmDelta.Error != nil {
			llmStreamErr = fmt.Errorf("error in LLM stream: %w", llmDelta.Error)
			s.logger.Error(llmStreamErr.Error(), zap.String("assistantMsgID", currentMessageID))
			send(assistantMessage, nil, streamEventMarkerPtr(MarkerEnd), errorPtr(llmStreamErr))
			return llmStreamErr
		}

		fullResponseBuilder.WriteString(llmDelta.ContentDelta)

		if llmDelta.ContentDelta != "" {
			send(assistantMessage, stringPtr(llmDelta.ContentDelta), nil, nil)
		} else if !firstChunkSentInThisTurn {
			// first chunk
			send(assistantMessage, nil, nil, nil)
		}

		if llmDelta.IsFinal {
			s.logger.Debug("LLM indicated end of its current stream part.", zap.String("assistantMsgID", currentMessageID))
			// fill the assistant message content from builder
			assistantMessage.Content = fullResponseBuilder.String()

			s.logger.Debugw("Saving assistant message to history (on IsFinal in react_service.go)",
				"assistantMsgID", assistantMessage.MsgID,
				"conversationID", conversation.ID.Hex(),
				"contentLength", len(assistantMessage.Content))
			// Using 'conversation.ID.Hex()' for conversationID as used elsewhere in the func for repo calls
			if errDb := s.repo.AppendMessage(ctx, conversation.ID.Hex(), assistantMessage); errDb != nil {
				s.logger.Errorw("Failed to append assistant message to history (on IsFinal in react_service.go)",
					"assistantMsgID", assistantMessage.MsgID,
					"conversationID", conversation.ID.Hex(), // Added for context
					"error", errDb)
				// Not returning the DB error here to avoid disrupting the primary flow,
				// but logging it. The stream itself wasn't necessarily in error.
			}
			conversation, err = s.repo.GetConversationByID(ctx, conversation.ID.Hex())
			if err != nil {
				s.logger.Error("Failed to get conversation by ID", zap.Error(err), zap.String("assistantMsgID", currentMessageID))
				return fmt.Errorf("failed to refresh conversation after saving assistant message: %w", err)
			}
			if conversation == nil {
				s.logger.Error("Conversation is nil after refresh", zap.String("assistantMsgID", currentMessageID))
				return fmt.Errorf("conversation became nil after refresh")
			}
			send(assistantMessage, nil, streamEventMarkerPtr(MarkerEnd), nil)
			break
		}
	}

	s.logger.Debugw("LLM stream finished for cycle", "assistantMsgID", currentMessageID, "cycle", *cycleCount, "accumulatedLength", len(assistantMessage.Content))

	var (
		toolName                      string
		serverName                    string
		toolParams                    = make(map[string]interface{})
		followupQuestionWasAsked      bool
		parsedFollowupQuestionText    string
		parsedFollowupQuestionOptions []string
		attemptCompletionDetected     bool
		inThinkingBlock, inToolBlock  bool
		thinkingText                  string
	)

	parseErr := s.processResponseDeltaWithAttemptCompletion(
		assistantMessage.Content,
		&inThinkingBlock,
		&inToolBlock,
		&thinkingText,
		&serverName,
		&toolName,
		toolParams,
		&attemptCompletionDetected,
		&followupQuestionWasAsked,
		&parsedFollowupQuestionText,
		&parsedFollowupQuestionOptions,
	)
	if parseErr != nil {
		s.logger.Errorw("Failed to parse LLM response for ReAct tags", "error", parseErr, "assistantMsgID", currentMessageID)
		parsingErrText := "Error: Could not understand assistant's response structure."
		send(pesudoMsg, stringPtr(parsingErrText), streamEventMarkerPtr(MarkerEnd), errorPtr(parseErr))
		return parseErr
	}

	// followup question detected
	if followupQuestionWasAsked {
		s.logger.Infow("Pausing for user input due to <ask_followup_question>",
			zap.String("conversationID", conversation.ID.Hex()),
			zap.String("assistantMsgID", currentMessageID),
			zap.String("question", parsedFollowupQuestionText),
			zap.Strings("options", parsedFollowupQuestionOptions)) // Log parsed options
		return nil
	}

	// tool call detected
	if toolName != "" {
		s.logger.Infow("Tool call to be executed", "toolName", toolName, "serverName", serverName, "assistantMsgID", currentMessageID)
		toolCall := &agent.ToolCall{
			CallID:     GenerateMsgID(),
			MCPService: serverName,
			MCPMethod:  toolName,
			MCPParams:  toolParams,
		}
		toolResult, err := s.executor.ExecuteToolCall(ctx, toolCall)
		var toolResultText string
		if err != nil {
			toolResultText = fmt.Sprintf("Tool error: %v", err)
			s.logger.Error("Tool execution failed", zap.String("tool", toolName), zap.Error(err), zap.String("assistantMsgID", currentMessageID))
		} else if toolResult != nil {
			toolResultText = toolResult.Content
		}

		resultContent := fmt.Sprintf("<tool_result tool_name=\"%s\">%s</tool_result>", toolName, toolResultText)
		currentMessageID = GenerateMsgID()
		toolResultSystemMessage := agent.Message{
			MsgID:     currentMessageID,
			MsgType:   agent.MsgTypeToolResult,
			Role:      agent.RoleUser,
			Content:   resultContent,
			Timestamp: time.Now(),
		}
		if err := s.repo.AppendMessage(ctx, conversation.ID.Hex(), &toolResultSystemMessage); err != nil {
			s.logger.Error("Failed to append tool result message to history", zap.Error(err), zap.String("assistantMsgID", currentMessageID))
		}
		//refresh the conversation
		conversation, err = s.repo.GetConversationByID(ctx, conversation.ID.Hex())
		if err != nil {
			s.logger.Error("Failed to get conversation by ID", zap.Error(err), zap.String("assistantMsgID", currentMessageID))
			return fmt.Errorf("failed to refresh conversation after tool execution: %w", err)
		}
		if conversation == nil {
			s.logger.Error("Conversation is nil after refresh", zap.String("assistantMsgID", currentMessageID))
			return fmt.Errorf("conversation became nil after refresh")
		}

		send(&toolResultSystemMessage, stringPtr(resultContent), streamEventMarkerPtr(MarkerStart), nil)
		send(&toolResultSystemMessage, nil, streamEventMarkerPtr(MarkerEnd), nil)

		return s.recursivelyMakeRequests(ctx, conversation, responseChan, cycleCount, systemPrompt)
	}

	// attempt completion detected
	if attemptCompletionDetected {
		s.logger.Infow("Final response detected (<attempt_completion>).",
			zap.String("conversationID", conversation.ID.Hex()),
			zap.String("assistantMsgID", currentMessageID))
		return nil
	}

	// if the assistant message is not <ask_followup_question> and <attempt_completion>, the agent loop should be continued, append the pesudo user message to the conversation
	continueContent := "Please clearify and continue the conversation. If you want to end the conversation, please use <attempt_completion> tool or <ask_followup_question> tool."
	currentMessageID = GenerateMsgID()
	userMessage := agent.Message{
		MsgID:     currentMessageID,
		MsgType:   agent.MsgTypeContinueContent,
		Role:      agent.RoleUser,
		Content:   continueContent,
		Timestamp: time.Now(),
	}
	s.repo.AppendMessage(ctx, conversation.ID.Hex(), &userMessage)
	conversation, err = s.repo.GetConversationByID(ctx, conversation.ID.Hex())
	if err != nil {
		s.logger.Error("Failed to get conversation by ID", zap.Error(err), zap.String("assistantMsgID", currentMessageID))
		return fmt.Errorf("failed to refresh conversation after appending continue message: %w", err)
	}
	if conversation == nil {
		s.logger.Error("Conversation is nil after refresh", zap.String("assistantMsgID", currentMessageID))
		return fmt.Errorf("conversation became nil after refresh")
	}
	send(&userMessage, stringPtr(continueContent), streamEventMarkerPtr(MarkerStart), nil)
	send(&userMessage, nil, streamEventMarkerPtr(MarkerEnd), nil)

	return s.recursivelyMakeRequests(ctx, conversation, responseChan, cycleCount, systemPrompt)
}

// Project-level conversation management methods
func (s *AgentReActDomain) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	return s.repo.GetOrCreateProjectConversation(ctx, projectID, userID)
}
func (s *AgentReActDomain) GetProjectConversation(ctx context.Context, projectID string) (*agent.Conversation, error) {
	return s.repo.GetConversationByProjectID(ctx, projectID)
}
func (s *AgentReActDomain) ClearProjectConversation(ctx context.Context, projectID string) error {
	conversation, err := s.repo.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return fmt.Errorf("failed to get project conversation: %w", err)
	}
	if conversation == nil {
		return nil
	}
	return s.repo.ClearConversationMessages(ctx, conversation.ID.Hex())
}
func (s *AgentReActDomain) ExportProjectConversation(ctx context.Context, projectID string, format string) (string, error) {
	conversation, err := s.repo.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return "", fmt.Errorf("failed to get project conversation: %w", err)
	}
	if conversation == nil {
		return "", fmt.Errorf("no conversation found for project %s", projectID)
	}

	switch format {
	case "markdown":
		return s.exportToMarkdown(conversation), nil
	case "json":
		return s.exportToJSON(conversation)
	case "txt":
		return s.exportToText(conversation), nil
	default:
		return "", fmt.Errorf("unsupported export format: %s", format)
	}
}

// DeleteProjectConversation completely removes the conversation record for a specific project.
// This is typically called when deleting a project to clean up all associated data.
func (s *AgentReActDomain) DeleteProjectConversation(ctx context.Context, projectID string) error {
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}

	s.logger.Infow("Deleting project conversation", "projectID", projectID)

	err := s.repo.DeleteProjectConversation(ctx, projectID)
	if err != nil {
		return fmt.Errorf("failed to delete project conversation: %w", err)
	}

	s.logger.Infow("Project conversation deleted successfully", "projectID", projectID)
	return nil
}

func (s *AgentReActDomain) HandleProjectStreamingInteraction(
	ctx context.Context,
	projectID string,
	userID string,
	userMessage agent.Message,
) (<-chan DomainStreamChunk, error) {
	conversation, err := s.GetOrCreateProjectConversation(ctx, projectID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get or create project conversation: %w", err)
	}
	// Note: This recursive call might be problematic if HandleStreamingInteraction is changed significantly
	// from its original single-conversation ID version. Assuming the logic here is to delegate to the
	// primary HandleStreamingInteraction after resolving the conversation.
	return s.HandleStreamingInteraction(ctx, conversation, userMessage, "", false)
}

func (s *AgentReActDomain) exportToMarkdown(conversation *agent.Conversation) string {
	var builder strings.Builder

	builder.WriteString("# Conversation Export\n\n")
	builder.WriteString(fmt.Sprintf("**Project ID:** %s\n", conversation.ProjectID))
	builder.WriteString(fmt.Sprintf("**Created:** %s\n", conversation.CreatedAt.Format("2006-01-02 15:04:05")))
	builder.WriteString(fmt.Sprintf("**Last Updated:** %s\n\n", conversation.LastUpdatedAt.Format("2006-01-02 15:04:05")))

	for i, message := range conversation.Messages {
		switch message.Role {
		case agent.RoleUser:
			builder.WriteString(fmt.Sprintf("## User Message %d\n\n", i+1))
		case agent.RoleAssistant:
			builder.WriteString(fmt.Sprintf("## Assistant Message %d\n\n", i+1))
		case agent.RoleSystem:
			builder.WriteString(fmt.Sprintf("## System Message %d\n\n", i+1))
		}

		builder.WriteString(fmt.Sprintf("**Time:** %s\n\n", message.Timestamp.Format("2006-01-02 15:04:05")))
		builder.WriteString(fmt.Sprintf("%s\n\n", message.Content))
		builder.WriteString("---\n\n")
	}

	result := builder.String()
	return result
}

func (s *AgentReActDomain) exportToJSON(conversation *agent.Conversation) (string, error) {
	data, err := json.MarshalIndent(conversation, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal conversation to JSON: %w", err)
	}
	result := string(data)
	return result, nil
}

func (s *AgentReActDomain) exportToText(conversation *agent.Conversation) string {
	var builder strings.Builder

	builder.WriteString("Conversation Export\n")
	builder.WriteString("===================\n\n")
	builder.WriteString(fmt.Sprintf("Project ID: %s\n", conversation.ProjectID))
	builder.WriteString(fmt.Sprintf("Created: %s\n", conversation.CreatedAt.Format("2006-01-02 15:04:05")))
	builder.WriteString(fmt.Sprintf("Last Updated:** %s\n\n", conversation.LastUpdatedAt.Format("2006-01-02 15:04:05")))

	for _, message := range conversation.Messages {
		builder.WriteString(fmt.Sprintf("[%s] %s (%s)\n",
			message.Role,
			message.Timestamp.Format("15:04:05"),
			message.MsgID))
		builder.WriteString(fmt.Sprintf("%s\n\n", message.Content))
	}

	result := builder.String()
	return result
}
