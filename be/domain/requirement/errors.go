package requirement

import (
	"errors"
	"fmt"
)

// General requirement domain errors
var (
	// ErrNotFound indicates that a requested requirement entity was not found.
	// This can be returned by repository or domain logic.
	ErrNotFound = errors.New("requirement not found")

	// ErrInvalidInput indicates that the input provided for a requirement operation was invalid.
	// Examples: empty title, content too long, invalid status.
	ErrInvalidInput = errors.New("invalid input for requirement operation")

	// ErrUnauthorized indicates that the user is not authorized to access or modify the requirement.
	// This enforces user isolation - users can only access their own requirements.
	ErrUnauthorized = errors.New("unauthorized access to requirement")

	// ErrInvalidStatus indicates an attempt to set an invalid status value.
	// Valid statuses are: pending, processing, completed.
	ErrInvalidStatus = errors.New("invalid requirement status")

	// ErrUserQuotaExceeded indicates that a user has exceeded their allowed requirement quota.
	// This is a domain-specific business rule for preventing spam.
	ErrUserQuotaExceeded = errors.New("user requirement quota exceeded")

	// ErrOperationFailed is a more generic error for domain-level operation failures
	// not covered by more specific errors. It can wrap underlying causes.
	ErrOperationFailed = errors.New("requirement operation failed")

	// ErrQuestionGenerationFailed indicates that AI question generation failed.
	// This can be due to LLM API errors or invalid requirement content.
	ErrQuestionGenerationFailed = errors.New("AI question generation failed")

	// ErrInvalidAnswers indicates that the provided answers are invalid.
	// Examples: missing required answers, answer for non-existent question.
	ErrInvalidAnswers = errors.New("invalid answers provided")
)

// RepositoryError wraps an error originating from the requirement repository layer.
// It helps in providing context or checking for generic repository issues.
func RepositoryError(err error) error {
	if err == nil {
		return nil
	}
	// Avoid double wrapping if it's already a well-known repository error that we might expose directly
	if errors.Is(err, ErrNotFound) || errors.Is(err, ErrInvalidInput) || errors.Is(err, ErrUnauthorized) {
		return err
	}
	return fmt.Errorf("requirement repository: %w", err)
}
