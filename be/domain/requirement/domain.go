package requirement

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"regexp"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"encoding/base64"
	"io"
	"os"
	"path/filepath"

	agentDomain "git.nevint.com/fota3/t-rex/domain/agent"
	projectDomain "git.nevint.com/fota3/t-rex/domain/project"
	model "git.nevint.com/fota3/t-rex/model/agent"
	reqModel "git.nevint.com/fota3/t-rex/model/requirement"
	"git.nevint.com/fota3/t-rex/pkg/fileupload"
)

// ImageAnalyzer defines the interface for AI-powered image analysis
type ImageAnalyzer interface {
	AnalyzeImage(ctx context.Context, imagePath string, modelName string) (*reqModel.ImageAnalysis, error)
}

// WebsiteAnalyzer defines the interface for AI-powered website analysis
type WebsiteAnalyzer interface {
	AnalyzeWebsite(ctx context.Context, url string, modelName string) (*reqModel.WebsiteAnalysis, error)
}

// FileUploader defines the interface for file upload operations
type FileUploader interface {
	UploadImage(file *multipart.FileHeader) (*fileupload.UploadedFile, error)
	DeleteFile(relativePath string) error
}

// LLMClientFactory defines the interface for creating LLM clients
type LLMClientFactory interface {
	GetLLMClient(ctx context.Context, modelName string) (agentDomain.LLMClient, error)
}

// Domain is the single entry point for requirement domain logic.
type Domain struct {
	repo             RequirementRepository
	llmClientFactory LLMClientFactory
	imageAnalyzer    ImageAnalyzer
	websiteAnalyzer  WebsiteAnalyzer
	fileUploader     FileUploader
	projectDomain    *projectDomain.Domain // Added project domain dependency
	uploadDir        string                // Upload directory for accessing image files
	logger           *zap.Logger
	defaultModelName string // Default model name when requirement doesn't specify one
}

// New creates a new requirement domain entry point.
func New(repo RequirementRepository, llmClientFactory LLMClientFactory, imageAnalyzer ImageAnalyzer, websiteAnalyzer WebsiteAnalyzer, fileUploader FileUploader, projectDomain *projectDomain.Domain, uploadDir string, logger *zap.Logger, defaultModelName string) (*Domain, error) {
	if repo == nil {
		return nil, fmt.Errorf("requirement repository is required")
	}
	if llmClientFactory == nil {
		return nil, fmt.Errorf("LLM client factory is required")
	}
	if projectDomain == nil {
		return nil, fmt.Errorf("project domain is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	if strings.TrimSpace(defaultModelName) == "" {
		return nil, fmt.Errorf("default model name is required")
	}
	if strings.TrimSpace(uploadDir) == "" {
		return nil, fmt.Errorf("upload directory is required")
	}
	// imageAnalyzer, websiteAnalyzer and fileUploader are optional - can be nil
	return &Domain{
		repo:             repo,
		llmClientFactory: llmClientFactory,
		imageAnalyzer:    imageAnalyzer,
		websiteAnalyzer:  websiteAnalyzer,
		fileUploader:     fileUploader,
		projectDomain:    projectDomain,
		uploadDir:        strings.TrimSpace(uploadDir),
		logger:           logger.Named("requirement_domain"),
		defaultModelName: strings.TrimSpace(defaultModelName),
	}, nil
}

// getModelNameForRequirement returns the model name to use for AI calls.
// Uses the requirement's ModelName if available, otherwise falls back to the default model.
func (d *Domain) getModelNameForRequirement(req *reqModel.Requirement) string {
	if req != nil && strings.TrimSpace(req.ModelName) != "" {
		return strings.TrimSpace(req.ModelName)
	}
	return d.defaultModelName
}

// CreateRequirement handles the creation of a new requirement.
func (d *Domain) CreateRequirement(ctx context.Context, userID primitive.ObjectID, title, content, modelName string) (*reqModel.Requirement, error) {
	d.logger.Info("Starting requirement creation process",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName))

	// Validate input
	if strings.TrimSpace(title) == "" {
		d.logger.Warn("Requirement creation validation failed: empty title",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: title cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(content) == "" {
		d.logger.Warn("Requirement creation validation failed: empty content",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: content cannot be empty", ErrInvalidInput)
	}

	// Create requirement object
	req := &reqModel.Requirement{
		UserID:    userID,
		Title:     strings.TrimSpace(title),
		Content:   strings.TrimSpace(content),
		ModelName: strings.TrimSpace(modelName),
		Questions: []reqModel.Question{},
		Answers:   []reqModel.Answer{},
		Status:    reqModel.StatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to repository
	err := d.repo.Create(ctx, req)
	if err != nil {
		d.logger.Error("Error creating requirement in repository",
			zap.String("user_id", userID.Hex()),
			zap.String("title", title),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Requirement creation completed successfully",
		zap.String("requirement_id", req.ID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("title", title))

	return req, nil
}

// CreateRequirementWithWebsite handles the creation of a new requirement with reference website.
func (d *Domain) CreateRequirementWithWebsite(ctx context.Context, userID primitive.ObjectID, title, content, modelName, websiteURL string) (*reqModel.Requirement, error) {
	d.logger.Info("Starting requirement creation process with website",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName),
		zap.String("website_url", websiteURL))

	// Validate input
	if strings.TrimSpace(title) == "" {
		d.logger.Warn("Requirement creation validation failed: empty title",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: title cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(content) == "" {
		d.logger.Warn("Requirement creation validation failed: empty content",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: content cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(websiteURL) == "" {
		d.logger.Warn("Requirement creation validation failed: empty website URL",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: website URL cannot be empty", ErrInvalidInput)
	}

	// Validate URL format
	if !d.isValidURL(websiteURL) {
		d.logger.Warn("Requirement creation validation failed: invalid URL format",
			zap.String("user_id", userID.Hex()),
			zap.String("website_url", websiteURL))
		return nil, fmt.Errorf("%w: invalid URL format", ErrInvalidInput)
	}

	// Determine initial status
	initialStatus := reqModel.StatusWebsiteAnalyzing

	// Create requirement object
	req := &reqModel.Requirement{
		UserID:          userID,
		Title:           strings.TrimSpace(title),
		Content:         strings.TrimSpace(content),
		ModelName:       strings.TrimSpace(modelName),
		ReferenceImages: []reqModel.ReferenceImage{},
		ReferenceWebsite: &reqModel.ReferenceWebsite{
			URL:       strings.TrimSpace(websiteURL),
			CreatedAt: time.Now(),
		},
		Questions: []reqModel.Question{},
		Answers:   []reqModel.Answer{},
		Status:    initialStatus,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to repository
	err := d.repo.Create(ctx, req)
	if err != nil {
		d.logger.Error("Error creating requirement in repository",
			zap.String("user_id", userID.Hex()),
			zap.String("title", title),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Requirement creation with website completed successfully",
		zap.String("requirement_id", req.ID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("website_url", websiteURL))

	// Start asynchronous website analysis if website analyzer is available
	if d.websiteAnalyzer != nil {
		go func() {
			d.logger.Info("Starting asynchronous website analysis",
				zap.String("requirement_id", req.ID.Hex()),
				zap.String("website_url", websiteURL))

			if err := d.analyzeWebsiteAsync(context.Background(), req.ID); err != nil {
				d.logger.Error("Asynchronous website analysis failed",
					zap.String("requirement_id", req.ID.Hex()),
					zap.Error(err))
			}
		}()
	}

	return req, nil
}

// CreateRequirementWithImages handles the creation of a new requirement with reference images.
func (d *Domain) CreateRequirementWithImages(ctx context.Context, userID primitive.ObjectID, title, content, modelName string, imageFiles []*multipart.FileHeader) (*reqModel.Requirement, error) {
	d.logger.Info("Starting requirement creation process with images",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName),
		zap.Int("image_count", len(imageFiles)))

	// Validate input
	if strings.TrimSpace(title) == "" {
		d.logger.Warn("Requirement creation validation failed: empty title",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: title cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(content) == "" {
		d.logger.Warn("Requirement creation validation failed: empty content",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: content cannot be empty", ErrInvalidInput)
	}

	// Determine initial status based on whether images are provided
	initialStatus := reqModel.StatusPending
	if len(imageFiles) > 0 {
		initialStatus = reqModel.StatusImageAnalyzing
	}

	// Create requirement object
	req := &reqModel.Requirement{
		UserID:          userID,
		Title:           strings.TrimSpace(title),
		Content:         strings.TrimSpace(content),
		ModelName:       strings.TrimSpace(modelName),
		ReferenceImages: []reqModel.ReferenceImage{},
		Questions:       []reqModel.Question{},
		Answers:         []reqModel.Answer{},
		Status:          initialStatus,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Process uploaded images if file uploader is available (upload only, no analysis)
	if d.fileUploader != nil && len(imageFiles) > 0 {
		for i, imageFile := range imageFiles {
			// Upload image
			uploadResult, err := d.fileUploader.UploadImage(imageFile)
			if err != nil {
				d.logger.Error("Failed to upload image",
					zap.String("user_id", userID.Hex()),
					zap.Int("image_index", i),
					zap.Error(err))
				// Clean up any previously uploaded images
				d.cleanupUploadedImages(req.ReferenceImages)
				return nil, fmt.Errorf("failed to upload image %d: %w", i+1, err)
			}

			// Convert upload result to ReferenceImage (without analysis)
			refImage, err := d.convertUploadResultToReferenceImage(uploadResult)
			if err != nil {
				d.logger.Error("Failed to convert upload result",
					zap.String("user_id", userID.Hex()),
					zap.Int("image_index", i),
					zap.Error(err))
				// Clean up any previously uploaded images
				d.cleanupUploadedImages(req.ReferenceImages)
				return nil, fmt.Errorf("failed to process uploaded image %d: %w", i+1, err)
			}

			req.ReferenceImages = append(req.ReferenceImages, *refImage)
		}
	}

	// Save to repository
	err := d.repo.Create(ctx, req)
	if err != nil {
		d.logger.Error("Error creating requirement in repository",
			zap.String("user_id", userID.Hex()),
			zap.String("title", title),
			zap.Error(err))
		// Clean up uploaded images on repository error
		d.cleanupUploadedImages(req.ReferenceImages)
		return nil, RepositoryError(err)
	}

	d.logger.Info("Requirement creation with images completed successfully",
		zap.String("requirement_id", req.ID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.Int("image_count", len(req.ReferenceImages)))

	// Start asynchronous image analysis if images were uploaded
	if len(req.ReferenceImages) > 0 && d.imageAnalyzer != nil {
		go func() {
			d.logger.Info("Starting asynchronous image analysis",
				zap.String("requirement_id", req.ID.Hex()),
				zap.Int("image_count", len(req.ReferenceImages)))

			if err := d.analyzeImagesAsync(context.Background(), req.ID); err != nil {
				d.logger.Error("Asynchronous image analysis failed",
					zap.String("requirement_id", req.ID.Hex()),
					zap.Error(err))
			}
		}()
	}

	return req, nil
}

// CreateRequirementWithImagesAndWebsite handles the creation of a new requirement with both reference images and website.
func (d *Domain) CreateRequirementWithImagesAndWebsite(ctx context.Context, userID primitive.ObjectID, title, content, modelName, websiteURL string, imageFiles []*multipart.FileHeader) (*reqModel.Requirement, error) {
	d.logger.Info("Starting requirement creation process with images and website",
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("model_name", modelName),
		zap.String("website_url", websiteURL),
		zap.Int("image_count", len(imageFiles)))

	// Validate input
	if strings.TrimSpace(title) == "" {
		d.logger.Warn("Requirement creation validation failed: empty title",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: title cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(content) == "" {
		d.logger.Warn("Requirement creation validation failed: empty content",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: content cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(websiteURL) == "" {
		d.logger.Warn("Requirement creation validation failed: empty website URL",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: website URL cannot be empty", ErrInvalidInput)
	}
	if len(imageFiles) == 0 {
		d.logger.Warn("Requirement creation validation failed: no images provided",
			zap.String("user_id", userID.Hex()))
		return nil, fmt.Errorf("%w: no images provided", ErrInvalidInput)
	}

	// Validate URL format
	if !d.isValidURL(websiteURL) {
		d.logger.Warn("Requirement creation validation failed: invalid URL format",
			zap.String("user_id", userID.Hex()),
			zap.String("website_url", websiteURL))
		return nil, fmt.Errorf("%w: invalid URL format", ErrInvalidInput)
	}

	// Create requirement object with both images and website
	req := &reqModel.Requirement{
		UserID:          userID,
		Title:           strings.TrimSpace(title),
		Content:         strings.TrimSpace(content),
		ModelName:       strings.TrimSpace(modelName),
		ReferenceImages: []reqModel.ReferenceImage{},
		ReferenceWebsite: &reqModel.ReferenceWebsite{
			URL:       strings.TrimSpace(websiteURL),
			CreatedAt: time.Now(),
		},
		Questions: []reqModel.Question{},
		Answers:   []reqModel.Answer{},
		Status:    reqModel.StatusImageAnalyzing, // Start with image analysis
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Process uploaded images if file uploader is available
	if d.fileUploader != nil && len(imageFiles) > 0 {
		for i, imageFile := range imageFiles {
			// Upload image
			uploadResult, err := d.fileUploader.UploadImage(imageFile)
			if err != nil {
				d.logger.Error("Failed to upload image",
					zap.String("user_id", userID.Hex()),
					zap.Int("image_index", i),
					zap.Error(err))
				// Clean up any previously uploaded images
				d.cleanupUploadedImages(req.ReferenceImages)
				return nil, fmt.Errorf("failed to upload image %d: %w", i+1, err)
			}

			// Convert upload result to ReferenceImage
			refImage, err := d.convertUploadResultToReferenceImage(uploadResult)
			if err != nil {
				d.logger.Error("Failed to convert upload result",
					zap.String("user_id", userID.Hex()),
					zap.Int("image_index", i),
					zap.Error(err))
				// Clean up any previously uploaded images
				d.cleanupUploadedImages(req.ReferenceImages)
				return nil, fmt.Errorf("failed to process uploaded image %d: %w", i+1, err)
			}

			req.ReferenceImages = append(req.ReferenceImages, *refImage)
		}
	}

	// Save to repository
	err := d.repo.Create(ctx, req)
	if err != nil {
		d.logger.Error("Error creating requirement in repository",
			zap.String("user_id", userID.Hex()),
			zap.String("title", title),
			zap.Error(err))
		// Clean up uploaded images on repository error
		d.cleanupUploadedImages(req.ReferenceImages)
		return nil, RepositoryError(err)
	}

	d.logger.Info("Requirement creation with images and website completed successfully",
		zap.String("requirement_id", req.ID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("title", title),
		zap.String("website_url", websiteURL),
		zap.Int("image_count", len(req.ReferenceImages)))

	// Start asynchronous image analysis first, website analysis will be triggered after image analysis completes
	if len(req.ReferenceImages) > 0 && d.imageAnalyzer != nil {
		go func() {
			d.logger.Info("Starting asynchronous image analysis (website analysis will follow)",
				zap.String("requirement_id", req.ID.Hex()),
				zap.Int("image_count", len(req.ReferenceImages)))

			if err := d.analyzeImagesAsync(context.Background(), req.ID); err != nil {
				d.logger.Error("Asynchronous image analysis failed",
					zap.String("requirement_id", req.ID.Hex()),
					zap.Error(err))
			}
		}()
	}

	return req, nil
}

// GenerateQuestions uses AI to generate clarifying questions for a requirement.
func (d *Domain) GenerateQuestions(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID) (*reqModel.Requirement, error) {
	d.logger.Info("Starting question generation process",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	// Get the requirement and verify ownership
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for question generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt for question generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	// Check if image analysis is still in progress
	if req.Status == reqModel.StatusImageAnalyzing {
		d.logger.Warn("Question generation rejected: image analysis still in progress",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("current_status", req.Status))
		return nil, fmt.Errorf("cannot generate questions while image analysis is in progress")
	}

	// Ensure requirement is in the correct status for question generation
	if req.Status != reqModel.StatusPending {
		d.logger.Warn("Question generation rejected: requirement not in pending status",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("current_status", req.Status))
		return nil, fmt.Errorf("requirement must be in pending status for question generation")
	}

	// Build AI prompt for question generation
	systemPrompt := d.buildQuestionGenerationPrompt()
	userMessage := d.buildUserMessageForQuestions(req)

	messages := []model.Message{
		{
			Role:    model.RoleSystem,
			Content: systemPrompt,
		},
		{
			Role:    model.RoleUser,
			Content: userMessage,
		},
	}

	// Call LLM for question generation using dynamic model selection
	modelName := d.getModelNameForRequirement(req)
	llmRequest := &agentDomain.ChatCompletionRequest{
		Model:    modelName, // Use requirement-specific model or fallback to default
		Messages: messages,
		Stream:   false,
	}

	// Get LLM client for the specific model
	llmClient, err := d.llmClientFactory.GetLLMClient(ctx, modelName)
	if err != nil {
		d.logger.Error("Failed to get LLM client for question generation",
			zap.Error(err),
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("model_name", modelName))
		return nil, fmt.Errorf("failed to get LLM client for model '%s': %w", modelName, err)
	}

	d.logger.Debug("Calling LLM for question generation",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("model_name", modelName))
	response, err := llmClient.ChatCompletionNonStreaming(ctx, llmRequest)
	if err != nil {
		d.logger.Error("Error calling LLM for question generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, ErrQuestionGenerationFailed
	}

	// Parse the AI response
	questions, err := d.parseQuestionsFromAI(response.Content)
	if err != nil {
		d.logger.Error("Error parsing questions from AI response",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("ai_response", response.Content),
			zap.Error(err))
		return nil, ErrQuestionGenerationFailed
	}

	// Update requirement with generated questions
	req.Questions = questions
	req.Status = reqModel.StatusProcessing
	req.UpdatedAt = time.Now()

	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement with questions",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Question generation completed successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("question_count", len(questions)))

	return req, nil
}

// SubmitAnswers processes user answers to the generated questions.
func (d *Domain) SubmitAnswers(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID, answers []reqModel.Answer) (*reqModel.Requirement, error) {
	d.logger.Info("Starting answer submission process",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Int("answer_count", len(answers)))

	// Get the requirement and verify ownership
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for answer submission",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt for answer submission",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	// Validate answers
	if len(answers) == 0 {
		d.logger.Warn("Answer submission validation failed: no answers provided",
			zap.String("requirement_id", requirementID.Hex()))
		return nil, fmt.Errorf("%w: no answers provided", ErrInvalidInput)
	}

	// Validate that all question IDs exist
	questionMap := make(map[string]bool)
	for _, q := range req.Questions {
		questionMap[q.ID] = true
	}

	for _, answer := range answers {
		if strings.TrimSpace(answer.Text) == "" {
			d.logger.Warn("Answer submission validation failed: empty answer text",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("question_id", answer.QuestionID))
			return nil, fmt.Errorf("%w: answer text cannot be empty", ErrInvalidInput)
		}
		if !questionMap[answer.QuestionID] {
			d.logger.Warn("Answer submission validation failed: invalid question ID",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("question_id", answer.QuestionID))
			return nil, fmt.Errorf("%w: invalid question ID", ErrInvalidInput)
		}
	}

	// Set answer timestamps
	now := time.Now()
	for i := range answers {
		answers[i].AnsweredAt = now
	}

	// Update requirement with answers and set status to plan generating
	req.Answers = answers
	req.Status = reqModel.StatusPlanGenerating
	req.UpdatedAt = now

	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement with answers",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Answer submission completed successfully, starting development plan generation",
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("answer_count", len(answers)))

	// Start async development plan generation
	go func() {
		// Use a background context to avoid cancellation issues
		bgCtx := context.Background()
		planErr := d.generateDevelopmentPlan(bgCtx, requirementID)
		if planErr != nil {
			d.logger.Error("Failed to generate development plan",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Error(planErr))
		}
	}()

	return req, nil
}

// GetUserRequirements retrieves all requirements for a specific user.
func (d *Domain) GetUserRequirements(ctx context.Context, userID primitive.ObjectID) ([]*reqModel.Requirement, error) {
	d.logger.Debug("Getting user requirements",
		zap.String("user_id", userID.Hex()))

	requirements, err := d.repo.FindByUserID(ctx, userID)
	if err != nil {
		d.logger.Error("Error getting user requirements",
			zap.String("user_id", userID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Debug("Successfully retrieved user requirements",
		zap.String("user_id", userID.Hex()),
		zap.Int("requirement_count", len(requirements)))

	return requirements, nil
}

// GetUserRequirementsWithPagination retrieves user requirements with pagination support.
func (d *Domain) GetUserRequirementsWithPagination(ctx context.Context, userID primitive.ObjectID, page, limit int) ([]*reqModel.Requirement, int64, error) {
	d.logger.Debug("Getting user requirements with pagination",
		zap.String("user_id", userID.Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit))

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20 // Default limit
	}

	requirements, total, err := d.repo.FindByUserIDWithPagination(ctx, userID, page, limit)
	if err != nil {
		d.logger.Error("Error getting user requirements with pagination",
			zap.String("user_id", userID.Hex()),
			zap.Int("page", page),
			zap.Int("limit", limit),
			zap.Error(err))
		return nil, 0, RepositoryError(err)
	}

	d.logger.Debug("Successfully retrieved user requirements with pagination",
		zap.String("user_id", userID.Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.Int("requirement_count", len(requirements)),
		zap.Int64("total_count", total))

	return requirements, total, nil
}

// GetRequirementByID retrieves a specific requirement by ID with ownership verification.
func (d *Domain) GetRequirementByID(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID) (*reqModel.Requirement, error) {
	d.logger.Debug("Getting requirement by ID",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement by ID",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	// Verify ownership
	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	d.logger.Debug("Successfully retrieved requirement by ID",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	return req, nil
}

// GetRequirementsByStatus retrieves requirements by status for administrative purposes.
func (d *Domain) GetRequirementsByStatus(ctx context.Context, status string) ([]*reqModel.Requirement, error) {
	d.logger.Debug("Getting requirements by status",
		zap.String("status", status))

	// Validate status
	if status != reqModel.StatusPending && status != reqModel.StatusProcessing && status != reqModel.StatusPlanGenerating && status != reqModel.StatusCompleted {
		d.logger.Warn("Invalid status filter",
			zap.String("status", status))
		return nil, fmt.Errorf("%w: invalid status", ErrInvalidInput)
	}

	requirements, err := d.repo.FindByStatus(ctx, status)
	if err != nil {
		d.logger.Error("Error getting requirements by status",
			zap.String("status", status),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Debug("Successfully retrieved requirements by status",
		zap.String("status", status),
		zap.Int("requirement_count", len(requirements)))

	return requirements, nil
}

// GetUserRequirementsByStatus retrieves user requirements filtered by status.
func (d *Domain) GetUserRequirementsByStatus(ctx context.Context, userID primitive.ObjectID, status string) ([]*reqModel.Requirement, error) {
	d.logger.Debug("Getting user requirements by status",
		zap.String("user_id", userID.Hex()),
		zap.String("status", status))

	// Validate status
	if status != reqModel.StatusPending && status != reqModel.StatusProcessing && status != reqModel.StatusPlanGenerating && status != reqModel.StatusCompleted {
		d.logger.Warn("Invalid status filter",
			zap.String("user_id", userID.Hex()),
			zap.String("status", status))
		return nil, fmt.Errorf("%w: invalid status", ErrInvalidInput)
	}

	requirements, err := d.repo.FindByUserIDAndStatus(ctx, userID, status)
	if err != nil {
		d.logger.Error("Error getting user requirements by status",
			zap.String("user_id", userID.Hex()),
			zap.String("status", status),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Debug("Successfully retrieved user requirements by status",
		zap.String("user_id", userID.Hex()),
		zap.String("status", status),
		zap.Int("requirement_count", len(requirements)))

	return requirements, nil
}

// DeleteRequirement deletes a requirement with ownership verification and cleans up associated files.
func (d *Domain) DeleteRequirement(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID) error {
	d.logger.Info("Starting requirement deletion process",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	// Get the requirement and verify ownership
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for deletion",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	if req.UserID != userID {
		d.logger.Warn("Unauthorized deletion attempt",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return ErrUnauthorized
	}

	// Delete associated image files if file uploader is available
	if d.fileUploader != nil && len(req.ReferenceImages) > 0 {
		d.logger.Info("Deleting associated image files",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Int("image_count", len(req.ReferenceImages)))

		for _, image := range req.ReferenceImages {
			if image.FilePath != "" {
				d.logger.Debug("Deleting image file",
					zap.String("requirement_id", requirementID.Hex()),
					zap.String("image_id", image.ID),
					zap.String("file_path", image.FilePath))

				if err := d.fileUploader.DeleteFile(image.FilePath); err != nil {
					// Log the error but don't fail the entire deletion process
					// This ensures database consistency even if file deletion fails
					d.logger.Warn("Failed to delete image file, continuing with requirement deletion",
						zap.String("requirement_id", requirementID.Hex()),
						zap.String("image_id", image.ID),
						zap.String("file_path", image.FilePath),
						zap.Error(err))
				} else {
					d.logger.Debug("Image file deleted successfully",
						zap.String("requirement_id", requirementID.Hex()),
						zap.String("image_id", image.ID),
						zap.String("file_path", image.FilePath))
				}
			}
		}

		d.logger.Info("Finished deleting associated image files",
			zap.String("requirement_id", requirementID.Hex()))
	}

	// Delete the requirement from database
	err = d.repo.Delete(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error deleting requirement from database",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	d.logger.Info("Requirement deletion completed successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Int("deleted_images", len(req.ReferenceImages)))

	return nil
}

// isValidURL validates if the provided string is a valid URL
func (d *Domain) isValidURL(urlStr string) bool {
	// Basic URL validation using regex
	urlPattern := `^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$`
	matched, err := regexp.MatchString(urlPattern, urlStr)
	if err != nil {
		d.logger.Warn("Error validating URL",
			zap.String("url", urlStr),
			zap.Error(err))
		return false
	}
	return matched
}

// analyzeWebsiteAsync performs asynchronous website analysis for a requirement
func (d *Domain) analyzeWebsiteAsync(ctx context.Context, requirementID primitive.ObjectID) error {
	d.logger.Info("Starting asynchronous website analysis",
		zap.String("requirement_id", requirementID.Hex()))

	// Get the requirement
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for website analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	// Check if requirement is in the correct status for analysis
	if req.Status != reqModel.StatusWebsiteAnalyzing {
		d.logger.Warn("Requirement is not in website_analyzing status, skipping analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("current_status", req.Status))
		return nil
	}

	// Check if website reference exists
	if req.ReferenceWebsite == nil {
		d.logger.Error("No reference website found for analysis",
			zap.String("requirement_id", requirementID.Hex()))
		return fmt.Errorf("no reference website found for requirement %s", requirementID.Hex())
	}

	websiteURL := req.ReferenceWebsite.URL
	d.logger.Info("Analyzing website",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("website_url", websiteURL))

	// Analyze website with retry mechanism
	maxRetries := 2
	var analysis *reqModel.WebsiteAnalysis
	var lastErr error

	// Retry logic for website analysis
	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			d.logger.Info("Retrying website analysis",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("website_url", websiteURL),
				zap.Int("attempt", attempt))
		}

		// Get model name for this requirement
		modelName := d.getModelNameForRequirement(req)
		analysis, lastErr = d.websiteAnalyzer.AnalyzeWebsite(ctx, websiteURL, modelName)
		if lastErr == nil {
			// Success
			break
		}

		d.logger.Warn("Website analysis attempt failed",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("website_url", websiteURL),
			zap.Int("attempt", attempt),
			zap.Error(lastErr))

		// If this was the last attempt, log the final failure
		if attempt == maxRetries {
			d.logger.Error("Failed to analyze website after all retries",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("website_url", websiteURL),
				zap.Int("max_retries", maxRetries),
				zap.Error(lastErr))
		}
	}

	// Determine final status based on analysis results
	var newStatus string
	if lastErr == nil && analysis != nil {
		// Analysis successful
		req.ReferenceWebsite.Analysis = *analysis
		now := time.Now()
		req.ReferenceWebsite.AnalyzedAt = &now
		newStatus = reqModel.StatusPending

		d.logger.Info("Website analysis completed successfully",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("website_url", websiteURL))
	} else {
		// Analysis failed - keep in analyzing status for potential retry
		d.logger.Error("Website analysis failed completely",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("website_url", websiteURL),
			zap.Error(lastErr))
		return fmt.Errorf("website analysis failed for requirement %s: %w", requirementID.Hex(), lastErr)
	}

	// Update requirement status
	req.Status = newStatus
	req.UpdatedAt = time.Now()

	// Save updated requirement
	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement after website analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	d.logger.Info("Asynchronous website analysis completed",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("new_status", newStatus),
		zap.String("website_url", websiteURL))

	return nil
}

// buildQuestionGenerationPrompt creates the AI prompt for generating clarifying questions.
func (d *Domain) buildQuestionGenerationPrompt() string {
	return `你是一位专业的业务分析师。你的任务是生成4个多选题，帮助细化和更好地理解用户需求。

**重要：请用中文回复所有内容，包括问题文本和选项。**

请分析给定的需求，并生成4个单选题。每个问题应该包含4个选项：3个实用、具体的选择 + 1个"其他"选项。重点关注：

1. 功能需求 - 系统应该具体做什么？
2. 技术约束 - 技术栈、性能或实现方式偏好
3. 用户体验 - 目标用户、界面偏好、工作流程
4. 业务背景 - 优先级、范围或部署偏好

请返回JSON数组格式的回复，结构如下：
[
  {
    "id": "q1",
    "text": "这个功能的主要目标用户群体是？",
    "type": "choice",
    "options": ["终端用户", "系统管理员", "开发人员", "其他"],
    "category": "ux"
  },
  {
    "id": "q2",
    "text": "预期的实现优先级是？",
    "type": "choice", 
    "options": ["高优先级（立即实现）", "中等优先级（下个季度）", "低优先级（未来版本）", "其他"],
    "category": "business"
  }
]

确保：
- 生成4个问题，ID分别为：q1, q2, q3, q4
- 每个问题包含4个选项：3个具体选择 + 1个"其他"选项
- 前3个选项是实用、现实且互斥的
- 第4个选项始终是"其他"
- 问题能帮助澄清需求的最重要方面
- JSON格式正确且规范
- **所有内容必须使用中文**
- 只返回JSON数组，不要额外文本`
}

// AIQuestionResponse represents the expected structure from AI question generation.
type AIQuestionResponse struct {
	ID       string   `json:"id"`
	Text     string   `json:"text"`
	Type     string   `json:"type"`
	Options  []string `json:"options"`
	Category string   `json:"category"`
}

// parseQuestionsFromAI parses the AI response into Question objects.
func (d *Domain) parseQuestionsFromAI(aiResponse string) ([]reqModel.Question, error) {
	// Clean the response to extract just the JSON part
	cleanResponse := strings.TrimSpace(aiResponse)

	// Try to find JSON array in the response
	startIdx := strings.Index(cleanResponse, "[")
	endIdx := strings.LastIndex(cleanResponse, "]")

	if startIdx == -1 || endIdx == -1 || endIdx <= startIdx {
		return nil, fmt.Errorf("no valid JSON array found in AI response")
	}

	jsonPart := cleanResponse[startIdx : endIdx+1]

	var aiQuestions []AIQuestionResponse
	err := json.Unmarshal([]byte(jsonPart), &aiQuestions)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Convert to our Question model
	questions := make([]reqModel.Question, 0, len(aiQuestions))
	now := time.Now()

	for _, aiQ := range aiQuestions {
		if strings.TrimSpace(aiQ.ID) == "" || strings.TrimSpace(aiQ.Text) == "" {
			continue // Skip invalid questions
		}

		question := reqModel.Question{
			ID:        strings.TrimSpace(aiQ.ID),
			Text:      strings.TrimSpace(aiQ.Text),
			Type:      strings.TrimSpace(aiQ.Type),
			Options:   aiQ.Options,
			Category:  strings.TrimSpace(aiQ.Category),
			CreatedAt: now,
		}
		questions = append(questions, question)
	}

	if len(questions) == 0 {
		return nil, fmt.Errorf("no valid questions found in AI response")
	}

	return questions, nil
}

// generateDevelopmentPlan generates a detailed development plan based on requirement and answers.
func (d *Domain) generateDevelopmentPlan(ctx context.Context, requirementID primitive.ObjectID) error {
	d.logger.Info("Starting development plan generation",
		zap.String("requirement_id", requirementID.Hex()))

	// Get the requirement
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for plan generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	// Validate that requirement is in the correct state
	if req.Status != reqModel.StatusPlanGenerating {
		d.logger.Warn("Attempted to generate plan for requirement not in plan_generating state",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("current_status", req.Status))
		return fmt.Errorf("requirement is not in plan_generating state")
	}

	// Build AI prompt for development plan generation
	systemPrompt := d.buildDevelopmentPlanPrompt()
	userMessage := d.buildUserMessageForPlan(req)

	messages := []model.Message{
		{
			Role:    model.RoleSystem,
			Content: systemPrompt,
		},
		{
			Role:    model.RoleUser,
			Content: userMessage,
		},
	}

	// Call LLM for development plan generation using dynamic model selection
	modelName := d.getModelNameForRequirement(req)
	llmRequest := &agentDomain.ChatCompletionRequest{
		Model:    modelName, // Use requirement-specific model or fallback to default
		Messages: messages,
		Stream:   false,
	}

	// Get LLM client for the specific model
	llmClient, err := d.llmClientFactory.GetLLMClient(ctx, modelName)
	if err != nil {
		d.logger.Error("Failed to get LLM client for development plan generation",
			zap.Error(err),
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("model_name", modelName))
		return fmt.Errorf("failed to get LLM client for model '%s': %w", modelName, err)
	}

	d.logger.Debug("Calling LLM for development plan generation",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("model_name", modelName))
	response, err := llmClient.ChatCompletionNonStreaming(ctx, llmRequest)
	if err != nil {
		d.logger.Error("Error calling LLM for development plan generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return fmt.Errorf("LLM call failed: %w", err)
	}

	// Update requirement with generated development plan
	req.DevelopmentPlan = strings.TrimSpace(response.Content)
	req.Status = reqModel.StatusCompleted
	req.UpdatedAt = time.Now()

	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement with development plan",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	d.logger.Info("Development plan generation completed successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("plan_length", len(req.DevelopmentPlan)))

	return nil
}

// buildDevelopmentPlanPrompt creates the AI prompt for generating development plans.
func (d *Domain) buildDevelopmentPlanPrompt() string {
	return `你是一位资深的软件架构师和项目经理。你的任务是基于用户需求和用户对澄清问题的回答，以及以及用户上传的参考图的页面和功能，创建一个全面、详细的开发计划。

**重要：请用中文回复所有内容。**

根据用户需求和他们对澄清问题的回答，请生成一个详细的开发计划，包括以下内容：

## 1. 项目概述
- 将要构建内容的简要总结
- 关键目标和目的
- 目标用户和使用场景

## 2. 技术架构
- 技术栈推荐
- 系统架构概述
- 数据库设计考虑
- API设计方案

## 3. 功能分解
- 核心功能列表及描述
- 功能优先级（必需、应有、可选）
- 功能间的依赖关系

## 4. 开发阶段
- 第一阶段：基础建设（基本设置、核心基础设施）
- 第二阶段：核心功能（主要功能实现）
- 第三阶段：功能增强（附加功能、优化）
- 第四阶段：完善打磨（UI/UX改进、测试、部署）

## 5. 实施时间表
- 每个阶段的预估时间
- 关键里程碑和交付成果
- 关键路径考虑

## 6. 技术考虑
- 安全要求
- 性能要求
- 可扩展性考虑
- 测试策略

## 7. 风险分析
- 潜在技术风险
- 缓解策略
- 应急计划

## 8. 成功指标
- 如何衡量项目成功
- 关键绩效指标
- 用户验收标准

请提供一个详细、可操作的计划，开发团队可以据此执行。使用清晰的章节、要点和基于需求及用户回答的具体技术建议。

**所有回复内容必须使用中文，使用清晰的Markdown格式和合适的标题结构。**`
}

// buildUserMessageForPlan creates the user message for development plan generation, including image analysis and website analysis if available
func (d *Domain) buildUserMessageForPlan(req *reqModel.Requirement) string {
	var message strings.Builder

	message.WriteString("# User Requirement\n\n")
	message.WriteString(fmt.Sprintf("**Title:** %s\n\n", req.Title))
	message.WriteString(fmt.Sprintf("**Description:**\n%s\n\n", req.Content))

	// Include website analysis if available
	if req.ReferenceWebsite != nil && req.ReferenceWebsite.AnalyzedAt != nil {
		message.WriteString("# Reference Website Analysis\n\n")
		message.WriteString("The user has provided a reference website with detailed AI analysis. Consider these insights when creating the development plan:\n\n")
		message.WriteString(fmt.Sprintf("**Website URL:** %s\n\n", req.ReferenceWebsite.URL))

		// Visual Design
		if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" || len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
			message.WriteString("**Visual Design:**\n")
			if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" {
				message.WriteString(fmt.Sprintf("- Visual style: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle))
			}
			if len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
				message.WriteString(fmt.Sprintf("- Color scheme: %s\n", strings.Join(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme, ", ")))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Typography != "" {
				message.WriteString(fmt.Sprintf("- Typography: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Typography))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Layout != "" {
				message.WriteString(fmt.Sprintf("- Layout: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Layout))
			}
			message.WriteString("\n")
		}

		// Technical Aspects
		if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" || req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
			message.WriteString("**Technical Aspects:**\n")
			if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" {
				message.WriteString(fmt.Sprintf("- Framework/Tech: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech))
			}
			if req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
				message.WriteString(fmt.Sprintf("- Performance: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.Performance))
			}
			message.WriteString("\n")
		}

		// User Experience
		if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" || req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
			message.WriteString("**User Experience:**\n")
			if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" {
				message.WriteString(fmt.Sprintf("- Navigation: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Navigation))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
				message.WriteString(fmt.Sprintf("- Interaction Style: %s\n", req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.Accessibility != "" {
				message.WriteString(fmt.Sprintf("- Accessibility: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Accessibility))
			}
			message.WriteString("\n")
		}

		// Content Structure
		if len(req.ReferenceWebsite.Analysis.ContentStructure.ContentTypes) > 0 || req.ReferenceWebsite.Analysis.ContentStructure.InformationHierarchy != "" {
			message.WriteString("**Content Structure:**\n")
			if len(req.ReferenceWebsite.Analysis.ContentStructure.ContentTypes) > 0 {
				message.WriteString(fmt.Sprintf("- Content types: %s\n", strings.Join(req.ReferenceWebsite.Analysis.ContentStructure.ContentTypes, ", ")))
			}
			if req.ReferenceWebsite.Analysis.ContentStructure.InformationHierarchy != "" {
				message.WriteString(fmt.Sprintf("- Information hierarchy: %s\n", req.ReferenceWebsite.Analysis.ContentStructure.InformationHierarchy))
			}
			message.WriteString("\n")
		}

		// Summary
		if req.ReferenceWebsite.Analysis.Summary != "" {
			message.WriteString("**AI Summary:**\n")
			message.WriteString(fmt.Sprintf("%s\n\n", req.ReferenceWebsite.Analysis.Summary))
		}

		message.WriteString("Please incorporate these website analysis insights into the development plan, especially for technical architecture, UI/UX design decisions, content strategy, and overall system design.\n\n")
	}

	// Include image analysis if available
	if len(req.ReferenceImages) > 0 {
		message.WriteString("# Reference Images Analysis\n\n")
		message.WriteString("The user has provided reference images with detailed AI analysis. Consider these insights when creating the development plan:\n\n")

		for i, img := range req.ReferenceImages {
			if img.AnalyzedAt != nil { // Only include analyzed images
				message.WriteString(fmt.Sprintf("## Image %d: %s\n\n", i+1, img.FileName))

				// Visual Elements
				if len(img.Analysis.VisualElements.MainSubjects) > 0 || img.Analysis.VisualElements.Composition != "" {
					message.WriteString("**Visual Elements:**\n")
					if len(img.Analysis.VisualElements.MainSubjects) > 0 {
						message.WriteString(fmt.Sprintf("- Main subjects: %s\n", strings.Join(img.Analysis.VisualElements.MainSubjects, ", ")))
					}
					if img.Analysis.VisualElements.Composition != "" {
						message.WriteString(fmt.Sprintf("- Composition: %s\n", img.Analysis.VisualElements.Composition))
					}
					if len(img.Analysis.VisualElements.ColorPalette) > 0 {
						message.WriteString(fmt.Sprintf("- Color palette: %s\n", strings.Join(img.Analysis.VisualElements.ColorPalette, ", ")))
					}
					if img.Analysis.VisualElements.LightingStyle != "" {
						message.WriteString(fmt.Sprintf("- Lighting: %s\n", img.Analysis.VisualElements.LightingStyle))
					}
					message.WriteString("\n")
				}

				// Technical Style
				if img.Analysis.TechnicalStyle.ArtisticStyle != "" || img.Analysis.TechnicalStyle.Medium != "" {
					message.WriteString("**Technical & Style:**\n")
					if img.Analysis.TechnicalStyle.Medium != "" {
						message.WriteString(fmt.Sprintf("- Medium: %s\n", img.Analysis.TechnicalStyle.Medium))
					}
					if img.Analysis.TechnicalStyle.ArtisticStyle != "" {
						message.WriteString(fmt.Sprintf("- Style: %s\n", img.Analysis.TechnicalStyle.ArtisticStyle))
					}
					message.WriteString("\n")
				}

				// Emotional Tone
				if img.Analysis.EmotionalTone.OverallMood != "" || img.Analysis.EmotionalTone.EmotionalTone != "" {
					message.WriteString("**Emotional & Atmospheric:**\n")
					if img.Analysis.EmotionalTone.OverallMood != "" {
						message.WriteString(fmt.Sprintf("- Mood: %s\n", img.Analysis.EmotionalTone.OverallMood))
					}
					if img.Analysis.EmotionalTone.EmotionalTone != "" {
						message.WriteString(fmt.Sprintf("- Tone: %s\n", img.Analysis.EmotionalTone.EmotionalTone))
					}
					message.WriteString("\n")
				}

				// Summary
				if img.Analysis.Summary != "" {
					message.WriteString("**AI Summary:**\n")
					message.WriteString(fmt.Sprintf("%s\n\n", img.Analysis.Summary))
				}
			}
		}

		message.WriteString("Please incorporate these visual and stylistic insights into the development plan, especially for UI/UX design decisions, color schemes, layout preferences, and overall aesthetic direction.\n\n")
	}

	if len(req.Questions) > 0 && len(req.Answers) > 0 {
		message.WriteString("# User Answers to Clarifying Questions\n\n")

		// Create a map of question ID to question for easy lookup
		questionMap := make(map[string]reqModel.Question)
		for _, q := range req.Questions {
			questionMap[q.ID] = q
		}

		for i, answer := range req.Answers {
			if question, exists := questionMap[answer.QuestionID]; exists {
				message.WriteString(fmt.Sprintf("## Question %d: %s\n", i+1, question.Text))

				// Show the selected option if it's a choice question
				if question.Type == "choice" && answer.SelectedIndex >= 0 && answer.SelectedIndex < len(question.Options) {
					selectedOption := question.Options[answer.SelectedIndex]
					message.WriteString(fmt.Sprintf("**Selected:** %s\n", selectedOption))
				}

				// Also show the text answer if available
				if strings.TrimSpace(answer.Text) != "" {
					message.WriteString(fmt.Sprintf("**Answer:** %s\n", answer.Text))
				}

				message.WriteString("\n")
			}
		}
	}

	message.WriteString("---\n\n")
	message.WriteString("Please generate a comprehensive development plan based on the above requirement, image analysis insights, and user answers. Pay special attention to the visual and stylistic elements identified in the image analysis when planning the UI/UX aspects.")

	return message.String()
}

// GenerateWireframe generates an HTML wireframe based on the development plan.
func (d *Domain) GenerateWireframe(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID) (*reqModel.Requirement, error) {
	d.logger.Info("Starting wireframe generation",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()))

	// Get the requirement and verify ownership
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for wireframe generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt for wireframe generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	// Validate that requirement has development plan
	if strings.TrimSpace(req.DevelopmentPlan) == "" {
		d.logger.Warn("Attempted to generate wireframe without development plan",
			zap.String("requirement_id", requirementID.Hex()))
		return nil, fmt.Errorf("development plan is required to generate wireframe")
	}

	// Update status to wireframe_generating immediately
	req.Status = reqModel.StatusWireframeGenerating
	req.UpdatedAt = time.Now()
	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement status to wireframe_generating",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Updated requirement status to wireframe_generating",
		zap.String("requirement_id", requirementID.Hex()))

	// Build AI prompt for wireframe generation
	systemPrompt := d.buildWireframeGenerationPrompt()
	userMessage := d.buildUserMessageForWireframe(req)

	// Prepare user message with images if available
	userMsg := model.Message{
		Role:    model.RoleUser,
		Content: userMessage,
	}

	// Add images if available and analyzed
	var imageBase64s []string
	if len(req.ReferenceImages) > 0 {
		d.logger.Info("Loading reference images for wireframe generation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Int("image_count", len(req.ReferenceImages)))

		for _, img := range req.ReferenceImages {
			if img.AnalyzedAt != nil { // Only include analyzed images
				imageBase64, err := d.loadImageAsBase64(img.FilePath)
				if err != nil {
					d.logger.Warn("Failed to load image for wireframe generation",
						zap.String("requirement_id", requirementID.Hex()),
						zap.String("image_id", img.ID),
						zap.String("image_path", img.FilePath),
						zap.Error(err))
					continue
				}
				imageBase64s = append(imageBase64s, imageBase64)
				d.logger.Debug("Successfully loaded image for wireframe generation",
					zap.String("requirement_id", requirementID.Hex()),
					zap.String("image_id", img.ID))
			}
		}

		if len(imageBase64s) > 0 {
			userMsg.Images = imageBase64s
			d.logger.Info("Added images to wireframe generation request",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("images_loaded", len(imageBase64s)))
		}
	}

	messages := []model.Message{
		{
			Role:    model.RoleSystem,
			Content: systemPrompt,
		},
		userMsg,
	}

	// Get model name for this requirement
	modelName := d.getModelNameForRequirement(req)

	// Generate 5 wireframes concurrently
	d.logger.Info("Starting concurrent generation of 5 wireframes",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("model_name", modelName))

	// Use sync package for goroutines
	type wireframeResult struct {
		index     int
		wireframe *reqModel.Wireframe
		err       error
	}

	results := make(chan wireframeResult, 5)

	// Generate 5 wireframes concurrently with retry mechanism
	for i := 0; i < 5; i++ {
		go func(index int) {
			d.logger.Debug("Starting wireframe generation",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("wireframe_index", index))

			// Prepare LLM request for this wireframe with temperature for creativity
			llmRequest := &agentDomain.ChatCompletionRequest{
				Model:       modelName, // Use requirement-specific model or fallback to default
				Messages:    messages,
				Stream:      false,
				Temperature: func() *float32 { v := float32(0.8); return &v }(), // Set temperature for creative variety in wireframe generation
			}

			// Call LLM with retry mechanism for network issues
			response, err := d.callLLMWithRetry(ctx, llmRequest, modelName, index, requirementID.Hex())
			if err != nil {
				d.logger.Error("Error calling LLM for wireframe generation after retries",
					zap.String("requirement_id", requirementID.Hex()),
					zap.Int("wireframe_index", index),
					zap.Error(err))
				results <- wireframeResult{index: index, err: err}
				return
			}

			// Parse single wireframe response
			htmlContent, err := d.parseWireframeFromAI(response.Content)
			if err != nil {
				d.logger.Error("Error parsing wireframe from AI response",
					zap.String("requirement_id", requirementID.Hex()),
					zap.Int("wireframe_index", index),
					zap.Error(err))
				results <- wireframeResult{index: index, err: err}
				return
			}

			// Create wireframe object
			wireframe := &reqModel.Wireframe{
				ID:          fmt.Sprintf("wireframe_%d", index+1),
				Title:       fmt.Sprintf("线框图方案 %d", index+1),
				Description: fmt.Sprintf("基于开发计划生成的第%d个线框图方案", index+1),
				HTML:        htmlContent,
				GeneratedAt: time.Now(),
			}

			d.logger.Debug("Successfully generated wireframe",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("wireframe_index", index),
				zap.String("wireframe_id", wireframe.ID))

			results <- wireframeResult{index: index, wireframe: wireframe}
		}(i)
	}

	// Collect results
	var wireframes []reqModel.Wireframe
	var errors []error

	for i := 0; i < 5; i++ {
		result := <-results
		if result.err != nil {
			errors = append(errors, result.err)
		} else if result.wireframe != nil {
			wireframes = append(wireframes, *result.wireframe)
		}
	}

	// Check if we have at least one successful wireframe
	if len(wireframes) == 0 {
		d.logger.Error("All wireframe generations failed",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Int("error_count", len(errors)))
		// Revert status on error
		req.Status = reqModel.StatusPlanGenerating
		req.UpdatedAt = time.Now()
		d.repo.Update(ctx, req)
		return nil, fmt.Errorf("all wireframe generations failed, errors: %v", errors)
	}

	d.logger.Info("Wireframe generation completed",
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("successful_wireframes", len(wireframes)),
		zap.Int("failed_wireframes", len(errors)))

	// Update requirement with generated wireframes and completed status
	req.Wireframes = wireframes
	// For backward compatibility, also set the first wireframe as the legacy single wireframe
	if len(wireframes) > 0 {
		req.WireframeHTML = wireframes[0].HTML
	}
	req.Status = reqModel.StatusCompleted
	req.UpdatedAt = time.Now()

	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement with wireframes",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Wireframes generation completed successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.Int("wireframes_count", len(req.Wireframes)))

	return req, nil
}

// loadImageAsBase64 loads an image file and encodes it as base64 with data URI prefix
func (d *Domain) loadImageAsBase64(imagePath string) (string, error) {
	if d.uploadDir == "" {
		return "", fmt.Errorf("upload directory not configured")
	}

	// Construct full file path
	fullPath := filepath.Join(d.uploadDir, imagePath)

	// Security check: ensure the path is within upload directory
	if !strings.HasPrefix(fullPath, d.uploadDir) {
		return "", fmt.Errorf("invalid image path: %s", imagePath)
	}

	// Read file content
	file, err := os.Open(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read image data: %w", err)
	}

	// Determine MIME type based on file extension
	ext := strings.ToLower(filepath.Ext(imagePath))
	var mimeType string
	switch ext {
	case ".jpg", ".jpeg":
		mimeType = "image/jpeg"
	case ".png":
		mimeType = "image/png"
	case ".gif":
		mimeType = "image/gif"
	case ".webp":
		mimeType = "image/webp"
	default:
		mimeType = "image/jpeg" // Default fallback
	}

	encoded := base64.StdEncoding.EncodeToString(data)
	return fmt.Sprintf("data:%s;base64,%s", mimeType, encoded), nil
}

// buildWireframeGenerationPrompt creates the AI prompt for generating HTML wireframes.
func (d *Domain) buildWireframeGenerationPrompt() string {
	return `你是一位资深的UI/UX设计师和前端开发者。你的任务是基于详细的开发计划创建一个完整、交互式的HTML线框图。

**重要：请用中文回复所有内容，包括HTML注释和占位文本。**

根据开发计划，请生成一个完整、交互式的HTML线框图，要求：

## 技术要求：
1. **完整HTML文档**：包含<!DOCTYPE html>、<html>、<head>和<body>标签
2. **响应式设计**：使用CSS Grid/Flexbox进行响应式布局
3. **交互元素**：添加基本JavaScript处理按钮点击、表单交互等
4. **现代样式**：使用简洁、现代的CSS，注重间距和排版
5. **功能导航**：使导航链接和按钮可交互
6. **表单交互**：使表单功能完整并带有验证反馈

## 设计原则：
- 遵循用户体验最佳实践
- 确保界面清晰易用
- 适当使用颜色和排版
- 保持视觉层次分明
- 考虑不同设备的兼容性

**重要说明：**
- 生成的HTML应该是完整、自包含的文档
- 所有文本内容、注释和占位文字都必须使用中文
- 确保HTML有效且结构良好
- 基于开发计划实现所需的功能`
}

// buildUserMessageForWireframe creates the user message for wireframe generation, including image analysis and website analysis if available
func (d *Domain) buildUserMessageForWireframe(req *reqModel.Requirement) string {
	var message strings.Builder

	message.WriteString("# Project Information\n\n")
	message.WriteString(fmt.Sprintf("**Title:** %s\n\n", req.Title))
	message.WriteString(fmt.Sprintf("**Description:** %s\n\n", req.Content))

	// Include website analysis if available
	if req.ReferenceWebsite != nil && req.ReferenceWebsite.AnalyzedAt != nil {
		message.WriteString("# Reference Website Analysis\n\n")
		message.WriteString("The user has provided a reference website with detailed AI analysis. Use these insights to guide the wireframe design:\n\n")
		message.WriteString(fmt.Sprintf("**Website URL:** %s\n\n", req.ReferenceWebsite.URL))

		// Visual Design
		if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" || len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
			message.WriteString("**Visual Design:**\n")
			if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" {
				message.WriteString(fmt.Sprintf("- Visual style: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle))
			}
			if len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
				message.WriteString(fmt.Sprintf("- Color scheme: %s\n", strings.Join(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme, ", ")))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Typography != "" {
				message.WriteString(fmt.Sprintf("- Typography: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Typography))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Layout != "" {
				message.WriteString(fmt.Sprintf("- Layout: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Layout))
			}
			message.WriteString("\n")
		}

		// Technical Aspects
		if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" || req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
			message.WriteString("**Technical Aspects:**\n")
			if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" {
				message.WriteString(fmt.Sprintf("- Framework/Tech: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech))
			}
			if req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
				message.WriteString(fmt.Sprintf("- Performance: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.Performance))
			}
			message.WriteString("\n")
		}

		// User Experience
		if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" || req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
			message.WriteString("**User Experience:**\n")
			if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" {
				message.WriteString(fmt.Sprintf("- Navigation: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Navigation))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
				message.WriteString(fmt.Sprintf("- Interaction Style: %s\n", req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.Responsiveness != "" {
				message.WriteString(fmt.Sprintf("- Responsiveness: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Responsiveness))
			}
			message.WriteString("\n")
		}

		// Summary
		if req.ReferenceWebsite.Analysis.Summary != "" {
			message.WriteString("**AI Summary:**\n")
			message.WriteString(fmt.Sprintf("%s\n\n", req.ReferenceWebsite.Analysis.Summary))
		}

		message.WriteString("Please reflect these website design and user experience elements in the wireframe. Consider the layout patterns, navigation structure, visual hierarchy, and interaction patterns when creating the HTML wireframe.\n\n")
	}

	// Include image analysis if available
	if len(req.ReferenceImages) > 0 {
		message.WriteString("# Reference Images Analysis\n\n")
		message.WriteString("The user has provided reference images with detailed AI analysis. Use these insights to guide the wireframe design:\n\n")

		for i, img := range req.ReferenceImages {
			if img.AnalyzedAt != nil { // Only include analyzed images
				message.WriteString(fmt.Sprintf("## Image %d: %s\n\n", i+1, img.FileName))

				// Visual Elements
				if len(img.Analysis.VisualElements.MainSubjects) > 0 || img.Analysis.VisualElements.Composition != "" {
					message.WriteString("**Visual Elements:**\n")
					if len(img.Analysis.VisualElements.MainSubjects) > 0 {
						message.WriteString(fmt.Sprintf("- Main subjects: %s\n", strings.Join(img.Analysis.VisualElements.MainSubjects, ", ")))
					}
					if img.Analysis.VisualElements.Composition != "" {
						message.WriteString(fmt.Sprintf("- Composition: %s\n", img.Analysis.VisualElements.Composition))
					}
					if len(img.Analysis.VisualElements.ColorPalette) > 0 {
						message.WriteString(fmt.Sprintf("- Color palette: %s\n", strings.Join(img.Analysis.VisualElements.ColorPalette, ", ")))
					}
					if img.Analysis.VisualElements.LightingStyle != "" {
						message.WriteString(fmt.Sprintf("- Lighting: %s\n", img.Analysis.VisualElements.LightingStyle))
					}
					message.WriteString("\n")
				}

				// Technical Style
				if img.Analysis.TechnicalStyle.ArtisticStyle != "" || img.Analysis.TechnicalStyle.Medium != "" {
					message.WriteString("**Technical & Style:**\n")
					if img.Analysis.TechnicalStyle.Medium != "" {
						message.WriteString(fmt.Sprintf("- Medium: %s\n", img.Analysis.TechnicalStyle.Medium))
					}
					if img.Analysis.TechnicalStyle.ArtisticStyle != "" {
						message.WriteString(fmt.Sprintf("- Style: %s\n", img.Analysis.TechnicalStyle.ArtisticStyle))
					}
					message.WriteString("\n")
				}

				// Emotional Tone
				if img.Analysis.EmotionalTone.OverallMood != "" || img.Analysis.EmotionalTone.EmotionalTone != "" {
					message.WriteString("**Emotional & Atmospheric:**\n")
					if img.Analysis.EmotionalTone.OverallMood != "" {
						message.WriteString(fmt.Sprintf("- Mood: %s\n", img.Analysis.EmotionalTone.OverallMood))
					}
					if img.Analysis.EmotionalTone.EmotionalTone != "" {
						message.WriteString(fmt.Sprintf("- Tone: %s\n", img.Analysis.EmotionalTone.EmotionalTone))
					}
					message.WriteString("\n")
				}

				// Summary
				if img.Analysis.Summary != "" {
					message.WriteString("**AI Summary:**\n")
					message.WriteString(fmt.Sprintf("%s\n\n", img.Analysis.Summary))
				}
			}
		}

		message.WriteString("Please reflect these visual and stylistic elements in the wireframe design. Consider the color palette, composition style, mood, and overall aesthetic when creating the HTML wireframe.\n\n")
	}

	if len(req.Questions) > 0 && len(req.Answers) > 0 {
		message.WriteString("# User Requirements (from Q&A)\n\n")

		// Create a map of question ID to question for easy lookup
		questionMap := make(map[string]reqModel.Question)
		for _, q := range req.Questions {
			questionMap[q.ID] = q
		}

		for i, answer := range req.Answers {
			if question, exists := questionMap[answer.QuestionID]; exists {
				message.WriteString(fmt.Sprintf("**Q%d:** %s\n", i+1, question.Text))

				// Show the selected option if it's a choice question
				if question.Type == "choice" && answer.SelectedIndex >= 0 && answer.SelectedIndex < len(question.Options) {
					selectedOption := question.Options[answer.SelectedIndex]
					message.WriteString(fmt.Sprintf("**A%d:** %s\n\n", i+1, selectedOption))
				}

				// Also show the text answer if available
				if strings.TrimSpace(answer.Text) != "" {
					message.WriteString(fmt.Sprintf("**Additional Notes:** %s\n\n", answer.Text))
				}
			}
		}
	}

	message.WriteString("# Development Plan\n\n")
	message.WriteString(req.DevelopmentPlan)

	message.WriteString("\n\n---\n\n")
	message.WriteString("Please generate an interactive HTML wireframe based on the above information. Focus on the key features and user flows described in the development plan, while incorporating the visual style and aesthetic preferences identified in the image analysis.")

	return message.String()
}

// parseWireframeFromAI parses and cleans the AI response to extract HTML wireframe.
func (d *Domain) parseWireframeFromAI(aiResponse string) (string, error) {
	// Clean the response to extract just the HTML part
	response := strings.TrimSpace(aiResponse)

	// Look for HTML document start
	startIdx := strings.Index(response, "<!DOCTYPE html>")
	if startIdx == -1 {
		startIdx = strings.Index(response, "<html")
		if startIdx == -1 {
			return "", fmt.Errorf("no valid HTML document found in AI response")
		}
	}

	// Look for HTML document end
	endIdx := strings.LastIndex(response, "</html>")
	if endIdx == -1 {
		return "", fmt.Errorf("incomplete HTML document in AI response")
	}

	// Extract HTML content
	htmlContent := response[startIdx : endIdx+7] // +7 for "</html>"

	// Basic security check - remove any potentially dangerous scripts
	// Note: In production, you might want to use a proper HTML sanitizer
	if strings.Contains(strings.ToLower(htmlContent), "<script") {
		// For now, we'll allow scripts but add a comment
		d.logger.Warn("Generated wireframe contains JavaScript - ensuring it's safe")
	}

	if len(htmlContent) < 100 {
		return "", fmt.Errorf("generated HTML wireframe is too short to be valid")
	}

	return htmlContent, nil
}

// WireframeResponse represents the expected JSON response structure from AI for multiple wireframes
type WireframeResponse struct {
	Wireframes []struct {
		Title       string `json:"title"`
		Description string `json:"description"`
		HTML        string `json:"html"`
	} `json:"wireframes"`
}

// parseWireframesFromAI parses and cleans the AI response to extract multiple HTML wireframes.
func (d *Domain) parseWireframesFromAI(aiResponse string) ([]reqModel.Wireframe, error) {
	// Clean the response to extract just the JSON part
	response := strings.TrimSpace(aiResponse)

	// Try to extract JSON from markdown code blocks first
	jsonContent := d.extractJSONFromResponse(response)
	if jsonContent == "" {
		return nil, fmt.Errorf("no valid JSON found in AI response")
	}

	// Parse JSON response
	var wireframeResponse WireframeResponse
	if err := json.Unmarshal([]byte(jsonContent), &wireframeResponse); err != nil {
		d.logger.Error("Failed to parse wireframes JSON",
			zap.String("json_content", jsonContent),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse wireframes JSON: %w", err)
	}

	// Validate that we have wireframes
	if len(wireframeResponse.Wireframes) == 0 {
		return nil, fmt.Errorf("no wireframes found in AI response")
	}

	// Convert to our model format
	var result []reqModel.Wireframe
	now := time.Now()

	for i, wf := range wireframeResponse.Wireframes {
		// Validate HTML content
		if strings.TrimSpace(wf.HTML) == "" {
			d.logger.Warn("Empty HTML content for wireframe",
				zap.Int("wireframe_index", i),
				zap.String("title", wf.Title))
			continue
		}

		// Basic HTML validation
		if !strings.Contains(wf.HTML, "<html") && !strings.Contains(wf.HTML, "<!DOCTYPE html>") {
			d.logger.Warn("Invalid HTML structure for wireframe",
				zap.Int("wireframe_index", i),
				zap.String("title", wf.Title))
			continue
		}

		// Basic security check
		if strings.Contains(strings.ToLower(wf.HTML), "<script") {
			d.logger.Warn("Generated wireframe contains JavaScript - ensuring it's safe",
				zap.String("title", wf.Title))
		}

		wireframe := reqModel.Wireframe{
			ID:          fmt.Sprintf("wf_%d_%d", now.Unix(), i),
			Title:       wf.Title,
			Description: wf.Description,
			HTML:        wf.HTML,
			GeneratedAt: now,
		}

		result = append(result, wireframe)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("no valid wireframes after validation")
	}

	d.logger.Info("Successfully parsed wireframes from AI response",
		zap.Int("total_wireframes", len(result)))

	return result, nil
}

// extractJSONFromResponse extracts JSON content from AI response, handling markdown code blocks
func (d *Domain) extractJSONFromResponse(response string) string {
	// First try to find JSON within markdown code blocks
	jsonBlockPattern := regexp.MustCompile("```json\\s*\\n([\\s\\S]*?)\\n```")
	matches := jsonBlockPattern.FindStringSubmatch(response)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// Fallback: look for JSON content by finding balanced braces
	response = strings.TrimSpace(response)

	var jsonStart, jsonEnd int
	braceCount := 0
	foundStart := false

	for i, char := range response {
		if char == '{' {
			if !foundStart {
				jsonStart = i
				foundStart = true
			}
			braceCount++
		} else if char == '}' && foundStart {
			braceCount--
			if braceCount == 0 {
				jsonEnd = i + 1
				break
			}
		}
	}

	if foundStart && jsonEnd > jsonStart {
		return response[jsonStart:jsonEnd]
	}

	return ""
}

// Helper methods for image handling

// cleanupUploadedImages removes uploaded image files in case of errors
func (d *Domain) cleanupUploadedImages(images []reqModel.ReferenceImage) {
	if d.fileUploader == nil {
		return
	}

	for _, img := range images {
		if img.FilePath != "" {
			err := d.fileUploader.DeleteFile(img.FilePath)
			if err != nil {
				d.logger.Warn("Failed to cleanup uploaded image file",
					zap.String("file_path", img.FilePath),
					zap.Error(err))
			}
		}
	}
}

// convertUploadResultToReferenceImage converts file upload result to ReferenceImage model
func (d *Domain) convertUploadResultToReferenceImage(uploadResult *fileupload.UploadedFile) (*reqModel.ReferenceImage, error) {
	if uploadResult == nil {
		return nil, fmt.Errorf("upload result is nil")
	}

	refImage := &reqModel.ReferenceImage{
		ID:         uploadResult.ID,
		FileName:   uploadResult.FileName,
		FilePath:   uploadResult.FilePath,
		FileSize:   uploadResult.FileSize,
		MimeType:   uploadResult.MimeType,
		UploadedAt: time.Now(),
		Analysis:   reqModel.ImageAnalysis{}, // Empty analysis, to be filled later
	}

	return refImage, nil
}

// buildUserMessageForQuestions creates the user message for question generation, including image analysis and website analysis if available
func (d *Domain) buildUserMessageForQuestions(req *reqModel.Requirement) string {
	var message strings.Builder

	message.WriteString(fmt.Sprintf("Title: %s\n\nContent: %s", req.Title, req.Content))

	// Include website analysis if available
	if req.ReferenceWebsite != nil && req.ReferenceWebsite.AnalyzedAt != nil {
		message.WriteString("\n\n## Reference Website Analysis\n\n")
		message.WriteString("The user has provided a reference website. Here's the AI analysis of this website:\n\n")
		message.WriteString(fmt.Sprintf("**Website URL:** %s\n\n", req.ReferenceWebsite.URL))

		// Visual Design
		if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" || len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
			message.WriteString("**Visual Design:**\n")
			if req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle != "" {
				message.WriteString(fmt.Sprintf("- Overall style: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.VisualStyle))
			}
			if len(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme) > 0 {
				message.WriteString(fmt.Sprintf("- Color scheme: %s\n", strings.Join(req.ReferenceWebsite.Analysis.VisualDesign.ColorScheme, ", ")))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Typography != "" {
				message.WriteString(fmt.Sprintf("- Typography: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Typography))
			}
			if req.ReferenceWebsite.Analysis.VisualDesign.Layout != "" {
				message.WriteString(fmt.Sprintf("- Layout: %s\n", req.ReferenceWebsite.Analysis.VisualDesign.Layout))
			}
			message.WriteString("\n")
		}

		// Technical Aspects
		if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" || req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
			message.WriteString("**Technical Aspects:**\n")
			if req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech != "" {
				message.WriteString(fmt.Sprintf("- Framework/Tech: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.FrameworkTech))
			}
			if req.ReferenceWebsite.Analysis.TechnicalAspects.Performance != "" {
				message.WriteString(fmt.Sprintf("- Performance: %s\n", req.ReferenceWebsite.Analysis.TechnicalAspects.Performance))
			}
			message.WriteString("\n")
		}

		// User Experience
		if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" || req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
			message.WriteString("**User Experience:**\n")
			if req.ReferenceWebsite.Analysis.UserExperience.Navigation != "" {
				message.WriteString(fmt.Sprintf("- Navigation: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Navigation))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle != "" {
				message.WriteString(fmt.Sprintf("- Interaction Style: %s\n", req.ReferenceWebsite.Analysis.UserExperience.InteractionStyle))
			}
			if req.ReferenceWebsite.Analysis.UserExperience.Responsiveness != "" {
				message.WriteString(fmt.Sprintf("- Responsiveness: %s\n", req.ReferenceWebsite.Analysis.UserExperience.Responsiveness))
			}
			message.WriteString("\n")
		}

		// Summary
		if req.ReferenceWebsite.Analysis.Summary != "" {
			message.WriteString("**AI Summary:**\n")
			message.WriteString(fmt.Sprintf("%s\n\n", req.ReferenceWebsite.Analysis.Summary))
		}

		message.WriteString("Please consider these website analysis insights when generating questions to better understand the user's requirements and design preferences.\n")
	}

	// Include image analysis if available
	if len(req.ReferenceImages) > 0 {
		message.WriteString("\n\n## Reference Images Analysis\n\n")
		message.WriteString("The user has provided reference images. Here's the AI analysis of these images:\n\n")

		for i, img := range req.ReferenceImages {
			if img.AnalyzedAt != nil { // Only include analyzed images
				message.WriteString(fmt.Sprintf("### Image %d: %s\n\n", i+1, img.FileName))

				// Visual Elements
				if len(img.Analysis.VisualElements.MainSubjects) > 0 || img.Analysis.VisualElements.Composition != "" {
					message.WriteString("**Visual Elements:**\n")
					if len(img.Analysis.VisualElements.MainSubjects) > 0 {
						message.WriteString(fmt.Sprintf("- Main subjects: %s\n", strings.Join(img.Analysis.VisualElements.MainSubjects, ", ")))
					}
					if img.Analysis.VisualElements.Composition != "" {
						message.WriteString(fmt.Sprintf("- Composition: %s\n", img.Analysis.VisualElements.Composition))
					}
					if len(img.Analysis.VisualElements.ColorPalette) > 0 {
						message.WriteString(fmt.Sprintf("- Color palette: %s\n", strings.Join(img.Analysis.VisualElements.ColorPalette, ", ")))
					}
					if img.Analysis.VisualElements.LightingStyle != "" {
						message.WriteString(fmt.Sprintf("- Lighting: %s\n", img.Analysis.VisualElements.LightingStyle))
					}
					message.WriteString("\n")
				}

				// Technical Style
				if img.Analysis.TechnicalStyle.ArtisticStyle != "" || img.Analysis.TechnicalStyle.Medium != "" {
					message.WriteString("**Technical & Style:**\n")
					if img.Analysis.TechnicalStyle.Medium != "" {
						message.WriteString(fmt.Sprintf("- Medium: %s\n", img.Analysis.TechnicalStyle.Medium))
					}
					if img.Analysis.TechnicalStyle.ArtisticStyle != "" {
						message.WriteString(fmt.Sprintf("- Style: %s\n", img.Analysis.TechnicalStyle.ArtisticStyle))
					}
					message.WriteString("\n")
				}

				// Emotional Tone
				if img.Analysis.EmotionalTone.OverallMood != "" || img.Analysis.EmotionalTone.EmotionalTone != "" {
					message.WriteString("**Emotional & Atmospheric:**\n")
					if img.Analysis.EmotionalTone.OverallMood != "" {
						message.WriteString(fmt.Sprintf("- Mood: %s\n", img.Analysis.EmotionalTone.OverallMood))
					}
					if img.Analysis.EmotionalTone.EmotionalTone != "" {
						message.WriteString(fmt.Sprintf("- Tone: %s\n", img.Analysis.EmotionalTone.EmotionalTone))
					}
					message.WriteString("\n")
				}

				// Summary
				if img.Analysis.Summary != "" {
					message.WriteString("**AI Summary:**\n")
					message.WriteString(fmt.Sprintf("%s\n\n", img.Analysis.Summary))
				}
			}
		}

		message.WriteString("Please consider these image analysis insights when generating questions to better understand the user's requirements and design preferences.\n")
	}

	return message.String()
}

// analyzeImagesAsync performs asynchronous image analysis for a requirement
func (d *Domain) analyzeImagesAsync(ctx context.Context, requirementID primitive.ObjectID) error {
	d.logger.Info("Starting asynchronous image analysis",
		zap.String("requirement_id", requirementID.Hex()))

	// Get the requirement
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for image analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	// Check if requirement is in the correct status for analysis
	if req.Status != reqModel.StatusImageAnalyzing {
		d.logger.Warn("Requirement is not in image_analyzing status, skipping analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("current_status", req.Status))
		return nil
	}

	// Analyze each image with retry mechanism
	successfulAnalyses := 0
	totalImages := len(req.ReferenceImages)
	maxRetries := 2 // Maximum retries per image

	for i := range req.ReferenceImages {
		imageID := req.ReferenceImages[i].ID
		imagePath := req.ReferenceImages[i].FilePath

		d.logger.Info("Analyzing image",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("image_id", imageID),
			zap.Int("image_index", i))

		var analysis *reqModel.ImageAnalysis
		var lastErr error

		// Retry logic for each image
		for attempt := 0; attempt <= maxRetries; attempt++ {
			if attempt > 0 {
				d.logger.Info("Retrying image analysis",
					zap.String("requirement_id", requirementID.Hex()),
					zap.String("image_id", imageID),
					zap.Int("attempt", attempt))
			}

			// Get model name for this requirement
			modelName := d.getModelNameForRequirement(req)
			analysis, lastErr = d.imageAnalyzer.AnalyzeImage(ctx, imagePath, modelName)
			if lastErr == nil {
				// Success
				break
			}

			d.logger.Warn("Image analysis attempt failed",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("image_id", imageID),
				zap.Int("attempt", attempt),
				zap.Error(lastErr))

			// If this was the last attempt, log the final failure
			if attempt == maxRetries {
				d.logger.Error("Failed to analyze image after all retries",
					zap.String("requirement_id", requirementID.Hex()),
					zap.String("image_id", imageID),
					zap.Int("max_retries", maxRetries),
					zap.Error(lastErr))
			}
		}

		// If analysis succeeded, update the image
		if lastErr == nil && analysis != nil {
			req.ReferenceImages[i].Analysis = *analysis
			now := time.Now()
			req.ReferenceImages[i].AnalyzedAt = &now
			successfulAnalyses++

			d.logger.Info("Image analysis completed successfully",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("image_id", imageID))
		} else {
			// Mark the image as having failed analysis
			d.logger.Error("Image analysis failed completely",
				zap.String("requirement_id", requirementID.Hex()),
				zap.String("image_id", imageID),
				zap.Error(lastErr))
		}
	}

	// Determine final status based on analysis results
	var newStatus string
	var shouldTriggerWebsiteAnalysis bool

	if successfulAnalyses == totalImages {
		// Check if there's a website to analyze
		if req.ReferenceWebsite != nil && req.ReferenceWebsite.URL != "" {
			newStatus = reqModel.StatusWebsiteAnalyzing
			shouldTriggerWebsiteAnalysis = true
			d.logger.Info("All images analyzed successfully, proceeding to website analysis",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("successful_analyses", successfulAnalyses),
				zap.Int("total_images", totalImages),
				zap.String("website_url", req.ReferenceWebsite.URL))
		} else {
			newStatus = reqModel.StatusPending // All analyses successful, no website
			d.logger.Info("All images analyzed successfully",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("successful_analyses", successfulAnalyses),
				zap.Int("total_images", totalImages))
		}
	} else if successfulAnalyses > 0 {
		// Check if there's a website to analyze
		if req.ReferenceWebsite != nil && req.ReferenceWebsite.URL != "" {
			newStatus = reqModel.StatusWebsiteAnalyzing
			shouldTriggerWebsiteAnalysis = true
			d.logger.Warn("Some images analyzed successfully, proceeding to website analysis with partial results",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("successful_analyses", successfulAnalyses),
				zap.Int("total_images", totalImages),
				zap.String("website_url", req.ReferenceWebsite.URL))
		} else {
			newStatus = reqModel.StatusPending // Partial success, proceed with available data
			d.logger.Warn("Some images analyzed successfully, proceeding with partial results",
				zap.String("requirement_id", requirementID.Hex()),
				zap.Int("successful_analyses", successfulAnalyses),
				zap.Int("total_images", totalImages))
		}
	} else {
		// All analyses failed - keep in analyzing status for potential manual retry
		// or future automatic retry, but log this as a warning
		d.logger.Error("All image analyses failed, keeping requirement in analyzing status",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Int("successful_analyses", successfulAnalyses),
			zap.Int("total_images", totalImages))
		return fmt.Errorf("all image analyses failed for requirement %s", requirementID.Hex())
	}

	// Update requirement status only if we have some successful analyses
	req.Status = newStatus
	req.UpdatedAt = time.Now()

	// Save updated requirement
	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement after image analysis",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return RepositoryError(err)
	}

	d.logger.Info("Asynchronous image analysis completed",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("new_status", newStatus),
		zap.Int("successful_analyses", successfulAnalyses),
		zap.Int("total_images", totalImages))

	// Trigger website analysis if needed
	if shouldTriggerWebsiteAnalysis {
		go func() {
			d.logger.Info("Starting asynchronous website analysis after image analysis",
				zap.String("requirement_id", requirementID.Hex()))

			if err := d.analyzeWebsiteAsync(context.Background(), requirementID); err != nil {
				d.logger.Error("Asynchronous website analysis failed after image analysis",
					zap.String("requirement_id", requirementID.Hex()),
					zap.Error(err))
			}
		}()
	}

	return nil
}

// callLLMWithRetry implements retry mechanism for LLM calls to handle network issues like EOF and timeouts
func (d *Domain) callLLMWithRetry(ctx context.Context, llmRequest *agentDomain.ChatCompletionRequest, modelName string, index int, requirementID string) (*agentDomain.ChatCompletionResponse, error) {
	const maxRetries = 3
	const baseDelay = time.Millisecond * 500 // 500ms base delay
	const llmTimeout = time.Minute * 3       // 3 minutes timeout for wireframe generation

	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// Exponential backoff with jitter for concurrent requests
			delay := baseDelay * time.Duration(1<<uint(attempt-1)) // 500ms, 1s, 2s
			jitter := time.Duration(index*100) * time.Millisecond  // Add jitter based on index to spread requests
			time.Sleep(delay + jitter)

			d.logger.Info("Retrying LLM call for wireframe generation",
				zap.String("requirement_id", requirementID),
				zap.Int("wireframe_index", index),
				zap.Int("attempt", attempt+1),
				zap.Int("max_retries", maxRetries),
				zap.Duration("timeout", llmTimeout))
		}

		// Create timeout context for this specific LLM call
		timeoutCtx, cancel := context.WithTimeout(ctx, llmTimeout)
		defer cancel()

		// Record start time for timeout logging
		startTime := time.Now()

		// Get LLM client for the specific model
		llmClient, clientErr := d.llmClientFactory.GetLLMClient(ctx, modelName)
		if clientErr != nil {
			d.logger.Error("Failed to get LLM client in retry function",
				zap.Error(clientErr),
				zap.String("requirement_id", requirementID),
				zap.String("model_name", modelName),
				zap.Int("wireframe_index", index))
			return nil, fmt.Errorf("failed to get LLM client for model '%s': %w", modelName, clientErr)
		}

		response, err := llmClient.ChatCompletionNonStreaming(timeoutCtx, llmRequest)

		// Calculate actual duration for logging
		duration := time.Since(startTime)

		if err != nil {
			lastErr = err

			// Enhanced timeout and error logging
			if d.isTimeoutError(err) {
				d.logger.Warn("Timeout error in LLM call",
					zap.String("requirement_id", requirementID),
					zap.Int("wireframe_index", index),
					zap.Int("attempt", attempt+1),
					zap.Duration("duration", duration),
					zap.Duration("timeout_limit", llmTimeout),
					zap.Error(err))
			} else {
				d.logger.Debug("LLM call completed with error",
					zap.String("requirement_id", requirementID),
					zap.Int("wireframe_index", index),
					zap.Duration("duration", duration),
					zap.Error(err))
			}

			// Check if this is a network issue that we can retry
			if d.isRetryableError(err) {
				d.logger.Warn("Retryable error encountered in LLM call",
					zap.String("requirement_id", requirementID),
					zap.Int("wireframe_index", index),
					zap.Int("attempt", attempt+1),
					zap.String("error_type", d.getErrorType(err)),
					zap.Error(err))
				continue // Retry
			} else {
				// Non-retryable error, fail immediately
				d.logger.Error("Non-retryable error in LLM call",
					zap.String("requirement_id", requirementID),
					zap.Int("wireframe_index", index),
					zap.Error(err))
				return nil, err
			}
		}

		// Success
		if attempt > 0 {
			d.logger.Info("LLM call succeeded after retry",
				zap.String("requirement_id", requirementID),
				zap.Int("wireframe_index", index),
				zap.Int("attempt", attempt+1))
		}
		return response, nil
	}

	// All retries exhausted
	d.logger.Error("All LLM call retries exhausted",
		zap.String("requirement_id", requirementID),
		zap.Int("wireframe_index", index),
		zap.Int("max_retries", maxRetries),
		zap.Error(lastErr))
	return nil, fmt.Errorf("LLM call failed after %d retries: %w", maxRetries, lastErr)
}

// isRetryableError determines if an error is worth retrying
func (d *Domain) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// Network-related errors that are often temporary
	retryableErrors := []string{
		"unexpected eof",
		"connection reset",
		"connection refused",
		"timeout",
		"temporary failure",
		"service unavailable",
		"too many requests",
		"rate limit",
		"network is unreachable",
		"no route to host",
		"context deadline exceeded",
		"i/o timeout",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}

// isTimeoutError checks if the error is specifically a timeout-related error
func (d *Domain) isTimeoutError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	timeoutErrors := []string{
		"timeout",
		"context deadline exceeded",
		"i/o timeout",
		"request timeout",
		"gateway timeout",
	}

	for _, timeoutErr := range timeoutErrors {
		if strings.Contains(errStr, timeoutErr) {
			return true
		}
	}

	return false
}

// getErrorType categorizes the error type for better logging and debugging
func (d *Domain) getErrorType(err error) string {
	if err == nil {
		return "none"
	}

	errStr := strings.ToLower(err.Error())

	if d.isTimeoutError(err) {
		return "timeout"
	}

	if strings.Contains(errStr, "eof") {
		return "connection_eof"
	}

	if strings.Contains(errStr, "connection") {
		return "connection_error"
	}

	if strings.Contains(errStr, "rate limit") || strings.Contains(errStr, "too many requests") {
		return "rate_limit"
	}

	if strings.Contains(errStr, "service unavailable") {
		return "service_unavailable"
	}

	if strings.Contains(errStr, "network") {
		return "network_error"
	}

	return "unknown"
}

// UpdateRequirement updates an existing requirement with the given fields.
func (d *Domain) UpdateRequirement(ctx context.Context, requirementID primitive.ObjectID, userID primitive.ObjectID, updates map[string]interface{}) (*reqModel.Requirement, error) {
	d.logger.Info("Starting requirement update process",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.Any("updates", updates))

	// Get the requirement and verify ownership
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for update",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt for update",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	// Validate and apply updates
	// For now, we allow updating title, content, and development_plan
	if title, ok := updates["title"].(string); ok {
		if strings.TrimSpace(title) == "" {
			return nil, fmt.Errorf("%w: title cannot be empty", ErrInvalidInput)
		}
		req.Title = strings.TrimSpace(title)
	}
	if content, ok := updates["content"].(string); ok {
		if strings.TrimSpace(content) == "" {
			return nil, fmt.Errorf("%w: content cannot be empty", ErrInvalidInput)
		}
		req.Content = strings.TrimSpace(content)
	}
	if plan, ok := updates["development_plan"].(string); ok {
		req.DevelopmentPlan = plan // Allow empty plan to be set
	}

	// Set update timestamp
	req.UpdatedAt = time.Now()

	// Save updated requirement to the repository
	err = d.repo.Update(ctx, req)
	if err != nil {
		d.logger.Error("Error updating requirement in repository",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	d.logger.Info("Requirement update completed successfully",
		zap.String("requirement_id", req.ID.Hex()))

	return req, nil
}

// CreateProjectFromRequirement creates a new project based on a requirement and selected wireframe.
// It validates the requirement state, creates the project, and populates it with the development plan and wireframe.
func (d *Domain) CreateProjectFromRequirement(ctx context.Context, requirementID primitive.ObjectID, wireframeID string, projectID string, projectName string, userID primitive.ObjectID) (*reqModel.Requirement, error) {
	d.logger.Info("Starting project creation from requirement",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("wireframe_id", wireframeID),
		zap.String("project_id", projectID),
		zap.String("project_name", projectName),
		zap.String("user_id", userID.Hex()))

	// 1. Get and validate the requirement
	req, err := d.repo.FindByID(ctx, requirementID)
	if err != nil {
		d.logger.Error("Error finding requirement for project creation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.Error(err))
		return nil, RepositoryError(err)
	}

	// Verify ownership
	if req.UserID != userID {
		d.logger.Warn("Unauthorized access attempt for project creation",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("actual_owner", req.UserID.Hex()))
		return nil, ErrUnauthorized
	}

	// Validate requirement state
	if req.Status != reqModel.StatusCompleted {
		d.logger.Warn("Cannot create project from incomplete requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("status", req.Status))
		return nil, fmt.Errorf("requirement must be completed to create project, current status: %s", req.Status)
	}

	// Validate development plan exists
	if strings.TrimSpace(req.DevelopmentPlan) == "" {
		d.logger.Warn("Cannot create project without development plan",
			zap.String("requirement_id", requirementID.Hex()))
		return nil, fmt.Errorf("requirement must have a development plan to create project")
	}

	// 2. Find and validate the selected wireframe
	var selectedWireframe *reqModel.Wireframe
	for _, wireframe := range req.Wireframes {
		if wireframe.ID == wireframeID {
			selectedWireframe = &wireframe
			break
		}
	}

	if selectedWireframe == nil {
		d.logger.Warn("Selected wireframe not found in requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("wireframe_id", wireframeID))
		return nil, fmt.Errorf("wireframe with ID '%s' not found in requirement", wireframeID)
	}

	// Validate wireframe has HTML content
	if strings.TrimSpace(selectedWireframe.HTML) == "" {
		d.logger.Warn("Selected wireframe has no HTML content",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("wireframe_id", wireframeID))
		return nil, fmt.Errorf("selected wireframe has no HTML content")
	}

	d.logger.Info("Validation completed, creating project",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("wireframe_title", selectedWireframe.Title))

	// 3. Create the project using project domain
	project, err := d.projectDomain.CreateProjectWithRuntime(ctx, projectID, projectName, userID)
	if err != nil {
		d.logger.Error("Failed to create project from requirement",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("project_id", projectID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to create project: %w", err)
	}

	d.logger.Info("Project created successfully, writing initial files",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("project_id", projectID),
		zap.String("project_db_id", project.ID.Hex()))

	// 4. Write development plan to .development_plan.md
	developmentPlanContent := fmt.Sprintf("# %s - 开发计划\n\n生成自需求：%s\n生成时间：%s\n\n---\n\n%s",
		req.Title,
		req.Title,
		time.Now().Format("2006-01-02 15:04:05"),
		req.DevelopmentPlan)

	err = d.projectDomain.WriteProjectFile(ctx, projectID, ".development_plan.md", []byte(developmentPlanContent))
	if err != nil {
		d.logger.Error("Failed to write development plan file",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("project_id", projectID),
			zap.Error(err))
		// Continue with wireframe creation even if development plan fails
	} else {
		d.logger.Info("Development plan file written successfully",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("project_id", projectID))
	}

	// 5. Write selected wireframe HTML to index.html
	wireframeContent := fmt.Sprintf("<!-- %s - %s -->\n<!-- 生成自需求：%s -->\n<!-- 生成时间：%s -->\n\n%s",
		selectedWireframe.Title,
		selectedWireframe.Description,
		req.Title,
		time.Now().Format("2006-01-02 15:04:05"),
		selectedWireframe.HTML)

	err = d.projectDomain.WriteProjectFile(ctx, projectID, "index.html", []byte(wireframeContent))
	if err != nil {
		d.logger.Error("Failed to write wireframe HTML file",
			zap.String("requirement_id", requirementID.Hex()),
			zap.String("project_id", projectID),
			zap.String("wireframe_id", wireframeID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to write wireframe HTML file: %w", err)
	}

	d.logger.Info("Project creation from requirement completed successfully",
		zap.String("requirement_id", requirementID.Hex()),
		zap.String("project_id", projectID),
		zap.String("wireframe_id", wireframeID),
		zap.String("wireframe_title", selectedWireframe.Title))

	return req, nil
}
