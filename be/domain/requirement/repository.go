package requirement

import (
	"context"

	model "git.nevint.com/fota3/t-rex/model/requirement"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// RequirementRepository defines the interface for data persistence operations related to user requirements.
// Implementations should handle database interactions and return appropriate errors for different scenarios.
type RequirementRepository interface {
	// Create inserts a new requirement record into the data store.
	// It should set the CreatedAt and UpdatedAt timestamps before persisting.
	// Returns an error if the operation fails.
	Create(ctx context.Context, req *model.Requirement) error

	// FindByID retrieves a requirement by its unique MongoDB ObjectID.
	// Should return ErrNotFound if no requirement matches the given ID.
	FindByID(ctx context.Context, id primitive.ObjectID) (*model.Requirement, error)

	// FindByUserID retrieves all requirement records associated with a specific user ID.
	// Returns an empty slice if no requirements are found, not an error.
	// This provides user isolation - users can only access their own requirements.
	FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*model.Requirement, error)

	// FindByUserIDWithPagination retrieves requirements for a user with pagination support.
	// Parameters:
	//   - userID: User whose requirements to retrieve
	//   - page: Page number (starting from 1)
	//   - limit: Number of requirements per page
	// Returns:
	//   - []*model.Requirement: Slice of requirements for the requested page
	//   - int64: Total count of requirements for this user
	//   - error: Any error that occurred during the operation
	FindByUserIDWithPagination(ctx context.Context, userID primitive.ObjectID, page, limit int) ([]*model.Requirement, int64, error)

	// Update updates an existing requirement record.
	// It should update the UpdatedAt timestamp automatically.
	// Should return ErrNotFound if no requirement matches the given ID.
	Update(ctx context.Context, req *model.Requirement) error

	// Delete removes a requirement record by its MongoDB ObjectID.
	// Should return ErrNotFound if no requirement matches the given ID to delete.
	// Note: Consider implementing soft delete in the future for audit purposes.
	Delete(ctx context.Context, id primitive.ObjectID) error

	// FindByStatus retrieves requirements by their processing status (pending/processing/completed).
	// This can be useful for administrative purposes or batch processing.
	// Returns an empty slice if no requirements match the given status.
	FindByStatus(ctx context.Context, status string) ([]*model.Requirement, error)

	// FindByUserIDAndStatus retrieves requirements for a specific user filtered by status.
	// Combines user isolation with status filtering for better query efficiency.
	FindByUserIDAndStatus(ctx context.Context, userID primitive.ObjectID, status string) ([]*model.Requirement, error)
}
