package projectfs

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestFileWatcher_BasicFunctionality(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "filewatcher_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create a file watcher
	watcher, err := NewFileWatcher("test-project", tempDir, zap.NewNop())
	require.NoError(t, err)
	require.NotNil(t, watcher)

	// Start the watcher
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = watcher.Start(ctx)
	require.NoError(t, err)
	defer watcher.Stop()

	// Subscribe to events
	subscriber, err := watcher.Subscribe(ctx, "test-subscriber")
	require.NoError(t, err)
	require.NotNil(t, subscriber)

	// Create a test file and verify we receive an event
	testFile := filepath.Join(tempDir, "test.txt")

	// Start a goroutine to wait for events
	eventReceived := make(chan FileWatcherEvent, 1)
	go func() {
		select {
		case event := <-subscriber.Channel:
			if event.Type == WatcherEventFileChange {
				eventReceived <- event
			}
		case <-time.After(5 * time.Second):
			// Timeout
		}
	}()

	// Give the watcher time to initialize
	time.Sleep(100 * time.Millisecond)

	// Create the test file
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err)

	// Wait for the event
	select {
	case event := <-eventReceived:
		changeEvent, ok := event.Data.(FileChangeEvent)
		assert.True(t, ok)
		assert.Equal(t, FileChangeCreate, changeEvent.Type)
		assert.Equal(t, "test.txt", changeEvent.Path)
		assert.False(t, changeEvent.IsDir)
	case <-time.After(2 * time.Second):
		t.Fatal("Expected to receive a file change event within timeout")
	}
}

func TestFileWatcher_DirectoryCreation(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "filewatcher_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create a file watcher
	watcher, err := NewFileWatcher("test-project", tempDir, zap.NewNop())
	require.NoError(t, err)

	// Start the watcher
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = watcher.Start(ctx)
	require.NoError(t, err)
	defer watcher.Stop()

	// Subscribe to events
	subscriber, err := watcher.Subscribe(ctx, "test-subscriber")
	require.NoError(t, err)

	// Create a test directory and verify we receive an event
	testDir := filepath.Join(tempDir, "testdir")

	// Start a goroutine to wait for events
	eventReceived := make(chan FileWatcherEvent, 1)
	go func() {
		select {
		case event := <-subscriber.Channel:
			if event.Type == WatcherEventFileChange {
				eventReceived <- event
			}
		case <-time.After(5 * time.Second):
			// Timeout
		}
	}()

	// Give the watcher time to initialize
	time.Sleep(100 * time.Millisecond)

	// Create the test directory
	err = os.Mkdir(testDir, 0755)
	require.NoError(t, err)

	// Wait for the event
	select {
	case event := <-eventReceived:
		changeEvent, ok := event.Data.(FileChangeEvent)
		assert.True(t, ok)
		assert.Equal(t, FileChangeCreate, changeEvent.Type)
		assert.Equal(t, "testdir", changeEvent.Path)
		assert.True(t, changeEvent.IsDir)
	case <-time.After(2 * time.Second):
		t.Fatal("Expected to receive a directory change event within timeout")
	}
}

func TestFileWatcher_MultipleSubscribers(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "filewatcher_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create a file watcher
	watcher, err := NewFileWatcher("test-project", tempDir, zap.NewNop())
	require.NoError(t, err)

	// Start the watcher
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = watcher.Start(ctx)
	require.NoError(t, err)
	defer watcher.Stop()

	// Subscribe multiple subscribers
	subscriber1, err := watcher.Subscribe(ctx, "subscriber-1")
	require.NoError(t, err)

	subscriber2, err := watcher.Subscribe(ctx, "subscriber-2")
	require.NoError(t, err)

	// Verify both receive events
	eventReceived1 := make(chan bool, 1)
	eventReceived2 := make(chan bool, 1)

	go func() {
		select {
		case event := <-subscriber1.Channel:
			if event.Type == WatcherEventFileChange {
				eventReceived1 <- true
			}
		case <-time.After(3 * time.Second):
		}
	}()

	go func() {
		select {
		case event := <-subscriber2.Channel:
			if event.Type == WatcherEventFileChange {
				eventReceived2 <- true
			}
		case <-time.After(3 * time.Second):
		}
	}()

	// Give the watcher time to initialize
	time.Sleep(100 * time.Millisecond)

	// Create a test file
	testFile := filepath.Join(tempDir, "test2.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err)

	// Verify both subscribers receive the event
	select {
	case <-eventReceived1:
		// Good
	case <-time.After(2 * time.Second):
		t.Fatal("Subscriber 1 did not receive event")
	}

	select {
	case <-eventReceived2:
		// Good
	case <-time.After(2 * time.Second):
		t.Fatal("Subscriber 2 did not receive event")
	}
}

func TestFileWatcher_StopAndCleanup(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "filewatcher_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create a file watcher
	watcher, err := NewFileWatcher("test-project", tempDir, zap.NewNop())
	require.NoError(t, err)

	// Start the watcher
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = watcher.Start(ctx)
	require.NoError(t, err)
	assert.True(t, watcher.IsRunning())

	// Subscribe to events
	subscriber, err := watcher.Subscribe(ctx, "test-subscriber")
	require.NoError(t, err)
	assert.Equal(t, 1, watcher.GetSubscriberCount())

	// Stop the watcher
	watcher.Stop()
	assert.False(t, watcher.IsRunning())
	assert.Equal(t, 0, watcher.GetSubscriberCount())

	// Verify subscriber channel is closed
	select {
	case _, ok := <-subscriber.Channel:
		assert.False(t, ok, "Subscriber channel should be closed")
	case <-time.After(1 * time.Second):
		t.Fatal("Expected subscriber channel to be closed")
	}
}
