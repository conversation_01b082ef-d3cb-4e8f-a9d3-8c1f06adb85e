package projectfs

import (
	"context"
	"fmt"
	"sync"

	"go.uber.org/zap"
)

// FileWatcherManager manages file watchers for multiple projects
type FileWatcherManager struct {
	logger   *zap.Logger
	mu       sync.RWMutex
	watchers map[string]*FileWatcher // projectID -> FileWatcher
	fsDomain *Domain                 // Reference to FS domain for getting project roots
}

// NewFileWatcherManager creates a new file watcher manager
func NewFileWatcherManager(fsDomain *Domain, logger *zap.Logger) *FileWatcherManager {
	if logger == nil {
		logger = zap.NewNop()
	}

	return &FileWatcherManager{
		logger:   logger.Named("FileWatcherManager"),
		watchers: make(map[string]*FileWatcher),
		fsDomain: fsDomain,
	}
}

// StartWatcher starts file watching for a project
func (m *FileWatcherManager) StartWatcher(ctx context.Context, projectID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if watcher already exists and is running
	if watcher, exists := m.watchers[projectID]; exists {
		if watcher.IsRunning() {
			m.logger.Debug("File watcher already running for project", zap.String("projectID", projectID))
			return nil
		}
		// Remove the existing non-running watcher
		delete(m.watchers, projectID)
	}

	// Get project root path
	projectRoot, err := m.fsDomain.GetProjectRoot(ctx, projectID)
	if err != nil {
		m.logger.Error("Failed to get project root for file watcher",
			zap.String("projectID", projectID), zap.Error(err))
		return fmt.Errorf("failed to get project root: %w", err)
	}

	// Create new file watcher
	watcher, err := NewFileWatcher(projectID, projectRoot, m.logger)
	if err != nil {
		m.logger.Error("Failed to create file watcher",
			zap.String("projectID", projectID), zap.Error(err))
		return fmt.Errorf("failed to create file watcher: %w", err)
	}

	// Start the watcher
	err = watcher.Start(ctx)
	if err != nil {
		m.logger.Error("Failed to start file watcher",
			zap.String("projectID", projectID), zap.Error(err))
		return fmt.Errorf("failed to start file watcher: %w", err)
	}

	// Store the watcher
	m.watchers[projectID] = watcher
	m.logger.Info("File watcher started for project",
		zap.String("projectID", projectID),
		zap.String("projectRoot", projectRoot))

	return nil
}

// StopWatcher stops file watching for a project
func (m *FileWatcherManager) StopWatcher(projectID string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if watcher, exists := m.watchers[projectID]; exists {
		watcher.Stop()
		delete(m.watchers, projectID)
		m.logger.Info("File watcher stopped for project", zap.String("projectID", projectID))
	}
}

// GetWatcher returns the file watcher for a project
func (m *FileWatcherManager) GetWatcher(projectID string) (*FileWatcher, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	watcher, exists := m.watchers[projectID]
	return watcher, exists
}

// Subscribe subscribes to file change events for a project
func (m *FileWatcherManager) Subscribe(ctx context.Context, projectID, subscriberID string) (*FileWatcherSubscriber, error) {
	// Ensure watcher is running for the project
	err := m.StartWatcher(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to start watcher for subscription: %w", err)
	}

	m.mu.RLock()
	watcher, exists := m.watchers[projectID]
	m.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("watcher not found for project %s", projectID)
	}

	return watcher.Subscribe(ctx, subscriberID)
}

// Unsubscribe unsubscribes from file change events for a project
func (m *FileWatcherManager) Unsubscribe(projectID, subscriberID string) {
	m.mu.RLock()
	watcher, exists := m.watchers[projectID]
	m.mu.RUnlock()

	if exists {
		watcher.Unsubscribe(subscriberID)

		// If no more subscribers, stop the watcher to save resources
		if watcher.GetSubscriberCount() == 0 {
			m.logger.Debug("No more subscribers, stopping watcher", zap.String("projectID", projectID))
			m.StopWatcher(projectID)
		}
	}
}

// StopAll stops all file watchers
func (m *FileWatcherManager) StopAll() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for projectID, watcher := range m.watchers {
		watcher.Stop()
		m.logger.Info("File watcher stopped", zap.String("projectID", projectID))
	}

	// Clear the map
	m.watchers = make(map[string]*FileWatcher)
	m.logger.Info("All file watchers stopped")
}

// GetWatcherStatus returns status information about all watchers
func (m *FileWatcherManager) GetWatcherStatus() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	status := make(map[string]interface{})
	for projectID, watcher := range m.watchers {
		status[projectID] = map[string]interface{}{
			"is_running":       watcher.IsRunning(),
			"subscriber_count": watcher.GetSubscriberCount(),
			"project_root":     watcher.projectRoot,
		}
	}

	return status
}
