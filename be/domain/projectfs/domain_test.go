package projectfs

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	"git.nevint.com/fota3/t-rex/pkg/storage" // Import storage types
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
)

// MockProjectStorage is a mock implementation of storage.ProjectStorage
type MockProjectStorage struct {
	mock.Mock
}

// Implement storage.ProjectStorage methods for the mock
func (m *MockProjectStorage) ReadFile(ctx context.Context, projectID, filePath string) ([]byte, error) {
	args := m.Called(ctx, projectID, filePath)
	// Need to cast the first result explicitly if it's potentially nil
	var data []byte
	if args.Get(0) != nil {
		data = args.Get(0).([]byte)
	}
	return data, args.Error(1)
}

func (m *MockProjectStorage) WriteFile(ctx context.Context, projectID, filePath string, content []byte, overwrite bool) error {
	args := m.Called(ctx, projectID, filePath, content, overwrite)
	return args.Error(0)
}

func (m *MockProjectStorage) ListDirectory(ctx context.Context, projectID, dirPath string) ([]storage.FileInfo, error) {
	args := m.Called(ctx, projectID, dirPath)
	var files []storage.FileInfo
	if args.Get(0) != nil {
		files = args.Get(0).([]storage.FileInfo)
	}
	return files, args.Error(1)
}

func (m *MockProjectStorage) CreateDirectory(ctx context.Context, projectID, dirPath string) error {
	args := m.Called(ctx, projectID, dirPath)
	return args.Error(0)
}

func (m *MockProjectStorage) Delete(ctx context.Context, projectID, path string, recursive bool) error {
	args := m.Called(ctx, projectID, path, recursive)
	return args.Error(0)
}

func (m *MockProjectStorage) Rename(ctx context.Context, projectID, oldPath, newPath string) error {
	args := m.Called(ctx, projectID, oldPath, newPath)
	return args.Error(0)
}

func (m *MockProjectStorage) Exists(ctx context.Context, projectID, path string) (bool, error) {
	args := m.Called(ctx, projectID, path)
	return args.Bool(0), args.Error(1)
}

func (m *MockProjectStorage) Stat(ctx context.Context, projectID, path string) (storage.FileInfo, error) {
	args := m.Called(ctx, projectID, path)
	// Return zero value FileInfo if first arg is nil
	var info storage.FileInfo
	if args.Get(0) != nil {
		info = args.Get(0).(storage.FileInfo)
	}
	return info, args.Error(1)
}

func (m *MockProjectStorage) InitProjectWorkspace(ctx context.Context, projectID string) error {
	args := m.Called(ctx, projectID)
	return args.Error(0)
}

func (m *MockProjectStorage) DeleteProjectWorkspace(ctx context.Context, projectID string) error {
	args := m.Called(ctx, projectID)
	return args.Error(0)
}

func (m *MockProjectStorage) GetProjectRootPath(ctx context.Context, projectID string) (string, error) {
	args := m.Called(ctx, projectID)
	return args.String(0), args.Error(1)
}

// --- Test Cases ---

func TestCoreProjectFS_EnsureProjectExists_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "test-project"

	// Expect CreateDirectory to be called for the root (".")
	mockStorage.On("CreateDirectory", ctx, projectID, ".").Return(nil) // Success

	err := coreFS.EnsureProjectExists(ctx, projectID)

	assert.NoError(t, err)
	mockStorage.AssertExpectations(t) // Verify that the expected method was called
}

func TestCoreProjectFS_EnsureProjectExists_AlreadyExists(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "test-project"

	// Expect CreateDirectory to be called, return ErrPathExists
	mockStorage.On("CreateDirectory", ctx, projectID, ".").Return(storage.ErrPathExists)

	err := coreFS.EnsureProjectExists(ctx, projectID)

	// EnsureProjectExists should treat ErrPathExists as success (return nil)
	assert.NoError(t, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_EnsureProjectExists_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "test-project"
	mockError := errors.New("disk full")

	// Expect CreateDirectory to be called, return a generic error
	mockStorage.On("CreateDirectory", ctx, projectID, ".").Return(mockError)

	err := coreFS.EnsureProjectExists(ctx, projectID)

	// EnsureProjectExists should return the original storage error
	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ListDirectory_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "list-proj"
	listPath := "src"

	expectedFiles := []storage.FileInfo{
		{Name: "file1.go", Path: "src/file1.go", IsDir: false, Size: 100, ModTime: time.Now()},
		{Name: "sub", Path: "src/sub", IsDir: true, Size: 0, ModTime: time.Now()},
	}

	// Expect ListDirectory to be called with the cleaned path
	mockStorage.On("ListDirectory", ctx, projectID, listPath).Return(expectedFiles, nil)

	files, err := coreFS.ListDirectory(ctx, projectID, listPath)

	assert.NoError(t, err)
	assert.Equal(t, expectedFiles, files)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ListDirectory_Empty(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "list-proj"
	listPath := "empty_dir"

	// Expect ListDirectory to return an empty slice and no error
	mockStorage.On("ListDirectory", ctx, projectID, listPath).Return([]storage.FileInfo{}, nil)

	files, err := coreFS.ListDirectory(ctx, projectID, listPath)

	assert.NoError(t, err)
	assert.Empty(t, files)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ListDirectory_NotExist(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "list-proj"
	listPath := "nonexistent"

	// Expect ListDirectory to return ErrNotExist
	mockStorage.On("ListDirectory", ctx, projectID, listPath).Return(nil, storage.ErrNotExist)

	_, err := coreFS.ListDirectory(ctx, projectID, listPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrNotExist))
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ListDirectory_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage) // Mock might not even be called
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "list-proj"
	listPath := "../invalid_escape"

	// No mock expectation needed as cleanAndValidatePath should fail first

	_, err := coreFS.ListDirectory(ctx, projectID, listPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath)) // Expecting the validation error
	mockStorage.AssertNotCalled(t, "ListDirectory", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_ListDirectory_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "list-proj"
	listPath := "src"
	mockError := errors.New("read permission denied")

	// Expect ListDirectory to return a generic error
	mockStorage.On("ListDirectory", ctx, projectID, listPath).Return(nil, mockError)

	_, err := coreFS.ListDirectory(ctx, projectID, listPath)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_CreateFile_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "new/file.txt"
	content := []byte("hello")

	expectedInfo := storage.FileInfo{Name: "file.txt", Path: "new/file.txt", IsDir: false, Size: int64(len(content)), ModTime: time.Now()}

	// Expect Stat to be called and return os.ErrNotExist to pass the check
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{}, os.ErrNotExist).Once()
	// Expect WriteFile to be called
	mockStorage.On("WriteFile", ctx, projectID, filePath, content, false).Return(nil).Once()
	// Expect Stat again after write, return success
	mockStorage.On("Stat", ctx, projectID, filePath).Return(expectedInfo, nil).Once()

	info, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.NoError(t, err)
	assert.Equal(t, expectedInfo, info)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_CreateFile_PathExists(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "existing/file.txt"
	content := []byte("hello")

	// Expect Stat to be called and return success (file exists)
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{Name: "file.txt"}, nil).Once()

	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrPathExists))
	mockStorage.AssertExpectations(t)
	// Ensure WriteFile was not called
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateFile_CannotCreateRoot(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "."
	content := []byte("hello")

	// No storage calls expected
	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, ErrCannotOperateRoot)) // Check domain specific error
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateFile_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "../../escape.txt"
	content := []byte("hello")

	// No storage calls expected
	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateFile_StatErrorBeforeWrite(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "new/file.txt"
	content := []byte("hello")
	mockError := errors.New("permission denied")

	// Expect Stat to be called and return an error other than NotExist
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{}, mockError).Once()

	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.Equal(t, mockError, err) // Should return the stat error
	mockStorage.AssertExpectations(t)
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateFile_WriteError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "new/file.txt"
	content := []byte("hello")
	mockError := errors.New("disk quota exceeded")

	// Expect Stat -> os.ErrNotExist
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{}, os.ErrNotExist).Once()
	// Expect WriteFile -> Error
	mockStorage.On("WriteFile", ctx, projectID, filePath, content, false).Return(mockError).Once()

	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.Equal(t, mockError, err) // Should return the write error
	mockStorage.AssertExpectations(t)
	// Ensure Stat was not called again after failed write
	mockStorage.AssertNumberOfCalls(t, "Stat", 1)
}

func TestCoreProjectFS_CreateFile_StatErrorAfterWrite(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-proj"
	filePath := "new/file.txt"
	content := []byte("hello")
	mockError := errors.New("filesystem became read-only")

	// Expect Stat -> os.ErrNotExist
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{}, os.ErrNotExist).Once()
	// Expect WriteFile -> Success
	mockStorage.On("WriteFile", ctx, projectID, filePath, content, false).Return(nil).Once()
	// Expect Stat again -> Error
	mockStorage.On("Stat", ctx, projectID, filePath).Return(storage.FileInfo{}, mockError).Once()

	_, err := coreFS.CreateFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.Equal(t, mockError, err) // Should return the second stat error
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_CreateDirectory_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "new/dir/to/create"

	expectedInfo := storage.FileInfo{Name: "create", Path: "new/dir/to/create", IsDir: true, ModTime: time.Now()}

	// Expect CreateDirectory call -> success
	mockStorage.On("CreateDirectory", ctx, projectID, dirPath).Return(nil).Once()
	// Expect Stat call after creation -> success
	mockStorage.On("Stat", ctx, projectID, dirPath).Return(expectedInfo, nil).Once()

	info, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	assert.NoError(t, err)
	assert.Equal(t, expectedInfo, info)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_CreateDirectory_PathExists(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "existing/dir"

	// Expect CreateDirectory call -> ErrPathExists
	mockStorage.On("CreateDirectory", ctx, projectID, dirPath).Return(storage.ErrPathExists).Once()

	_, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrPathExists))
	mockStorage.AssertExpectations(t)
	// Ensure Stat was not called if CreateDirectory failed
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateDirectory_CannotCreateRoot(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "."

	// Domain layer prevents explicitly creating root "."
	// We expect the storage layer's CreateDirectory(".") to handle this, likely returning ErrPathExists if it already exists
	// Let's test the case where storage returns ErrPathExists for root
	mockStorage.On("CreateDirectory", ctx, projectID, dirPath).Return(storage.ErrPathExists).Once()

	_, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	// The domain layer currently allows passing "." down, and expects storage to handle it.
	// So we expect ErrPathExists from the storage layer in this case.
	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrPathExists))
	mockStorage.AssertExpectations(t)
	// Alternative interpretation: Domain layer should prevent call for "." entirely.
	// If so, the test would be: assert.True(t, errors.Is(err, ErrCannotOperateRoot)) and AssertNotCalled for CreateDirectory.
}

func TestCoreProjectFS_CreateDirectory_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "/absolute/path"

	// No storage calls expected, validation should fail first.
	_, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "CreateDirectory", mock.Anything, mock.Anything, mock.Anything)
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateDirectory_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "new/dir"
	mockError := errors.New("filesystem error")

	// Expect CreateDirectory call -> Generic Error
	mockStorage.On("CreateDirectory", ctx, projectID, dirPath).Return(mockError).Once()

	_, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_CreateDirectory_StatErrorAfterCreate(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "create-dir-proj"
	dirPath := "new/dir"
	mockError := errors.New("stat failed")

	// Expect CreateDirectory -> success
	mockStorage.On("CreateDirectory", ctx, projectID, dirPath).Return(nil).Once()
	// Expect Stat -> error
	mockStorage.On("Stat", ctx, projectID, dirPath).Return(storage.FileInfo{}, mockError).Once()

	_, err := coreFS.CreateDirectory(ctx, projectID, dirPath)

	assert.Error(t, err)
	assert.Equal(t, mockError, err) // Expect the stat error
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ReadFile_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "read-proj"
	filePath := "src/main.go"
	expectedContent := []byte("package main\nfunc main(){}")

	// Expect ReadFile call -> success
	mockStorage.On("ReadFile", ctx, projectID, filePath).Return(expectedContent, nil).Once()

	content, err := coreFS.ReadFile(ctx, projectID, filePath)

	assert.NoError(t, err)
	assert.Equal(t, expectedContent, content)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ReadFile_NotExist(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "read-proj"
	filePath := "not/found.txt"

	// Expect ReadFile call -> ErrNotExist
	mockStorage.On("ReadFile", ctx, projectID, filePath).Return(nil, storage.ErrNotExist).Once()

	_, err := coreFS.ReadFile(ctx, projectID, filePath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrNotExist))
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_ReadFile_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "read-proj"
	filePath := "../secrets.txt"

	// Expect validation failure, no storage call
	_, err := coreFS.ReadFile(ctx, projectID, filePath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "ReadFile", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_ReadFile_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "read-proj"
	filePath := "src/main.go"
	mockError := errors.New("i/o error")

	// Expect ReadFile call -> generic error
	mockStorage.On("ReadFile", ctx, projectID, filePath).Return(nil, mockError).Once()

	_, err := coreFS.ReadFile(ctx, projectID, filePath)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_WriteFile_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "write-proj"
	filePath := "config.yml"
	content := []byte("key: value")

	// Expect WriteFile call (overwrite=true is assumed based on core.go logic)
	mockStorage.On("WriteFile", ctx, projectID, filePath, content, true).Return(nil).Once()

	err := coreFS.WriteFile(ctx, projectID, filePath, content)

	assert.NoError(t, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_WriteFile_CannotWriteRoot(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "write-proj"
	filePath := "."
	content := []byte("root content")

	// Expect failure before storage call
	err := coreFS.WriteFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, ErrCannotOperateRoot))
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_WriteFile_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "write-proj"
	filePath := "../write_escape.txt"
	content := []byte("data")

	// Expect validation failure, no storage call
	err := coreFS.WriteFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "WriteFile", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_WriteFile_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "write-proj"
	filePath := "data.bin"
	content := []byte{1, 2, 3}
	mockError := errors.New("disk write failed")

	// Expect WriteFile call -> generic error
	mockStorage.On("WriteFile", ctx, projectID, filePath, content, true).Return(mockError).Once()

	err := coreFS.WriteFile(ctx, projectID, filePath, content)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Delete_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "delete-proj"
	pathToDelete := "file/to/delete.txt"

	// Expect Delete call (recursive=true is assumed based on core.go logic)
	mockStorage.On("Delete", ctx, projectID, pathToDelete, true).Return(nil).Once()

	err := coreFS.Delete(ctx, projectID, pathToDelete)

	assert.NoError(t, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Delete_NotExist(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "delete-proj"
	pathToDelete := "non/existent.file"

	// Expect Delete call -> ErrNotExist
	mockStorage.On("Delete", ctx, projectID, pathToDelete, true).Return(storage.ErrNotExist).Once()

	err := coreFS.Delete(ctx, projectID, pathToDelete)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrNotExist)) // Domain layer passes through ErrNotExist
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Delete_CannotDeleteRoot(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "delete-proj"
	pathToDelete := "."

	// Expect failure before storage call
	err := coreFS.Delete(ctx, projectID, pathToDelete)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, ErrCannotOperateRoot))
	mockStorage.AssertNotCalled(t, "Delete", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_Delete_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "delete-proj"
	pathToDelete := "../delete_escape.txt"

	// Expect validation failure, no storage call
	err := coreFS.Delete(ctx, projectID, pathToDelete)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "Delete", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_Delete_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "delete-proj"
	pathToDelete := "dir/to/remove"
	mockError := errors.New("permission issue")

	// Expect Delete call -> generic error
	mockStorage.On("Delete", ctx, projectID, pathToDelete, true).Return(mockError).Once()

	err := coreFS.Delete(ctx, projectID, pathToDelete)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Rename_Success(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"
	oldPath := "src/old.go"
	newPath := "pkg/new.go"

	// Expect Rename call -> success
	mockStorage.On("Rename", ctx, projectID, oldPath, newPath).Return(nil).Once()

	err := coreFS.Rename(ctx, projectID, oldPath, newPath)

	assert.NoError(t, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Rename_OldPathNotExist(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"
	oldPath := "nonexistent.txt"
	newPath := "new.txt"

	// Expect Rename call -> ErrNotExist
	mockStorage.On("Rename", ctx, projectID, oldPath, newPath).Return(storage.ErrNotExist).Once()

	err := coreFS.Rename(ctx, projectID, oldPath, newPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrNotExist))
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Rename_NewPathExists(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"
	oldPath := "file.txt"
	newPath := "existing.txt"

	// Expect Rename call -> ErrPathExists
	mockStorage.On("Rename", ctx, projectID, oldPath, newPath).Return(storage.ErrPathExists).Once()

	err := coreFS.Rename(ctx, projectID, oldPath, newPath)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrPathExists))
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Rename_CannotRenameRoot(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"

	// Test renaming from root
	err := coreFS.Rename(ctx, projectID, ".", "newRootName")
	assert.Error(t, err)
	assert.True(t, errors.Is(err, ErrCannotOperateRoot))

	// Test renaming to root
	err = coreFS.Rename(ctx, projectID, "someDir", ".")
	assert.Error(t, err)
	assert.True(t, errors.Is(err, ErrCannotOperateRoot))

	mockStorage.AssertNotCalled(t, "Rename", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_Rename_InvalidPaths(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"

	// Test invalid old path
	err := coreFS.Rename(ctx, projectID, "../escape", "valid")
	assert.Error(t, err)
	assert.ErrorContains(t, err, "invalid old path") // Checks wrapped error message
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))

	// Test invalid new path
	err = coreFS.Rename(ctx, projectID, "valid", "/absolute/path")
	assert.Error(t, err)
	assert.ErrorContains(t, err, "invalid new path")
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))

	mockStorage.AssertNotCalled(t, "Rename", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_Rename_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "rename-proj"
	oldPath := "a.txt"
	newPath := "b.txt"
	mockError := errors.New("filesystem busy")

	// Expect Rename call -> generic error
	mockStorage.On("Rename", ctx, projectID, oldPath, newPath).Return(mockError).Once()

	err := coreFS.Rename(ctx, projectID, oldPath, newPath)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Stat_Success_File(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "stat-proj"
	filePath := "path/to/file.txt"
	expectedInfo := storage.FileInfo{Name: "file.txt", Path: "path/to/file.txt", IsDir: false, Size: 1234, ModTime: time.Now()}

	// Expect Stat call -> success
	mockStorage.On("Stat", ctx, projectID, filePath).Return(expectedInfo, nil).Once()

	info, err := coreFS.Stat(ctx, projectID, filePath)

	assert.NoError(t, err)
	assert.Equal(t, expectedInfo, info)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Stat_Success_Dir(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "stat-proj"
	dirPath := "path/to/dir"
	expectedInfo := storage.FileInfo{Name: "dir", Path: "path/to/dir", IsDir: true, ModTime: time.Now()}

	// Expect Stat call -> success
	mockStorage.On("Stat", ctx, projectID, dirPath).Return(expectedInfo, nil).Once()

	info, err := coreFS.Stat(ctx, projectID, dirPath)

	assert.NoError(t, err)
	assert.Equal(t, expectedInfo, info)
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Stat_NotExist(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "stat-proj"
	path := "non/existent"

	// Expect Stat call -> ErrNotExist
	mockStorage.On("Stat", ctx, projectID, path).Return(storage.FileInfo{}, storage.ErrNotExist).Once()

	_, err := coreFS.Stat(ctx, projectID, path)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrNotExist))
	mockStorage.AssertExpectations(t)
}

func TestCoreProjectFS_Stat_InvalidPath(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "stat-proj"
	path := "../../stat_escape.txt"

	// Expect validation failure, no storage call
	_, err := coreFS.Stat(ctx, projectID, path)

	assert.Error(t, err)
	assert.True(t, errors.Is(err, storage.ErrInvalidPath))
	mockStorage.AssertNotCalled(t, "Stat", mock.Anything, mock.Anything, mock.Anything)
}

func TestCoreProjectFS_Stat_StorageError(t *testing.T) {
	mockStorage := new(MockProjectStorage)
	logger := zap.NewNop()
	coreFS := NewDomain(mockStorage, logger)
	ctx := context.Background()
	projectID := "stat-proj"
	path := "some/path"
	mockError := errors.New("stat failed")

	// Expect Stat call -> generic error
	mockStorage.On("Stat", ctx, projectID, path).Return(storage.FileInfo{}, mockError).Once()

	_, err := coreFS.Stat(ctx, projectID, path)

	assert.Error(t, err)
	assert.Equal(t, mockError, err)
	mockStorage.AssertExpectations(t)
}
