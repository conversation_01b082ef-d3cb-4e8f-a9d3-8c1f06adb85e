package projectfs

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"git.nevint.com/fota3/t-rex/pkg/storage" // Corrected import path

	"go.uber.org/zap"
)

// Errors for the projectfs domain.
var (
	// ErrCannotOperateRoot is an error indicating an operation cannot be performed on the project root directory.
	ErrCannotOperateRoot = errors.New("cannot operate on project root")
	// ErrInvalidInput indicates that the provided input (e.g., path, projectID) is invalid.
	ErrInvalidInput = errors.New("invalid input")
	// ErrStorageOperationFailed indicates a failure during an operation with the underlying storage.
	ErrStorageOperationFailed = errors.New("storage operation failed")
	// ErrPathExists is defined in pkg/storage and is re-exported or used directly by this package.
	// ErrNotExist is defined in pkg/storage and is re-exported or used directly by this package.
)

// Domain represents the core business logic for project filesystem operations.
// It encapsulates interactions with the underlying storage layer and enforces domain-specific rules.
// It is the domain object for project filesystem operations.
type Domain struct {
	storage        storage.ProjectStorage
	logger         *zap.Logger
	watcherManager *FileWatcherManager
}

// NewDomain creates and returns a new instance of the Domain service for project filesystem operations.
// It requires a storage.ProjectStorage implementation for data persistence and a zap.Logger for logging.
// If the provided logger is nil, a no-op logger will be used to prevent panics.
func NewDomain(storage storage.ProjectStorage, logger *zap.Logger) *Domain {
	// Ensure logger is not nil to prevent panics
	if logger == nil {
		logger = zap.NewNop() // Use a no-op logger if none provided
	}

	domain := &Domain{
		storage: storage,
		logger:  logger.Named("CoreProjectFS"), // Retained original logger name for continuity
	}

	// Initialize file watcher manager
	domain.watcherManager = NewFileWatcherManager(domain, logger)

	return domain
}

// cleanAndValidatePath cleans the given path and performs basic validation.
// It ensures that the path is relative and does not attempt to traverse outside the project root.
// An empty string "" as input is mapped to "." to represent the project root directory internally.
// It returns the cleaned path or an error if validation fails (e.g., ErrInvalidPath for traversal attempts).
func (c *Domain) cleanAndValidatePath(path string) (string, error) {
	// Map empty string input to "." representing the root directory internally.
	if path == "" {
		path = "."
	}

	// Treat leading slash as relative to project root.
	// This was kept for robustness, although frontend changes might make it less likely to receive such paths.
	relativePath := path
	if strings.HasPrefix(path, "/") {
		relativePath = path[1:] // Remove leading slash
		if relativePath == "" { // Path was just "/"
			relativePath = "." // Represent root as "."
		}
	}

	// Clean the relative path (removes trailing slashes, resolves ., ..)
	cleanPath := filepath.Clean(relativePath)

	// Security check: Ensure the cleaned path doesn't try to escape the intended root.
	// It should not be exactly "..", start with "../", or resolve to an absolute path.
	if cleanPath == ".." || strings.HasPrefix(cleanPath, ".."+string(filepath.Separator)) || filepath.IsAbs(cleanPath) {
		c.logger.Warn("Path validation failed: attempted directory traversal or absolute path",
			zap.String("originalPath", path),
			zap.String("relativePath", relativePath),
			zap.String("cleanPath", cleanPath))
		return "", storage.ErrInvalidPath // Use the error from storage package
	}

	// If cleaning resulted in an empty string (e.g. path was "/"), treat as root.
	if cleanPath == "" {
		cleanPath = "."
	}

	return cleanPath, nil
}

// EnsureProjectExists checks if the project's root directory exists in the storage, and creates it if it doesn't.
// It logs the operation and returns an error if the creation fails for reasons other than the directory already existing.
// A successful call (nil error) means the project directory is present or has been created.
func (c *Domain) EnsureProjectExists(ctx context.Context, projectID string) error {
	c.logger.Info("Ensuring project exists", zap.String("projectID", projectID))
	// "." represents the project root directory in the storage layer for this projectID.
	err := c.storage.CreateDirectory(ctx, projectID, ".")
	// Use errors.Is for checking specific error types. It's not an error if the path already exists.
	if err != nil && !errors.Is(err, storage.ErrPathExists) {
		c.logger.Error("Failed to ensure project directory", zap.String("projectID", projectID), zap.Error(err))
		return err // Return the original error
	}
	c.logger.Debug("Project directory ensured", zap.String("projectID", projectID))
	return nil // Consider existing as success
}

// ListDirectory retrieves a list of files and directories within the specified path for a given project.
// The path is cleaned and validated before being passed to the storage layer.
// It returns a slice of storage.FileInfo and an error if any occurred (e.g., path not found, permission issues).
func (c *Domain) ListDirectory(ctx context.Context, projectID, path string) ([]storage.FileInfo, error) {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for ListDirectory", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return nil, err
	}
	c.logger.Debug("Listing directory", zap.String("projectID", projectID), zap.String("path", cleanPath))
	files, err := c.storage.ListDirectory(ctx, projectID, cleanPath)
	if err != nil {
		// Log error unless it's just "not found" which might be an expected scenario.
		if !errors.Is(err, storage.ErrNotExist) {
			c.logger.Error("Failed to list directory", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		}
		return nil, err // Return original error
	}
	return files, nil
}

// CreateFile creates a new file at the specified path within a project, with the given content.
// It first validates the path and checks if a file or directory already exists at that path.
// If the path is valid and does not exist, it writes the content to the new file.
// It returns storage.FileInfo for the created file or an error if the operation fails (e.g., path invalid, path exists, write error).
// Creation at the project root (".") is not allowed and will return ErrCannotOperateRoot.
func (c *Domain) CreateFile(ctx context.Context, projectID, path string, content []byte) (storage.FileInfo, error) {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for CreateFile", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return storage.FileInfo{}, err
	}
	// Prevent file creation at the root directory itself.
	if cleanPath == "." {
		c.logger.Warn("Attempted to create file at project root", zap.String("projectID", projectID), zap.String("path", cleanPath))
		return storage.FileInfo{}, ErrCannotOperateRoot
	}
	c.logger.Info("Attempting to create file", zap.String("projectID", projectID), zap.String("path", cleanPath))

	// Check if path already exists before trying to create.
	_, statErr := c.storage.Stat(ctx, projectID, cleanPath)
	if statErr == nil {
		// Path exists, return error.
		c.logger.Warn("File path already exists", zap.String("projectID", projectID), zap.String("path", cleanPath))
		return storage.FileInfo{}, storage.ErrPathExists
	}
	// Check if the stat error is something *other* than the expected "Not Found" error.
	// os.ErrNotExist (or a wrapped version of it) is the expected error if the path is available.
	if !errors.Is(statErr, os.ErrNotExist) {
		// Stat failed for a reason other than NotExist (e.g., permission denied on parent, or other I/O error).
		c.logger.Error("Failed to stat path before creating file", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(statErr))
		return storage.FileInfo{}, statErr // Return the unexpected stat error.
	}

	// If we reached here, statErr must have been os.ErrNotExist, which is expected for a new file path.
	c.logger.Info("Path does not exist, proceeding with file creation", zap.String("projectID", projectID), zap.String("path", cleanPath))

	// Path does not exist, proceed with writing.
	// The `overwrite` flag is set to false, as this is a create operation.
	// The storage layer should ideally respect this and fail if the file somehow gets created concurrently,
	// though the preceding Stat check makes this less likely.
	err = c.storage.WriteFile(ctx, projectID, cleanPath, content, false)
	if err != nil {
		c.logger.Error("Failed to write file during creation", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		return storage.FileInfo{}, err // Return the write error.
	}
	c.logger.Info("File created successfully", zap.String("projectID", projectID), zap.String("path", cleanPath))

	// Get info of the created file to return to the caller.
	info, err := c.storage.Stat(ctx, projectID, cleanPath)
	if err != nil {
		// This is unexpected if WriteFile succeeded. The file should exist.
		c.logger.Error("Failed to stat file immediately after creation", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		// Return the error as the state is inconsistent.
		return storage.FileInfo{}, err
	}
	return info, nil
}

// CreateDirectory creates a new directory at the specified path within a project.
// It first validates the path. If the path is valid, it attempts to create the directory using the storage layer.
// If the directory already exists, storage.ErrPathExists is returned.
// Explicit creation of the project root (".") is not allowed and might result in ErrPathExists or similar from storage.
// It returns storage.FileInfo for the created directory or an error if the operation fails.
func (c *Domain) CreateDirectory(ctx context.Context, projectID, path string) (storage.FileInfo, error) {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for CreateDirectory", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return storage.FileInfo{}, err
	}
	// Creating the root directory (".") explicitly might be okay if it doesn't exist,
	// but EnsureProjectExists is the preferred way. If it already exists, storage.CreateDirectory should return ErrPathExists.
	if cleanPath == "." {
		c.logger.Warn("Attempted to explicitly create root directory via CreateDirectory", zap.String("projectID", projectID), zap.String("path", cleanPath))
		// Let the storage layer handle this; it should return ErrPathExists if root already exists.
	}
	c.logger.Info("Attempting to create directory", zap.String("projectID", projectID), zap.String("path", cleanPath))

	err = c.storage.CreateDirectory(ctx, projectID, cleanPath)
	if err != nil {
		// Log if it's not the expected "already exists" error.
		if !errors.Is(err, storage.ErrPathExists) {
			c.logger.Error("Failed to create directory", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		}
		return storage.FileInfo{}, err // Pass through the error (including ErrPathExists).
	}
	c.logger.Info("Directory created successfully", zap.String("projectID", projectID), zap.String("path", cleanPath))

	// Get info of the created directory.
	info, err := c.storage.Stat(ctx, projectID, cleanPath)
	if err != nil {
		c.logger.Error("Failed to stat directory immediately after creation", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		return storage.FileInfo{}, err
	}
	return info, nil
}

// ReadFile reads the content of a file at the specified path within a project.
// The path is cleaned and validated before being passed to the storage layer.
// It returns the file content as a byte slice or an error if the operation fails (e.g., path not found, permission issues).
func (c *Domain) ReadFile(ctx context.Context, projectID, path string) ([]byte, error) {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for ReadFile", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return nil, err
	}
	c.logger.Debug("Reading file", zap.String("projectID", projectID), zap.String("path", cleanPath))
	content, err := c.storage.ReadFile(ctx, projectID, cleanPath)
	if err != nil {
		// Log error unless it's just "not found".
		if !errors.Is(err, storage.ErrNotExist) {
			c.logger.Error("Failed to read file", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		}
		return nil, err // Return original error.
	}
	return content, nil
}

// WriteFile writes content to a file at the specified path within a project.
// If the file exists, its content will be overwritten.
// The path is cleaned and validated. Writing to the project root (".") is not allowed.
// It returns an error if the operation fails (e.g., path invalid, write error).
func (c *Domain) WriteFile(ctx context.Context, projectID, path string, content []byte) error {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for WriteFile", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return err
	}
	// Prevent writing directly to the root directory placeholder.
	if cleanPath == "." {
		c.logger.Warn("Attempted to write to project root", zap.String("projectID", projectID), zap.String("path", cleanPath))
		return ErrCannotOperateRoot
	}
	c.logger.Info("Writing file (overwrite mode set to true in storage call)", zap.String("projectID", projectID), zap.String("path", cleanPath))
	// The `overwrite` flag is set to true for this method, implying existing content should be replaced.
	err = c.storage.WriteFile(ctx, projectID, cleanPath, content, true)
	if err != nil {
		c.logger.Error("Failed to write file", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		return err // Return original error.
	}
	c.logger.Info("File written successfully", zap.String("projectID", projectID), zap.String("path", cleanPath))
	return nil
}

// Delete removes a file or directory at the specified path within a project.
// The path is cleaned and validated. Deleting the project root (".") is not allowed and returns ErrCannotOperateRoot.
// The storage layer is called with recursive delete enabled (true).
// It returns an error if the operation fails (e.g., path invalid, path not found, deletion error).
func (c *Domain) Delete(ctx context.Context, projectID, path string) error {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for Delete", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return err
	}
	// Prevent deleting the root directory.
	if cleanPath == "." {
		c.logger.Warn("Attempted to delete project root", zap.String("projectID", projectID), zap.String("path", cleanPath))
		return ErrCannotOperateRoot
	}
	c.logger.Info("Deleting path", zap.String("projectID", projectID), zap.String("path", cleanPath))
	// Assuming recursive delete for simplicity; can be made a parameter if needed.
	err = c.storage.Delete(ctx, projectID, cleanPath, true)
	if err != nil {
		// Log error unless it's just "not found".
		if !errors.Is(err, storage.ErrNotExist) {
			c.logger.Error("Failed to delete path", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		}
		return err // Return original error (including ErrNotExist).
	}
	c.logger.Info("Path deleted successfully", zap.String("projectID", projectID), zap.String("path", cleanPath))
	return nil
}

// Rename renames or moves a file or directory from an old path to a new path within a project.
// Both old and new paths are cleaned and validated. Renaming to or from the project root (".") is not allowed.
// It returns an error if the operation fails (e.g., paths invalid, old path not found, new path exists, rename error).
func (c *Domain) Rename(ctx context.Context, projectID, oldPath, newPath string) error {
	cleanOldPath, err := c.cleanAndValidatePath(oldPath)
	if err != nil {
		c.logger.Warn("Invalid oldPath for Rename", zap.String("projectID", projectID), zap.String("originalPath", oldPath), zap.Error(err))
		return fmt.Errorf("invalid old path: %w", err)
	}
	cleanNewPath, err := c.cleanAndValidatePath(newPath)
	if err != nil {
		c.logger.Warn("Invalid newPath for Rename", zap.String("projectID", projectID), zap.String("originalPath", newPath), zap.Error(err))
		return fmt.Errorf("invalid new path: %w", err)
	}

	// Prevent renaming from or to the root directory itself.
	if cleanOldPath == "." || cleanNewPath == "." {
		c.logger.Warn("Attempted to rename to/from project root", zap.String("projectID", projectID), zap.String("oldPath", oldPath), zap.String("newPath", newPath))
		return ErrCannotOperateRoot
	}
	c.logger.Info("Renaming path", zap.String("projectID", projectID), zap.String("oldPath", cleanOldPath), zap.String("newPath", cleanNewPath))
	err = c.storage.Rename(ctx, projectID, cleanOldPath, cleanNewPath)
	if err != nil {
		// Log errors, but pass through common ones like ErrPathExists or ErrNotExist, as the service layer handles them.
		c.logger.Warn("Rename operation failed", zap.String("projectID", projectID), zap.String("oldPath", cleanOldPath), zap.String("newPath", cleanNewPath), zap.Error(err))
		return err // Return original error.
	}
	c.logger.Info("Path renamed successfully", zap.String("projectID", projectID), zap.String("oldPath", cleanOldPath), zap.String("newPath", cleanNewPath))
	return nil
}

// Stat retrieves file information (storage.FileInfo) for a given path within a project.
// The path is cleaned and validated.
// It returns storage.FileInfo for the path or an error if the operation fails (e.g., path invalid, path not found).
func (c *Domain) Stat(ctx context.Context, projectID, path string) (storage.FileInfo, error) {
	cleanPath, err := c.cleanAndValidatePath(path)
	if err != nil {
		c.logger.Warn("Invalid path for Stat", zap.String("projectID", projectID), zap.String("originalPath", path), zap.Error(err))
		return storage.FileInfo{}, err
	}
	c.logger.Debug("Stat path", zap.String("projectID", projectID), zap.String("path", cleanPath))
	info, err := c.storage.Stat(ctx, projectID, cleanPath)
	if err != nil {
		// Log error unless it's just "not found".
		if !errors.Is(err, storage.ErrNotExist) {
			c.logger.Error("Failed to stat path", zap.String("projectID", projectID), zap.String("path", cleanPath), zap.Error(err))
		}
		return storage.FileInfo{}, err // Return original error (including ErrNotExist).
	}
	return info, nil
}

// GetProjectRoot returns the absolute host path for a project's root directory.
// This is a crucial function for other domains (like runtime) that need to know
// where the project's files are located on the host machine for mounting volumes.
// It delegates the call to the underlying storage implementation.
func (d *Domain) GetProjectRoot(ctx context.Context, projectID string) (string, error) {
	if projectID == "" {
		d.logger.Warn("GetProjectRoot called with empty projectID")
		return "", ErrInvalidInput
	}

	d.logger.Debug("Getting project root path", zap.String("projectID", projectID))
	path, err := d.storage.GetProjectRootPath(ctx, projectID)
	if err != nil {
		d.logger.Error("Failed to get project root path from storage",
			zap.String("projectID", projectID),
			zap.Error(err))
		return "", fmt.Errorf("%w: %v", ErrStorageOperationFailed, err)
	}
	return path, nil
}

// DeleteProjectWorkspace completely removes an entire project workspace from the filesystem.
// This operation deletes all project files, directories, and the project workspace itself.
// It is a destructive operation that cannot be undone.
// This method is typically called during complete project deletion workflows.
func (d *Domain) DeleteProjectWorkspace(ctx context.Context, projectID string) error {
	if projectID == "" {
		d.logger.Warn("DeleteProjectWorkspace called with empty projectID")
		return ErrInvalidInput
	}

	d.logger.Info("Deleting entire project workspace", zap.String("projectID", projectID))

	// Stop file watcher for this project before deletion
	d.watcherManager.StopWatcher(projectID)

	err := d.storage.DeleteProjectWorkspace(ctx, projectID)
	if err != nil {
		d.logger.Error("Failed to delete project workspace from storage",
			zap.String("projectID", projectID),
			zap.Error(err))
		return fmt.Errorf("%w: %v", ErrStorageOperationFailed, err)
	}

	d.logger.Info("Project workspace deleted successfully", zap.String("projectID", projectID))
	return nil
}

// GetFileWatcherManager returns the file watcher manager
func (d *Domain) GetFileWatcherManager() *FileWatcherManager {
	return d.watcherManager
}
