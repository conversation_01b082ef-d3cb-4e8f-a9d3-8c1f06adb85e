package projectfs

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

// FileChangeEventType represents the type of file system change
type FileChangeEventType string

const (
	FileChangeCreate FileChangeEventType = "create"
	FileChangeModify FileChangeEventType = "modify"
	FileChangeDelete FileChangeEventType = "delete"
	FileChangeRename FileChangeEventType = "rename"
)

// FileChangeEvent represents a file system change event
type FileChangeEvent struct {
	Type      FileChangeEventType `json:"type"`
	Path      string              `json:"path"`     // Relative path from project root
	OldPath   *string             `json:"old_path"` // For rename events, the original path
	IsDir     bool                `json:"is_dir"`
	Timestamp time.Time           `json:"timestamp"`
}

// FileWatcherEventType represents the type of watcher event
type FileWatcherEventType string

const (
	WatcherEventConnected  FileWatcherEventType = "connected"
	WatcherEventFileChange FileWatcherEventType = "file_change"
	WatcherEventError      FileWatcherEventType = "error"
)

// FileWatcherEvent represents an event sent to subscribers
type FileWatcherEvent struct {
	Type FileWatcherEventType `json:"type"`
	Data interface{}          `json:"data"`
}

// FileWatcherSubscriber represents a client that subscribes to file change events
type FileWatcherSubscriber struct {
	ID      string
	Channel chan FileWatcherEvent
	Ctx     context.Context // Changed from Cancel to Ctx
	Cancel  context.CancelFunc
}

// FileWatcher manages file system watching for a project
type FileWatcher struct {
	projectID   string
	projectRoot string
	watcher     *fsnotify.Watcher
	logger      *zap.Logger

	// Subscriber management
	mu          sync.RWMutex
	subscribers map[string]*FileWatcherSubscriber

	// Control channels
	stopCh    chan struct{}
	stoppedCh chan struct{}

	// State
	isRunning bool
}

// NewFileWatcher creates a new file watcher for a project
func NewFileWatcher(projectID, projectRoot string, logger *zap.Logger) (*FileWatcher, error) {
	if logger == nil {
		logger = zap.NewNop()
	}

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("failed to create fsnotify watcher: %w", err)
	}

	fw := &FileWatcher{
		projectID:   projectID,
		projectRoot: projectRoot,
		watcher:     watcher,
		logger:      logger.Named("FileWatcher").With(zap.String("projectID", projectID)),
		subscribers: make(map[string]*FileWatcherSubscriber),
		stopCh:      make(chan struct{}),
		stoppedCh:   make(chan struct{}),
		isRunning:   false,
	}

	return fw, nil
}

// Start begins watching the project directory
func (fw *FileWatcher) Start(ctx context.Context) error {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if fw.isRunning {
		return nil // Already running
	}

	// Add the project root to the watcher
	err := fw.addDirectoryRecursive(fw.projectRoot)
	if err != nil {
		return fmt.Errorf("failed to add project root to watcher: %w", err)
	}

	fw.isRunning = true

	// Log all watched directories for debugging
	watchedDirs := fw.getWatchedDirectories()
	fw.logger.Info("File watcher started",
		zap.String("projectRoot", fw.projectRoot),
		zap.Int("watchedDirectoryCount", len(watchedDirs)),
		zap.Strings("watchedDirectories", watchedDirs))

	// Start the event processing goroutine
	go fw.watchLoop(ctx)

	return nil
}

// Stop stops the file watcher
func (fw *FileWatcher) Stop() {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if !fw.isRunning {
		return // Already stopped
	}

	fw.logger.Info("Stopping file watcher")

	// Signal stop and wait for completion
	close(fw.stopCh)
	<-fw.stoppedCh

	// Close the underlying watcher
	fw.watcher.Close()

	// Close all subscriber channels
	for id, sub := range fw.subscribers {
		close(sub.Channel)
		sub.Cancel()
		delete(fw.subscribers, id)
	}

	fw.isRunning = false
	fw.logger.Info("File watcher stopped")
}

// Subscribe adds a new subscriber to receive file change events
func (fw *FileWatcher) Subscribe(ctx context.Context, subscriberID string) (*FileWatcherSubscriber, error) {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	// Remove existing subscriber with same ID if exists
	if existing, exists := fw.subscribers[subscriberID]; exists {
		close(existing.Channel)
		existing.Cancel()
		delete(fw.subscribers, subscriberID)
	}

	// Create new subscriber
	subCtx, cancel := context.WithCancel(ctx)
	subscriber := &FileWatcherSubscriber{
		ID:      subscriberID,
		Channel: make(chan FileWatcherEvent, 100), // Buffered channel
		Ctx:     subCtx,
		Cancel:  cancel,
	}

	fw.subscribers[subscriberID] = subscriber
	fw.logger.Debug("New subscriber added", zap.String("subscriberID", subscriberID))

	// Send initial connected event
	go func() {
		select {
		case subscriber.Channel <- FileWatcherEvent{
			Type: WatcherEventConnected,
			Data: map[string]interface{}{
				"message":   "File watcher connected",
				"timestamp": time.Now().Format(time.RFC3339),
			},
		}:
		case <-subCtx.Done():
		case <-time.After(5 * time.Second):
			fw.logger.Warn("Timeout sending connected event to subscriber", zap.String("subscriberID", subscriberID))
		}
	}()

	return subscriber, nil
}

// Unsubscribe removes a subscriber
func (fw *FileWatcher) Unsubscribe(subscriberID string) {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if subscriber, exists := fw.subscribers[subscriberID]; exists {
		close(subscriber.Channel)
		subscriber.Cancel()
		delete(fw.subscribers, subscriberID)
		fw.logger.Debug("Subscriber removed", zap.String("subscriberID", subscriberID))
	}
}

// watchLoop is the main event processing loop
func (fw *FileWatcher) watchLoop(ctx context.Context) {
	defer close(fw.stoppedCh)

	for {
		select {
		case <-ctx.Done():
			fw.logger.Info("File watcher stopping due to context cancellation")
			return
		case <-fw.stopCh:
			fw.logger.Info("File watcher stopping due to stop signal")
			return
		case event, ok := <-fw.watcher.Events:
			if !ok {
				fw.logger.Warn("Watcher events channel closed")
				return
			}
			fw.handleFileEvent(event)
		case err, ok := <-fw.watcher.Errors:
			if !ok {
				fw.logger.Warn("Watcher errors channel closed")
				return
			}
			fw.handleWatcherError(err)
		}
	}
}

// handleFileEvent processes a file system event
func (fw *FileWatcher) handleFileEvent(event fsnotify.Event) {
	// Convert absolute path to relative path
	relPath, err := fw.getRelativePath(event.Name)
	if err != nil {
		fw.logger.Error("Failed to get relative path", zap.String("path", event.Name), zap.Error(err))
		return
	}

	fw.logger.Debug("Raw file event received",
		zap.String("absolutePath", event.Name),
		zap.String("relativePath", relPath),
		zap.String("operation", event.Op.String()))

	// Skip hidden files and directories (starting with .)
	if strings.HasPrefix(filepath.Base(relPath), ".") {
		fw.logger.Debug("Skipping hidden file/directory", zap.String("path", relPath))
		return
	}

	// Determine event type
	var changeType FileChangeEventType
	var isDir bool

	switch {
	case event.Op&fsnotify.Create == fsnotify.Create:
		changeType = FileChangeCreate
		isDir = fw.isDirectory(event.Name)
		fw.logger.Debug("Create event detected",
			zap.String("path", relPath),
			zap.Bool("isDir", isDir))

		// If it's a new directory, add it and all subdirectories to the watcher
		if isDir {
			fw.logger.Info("Adding new directory to watcher recursively", zap.String("path", event.Name))
			err := fw.addDirectoryRecursive(event.Name)
			if err != nil {
				fw.logger.Error("Failed to add new directory recursively to watcher",
					zap.String("path", event.Name), zap.Error(err))
			} else {
				fw.logger.Info("Successfully added new directory recursively to watcher",
					zap.String("path", event.Name))
			}
		}
	case event.Op&fsnotify.Write == fsnotify.Write:
		changeType = FileChangeModify
		isDir = fw.isDirectory(event.Name)
		fw.logger.Debug("Write/Modify event detected",
			zap.String("path", relPath),
			zap.Bool("isDir", isDir))
	case event.Op&fsnotify.Remove == fsnotify.Remove:
		changeType = FileChangeDelete
		isDir = false // We can't check if removed path was a directory
		fw.logger.Debug("Remove event detected", zap.String("path", relPath))
	case event.Op&fsnotify.Rename == fsnotify.Rename:
		changeType = FileChangeRename
		isDir = fw.isDirectory(event.Name)
		fw.logger.Debug("Rename event detected",
			zap.String("path", relPath),
			zap.Bool("isDir", isDir))
	default:
		// Unknown event type, skip
		fw.logger.Debug("Unknown event type, skipping",
			zap.String("path", relPath),
			zap.String("operation", event.Op.String()))
		return
	}

	changeEvent := FileChangeEvent{
		Type:      changeType,
		Path:      relPath,
		IsDir:     isDir,
		Timestamp: time.Now(),
	}

	fw.logger.Info("File change detected and broadcasting",
		zap.String("type", string(changeType)),
		zap.String("path", relPath),
		zap.Bool("isDir", isDir))

	// Broadcast to all subscribers
	fw.broadcastEvent(FileWatcherEvent{
		Type: WatcherEventFileChange,
		Data: changeEvent,
	})
}

// handleWatcherError processes watcher errors
func (fw *FileWatcher) handleWatcherError(err error) {
	fw.logger.Error("File watcher error", zap.Error(err))

	fw.broadcastEvent(FileWatcherEvent{
		Type: WatcherEventError,
		Data: map[string]interface{}{
			"message":   err.Error(),
			"timestamp": time.Now().Format(time.RFC3339),
		},
	})
}

// broadcastEvent sends an event to all subscribers
func (fw *FileWatcher) broadcastEvent(event FileWatcherEvent) {
	fw.mu.RLock()
	defer fw.mu.RUnlock()

	for subscriberID, subscriber := range fw.subscribers {
		select {
		case subscriber.Channel <- event:
			// Event sent successfully
		case <-time.After(1 * time.Second):
			fw.logger.Warn("Timeout sending event to subscriber, removing", zap.String("subscriberID", subscriberID))
			// Remove slow subscriber
			go func(id string, sub *FileWatcherSubscriber) {
				fw.mu.Lock()
				defer fw.mu.Unlock()
				if _, exists := fw.subscribers[id]; exists {
					close(sub.Channel)
					sub.Cancel()
					delete(fw.subscribers, id)
				}
			}(subscriberID, subscriber)
		}
	}
}

// addDirectoryRecursive adds a directory and all its subdirectories to the watcher
func (fw *FileWatcher) addDirectoryRecursive(root string) error {
	fw.logger.Info("Starting recursive directory addition", zap.String("rootPath", root))

	dirCount := 0
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			fw.logger.Warn("Error walking directory", zap.String("path", path), zap.Error(err))
			return nil // Continue walking
		}

		// Check if it's a directory
		if !info.IsDir() {
			return nil
		}

		// Skip hidden directories
		if strings.HasPrefix(filepath.Base(path), ".") {
			fw.logger.Debug("Skipping hidden directory", zap.String("path", path))
			return filepath.SkipDir
		}

		err = fw.watcher.Add(path)
		if err != nil {
			fw.logger.Error("Failed to add directory to watcher", zap.String("path", path), zap.Error(err))
			return nil // Continue walking
		}

		dirCount++
		relPath, _ := fw.getRelativePath(path)
		if relPath == "" {
			relPath = "."
		}
		fw.logger.Debug("Added directory to watcher",
			zap.String("absolutePath", path),
			zap.String("relativePath", relPath))
		return nil
	})

	if err != nil {
		fw.logger.Error("Failed to complete recursive directory addition",
			zap.String("rootPath", root),
			zap.Int("directoriesAdded", dirCount),
			zap.Error(err))
		return err
	}

	fw.logger.Info("Completed recursive directory addition",
		zap.String("rootPath", root),
		zap.Int("directoriesAdded", dirCount))
	return nil
}

// getRelativePath converts an absolute path to a relative path from project root
func (fw *FileWatcher) getRelativePath(absPath string) (string, error) {
	relPath, err := filepath.Rel(fw.projectRoot, absPath)
	if err != nil {
		return "", err
	}

	// Convert to forward slashes for consistency
	relPath = filepath.ToSlash(relPath)

	// If the relative path is ".", return empty string (project root)
	if relPath == "." {
		relPath = ""
	}

	return relPath, nil
}

// isDirectory checks if a path is a directory
func (fw *FileWatcher) isDirectory(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

// IsRunning returns whether the watcher is currently running
func (fw *FileWatcher) IsRunning() bool {
	fw.mu.RLock()
	defer fw.mu.RUnlock()
	return fw.isRunning
}

// GetSubscriberCount returns the number of active subscribers
func (fw *FileWatcher) GetSubscriberCount() int {
	fw.mu.RLock()
	defer fw.mu.RUnlock()
	return len(fw.subscribers)
}

// getWatchedDirectories returns a list of directories currently being watched
func (fw *FileWatcher) getWatchedDirectories() []string {
	if fw.watcher == nil {
		return []string{}
	}

	watchList := fw.watcher.WatchList()
	dirs := make([]string, len(watchList))

	// Convert absolute paths to relative paths for better readability
	for i, absPath := range watchList {
		if relPath, err := fw.getRelativePath(absPath); err == nil {
			if relPath == "" {
				dirs[i] = "." // Project root
			} else {
				dirs[i] = relPath
			}
		} else {
			dirs[i] = absPath // Fallback to absolute path
		}
	}

	return dirs
}
