package multiagent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SubAgent represents a single agent instance in the multi-agent system
type SubAgent struct {
	ID          string
	Index       int
	Description string
	Prompt      string
	State       AgentState
	StartTime   time.Time
	Context     *SubAgentContext
	Results     *AgentResult
	Error       error
}

// AgentState represents the current state of a SubAgent
type AgentState int

const (
	AgentStateInitializing AgentState = iota
	AgentStateRunning
	AgentStateWaiting
	AgentStateCompleted
	AgentStateFailed
	AgentStateAborted
)

// String returns the string representation of AgentState
func (as AgentState) String() string {
	switch as {
	case AgentStateInitializing:
		return "initializing"
	case AgentStateRunning:
		return "running"
	case AgentStateWaiting:
		return "waiting"
	case AgentStateCompleted:
		return "completed"
	case AgentStateFailed:
		return "failed"
	case AgentStateAborted:
		return "aborted"
	default:
		return "unknown"
	}
}

// SubAgentContext represents the isolated execution context for a SubAgent
type SubAgentContext struct {
	AgentID              string
	ParentConversationID string
	AllowedTools         []string
	ResourceLimits       *ResourceLimits
	AbortController      context.CancelFunc
	ToolCallCount        int
	TokenCount           int
	ExecutionStartTime   time.Time
}

// ResourceLimits defines the resource constraints for a SubAgent
type ResourceLimits struct {
	MaxExecutionTime  time.Duration
	MaxTokens         int
	MaxToolCalls      int
	MaxFileOperations int
}

// AgentResult represents the result from a SubAgent execution
type AgentResult struct {
	AgentIndex    int
	Content       string
	ToolUseCount  int
	Tokens        int
	ExecutionTime time.Duration
	Success       bool
	Error         error
}

// SubAgentManager manages the lifecycle of SubAgents
type SubAgentManager struct {
	logger          *zap.SugaredLogger
	activeAgents    map[string]*SubAgent
	completedAgents map[string]*SubAgent
	agentCounter    int
	mutex           sync.RWMutex
}

// NewSubAgentManager creates a new SubAgentManager instance
func NewSubAgentManager(logger *zap.SugaredLogger) *SubAgentManager {
	return &SubAgentManager{
		logger:          logger.Named("SubAgentManager"),
		activeAgents:    make(map[string]*SubAgent),
		completedAgents: make(map[string]*SubAgent),
		agentCounter:    0,
	}
}

// CreateAgent creates a new SubAgent instance
func (sam *SubAgentManager) CreateAgent(
	taskDescription string,
	taskPrompt string,
	parentConversationID string,
) *SubAgent {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	agentID := sam.generateAgentID()
	agentIndex := sam.agentCounter
	sam.agentCounter++

	agent := &SubAgent{
		ID:          agentID,
		Index:       agentIndex,
		Description: taskDescription,
		Prompt:      taskPrompt,
		State:       AgentStateInitializing,
		StartTime:   time.Now(),
		Context:     sam.createIsolatedContext(agentID, parentConversationID),
	}

	sam.activeAgents[agentID] = agent

	sam.logger.Infow("Created new SubAgent",
		"agentID", agentID,
		"index", agentIndex,
		"parentConversationID", parentConversationID)

	return agent
}

// generateAgentID generates a unique ID for a SubAgent
func (sam *SubAgentManager) generateAgentID() string {
	return fmt.Sprintf("agent_%d_%d", time.Now().UnixNano(), sam.agentCounter)
}

// createIsolatedContext creates an isolated execution context for a SubAgent
func (sam *SubAgentManager) createIsolatedContext(agentID, parentConversationID string) *SubAgentContext {
	ctx, cancel := context.WithCancel(context.Background())
	_ = ctx // We'll use the cancel function for abort control

	return &SubAgentContext{
		AgentID:              agentID,
		ParentConversationID: parentConversationID,
		AllowedTools:         sam.getAllowedTools(),
		ResourceLimits:       sam.getDefaultResourceLimits(),
		AbortController:      cancel,
		ToolCallCount:        0,
		TokenCount:           0,
		ExecutionStartTime:   time.Now(),
	}
}

// getAllowedTools returns the list of tools allowed for SubAgents
func (sam *SubAgentManager) getAllowedTools() []string {
	// SubAgent tool whitelist - excludes Task tool to prevent recursion
	return []string{
		"mcp.projectfs.read_file",
		"mcp.projectfs.write_file",
		"mcp.projectfs.list_files",
		"mcp.projectfs.create_fs_object",
		"mcp.projectfs.delete_fs_object",
		"mcp.runtime.bash_exec",
		// Add other safe tools as needed
	}
}

// getDefaultResourceLimits returns the default resource limits for SubAgents
func (sam *SubAgentManager) getDefaultResourceLimits() *ResourceLimits {
	return &ResourceLimits{
		MaxExecutionTime:  5 * time.Minute,
		MaxTokens:         100000,
		MaxToolCalls:      50,
		MaxFileOperations: 100,
	}
}

// UpdateAgentState updates the state of a SubAgent
func (sam *SubAgentManager) UpdateAgentState(agentID string, state AgentState) {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	if agent, exists := sam.activeAgents[agentID]; exists {
		agent.State = state
		sam.logger.Debugw("Updated agent state", "agentID", agentID, "state", state.String())
	}
}

// CompleteAgent marks a SubAgent as completed and moves it to completed agents
func (sam *SubAgentManager) CompleteAgent(agentID string, result *AgentResult) {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	if agent, exists := sam.activeAgents[agentID]; exists {
		agent.State = AgentStateCompleted
		agent.Results = result

		// Move to completed agents
		sam.completedAgents[agentID] = agent
		delete(sam.activeAgents, agentID)

		// Clean up resources
		if agent.Context.AbortController != nil {
			agent.Context.AbortController()
		}

		sam.logger.Infow("Agent completed",
			"agentID", agentID,
			"executionTime", time.Since(agent.StartTime),
			"success", result.Success)
	}
}

// FailAgent marks a SubAgent as failed
func (sam *SubAgentManager) FailAgent(agentID string, err error) {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	if agent, exists := sam.activeAgents[agentID]; exists {
		agent.State = AgentStateFailed
		agent.Error = err

		// Move to completed agents
		sam.completedAgents[agentID] = agent
		delete(sam.activeAgents, agentID)

		// Clean up resources
		if agent.Context.AbortController != nil {
			agent.Context.AbortController()
		}

		sam.logger.Errorw("Agent failed",
			"agentID", agentID,
			"executionTime", time.Since(agent.StartTime),
			"error", err)
	}
}

// AbortAgent aborts a running SubAgent
func (sam *SubAgentManager) AbortAgent(agentID string) {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	if agent, exists := sam.activeAgents[agentID]; exists {
		agent.State = AgentStateAborted

		// Trigger abort
		if agent.Context.AbortController != nil {
			agent.Context.AbortController()
		}

		// Move to completed agents
		sam.completedAgents[agentID] = agent
		delete(sam.activeAgents, agentID)

		sam.logger.Warnw("Agent aborted",
			"agentID", agentID,
			"executionTime", time.Since(agent.StartTime))
	}
}

// AbortAllAgents aborts all active SubAgents
func (sam *SubAgentManager) AbortAllAgents() {
	sam.mutex.Lock()
	defer sam.mutex.Unlock()

	for agentID, agent := range sam.activeAgents {
		agent.State = AgentStateAborted

		if agent.Context.AbortController != nil {
			agent.Context.AbortController()
		}

		sam.completedAgents[agentID] = agent
		sam.logger.Warnw("Agent aborted during cleanup", "agentID", agentID)
	}

	// Clear active agents
	sam.activeAgents = make(map[string]*SubAgent)

	sam.logger.Info("All agents aborted")
}

// GetActiveAgents returns a copy of all active agents
func (sam *SubAgentManager) GetActiveAgents() map[string]*SubAgent {
	sam.mutex.RLock()
	defer sam.mutex.RUnlock()

	result := make(map[string]*SubAgent)
	for id, agent := range sam.activeAgents {
		result[id] = agent
	}
	return result
}

// GetCompletedAgents returns a copy of all completed agents
func (sam *SubAgentManager) GetCompletedAgents() map[string]*SubAgent {
	sam.mutex.RLock()
	defer sam.mutex.RUnlock()

	result := make(map[string]*SubAgent)
	for id, agent := range sam.completedAgents {
		result[id] = agent
	}
	return result
}

// GetAgentStatus returns the current status of all agents
func (sam *SubAgentManager) GetAgentStatus() map[string]interface{} {
	sam.mutex.RLock()
	defer sam.mutex.RUnlock()

	return map[string]interface{}{
		"active_count":     len(sam.activeAgents),
		"completed_count":  len(sam.completedAgents),
		"total_created":    sam.agentCounter,
		"active_agents":    sam.getAgentSummaries(sam.activeAgents),
		"completed_agents": sam.getAgentSummaries(sam.completedAgents),
	}
}

// getAgentSummaries returns summary information for a set of agents
func (sam *SubAgentManager) getAgentSummaries(agents map[string]*SubAgent) []map[string]interface{} {
	summaries := make([]map[string]interface{}, 0, len(agents))

	for _, agent := range agents {
		summary := map[string]interface{}{
			"id":             agent.ID,
			"index":          agent.Index,
			"state":          agent.State.String(),
			"start_time":     agent.StartTime,
			"execution_time": time.Since(agent.StartTime),
		}

		if agent.Results != nil {
			summary["success"] = agent.Results.Success
			summary["tool_use_count"] = agent.Results.ToolUseCount
			summary["tokens"] = agent.Results.Tokens
		}

		if agent.Error != nil {
			summary["error"] = agent.Error.Error()
		}

		summaries = append(summaries, summary)
	}

	return summaries
}
