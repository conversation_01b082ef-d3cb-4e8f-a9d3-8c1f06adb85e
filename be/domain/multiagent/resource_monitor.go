package multiagent

import (
	"sync"
	"time"

	"go.uber.org/zap"
)

// ResourceMonitor monitors resource usage across all SubAgents
type ResourceMonitor struct {
	logger *zap.SugaredLogger
	mutex  sync.RWMutex
	
	// Global resource tracking
	totalAgents       int
	totalTokensUsed   int
	totalToolCalls    int
	totalExecutionTime time.Duration
	
	// Per-agent resource tracking
	agentResources map[string]*AgentResourceUsage
}

// AgentResourceUsage tracks resource usage for a single agent
type AgentResourceUsage struct {
	AgentID        string
	StartTime      time.Time
	TokensUsed     int
	ToolCalls      int
	ExecutionTime  time.Duration
	LastUpdate     time.Time
}

// NewResourceMonitor creates a new ResourceMonitor instance
func NewResourceMonitor(logger *zap.SugaredLogger) *ResourceMonitor {
	return &ResourceMonitor{
		logger:         logger.Named("ResourceMonitor"),
		agentResources: make(map[string]*AgentResourceUsage),
	}
}

// StartMonitoring begins monitoring for a new agent
func (rm *ResourceMonitor) StartMonitoring(agentID string) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	rm.totalAgents++
	rm.agentResources[agentID] = &AgentResourceUsage{
		AgentID:    agentID,
		StartTime:  time.Now(),
		LastUpdate: time.Now(),
	}
	
	rm.logger.Debugw("Started monitoring agent", "agentID", agentID, "totalAgents", rm.totalAgents)
}

// StopMonitoring stops monitoring for an agent and returns final usage
func (rm *ResourceMonitor) StopMonitoring(agentID string) *AgentResourceUsage {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	usage, exists := rm.agentResources[agentID]
	if !exists {
		rm.logger.Warnw("Attempted to stop monitoring non-existent agent", "agentID", agentID)
		return nil
	}
	
	// Update final execution time
	usage.ExecutionTime = time.Since(usage.StartTime)
	usage.LastUpdate = time.Now()
	
	// Update global totals
	rm.totalTokensUsed += usage.TokensUsed
	rm.totalToolCalls += usage.ToolCalls
	rm.totalExecutionTime += usage.ExecutionTime
	
	// Remove from active monitoring
	delete(rm.agentResources, agentID)
	
	rm.logger.Debugw("Stopped monitoring agent", 
		"agentID", agentID,
		"tokensUsed", usage.TokensUsed,
		"toolCalls", usage.ToolCalls,
		"executionTime", usage.ExecutionTime)
	
	return usage
}

// UpdateTokenUsage updates token usage for an agent
func (rm *ResourceMonitor) UpdateTokenUsage(agentID string, tokens int) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	if usage, exists := rm.agentResources[agentID]; exists {
		usage.TokensUsed += tokens
		usage.LastUpdate = time.Now()
		
		rm.logger.Debugw("Updated token usage", 
			"agentID", agentID, 
			"addedTokens", tokens, 
			"totalTokens", usage.TokensUsed)
	}
}

// UpdateToolCallCount updates tool call count for an agent
func (rm *ResourceMonitor) UpdateToolCallCount(agentID string) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	if usage, exists := rm.agentResources[agentID]; exists {
		usage.ToolCalls++
		usage.LastUpdate = time.Now()
		
		rm.logger.Debugw("Updated tool call count", 
			"agentID", agentID, 
			"totalToolCalls", usage.ToolCalls)
	}
}

// GetAgentUsage returns current usage for a specific agent
func (rm *ResourceMonitor) GetAgentUsage(agentID string) *AgentResourceUsage {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	if usage, exists := rm.agentResources[agentID]; exists {
		// Return a copy to avoid race conditions
		return &AgentResourceUsage{
			AgentID:        usage.AgentID,
			StartTime:      usage.StartTime,
			TokensUsed:     usage.TokensUsed,
			ToolCalls:      usage.ToolCalls,
			ExecutionTime:  time.Since(usage.StartTime),
			LastUpdate:     usage.LastUpdate,
		}
	}
	
	return nil
}

// GetGlobalUsage returns global resource usage statistics
func (rm *ResourceMonitor) GetGlobalUsage() *GlobalResourceUsage {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	// Calculate current active usage
	currentTokens := 0
	currentToolCalls := 0
	activeAgents := len(rm.agentResources)
	
	for _, usage := range rm.agentResources {
		currentTokens += usage.TokensUsed
		currentToolCalls += usage.ToolCalls
	}
	
	return &GlobalResourceUsage{
		TotalAgentsCreated:    rm.totalAgents,
		ActiveAgents:          activeAgents,
		TotalTokensUsed:       rm.totalTokensUsed + currentTokens,
		TotalToolCalls:        rm.totalToolCalls + currentToolCalls,
		TotalExecutionTime:    rm.totalExecutionTime,
		CurrentActiveTokens:   currentTokens,
		CurrentActiveToolCalls: currentToolCalls,
	}
}

// GetAllActiveUsage returns usage for all currently active agents
func (rm *ResourceMonitor) GetAllActiveUsage() map[string]*AgentResourceUsage {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	result := make(map[string]*AgentResourceUsage)
	for agentID, usage := range rm.agentResources {
		result[agentID] = &AgentResourceUsage{
			AgentID:        usage.AgentID,
			StartTime:      usage.StartTime,
			TokensUsed:     usage.TokensUsed,
			ToolCalls:      usage.ToolCalls,
			ExecutionTime:  time.Since(usage.StartTime),
			LastUpdate:     usage.LastUpdate,
		}
	}
	
	return result
}

// CheckResourceLimits checks if global resource limits are exceeded
func (rm *ResourceMonitor) CheckResourceLimits(limits *GlobalResourceLimits) []string {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	var violations []string
	
	// Check active agent limit
	if limits.MaxActiveAgents > 0 && len(rm.agentResources) > limits.MaxActiveAgents {
		violations = append(violations, "Maximum active agents exceeded")
	}
	
	// Check total token limit
	currentTokens := 0
	for _, usage := range rm.agentResources {
		currentTokens += usage.TokensUsed
	}
	totalTokens := rm.totalTokensUsed + currentTokens
	
	if limits.MaxTotalTokens > 0 && totalTokens > limits.MaxTotalTokens {
		violations = append(violations, "Maximum total tokens exceeded")
	}
	
	// Check total tool calls limit
	currentToolCalls := 0
	for _, usage := range rm.agentResources {
		currentToolCalls += usage.ToolCalls
	}
	totalToolCalls := rm.totalToolCalls + currentToolCalls
	
	if limits.MaxTotalToolCalls > 0 && totalToolCalls > limits.MaxTotalToolCalls {
		violations = append(violations, "Maximum total tool calls exceeded")
	}
	
	return violations
}

// Reset resets all monitoring data
func (rm *ResourceMonitor) Reset() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	rm.totalAgents = 0
	rm.totalTokensUsed = 0
	rm.totalToolCalls = 0
	rm.totalExecutionTime = 0
	rm.agentResources = make(map[string]*AgentResourceUsage)
	
	rm.logger.Info("Resource monitor reset")
}

// GlobalResourceUsage represents global resource usage statistics
type GlobalResourceUsage struct {
	TotalAgentsCreated     int           `json:"total_agents_created"`
	ActiveAgents           int           `json:"active_agents"`
	TotalTokensUsed        int           `json:"total_tokens_used"`
	TotalToolCalls         int           `json:"total_tool_calls"`
	TotalExecutionTime     time.Duration `json:"total_execution_time"`
	CurrentActiveTokens    int           `json:"current_active_tokens"`
	CurrentActiveToolCalls int           `json:"current_active_tool_calls"`
}

// GlobalResourceLimits defines global resource limits
type GlobalResourceLimits struct {
	MaxActiveAgents   int `json:"max_active_agents"`
	MaxTotalTokens    int `json:"max_total_tokens"`
	MaxTotalToolCalls int `json:"max_total_tool_calls"`
}
