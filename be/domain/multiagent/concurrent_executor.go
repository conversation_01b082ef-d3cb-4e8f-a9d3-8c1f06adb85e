package multiagent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ConcurrentExecutor manages the concurrent execution of multiple SubAgents
// This is the implementation of the UH1 function from the Claude Code architecture
type ConcurrentExecutor struct {
	maxConcurrency int
	logger         *zap.SugaredLogger
	activeJobs     map[string]*ExecutionJob
	mutex          sync.RWMutex
}

// ExecutionJob represents a single agent execution job
type ExecutionJob struct {
	ID        string
	AgentID   string
	StartTime time.Time
	Context   context.Context
	Cancel    context.CancelFunc
	Done      chan *ExecutionResult
}

// ExecutionResult represents the result of an agent execution
type ExecutionResult struct {
	JobID     string
	AgentID   string
	Success   bool
	Result    *AgentResult
	Error     error
	Duration  time.Duration
}

// AgentExecutor defines the interface for executing a single agent
type AgentExecutor func(ctx context.Context, agent *SubAgent) (*AgentResult, error)

// NewConcurrentExecutor creates a new ConcurrentExecutor instance
func NewConcurrentExecutor(maxConcurrency int, logger *zap.SugaredLogger) *ConcurrentExecutor {
	if maxConcurrency <= 0 {
		maxConcurrency = 10 // Default maximum concurrency
	}

	return &ConcurrentExecutor{
		maxConcurrency: maxConcurrency,
		logger:         logger.Named("ConcurrentExecutor"),
		activeJobs:     make(map[string]*ExecutionJob),
	}
}

// ExecuteConcurrently executes multiple agents concurrently with controlled concurrency
// This is the core implementation of the UH1 concurrent execution pattern
func (ce *ConcurrentExecutor) ExecuteConcurrently(
	ctx context.Context,
	agents []*SubAgent,
	executor AgentExecutor,
) ([]*ExecutionResult, error) {
	if len(agents) == 0 {
		return []*ExecutionResult{}, nil
	}

	ce.logger.Infow("Starting concurrent execution", 
		"agentCount", len(agents), 
		"maxConcurrency", ce.maxConcurrency)

	// Create a semaphore to limit concurrency
	semaphore := make(chan struct{}, ce.maxConcurrency)
	results := make([]*ExecutionResult, len(agents))
	var wg sync.WaitGroup
	var resultMutex sync.Mutex

	// Execute agents with controlled concurrency
	for i, agent := range agents {
		wg.Add(1)
		go func(index int, ag *SubAgent) {
			defer wg.Done()

			// Acquire semaphore
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-ctx.Done():
				resultMutex.Lock()
				results[index] = &ExecutionResult{
					AgentID:  ag.ID,
					Success:  false,
					Error:    ctx.Err(),
					Duration: 0,
				}
				resultMutex.Unlock()
				return
			}

			// Execute the agent
			result := ce.executeAgent(ctx, ag, executor)
			
			resultMutex.Lock()
			results[index] = result
			resultMutex.Unlock()
		}(i, agent)
	}

	// Wait for all agents to complete
	wg.Wait()

	// Count successful executions
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	ce.logger.Infow("Concurrent execution completed", 
		"totalAgents", len(agents),
		"successfulAgents", successCount,
		"failedAgents", len(agents)-successCount)

	return results, nil
}

// executeAgent executes a single agent and returns the result
func (ce *ConcurrentExecutor) executeAgent(
	ctx context.Context,
	agent *SubAgent,
	executor AgentExecutor,
) *ExecutionResult {
	startTime := time.Now()
	jobID := fmt.Sprintf("job_%s_%d", agent.ID, startTime.UnixNano())

	ce.logger.Debugw("Starting agent execution", "agentID", agent.ID, "jobID", jobID)

	// Create job context with timeout
	jobCtx, cancel := context.WithTimeout(ctx, agent.Context.ResourceLimits.MaxExecutionTime)
	defer cancel()

	// Register the job
	job := &ExecutionJob{
		ID:        jobID,
		AgentID:   agent.ID,
		StartTime: startTime,
		Context:   jobCtx,
		Cancel:    cancel,
		Done:      make(chan *ExecutionResult, 1),
	}

	ce.registerJob(job)
	defer ce.unregisterJob(jobID)

	// Execute the agent
	result, err := executor(jobCtx, agent)
	duration := time.Since(startTime)

	executionResult := &ExecutionResult{
		JobID:     jobID,
		AgentID:   agent.ID,
		Success:   err == nil && result != nil,
		Result:    result,
		Error:     err,
		Duration:  duration,
	}

	if err != nil {
		ce.logger.Errorw("Agent execution failed", 
			"agentID", agent.ID, 
			"jobID", jobID,
			"duration", duration,
			"error", err)
	} else {
		ce.logger.Debugw("Agent execution completed", 
			"agentID", agent.ID, 
			"jobID", jobID,
			"duration", duration,
			"success", executionResult.Success)
	}

	return executionResult
}

// ExecuteSequentially executes agents one by one (fallback for non-concurrent-safe operations)
func (ce *ConcurrentExecutor) ExecuteSequentially(
	ctx context.Context,
	agents []*SubAgent,
	executor AgentExecutor,
) ([]*ExecutionResult, error) {
	ce.logger.Infow("Starting sequential execution", "agentCount", len(agents))

	results := make([]*ExecutionResult, len(agents))

	for i, agent := range agents {
		select {
		case <-ctx.Done():
			// Context cancelled, return partial results
			return results[:i], ctx.Err()
		default:
			result := ce.executeAgent(ctx, agent, executor)
			results[i] = result

			// If this agent failed critically, we might want to stop
			if result.Error != nil && ce.isCriticalError(result.Error) {
				ce.logger.Errorw("Critical error in sequential execution, stopping", 
					"agentID", agent.ID, 
					"error", result.Error)
				return results[:i+1], result.Error
			}
		}
	}

	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	ce.logger.Infow("Sequential execution completed", 
		"totalAgents", len(agents),
		"successfulAgents", successCount)

	return results, nil
}

// isCriticalError determines if an error should stop sequential execution
func (ce *ConcurrentExecutor) isCriticalError(err error) bool {
	// Define critical errors that should stop execution
	criticalErrors := []string{
		"context deadline exceeded",
		"context canceled",
		"permission denied",
		"resource exhausted",
	}

	errStr := err.Error()
	for _, critical := range criticalErrors {
		if contains(errStr, critical) {
			return true
		}
	}
	return false
}

// registerJob registers an active execution job
func (ce *ConcurrentExecutor) registerJob(job *ExecutionJob) {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()
	ce.activeJobs[job.ID] = job
}

// unregisterJob removes a completed execution job
func (ce *ConcurrentExecutor) unregisterJob(jobID string) {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()
	delete(ce.activeJobs, jobID)
}

// AbortAllJobs aborts all active execution jobs
func (ce *ConcurrentExecutor) AbortAllJobs() {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()

	for jobID, job := range ce.activeJobs {
		job.Cancel()
		ce.logger.Warnw("Aborted execution job", "jobID", jobID, "agentID", job.AgentID)
	}

	// Clear all jobs
	ce.activeJobs = make(map[string]*ExecutionJob)
	ce.logger.Info("All execution jobs aborted")
}

// GetActiveJobs returns information about currently active jobs
func (ce *ConcurrentExecutor) GetActiveJobs() map[string]interface{} {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	jobs := make([]map[string]interface{}, 0, len(ce.activeJobs))
	for _, job := range ce.activeJobs {
		jobs = append(jobs, map[string]interface{}{
			"job_id":        job.ID,
			"agent_id":      job.AgentID,
			"start_time":    job.StartTime,
			"execution_time": time.Since(job.StartTime),
		})
	}

	return map[string]interface{}{
		"active_job_count": len(ce.activeJobs),
		"max_concurrency": ce.maxConcurrency,
		"active_jobs":     jobs,
	}
}

// SetMaxConcurrency updates the maximum concurrency limit
func (ce *ConcurrentExecutor) SetMaxConcurrency(maxConcurrency int) {
	if maxConcurrency <= 0 {
		maxConcurrency = 1
	}
	
	ce.mutex.Lock()
	defer ce.mutex.Unlock()
	
	oldMax := ce.maxConcurrency
	ce.maxConcurrency = maxConcurrency
	
	ce.logger.Infow("Updated max concurrency", 
		"oldMax", oldMax, 
		"newMax", maxConcurrency)
}

// GetMaxConcurrency returns the current maximum concurrency limit
func (ce *ConcurrentExecutor) GetMaxConcurrency() int {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()
	return ce.maxConcurrency
}

// Helper function to check if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr || 
		      containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
