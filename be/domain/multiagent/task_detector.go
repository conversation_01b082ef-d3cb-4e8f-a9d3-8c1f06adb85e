package multiagent

import (
	"strings"

	"go.uber.org/zap"
)

// TaskDetector determines whether a user request requires multi-agent processing
type TaskDetector struct {
	logger *zap.SugaredLogger
}

// NewTaskDetector creates a new TaskDetector instance
func NewTaskDetector(logger *zap.SugaredLogger) *TaskDetector {
	return &TaskDetector{
		logger: logger.Named("TaskDetector"),
	}
}

// ShouldUseMultiAgent determines if the user request should be processed by multiple agents
func (td *TaskDetector) ShouldUseMultiAgent(userInput string) bool {
	// Convert to lowercase for case-insensitive matching
	input := strings.ToLower(userInput)

	// Check for explicit multi-agent keywords (English + Chinese)
	multiAgentKeywords := []string{
		// English keywords
		"analyze", "research", "investigate", "explore",
		"compare", "evaluate", "assess", "review",
		"comprehensive", "thorough", "detailed", "complete",
		"multiple", "various", "different", "several",
		"architecture", "design", "structure", "pattern",
		"refactor", "improve", "optimize", "enhance",
		"implement", "create", "build", "develop",
		"find all", "search for", "look for", "identify",
		"best practices", "recommendations", "suggestions",
		"pros and cons", "advantages", "disadvantages",

		// Chinese keywords
		"分析", "研究", "调查", "探索", "探讨",
		"比较", "对比", "评估", "评价", "审查", "检查",
		"全面", "详细", "完整", "彻底", "深入",
		"多个", "各种", "不同", "几个", "多种", "包含",
		"架构", "设计", "结构", "模式", "框架", "系统",
		"重构", "改进", "优化", "增强", "提升",
		"实现", "创建", "构建", "开发", "编写",
		"查找", "搜索", "寻找", "识别", "定位",
		"最佳实践", "建议", "推荐", "方案",
		"优缺点", "优点", "缺点", "利弊", "好处", "坏处",
		"功能", "模块", "组件", "服务", "管理",
	}

	// Check for complex task indicators (English + Chinese)
	complexityIndicators := []string{
		// English indicators
		"how to", "what are", "which", "where",
		"explain", "describe", "tell me about",
		"help me", "assist me", "guide me",
		"step by step", "walkthrough", "tutorial",
		"examples", "samples", "demonstrations",

		// Chinese indicators
		"如何", "怎么", "怎样", "什么是", "哪个", "哪里",
		"解释", "描述", "说明", "介绍", "讲解",
		"帮我", "帮助我", "协助我", "指导我",
		"一步步", "逐步", "教程", "指南",
		"例子", "示例", "样例", "演示", "案例",
	}

	// Check for file/code analysis keywords (English + Chinese)
	codeAnalysisKeywords := []string{
		// English keywords
		"code", "file", "function", "class", "method",
		"variable", "import", "export", "module",
		"component", "service", "controller", "model",
		"database", "api", "endpoint", "route",
		"test", "spec", "config", "configuration",

		// Chinese keywords
		"代码", "文件", "函数", "方法", "类", "接口",
		"变量", "导入", "导出", "模块", "包",
		"组件", "服务", "控制器", "模型", "实体",
		"数据库", "接口", "端点", "路由", "路径",
		"测试", "配置", "设置", "参数",
	}

	// Count keyword matches
	multiAgentScore := 0
	complexityScore := 0
	codeAnalysisScore := 0

	for _, keyword := range multiAgentKeywords {
		if strings.Contains(input, keyword) {
			multiAgentScore++
		}
	}

	for _, keyword := range complexityIndicators {
		if strings.Contains(input, keyword) {
			complexityScore++
		}
	}

	for _, keyword := range codeAnalysisKeywords {
		if strings.Contains(input, keyword) {
			codeAnalysisScore++
		}
	}

	// Check for length and complexity (improved for Chinese)
	wordCount := td.getEffectiveWordCount(input)
	sentenceCount := strings.Count(input, ".") + strings.Count(input, "?") + strings.Count(input, "!") +
		strings.Count(input, "。") + strings.Count(input, "？") + strings.Count(input, "！")

	// Decision logic
	shouldUseMultiAgent := false

	// High priority: explicit multi-agent keywords
	if multiAgentScore >= 2 {
		shouldUseMultiAgent = true
		td.logger.Debugw("Multi-agent triggered by keywords", "score", multiAgentScore)
	}

	// Medium priority: complex analysis tasks
	if complexityScore >= 1 && codeAnalysisScore >= 2 {
		shouldUseMultiAgent = true
		td.logger.Debugw("Multi-agent triggered by complexity", "complexityScore", complexityScore, "codeScore", codeAnalysisScore)
	}

	// Length-based heuristics (adjusted for Chinese)
	if wordCount > 15 && sentenceCount > 1 {
		shouldUseMultiAgent = true
		td.logger.Debugw("Multi-agent triggered by length", "wordCount", wordCount, "sentenceCount", sentenceCount)
	}

	// Special patterns that benefit from multi-agent (English + Chinese)
	specialPatterns := []string{
		// English patterns
		"find", "search", "locate", "discover",
		"all files", "all functions", "all classes",
		"entire project", "whole codebase", "complete system",
		"best way", "better approach", "alternative",
		"performance", "security", "scalability",
		"maintainability", "readability", "testability",

		// Chinese patterns
		"查找", "搜索", "定位", "发现",
		"所有文件", "所有函数", "所有类", "全部",
		"整个项目", "整个代码库", "完整系统", "全部代码",
		"最好的方法", "更好的方法", "替代方案", "其他方法",
		"性能", "安全", "可扩展性", "扩展性",
		"可维护性", "可读性", "可测试性", "维护性",
	}

	for _, pattern := range specialPatterns {
		if strings.Contains(input, pattern) {
			shouldUseMultiAgent = true
			td.logger.Debugw("Multi-agent triggered by special pattern", "pattern", pattern)
			break
		}
	}

	// Override: simple single-file operations should use single agent
	singleAgentPatterns := []string{
		"read file", "show file", "display file",
		"open file", "view file", "cat file",
		"edit line", "change line", "modify line",
		"add line", "insert line", "delete line",
		"fix typo", "correct spelling", "format code",
	}

	for _, pattern := range singleAgentPatterns {
		if strings.Contains(input, pattern) {
			shouldUseMultiAgent = false
			td.logger.Debugw("Single-agent forced by simple pattern", "pattern", pattern)
			break
		}
	}

	td.logger.Debugw("Task detection result",
		"shouldUseMultiAgent", shouldUseMultiAgent,
		"multiAgentScore", multiAgentScore,
		"complexityScore", complexityScore,
		"codeAnalysisScore", codeAnalysisScore,
		"wordCount", wordCount,
		"sentenceCount", sentenceCount)

	return shouldUseMultiAgent
}

// GenerateTaskDescription creates a detailed task description for multi-agent processing
func (td *TaskDetector) GenerateTaskDescription(userInput string) string {
	// Enhance the user input with additional context for multi-agent processing
	taskDescription := userInput + "\n\nProvide a thorough and complete analysis."

	// Add specific instructions based on the type of request
	input := strings.ToLower(userInput)

	if strings.Contains(input, "analyze") || strings.Contains(input, "analysis") ||
		strings.Contains(input, "分析") {
		taskDescription += "\n\nFocus on:\n1. Identifying main components and their relationships\n2. Finding potential issues or improvements\n3. Providing specific examples and code snippets\n4. Suggesting best practices and recommendations"
		taskDescription += "\n\n重点关注:\n1. 识别主要组件及其关系\n2. 发现潜在问题或改进点\n3. 提供具体示例和代码片段\n4. 建议最佳实践和推荐方案"
	}

	if strings.Contains(input, "find") || strings.Contains(input, "search") || strings.Contains(input, "locate") ||
		strings.Contains(input, "查找") || strings.Contains(input, "搜索") || strings.Contains(input, "定位") {
		taskDescription += "\n\nSearch strategy:\n1. Use appropriate search tools (grep, find, etc.)\n2. Check multiple file types and locations\n3. Provide exact file paths and line numbers\n4. Include relevant code context"
		taskDescription += "\n\n搜索策略:\n1. 使用适当的搜索工具(grep, find等)\n2. 检查多种文件类型和位置\n3. 提供准确的文件路径和行号\n4. 包含相关的代码上下文"
	}

	if strings.Contains(input, "implement") || strings.Contains(input, "create") || strings.Contains(input, "build") ||
		strings.Contains(input, "实现") || strings.Contains(input, "创建") || strings.Contains(input, "构建") {
		taskDescription += "\n\nImplementation approach:\n1. Design the overall structure first\n2. Break down into smaller components\n3. Provide complete, working code examples\n4. Include error handling and edge cases\n5. Add appropriate tests and documentation"
		taskDescription += "\n\n实现方法:\n1. 首先设计整体结构\n2. 分解为更小的组件\n3. 提供完整可运行的代码示例\n4. 包含错误处理和边界情况\n5. 添加适当的测试和文档"
	}

	if strings.Contains(input, "refactor") || strings.Contains(input, "improve") || strings.Contains(input, "optimize") ||
		strings.Contains(input, "重构") || strings.Contains(input, "改进") || strings.Contains(input, "优化") {
		taskDescription += "\n\nRefactoring guidelines:\n1. Identify current issues and limitations\n2. Propose specific improvements\n3. Maintain backward compatibility where possible\n4. Provide before/after code comparisons\n5. Explain the benefits of each change"
		taskDescription += "\n\n重构指南:\n1. 识别当前问题和限制\n2. 提出具体改进建议\n3. 尽可能保持向后兼容性\n4. 提供重构前后的代码对比\n5. 解释每个改变的好处"
	}

	return taskDescription
}

// GetTaskComplexity estimates the complexity level of a task
func (td *TaskDetector) GetTaskComplexity(userInput string) TaskComplexity {
	input := strings.ToLower(userInput)

	// Better word counting for Chinese and English mixed content
	wordCount := td.getEffectiveWordCount(input)

	// Simple tasks
	if wordCount <= 8 { // Increased threshold for Chinese
		return TaskComplexitySimple
	}

	// Check for complex keywords (English + Chinese)
	complexKeywords := []string{
		// English keywords
		"architecture", "design pattern", "refactor", "optimize",
		"comprehensive", "thorough", "detailed", "complete",
		"multiple files", "entire project", "whole system",
		"best practices", "performance", "security", "scalability",

		// Chinese keywords
		"架构", "设计模式", "重构", "优化",
		"全面", "详细", "完整", "彻底",
		"多个文件", "整个项目", "整个系统", "全部系统",
		"最佳实践", "性能", "安全", "可扩展性",
	}

	complexCount := 0
	for _, keyword := range complexKeywords {
		if strings.Contains(input, keyword) {
			complexCount++
		}
	}

	if complexCount >= 2 || wordCount > 25 {
		return TaskComplexityHigh
	}

	if complexCount >= 1 || wordCount > 12 {
		return TaskComplexityMedium
	}

	return TaskComplexitySimple
}

// TaskComplexity represents the complexity level of a task
type TaskComplexity int

const (
	TaskComplexitySimple TaskComplexity = iota
	TaskComplexityMedium
	TaskComplexityHigh
)

// String returns the string representation of TaskComplexity
func (tc TaskComplexity) String() string {
	switch tc {
	case TaskComplexitySimple:
		return "simple"
	case TaskComplexityMedium:
		return "medium"
	case TaskComplexityHigh:
		return "high"
	default:
		return "unknown"
	}
}

// GetRecommendedAgentCount returns the recommended number of agents for a given complexity
func (tc TaskComplexity) GetRecommendedAgentCount() int {
	switch tc {
	case TaskComplexitySimple:
		return 1
	case TaskComplexityMedium:
		return 2
	case TaskComplexityHigh:
		return 3
	default:
		return 1
	}
}

// getEffectiveWordCount calculates word count for mixed Chinese/English text
func (td *TaskDetector) getEffectiveWordCount(input string) int {
	// Count English words (space-separated)
	englishWords := len(strings.Fields(input))

	// Count Chinese characters (rough estimation)
	chineseCharCount := 0
	for _, r := range input {
		// Check if character is in CJK (Chinese, Japanese, Korean) range
		if (r >= 0x4E00 && r <= 0x9FFF) || // CJK Unified Ideographs
			(r >= 0x3400 && r <= 0x4DBF) || // CJK Extension A
			(r >= 0x20000 && r <= 0x2A6DF) { // CJK Extension B
			chineseCharCount++
		}
	}

	// Estimate Chinese "words" (every 2-3 characters ≈ 1 word)
	chineseWords := chineseCharCount / 2

	// Return the larger count (handles mixed content better)
	if chineseWords > englishWords {
		return chineseWords
	}
	return englishWords
}
