package multiagent

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/agent"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"
)

// MockConversationRepository implements ConversationRepository for testing
type MockConversationRepository struct {
	conversations map[string]*agent.Conversation
}

func NewMockConversationRepository() *MockConversationRepository {
	return &MockConversationRepository{
		conversations: make(map[string]*agent.Conversation),
	}
}

func (m *MockConversationRepository) CreateConversation(ctx context.Context, conversation *agent.Conversation) error {
	m.conversations[conversation.ID.Hex()] = conversation
	return nil
}

func (m *MockConversationRepository) GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error) {
	if conv, exists := m.conversations[id]; exists {
		return conv, nil
	}
	return nil, nil
}

func (m *MockConversationRepository) AppendMessage(ctx context.Context, conversationID string, message *agent.Message) error {
	if conv, exists := m.conversations[conversationID]; exists {
		conv.Messages = append(conv.Messages, *message)
	}
	return nil
}

func (m *MockConversationRepository) FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error) {
	var result []*agent.Conversation
	for _, conv := range m.conversations {
		if conv.UserID == userID {
			result = append(result, conv)
		}
	}
	return result, nil
}

func (m *MockConversationRepository) GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error) {
	for _, conv := range m.conversations {
		if conv.ProjectID == projectID {
			return conv, nil
		}
	}
	return nil, nil
}

func (m *MockConversationRepository) CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	// Implementation for mock
	return nil, nil
}

func (m *MockConversationRepository) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	// Implementation for mock
	return nil, nil
}

func (m *MockConversationRepository) ClearConversationMessages(ctx context.Context, conversationID string) error {
	if conv, exists := m.conversations[conversationID]; exists {
		conv.Messages = []agent.Message{}
	}
	return nil
}

func (m *MockConversationRepository) DeleteProjectConversation(ctx context.Context, projectID string) error {
	for id, conv := range m.conversations {
		if conv.ProjectID == projectID {
			delete(m.conversations, id)
			break
		}
	}
	return nil
}

// MockLLMClient implements LLMClient for testing
type MockLLMClient struct{}

func (m *MockLLMClient) ChatCompletionStream(
	ctx context.Context,
	llmConfig modelLLM.LLMConfig,
	req *ChatCompletionRequest,
) (<-chan ChatCompletionStreamResponse, error) {
	responseChan := make(chan ChatCompletionStreamResponse, 1)
	go func() {
		defer close(responseChan)
		responseChan <- ChatCompletionStreamResponse{
			ContentDelta: "Mock response for: " + req.Messages[0].Content,
			IsFinal:      true,
		}
	}()
	return responseChan, nil
}

func (m *MockLLMClient) ChatCompletionNonStreaming(ctx context.Context, req *ChatCompletionRequest) (*ChatCompletionResponse, error) {
	return &ChatCompletionResponse{
		Content:      "Mock response for: " + req.Messages[0].Content,
		FinishReason: "stop",
	}, nil
}

// MockLLMDomain implements basic LLM domain functionality for testing
type MockLLMDomain struct{}

func (m *MockLLMDomain) GetActiveLLMConfig(ctx context.Context) (modelLLM.LLMConfig, error) {
	return modelLLM.LLMConfig{
		ModelName: "mock-model",
	}, nil
}

// TestTaskDetector tests the task detection logic
func TestTaskDetector(t *testing.T) {
	logger := zap.NewNop().Sugar()
	detector := NewTaskDetector(logger)

	testCases := []struct {
		input    string
		expected bool
		reason   string
	}{
		{
			input:    "What is Go?",
			expected: false,
			reason:   "Simple question should not trigger multi-agent",
		},
		{
			input:    "Analyze the entire codebase and find all security vulnerabilities",
			expected: true,
			reason:   "Complex analysis should trigger multi-agent",
		},
		{
			input:    "Compare different approaches to implement authentication and provide comprehensive recommendations with detailed analysis",
			expected: true,
			reason:   "Comparison and analysis should trigger multi-agent",
		},
		{
			input:    "Read file main.go",
			expected: false,
			reason:   "Simple file operation should not trigger multi-agent",
		},
		{
			input:    "Find all functions that handle database operations, analyze their performance, and suggest improvements with detailed code examples",
			expected: true,
			reason:   "Complex search and analysis should trigger multi-agent",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			result := detector.ShouldUseMultiAgent(tc.input)
			assert.Equal(t, tc.expected, result, tc.reason)

			// Test complexity detection
			complexity := detector.GetTaskComplexity(tc.input)
			if tc.expected {
				assert.NotEqual(t, TaskComplexitySimple, complexity, "Complex tasks should not be marked as simple")
			}

			// Test task description generation
			taskDesc := detector.GenerateTaskDescription(tc.input)
			assert.Contains(t, taskDesc, tc.input, "Task description should contain original input")
		})
	}
}

// TestSubAgentManager tests the sub-agent management functionality
func TestSubAgentManager(t *testing.T) {
	logger := zap.NewNop().Sugar()
	manager := NewSubAgentManager(logger)

	// Test agent creation
	agent1 := manager.CreateAgent("Test task 1", "Test prompt 1", "conv-1")
	assert.NotNil(t, agent1)
	assert.NotEmpty(t, agent1.ID)
	assert.Equal(t, 0, agent1.Index)
	assert.Equal(t, AgentStateInitializing, agent1.State)

	agent2 := manager.CreateAgent("Test task 2", "Test prompt 2", "conv-1")
	assert.NotNil(t, agent2)
	assert.NotEqual(t, agent1.ID, agent2.ID)
	assert.Equal(t, 1, agent2.Index)

	// Test agent state updates
	manager.UpdateAgentState(agent1.ID, AgentStateRunning)
	activeAgents := manager.GetActiveAgents()
	assert.Equal(t, AgentStateRunning, activeAgents[agent1.ID].State)

	// Test agent completion
	result := &AgentResult{
		AgentIndex:    0,
		Content:       "Test result",
		Success:       true,
		ExecutionTime: time.Second,
	}
	manager.CompleteAgent(agent1.ID, result)

	// Verify agent moved to completed
	activeAgents = manager.GetActiveAgents()
	completedAgents := manager.GetCompletedAgents()
	assert.NotContains(t, activeAgents, agent1.ID)
	assert.Contains(t, completedAgents, agent1.ID)
	assert.Equal(t, AgentStateCompleted, completedAgents[agent1.ID].State)

	// Test status
	status := manager.GetAgentStatus()
	assert.Equal(t, 1, status["active_count"])
	assert.Equal(t, 1, status["completed_count"])
	assert.Equal(t, 2, status["total_created"])
}

// TestConcurrentExecutor tests the concurrent execution functionality
func TestConcurrentExecutor(t *testing.T) {
	logger := zap.NewNop().Sugar()
	executor := NewConcurrentExecutor(2, logger) // Max 2 concurrent

	// Create test agents
	agents := []*SubAgent{
		{ID: "agent-1", Index: 0, Context: &SubAgentContext{ResourceLimits: &ResourceLimits{MaxExecutionTime: 5 * time.Second}}},
		{ID: "agent-2", Index: 1, Context: &SubAgentContext{ResourceLimits: &ResourceLimits{MaxExecutionTime: 5 * time.Second}}},
		{ID: "agent-3", Index: 2, Context: &SubAgentContext{ResourceLimits: &ResourceLimits{MaxExecutionTime: 5 * time.Second}}},
	}

	// Mock executor function
	mockExecutor := func(ctx context.Context, agent *SubAgent) (*AgentResult, error) {
		// Simulate some work
		time.Sleep(100 * time.Millisecond)
		return &AgentResult{
			AgentIndex:    agent.Index,
			Content:       "Mock result for " + agent.ID,
			Success:       true,
			ExecutionTime: 100 * time.Millisecond,
		}, nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Test concurrent execution
	results, err := executor.ExecuteConcurrently(ctx, agents, mockExecutor)
	require.NoError(t, err)
	assert.Len(t, results, 3)

	// Verify all executions were successful
	for i, result := range results {
		assert.True(t, result.Success, "Execution %d should be successful", i)
		assert.NotNil(t, result.Result)
		assert.Equal(t, i, result.Result.AgentIndex)
	}
}

// TestResourceMonitor tests the resource monitoring functionality
func TestResourceMonitor(t *testing.T) {
	logger := zap.NewNop().Sugar()
	monitor := NewResourceMonitor(logger)

	// Test monitoring lifecycle
	agentID := "test-agent-1"
	monitor.StartMonitoring(agentID)

	// Update usage
	monitor.UpdateTokenUsage(agentID, 100)
	monitor.UpdateToolCallCount(agentID)
	monitor.UpdateToolCallCount(agentID)

	// Get usage
	usage := monitor.GetAgentUsage(agentID)
	assert.NotNil(t, usage)
	assert.Equal(t, agentID, usage.AgentID)
	assert.Equal(t, 100, usage.TokensUsed)
	assert.Equal(t, 2, usage.ToolCalls)

	// Stop monitoring
	finalUsage := monitor.StopMonitoring(agentID)
	assert.NotNil(t, finalUsage)
	assert.Equal(t, 100, finalUsage.TokensUsed)
	assert.Equal(t, 2, finalUsage.ToolCalls)

	// Verify global usage
	globalUsage := monitor.GetGlobalUsage()
	assert.Equal(t, 1, globalUsage.TotalAgentsCreated)
	assert.Equal(t, 0, globalUsage.ActiveAgents)
	assert.Equal(t, 100, globalUsage.TotalTokensUsed)
	assert.Equal(t, 2, globalUsage.TotalToolCalls)
}

// TestResultSynthesizer tests the result synthesis functionality
func TestResultSynthesizer(t *testing.T) {
	logger := zap.NewNop().Sugar()

	// Test fallback synthesis (without LLM)
	synthesizer := &ResultSynthesizer{
		logger: logger,
	}

	// Test with single result
	singleResult := []*AgentResult{
		{
			AgentIndex: 0,
			Content:    "Single agent result",
			Success:    true,
		},
	}

	result := synthesizer.fallbackSynthesis("Test task", singleResult)
	assert.Contains(t, result, "Single agent result")
	assert.Contains(t, result, "Test task")

	// Test with multiple results
	multipleResults := []*AgentResult{
		{
			AgentIndex:   0,
			Content:      "First agent result",
			Success:      true,
			Tokens:       100,
			ToolUseCount: 2,
		},
		{
			AgentIndex:   1,
			Content:      "Second agent result",
			Success:      true,
			Tokens:       150,
			ToolUseCount: 3,
		},
	}

	result = synthesizer.fallbackSynthesis("Test task", multipleResults)
	assert.Contains(t, result, "First agent result")
	assert.Contains(t, result, "Second agent result")
	assert.Contains(t, result, "Total agents: 2")
	assert.Contains(t, result, "Total tokens used: 250")
	assert.Contains(t, result, "Total tool calls: 5")
}

// TestPermissionValidator tests the permission validation functionality
func TestPermissionValidator(t *testing.T) {
	logger := zap.NewNop().Sugar()
	validator := NewPermissionValidator(logger)

	// Create test context
	context := &SubAgentContext{
		AgentID: "test-agent",
		AllowedTools: []string{
			"mcp.projectfs.read_file",
			"mcp.projectfs.write_file",
			"mcp.runtime.bash_exec",
		},
		ResourceLimits: &ResourceLimits{
			MaxToolCalls: 10,
			MaxTokens:    1000,
		},
		ToolCallCount: 0,
		TokenCount:    0,
	}

	// Test allowed tool call
	allowedToolCall := &agent.ToolCall{
		MCPService: "mcp.projectfs",
		MCPMethod:  "read_file",
	}

	err := validator.ValidateToolCall(context, allowedToolCall)
	assert.NoError(t, err, "Allowed tool call should pass validation")

	// Test disallowed tool call
	disallowedToolCall := &agent.ToolCall{
		MCPService: "mcp.forbidden",
		MCPMethod:  "dangerous_operation",
	}

	err = validator.ValidateToolCall(context, disallowedToolCall)
	assert.Error(t, err, "Disallowed tool call should fail validation")
	assert.IsType(t, &PermissionError{}, err)

	// Test resource limit exceeded
	context.ToolCallCount = 15 // Exceed the limit
	err = validator.ValidateToolCall(context, allowedToolCall)
	assert.Error(t, err, "Tool call exceeding resource limits should fail")
}
