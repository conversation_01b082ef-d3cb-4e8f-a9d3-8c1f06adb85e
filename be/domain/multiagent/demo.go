package multiagent

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/agent"
	"git.nevint.com/fota3/t-rex/model/llm"
)

// DemoMultiAgentSystem demonstrates the multi-agent system functionality
func DemoMultiAgentSystem() {
	fmt.Println("=== Multi-Agent System Demo ===")

	// Initialize logger
	logger := zap.NewNop().Sugar()

	// Create mock dependencies (simplified for demo)
	mockRepo := &mockConversationRepository{conversations: make(map[string]*agent.Conversation)}
	mockLLM := &mockLLMClient{}

	// Create multi-agent domain
	multiAgentDomainImpl := NewMultiAgentDomain(
		mockRepo,
		mockLLM,
		nil, // MCPToolExecutor
		nil, // HistoryManager
		logger,
		nil, // LLMDomain
	)

	// Demo test cases
	testCases := []struct {
		name        string
		userMessage string
		description string
	}{
		{
			name:        "Simple Question",
			userMessage: "What is Go?",
			description: "This should use single agent mode",
		},
		{
			name:        "Complex Analysis",
			userMessage: "Analyze the entire codebase architecture, find all security vulnerabilities, compare different design patterns, and provide comprehensive recommendations with detailed examples",
			description: "This should trigger multi-agent processing",
		},
		{
			name:        "Code Search",
			userMessage: "Find all functions that handle database operations, analyze their performance, and suggest improvements",
			description: "This should use multi-agent for thorough analysis",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("\n--- Test Case %d: %s ---\n", i+1, tc.name)
		fmt.Printf("User Message: %s\n", tc.userMessage)
		fmt.Printf("Description: %s\n", tc.description)

		// Test task detection
		taskDetector := multiAgentDomainImpl.taskDetector
		shouldUseMulti := taskDetector.ShouldUseMultiAgent(tc.userMessage)
		complexity := taskDetector.GetTaskComplexity(tc.userMessage)

		fmt.Printf("Task Detection Result: %v\n", shouldUseMulti)
		fmt.Printf("Task Complexity: %s\n", complexity.String())

		if shouldUseMulti {
			agentCount := complexity.GetRecommendedAgentCount()
			fmt.Printf("Recommended Agent Count: %d\n", agentCount)

			// Demo agent creation
			manager := multiAgentDomainImpl.subAgentManager
			agents := make([]*SubAgent, agentCount)

			for j := 0; j < agentCount; j++ {
				agent := manager.CreateAgent(
					tc.userMessage,
					fmt.Sprintf("Agent %d prompt for: %s", j+1, tc.userMessage),
					"demo-conversation",
				)
				agents[j] = agent
				fmt.Printf("  Created Agent %d: %s\n", j+1, agent.ID)
			}

			// Demo concurrent execution
			executor := multiAgentDomainImpl.concurrentExecutor

			mockExecutor := func(ctx context.Context, agent *SubAgent) (*AgentResult, error) {
				// Simulate work
				time.Sleep(100 * time.Millisecond)
				return &AgentResult{
					AgentIndex:    agent.Index,
					Content:       fmt.Sprintf("Mock analysis result from Agent %d", agent.Index+1),
					Success:       true,
					ExecutionTime: 100 * time.Millisecond,
					Tokens:        50,
					ToolUseCount:  2,
				}, nil
			}

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			results, err := executor.ExecuteConcurrently(ctx, agents, mockExecutor)
			if err != nil {
				fmt.Printf("  Error executing agents: %v\n", err)
			} else {
				fmt.Printf("  Executed %d agents successfully\n", len(results))
				for _, result := range results {
					if result.Success {
						fmt.Printf("    Agent %d: %s\n", result.Result.AgentIndex+1, result.Result.Content)
					}
				}
			}

			// Demo result synthesis
			agentResults := make([]*AgentResult, 0, len(results))
			for _, result := range results {
				if result.Success {
					agentResults = append(agentResults, result.Result)
				}
			}

			if len(agentResults) > 0 {
				synthesizer := multiAgentDomainImpl.resultSynthesizer
				synthesizedResult := synthesizer.fallbackSynthesis(tc.userMessage, agentResults)
				fmt.Printf("  Synthesized Result:\n%s\n", synthesizedResult)
			}
		} else {
			fmt.Printf("Single agent mode - would process directly\n")
		}

		fmt.Println("---")
	}

	// Demo resource monitoring
	fmt.Println("\n--- Resource Monitoring Demo ---")
	monitor := NewResourceMonitor(logger)

	// Simulate agent lifecycle
	agentID := "demo-agent-1"
	monitor.StartMonitoring(agentID)

	// Simulate usage
	monitor.UpdateTokenUsage(agentID, 150)
	monitor.UpdateToolCallCount(agentID)
	monitor.UpdateToolCallCount(agentID)
	monitor.UpdateToolCallCount(agentID)

	usage := monitor.GetAgentUsage(agentID)
	fmt.Printf("Agent Usage: Tokens=%d, ToolCalls=%d\n", usage.TokensUsed, usage.ToolCalls)

	finalUsage := monitor.StopMonitoring(agentID)
	fmt.Printf("Final Usage: Tokens=%d, ToolCalls=%d, Duration=%v\n",
		finalUsage.TokensUsed, finalUsage.ToolCalls, finalUsage.ExecutionTime)

	globalUsage := monitor.GetGlobalUsage()
	fmt.Printf("Global Usage: TotalAgents=%d, TotalTokens=%d, TotalToolCalls=%d\n",
		globalUsage.TotalAgentsCreated, globalUsage.TotalTokensUsed, globalUsage.TotalToolCalls)

	// Demo permission validation
	fmt.Println("\n--- Permission Validation Demo ---")
	validator := NewPermissionValidator(logger)

	context := &SubAgentContext{
		AgentID: "demo-agent",
		AllowedTools: []string{
			"mcp.projectfs.read_file",
			"mcp.projectfs.write_file",
		},
		ResourceLimits: &ResourceLimits{
			MaxToolCalls: 5,
			MaxTokens:    1000,
		},
		ToolCallCount: 2,
		TokenCount:    200,
	}

	// Test allowed tool
	allowedTool := &agent.ToolCall{
		MCPService: "mcp.projectfs",
		MCPMethod:  "read_file",
	}

	err := validator.ValidateToolCall(context, allowedTool)
	if err != nil {
		fmt.Printf("Allowed tool validation failed: %v\n", err)
	} else {
		fmt.Printf("Allowed tool validation passed\n")
	}

	// Test forbidden tool
	forbiddenTool := &agent.ToolCall{
		MCPService: "mcp.forbidden",
		MCPMethod:  "dangerous_operation",
	}

	err = validator.ValidateToolCall(context, forbiddenTool)
	if err != nil {
		fmt.Printf("Forbidden tool validation correctly failed: %v\n", err)
	} else {
		fmt.Printf("Forbidden tool validation incorrectly passed\n")
	}

	fmt.Println("\n=== Demo Complete ===")
}

// Simplified mocks for demo
type mockConversationRepository struct {
	conversations map[string]*agent.Conversation
}

func (m *mockConversationRepository) CreateConversation(ctx context.Context, conversation *agent.Conversation) error {
	m.conversations[conversation.ID.Hex()] = conversation
	return nil
}

func (m *mockConversationRepository) GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error) {
	if conv, exists := m.conversations[id]; exists {
		return conv, nil
	}
	return nil, nil
}

func (m *mockConversationRepository) AppendMessage(ctx context.Context, conversationID string, message *agent.Message) error {
	if conv, exists := m.conversations[conversationID]; exists {
		conv.Messages = append(conv.Messages, *message)
	}
	return nil
}

func (m *mockConversationRepository) FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error) {
	return nil, nil
}

func (m *mockConversationRepository) GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error) {
	return nil, nil
}

func (m *mockConversationRepository) CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	conv := &agent.Conversation{
		ID:        primitive.NewObjectID(),
		ProjectID: projectID,
		UserID:    userID,
		Messages:  []agent.Message{},
	}
	m.conversations[conv.ID.Hex()] = conv
	return conv, nil
}

func (m *mockConversationRepository) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	return nil, nil
}

func (m *mockConversationRepository) ClearConversationMessages(ctx context.Context, conversationID string) error {
	return nil
}

func (m *mockConversationRepository) DeleteProjectConversation(ctx context.Context, projectID string) error {
	return nil
}

type mockLLMClient struct{}

func (m *mockLLMClient) ChatCompletionStream(
	ctx context.Context,
	llmConfig llm.LLMConfig,
	req *ChatCompletionRequest,
) (<-chan ChatCompletionStreamResponse, error) {
	responseChan := make(chan ChatCompletionStreamResponse, 1)
	go func() {
		defer close(responseChan)
		responseChan <- ChatCompletionStreamResponse{
			ContentDelta: "Mock response",
			IsFinal:      true,
		}
	}()
	return responseChan, nil
}

func (m *mockLLMClient) ChatCompletionNonStreaming(ctx context.Context, req *ChatCompletionRequest) (*ChatCompletionResponse, error) {
	return &ChatCompletionResponse{
		Content:      "Mock response",
		FinishReason: "stop",
	}, nil
}
