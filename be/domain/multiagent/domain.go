package multiagent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	llmDomain "git.nevint.com/fota3/t-rex/domain/llm"
	"git.nevint.com/fota3/t-rex/model/agent"
)

// MultiAgentDomainImpl implements the MultiAgentDomain interface
// It provides a multi-agent architecture that can replace the original AgentReActDomain
type MultiAgentDomainImpl struct {
	repo           ConversationRepository
	llm            LLMClient
	executor       MCPToolExecutor
	historyManager HistoryManager
	logger         *zap.SugaredLogger
	llmDomain      *llmDomain.Domain

	// Multi-agent specific components
	taskDetector        *TaskDetector
	subAgentManager     *SubAgentManager
	concurrentExecutor  *ConcurrentExecutor
	resultSynthesizer   *ResultSynthesizer
	permissionValidator *PermissionValidator
	resourceMonitor     *ResourceMonitor

	// Configuration
	maxCycles           int
	parallelTasksCount  int
	maxConcurrentAgents int
}

// NewMultiAgentDomain creates a new instance of MultiAgentDomainImpl
func NewMultiAgentDomain(
	repo ConversationRepository,
	llm LLMClient,
	executor MCPToolExecutor,
	historyManager HistoryManager,
	logger *zap.SugaredLogger,
	llmDomain *llmDomain.Domain,
) *MultiAgentDomainImpl {
	if logger == nil {
		logger = zap.NewNop().Sugar()
	}

	domain := &MultiAgentDomainImpl{
		repo:                repo,
		llm:                 llm,
		executor:            executor,
		historyManager:      historyManager,
		logger:              logger.Named("MultiAgentDomain"),
		llmDomain:           llmDomain,
		maxCycles:           30,
		parallelTasksCount:  3, // Default to 3 parallel agents
		maxConcurrentAgents: 10,
	}

	// Initialize multi-agent components
	domain.taskDetector = NewTaskDetector(logger)
	domain.subAgentManager = NewSubAgentManager(logger)
	domain.concurrentExecutor = NewConcurrentExecutor(domain.maxConcurrentAgents, logger)
	domain.resultSynthesizer = NewResultSynthesizer(llm, llmDomain, logger)
	domain.permissionValidator = NewPermissionValidator(logger)
	domain.resourceMonitor = NewResourceMonitor(logger)

	return domain
}

// HandleStreamingInteraction handles a streaming conversation using multi-agent architecture
func (d *MultiAgentDomainImpl) HandleStreamingInteraction(
	ctx context.Context,
	conversation *agent.Conversation,
	userMessage agent.Message,
	systemPrompt string,
	isRetry bool,
) (<-chan DomainStreamChunk, error) {
	responseChan := make(chan DomainStreamChunk)

	go func() {
		defer close(responseChan)
		var err error

		if !isRetry {
			d.repo.AppendMessage(ctx, conversation.ID.Hex(), &userMessage)
			conversation, err = d.repo.GetConversationByID(ctx, conversation.ID.Hex())
			if err != nil {
				d.logger.Error("Failed to get conversation by ID", zap.Error(err), zap.String("conversationID", conversation.ID.Hex()))
				return
			}
			if conversation == nil {
				d.logger.Error("Conversation is nil after initial refresh")
				return
			}
		}

		// Detect if this request requires multi-agent processing
		shouldUseMultiAgent := d.taskDetector.ShouldUseMultiAgent(userMessage.Content)

		if shouldUseMultiAgent {
			d.logger.Infow("Using multi-agent processing", "conversationID", conversation.ID.Hex())
			err = d.handleMultiAgentInteraction(ctx, conversation, responseChan, systemPrompt)
		} else {
			d.logger.Infow("Using single-agent processing", "conversationID", conversation.ID.Hex())
			err = d.handleSingleAgentInteraction(ctx, conversation, responseChan, systemPrompt)
		}

		if err != nil {
			d.logger.Error("Error from multi-agent framework process", zap.Error(err), zap.String("conversationID", conversation.ID.Hex()))
		}
	}()

	return responseChan, nil
}

// HandleProjectStreamingInteraction handles project-level streaming interactions
func (d *MultiAgentDomainImpl) HandleProjectStreamingInteraction(
	ctx context.Context,
	projectID string,
	userID string,
	userMessage agent.Message,
) (<-chan DomainStreamChunk, error) {
	conversation, err := d.GetOrCreateProjectConversation(ctx, projectID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get or create project conversation: %w", err)
	}

	return d.HandleStreamingInteraction(ctx, conversation, userMessage, "", false)
}

// Project-level conversation management methods
func (d *MultiAgentDomainImpl) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	return d.repo.GetOrCreateProjectConversation(ctx, projectID, userID)
}

func (d *MultiAgentDomainImpl) GetProjectConversation(ctx context.Context, projectID string) (*agent.Conversation, error) {
	return d.repo.GetConversationByProjectID(ctx, projectID)
}

func (d *MultiAgentDomainImpl) ClearProjectConversation(ctx context.Context, projectID string) error {
	conversation, err := d.repo.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return err
	}
	if conversation == nil {
		return nil // No conversation to clear
	}
	return d.repo.ClearConversationMessages(ctx, conversation.ID.Hex())
}

func (d *MultiAgentDomainImpl) DeleteProjectConversation(ctx context.Context, projectID string) error {
	return d.repo.DeleteProjectConversation(ctx, projectID)
}

func (d *MultiAgentDomainImpl) ExportProjectConversation(ctx context.Context, projectID string) ([]agent.Message, error) {
	conversation, err := d.repo.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return nil, err
	}
	if conversation == nil {
		return []agent.Message{}, nil
	}
	return conversation.Messages, nil
}

// handleMultiAgentInteraction processes the request using multiple agents
func (d *MultiAgentDomainImpl) handleMultiAgentInteraction(
	ctx context.Context,
	conversation *agent.Conversation,
	responseChan chan<- DomainStreamChunk,
	systemPrompt string,
) error {
	startTime := time.Now()
	currentMessageID := GenerateMsgID()

	d.logger.Infow("Starting multi-agent interaction", "messageID", currentMessageID, "conversationID", conversation.ID.Hex())

	// Send start marker
	responseChan <- DomainStreamChunk{
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
		Marker:         stringPtr(MarkerStart),
	}

	// Get the last user message
	lastMessage := getLastUserMessage(conversation.Messages)
	if lastMessage == nil {
		return fmt.Errorf("no user message found in conversation")
	}

	// Create task description for multi-agent processing
	taskDescription := d.taskDetector.GenerateTaskDescription(lastMessage.Content)

	// Launch multiple sub-agents
	agentResults, err := d.launchMultipleAgents(ctx, taskDescription, conversation, systemPrompt, responseChan)
	if err != nil {
		return fmt.Errorf("failed to launch multiple agents: %w", err)
	}

	// Synthesize results from multiple agents
	synthesizedResult, err := d.resultSynthesizer.SynthesizeResults(ctx, taskDescription, agentResults)
	if err != nil {
		return fmt.Errorf("failed to synthesize results: %w", err)
	}

	// Send synthesized result
	responseChan <- DomainStreamChunk{
		Delta:          &synthesizedResult,
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
	}

	// Send end marker
	responseChan <- DomainStreamChunk{
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
		Marker:         stringPtr(MarkerEnd),
	}

	// Save the assistant's response to conversation
	assistantMessage := &agent.Message{
		MsgID:     currentMessageID,
		MsgType:   "assistant_response",
		Role:      agent.RoleAssistant,
		Content:   synthesizedResult,
		Timestamp: time.Now(),
	}

	err = d.repo.AppendMessage(ctx, conversation.ID.Hex(), assistantMessage)
	if err != nil {
		d.logger.Error("Failed to save assistant message", zap.Error(err))
	}

	d.logger.Infow("Multi-agent interaction completed",
		"messageID", currentMessageID,
		"duration", time.Since(startTime),
		"agentCount", len(agentResults))

	return nil
}

// handleSingleAgentInteraction processes the request using a single agent (fallback mode)
func (d *MultiAgentDomainImpl) handleSingleAgentInteraction(
	ctx context.Context,
	conversation *agent.Conversation,
	responseChan chan<- DomainStreamChunk,
	systemPrompt string,
) error {
	// For single agent mode, we can reuse the original ReAct logic
	// This ensures backward compatibility
	return d.executeSingleAgentReActCycle(ctx, conversation, responseChan, systemPrompt)
}

// launchMultipleAgents creates and executes multiple agents concurrently
func (d *MultiAgentDomainImpl) launchMultipleAgents(
	ctx context.Context,
	taskDescription string,
	conversation *agent.Conversation,
	systemPrompt string,
	responseChan chan<- DomainStreamChunk,
) ([]*AgentResult, error) {
	// Determine the number of agents to launch
	complexity := d.taskDetector.GetTaskComplexity(taskDescription)
	agentCount := complexity.GetRecommendedAgentCount()

	d.logger.Infow("Launching multiple agents",
		"agentCount", agentCount,
		"complexity", complexity.String())

	// Create multiple sub-agents
	agents := make([]*SubAgent, agentCount)
	for i := 0; i < agentCount; i++ {
		agentPrompt := d.generateAgentSpecificPrompt(systemPrompt, taskDescription, i, agentCount)
		agent := d.subAgentManager.CreateAgent(
			taskDescription,
			agentPrompt,
			conversation.ID.Hex(),
		)
		agents[i] = agent
	}

	// Execute agents concurrently
	executor := func(ctx context.Context, agent *SubAgent) (*AgentResult, error) {
		return d.executeSubAgent(ctx, agent, conversation)
	}

	results, err := d.concurrentExecutor.ExecuteConcurrently(ctx, agents, executor)
	if err != nil {
		return nil, fmt.Errorf("failed to execute agents concurrently: %w", err)
	}

	// Convert execution results to agent results
	agentResults := make([]*AgentResult, 0, len(results))
	for _, result := range results {
		if result.Success && result.Result != nil {
			agentResults = append(agentResults, result.Result)
		}
	}

	return agentResults, nil
}

// generateAgentSpecificPrompt creates a specialized prompt for each agent
func (d *MultiAgentDomainImpl) generateAgentSpecificPrompt(
	baseSystemPrompt string,
	taskDescription string,
	agentIndex int,
	totalAgents int,
) string {
	var promptBuilder strings.Builder

	promptBuilder.WriteString(baseSystemPrompt)
	promptBuilder.WriteString("\n\n")

	// Add multi-agent specific instructions
	promptBuilder.WriteString(fmt.Sprintf("You are Agent %d of %d working on this task: %s\n\n",
		agentIndex+1, totalAgents, taskDescription))

	// Add agent-specific focus based on index
	switch agentIndex {
	case 0:
		promptBuilder.WriteString("Focus on: Analysis and understanding of the problem. Provide a thorough analysis of the requirements and constraints.\n")
	case 1:
		promptBuilder.WriteString("Focus on: Implementation details and code examples. Provide specific code solutions and technical implementation.\n")
	case 2:
		promptBuilder.WriteString("Focus on: Testing, validation, and best practices. Ensure quality and provide recommendations for improvement.\n")
	default:
		promptBuilder.WriteString(fmt.Sprintf("Focus on: Additional analysis and alternative approaches (Agent %d perspective).\n", agentIndex+1))
	}

	promptBuilder.WriteString("\nWork independently and provide your best analysis. Your results will be combined with other agents' findings.")

	return promptBuilder.String()
}

// executeSubAgent executes a single sub-agent
func (d *MultiAgentDomainImpl) executeSubAgent(
	ctx context.Context,
	subAgent *SubAgent,
	conversation *agent.Conversation,
) (*AgentResult, error) {
	startTime := time.Now()

	d.logger.Debugw("Executing sub-agent", "agentID", subAgent.ID, "index", subAgent.Index)

	// Start resource monitoring
	d.resourceMonitor.StartMonitoring(subAgent.ID)
	defer func() {
		usage := d.resourceMonitor.StopMonitoring(subAgent.ID)
		if usage != nil {
			d.logger.Debugw("Sub-agent resource usage",
				"agentID", subAgent.ID,
				"tokens", usage.TokensUsed,
				"toolCalls", usage.ToolCalls,
				"duration", usage.ExecutionTime)
		}
	}()

	// Update agent state
	d.subAgentManager.UpdateAgentState(subAgent.ID, AgentStateRunning)

	// Create a simplified conversation for this sub-agent
	subAgentConversation := &agent.Conversation{
		ID:     conversation.ID,
		UserID: conversation.UserID,
		Messages: []agent.Message{
			{
				Role:    agent.RoleUser,
				Content: subAgent.Prompt,
			},
		},
	}

	// Execute the sub-agent using a simplified ReAct cycle
	result, err := d.executeSubAgentReActCycle(ctx, subAgentConversation, subAgent)

	executionTime := time.Since(startTime)

	if err != nil {
		d.subAgentManager.FailAgent(subAgent.ID, err)
		return nil, fmt.Errorf("sub-agent %s failed: %w", subAgent.ID, err)
	}

	// Create agent result
	agentResult := &AgentResult{
		AgentIndex:    subAgent.Index,
		Content:       result,
		ToolUseCount:  subAgent.Context.ToolCallCount,
		Tokens:        subAgent.Context.TokenCount,
		ExecutionTime: executionTime,
		Success:       true,
	}

	d.subAgentManager.CompleteAgent(subAgent.ID, agentResult)

	return agentResult, nil
}

// executeSingleAgentReActCycle executes a single agent ReAct cycle (fallback mode)
func (d *MultiAgentDomainImpl) executeSingleAgentReActCycle(
	ctx context.Context,
	conversation *agent.Conversation,
	responseChan chan<- DomainStreamChunk,
	systemPrompt string,
) error {
	// This is a simplified implementation that mimics the original ReAct cycle
	// In a real implementation, this would call the original agent domain

	currentMessageID := GenerateMsgID()

	// Send start marker
	responseChan <- DomainStreamChunk{
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
		Marker:         stringPtr(MarkerStart),
	}

	// Get the last user message
	lastMessage := getLastUserMessage(conversation.Messages)
	if lastMessage == nil {
		return fmt.Errorf("no user message found in conversation")
	}

	// Simple response for single agent mode
	response := fmt.Sprintf("Processing your request: %s\n\nThis is a single-agent response. For more complex analysis, try a request that would trigger multi-agent processing.", lastMessage.Content)

	// Send response content
	responseChan <- DomainStreamChunk{
		Delta:          &response,
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
	}

	// Send end marker
	responseChan <- DomainStreamChunk{
		ConversationID: conversation.ID.Hex(),
		MessageID:      currentMessageID,
		Role:           agent.RoleAssistant,
		MsgType:        "assistant_response",
		Marker:         stringPtr(MarkerEnd),
	}

	// Save the assistant's response to conversation
	assistantMessage := &agent.Message{
		MsgID:     currentMessageID,
		MsgType:   "assistant_response",
		Role:      agent.RoleAssistant,
		Content:   response,
		Timestamp: time.Now(),
	}

	err := d.repo.AppendMessage(ctx, conversation.ID.Hex(), assistantMessage)
	if err != nil {
		d.logger.Error("Failed to save assistant message", zap.Error(err))
	}

	return nil
}

// executeSubAgentReActCycle executes a simplified ReAct cycle for a sub-agent
func (d *MultiAgentDomainImpl) executeSubAgentReActCycle(
	ctx context.Context,
	conversation *agent.Conversation,
	subAgent *SubAgent,
) (string, error) {
	// This is a simplified implementation for sub-agents
	// In a real implementation, this would use the full ReAct cycle with tool calling

	d.logger.Debugw("Executing sub-agent ReAct cycle", "agentID", subAgent.ID)

	// Get the active LLM config
	activeLLMConfig, err := d.llmDomain.GetActiveLLMConfig(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get active LLM configuration: %w", err)
	}

	// Create LLM request
	messages := []agent.Message{
		{
			Role:    agent.RoleUser,
			Content: subAgent.Prompt,
		},
	}

	req := &ChatCompletionRequest{
		Model:       activeLLMConfig.ModelName,
		Messages:    messages,
		Stream:      false,
		Temperature: float32Ptr(0.7),
		MaxTokens:   intPtr(5000),
	}

	// Call LLM
	response, err := d.llm.ChatCompletionNonStreaming(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to call LLM for sub-agent: %w", err)
	}

	// Update resource usage
	d.resourceMonitor.UpdateTokenUsage(subAgent.ID, len(response.Content)/4) // Rough estimate

	return response.Content, nil
}

// Helper functions
func getLastUserMessage(messages []agent.Message) *agent.Message {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == agent.RoleUser {
			return &messages[i]
		}
	}
	return nil
}

func GenerateMsgID() string {
	return fmt.Sprintf("msg_%d", time.Now().UnixNano())
}
