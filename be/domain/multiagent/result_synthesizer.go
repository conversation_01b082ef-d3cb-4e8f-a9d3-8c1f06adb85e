package multiagent

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"go.uber.org/zap"

	llmDomain "git.nevint.com/fota3/t-rex/domain/llm"
	"git.nevint.com/fota3/t-rex/model/agent"
)

// ResultSynthesizer combines results from multiple agents into a coherent response
// This implements the KN5 function from the Claude Code architecture
type ResultSynthesizer struct {
	llm       LLMClient
	llmDomain *llmDomain.Domain
	logger    *zap.SugaredLogger
}

// NewResultSynthesizer creates a new ResultSynthesizer instance
func NewResultSynthesizer(
	llm LLMClient,
	llmDomain *llmDomain.Domain,
	logger *zap.SugaredLogger,
) *ResultSynthesizer {
	return &ResultSynthesizer{
		llm:       llm,
		llmDomain: llmDomain,
		logger:    logger.Named("ResultSynthesizer"),
	}
}

// SynthesizeResults combines multiple agent results into a unified response
func (rs *ResultSynthesizer) SynthesizeResults(
	ctx context.Context,
	originalTask string,
	agentResults []*AgentResult,
) (string, error) {
	if len(agentResults) == 0 {
		return "", fmt.Errorf("no agent results to synthesize")
	}

	// If only one result, return it directly
	if len(agentResults) == 1 {
		if agentResults[0].Success {
			return agentResults[0].Content, nil
		}
		return "", fmt.Errorf("single agent failed: %v", agentResults[0].Error)
	}

	rs.logger.Infow("Starting result synthesis", 
		"originalTask", originalTask,
		"agentCount", len(agentResults))

	// Filter successful results
	successfulResults := make([]*AgentResult, 0, len(agentResults))
	for _, result := range agentResults {
		if result.Success && result.Content != "" {
			successfulResults = append(successfulResults, result)
		}
	}

	if len(successfulResults) == 0 {
		return "", fmt.Errorf("no successful agent results to synthesize")
	}

	// Sort results by agent index for consistent ordering
	sort.Slice(successfulResults, func(i, j int) bool {
		return successfulResults[i].AgentIndex < successfulResults[j].AgentIndex
	})

	// Generate synthesis prompt
	synthesisPrompt := rs.generateSynthesisPrompt(originalTask, successfulResults)

	// Use LLM to synthesize the results
	synthesizedResult, err := rs.performSynthesis(ctx, synthesisPrompt)
	if err != nil {
		rs.logger.Errorw("Failed to synthesize results", "error", err)
		// Fallback to simple concatenation
		return rs.fallbackSynthesis(originalTask, successfulResults), nil
	}

	rs.logger.Infow("Result synthesis completed", 
		"originalTask", originalTask,
		"successfulResults", len(successfulResults),
		"synthesizedLength", len(synthesizedResult))

	return synthesizedResult, nil
}

// generateSynthesisPrompt creates a prompt for the synthesis LLM
func (rs *ResultSynthesizer) generateSynthesisPrompt(
	originalTask string,
	results []*AgentResult,
) string {
	var promptBuilder strings.Builder

	promptBuilder.WriteString(fmt.Sprintf("Original task: %s\n\n", originalTask))
	promptBuilder.WriteString("I've assigned multiple agents to tackle this task. Each agent has analyzed the problem and provided their findings.\n\n")

	// Add each agent's response
	for i, result := range results {
		promptBuilder.WriteString(fmt.Sprintf("== AGENT %d RESPONSE ==\n", i+1))
		promptBuilder.WriteString(result.Content)
		promptBuilder.WriteString("\n\n")
	}

	// Add synthesis instructions
	promptBuilder.WriteString("Based on all the information provided by these agents, synthesize a comprehensive and cohesive response that:\n")
	promptBuilder.WriteString("1. Combines the key insights from all agents\n")
	promptBuilder.WriteString("2. Resolves any contradictions between agent findings\n")
	promptBuilder.WriteString("3. Presents a unified solution that addresses the original task\n")
	promptBuilder.WriteString("4. Includes all important details and code examples from the individual responses\n")
	promptBuilder.WriteString("5. Is well-structured and complete\n\n")
	promptBuilder.WriteString("Your synthesis should be thorough but focused on the original task.")

	return promptBuilder.String()
}

// performSynthesis uses the LLM to synthesize the results
func (rs *ResultSynthesizer) performSynthesis(ctx context.Context, synthesisPrompt string) (string, error) {
	// Get the active LLM config
	activeLLMConfig, err := rs.llmDomain.GetActiveLLMConfig(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get active LLM configuration: %w", err)
	}

	// Create synthesis request
	messages := []agent.Message{
		{
			Role:    agent.RoleUser,
			Content: synthesisPrompt,
		},
	}

	req := &ChatCompletionRequest{
		Model:       activeLLMConfig.ModelName,
		Messages:    messages,
		Stream:      false,
		Temperature: float32Ptr(0.7),
		MaxTokens:   intPtr(10000),
	}

	// Call LLM for synthesis
	response, err := rs.llm.ChatCompletionNonStreaming(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to call LLM for synthesis: %w", err)
	}

	return response.Content, nil
}

// fallbackSynthesis provides a simple concatenation fallback when LLM synthesis fails
func (rs *ResultSynthesizer) fallbackSynthesis(
	originalTask string,
	results []*AgentResult,
) string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("# Analysis Results for: %s\n\n", originalTask))
	builder.WriteString("Multiple agents have analyzed this task. Here are their combined findings:\n\n")

	for i, result := range results {
		builder.WriteString(fmt.Sprintf("## Agent %d Analysis\n\n", i+1))
		builder.WriteString(result.Content)
		builder.WriteString("\n\n")
	}

	// Add summary statistics
	builder.WriteString("## Summary\n\n")
	builder.WriteString(fmt.Sprintf("- Total agents: %d\n", len(results)))
	
	totalTokens := 0
	totalToolCalls := 0
	for _, result := range results {
		totalTokens += result.Tokens
		totalToolCalls += result.ToolUseCount
	}
	
	builder.WriteString(fmt.Sprintf("- Total tokens used: %d\n", totalTokens))
	builder.WriteString(fmt.Sprintf("- Total tool calls: %d\n", totalToolCalls))

	return builder.String()
}

// Helper functions
func float32Ptr(f float32) *float32 {
	return &f
}

func intPtr(i int) *int {
	return &i
}

func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func stringPtr(s StreamEventMarker) *StreamEventMarker {
	return &s
}
