package multiagent

import (
	"os"
	"testing"
)

// TestSimpleLLMDemo runs the simple LLM demo
// Set SIMPLE_LLM_DEMO=true to run this test
func TestSimpleLLMDemo(t *testing.T) {
	// Skip if not explicitly enabled
	if os.Getenv("SIMPLE_LLM_DEMO") != "true" {
		t.Skip("Skipping simple LLM demo - set SIMPLE_LLM_DEMO=true to run")
	}

	// Run the demo
	err := SimpleLLMDemo()
	if err != nil {
		t.Fatalf("Simple LLM demo failed: %v", err)
	}
}
