package multiagent

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/config"
	agentDomain "git.nevint.com/fota3/t-rex/domain/agent"
	"git.nevint.com/fota3/t-rex/model/agent"
	"git.nevint.com/fota3/t-rex/model/llm"
	"git.nevint.com/fota3/t-rex/pkg/llm/provider"
)

// SimpleLLMDemo demonstrates the multi-agent system with real LLM using a simplified approach
func SimpleLLMDemo() error {
	fmt.Println("=== Multi-Agent System Simple LLM Demo ===")

	// Load configuration - try multiple paths
	var cfg config.Config
	var err error

	// Try different config paths
	configPaths := []string{"config", "./config", "../config", "be/config"}
	for _, path := range configPaths {
		cfg, err = config.LoadConfig(path)
		if err == nil {
			fmt.Printf("Successfully loaded config from: %s\n", path)
			break
		}
	}

	if err != nil {
		// If all paths fail, use environment variables or hardcoded values for demo
		fmt.Println("Warning: Could not load config file, using fallback configuration")
		cfg = config.Config{
			OpenAI: config.OpenAIConfig{
				APIKey:    "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",
				APIBase:   "https://openrouter.ai/api/v1",
				ModelName: "moonshotai/kimi-k2:free",
			},
		}
	}

	// Debug: Print loaded configuration
	fmt.Printf("Loaded OpenAI config: APIKey=%s, APIBase=%s, ModelName=%s\n",
		cfg.OpenAI.APIKey, cfg.OpenAI.APIBase, cfg.OpenAI.ModelName)

	// If OpenAI config is empty, use fallback
	if cfg.OpenAI.APIKey == "" {
		fmt.Println("OpenAI config is empty, using fallback configuration")
		cfg.OpenAI = config.OpenAIConfig{
			APIKey:    "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",
			APIBase:   "https://openrouter.ai/api/v1",
			ModelName: "moonshotai/kimi-k2:free",
		}
		fmt.Printf("Using fallback OpenAI config: APIKey=%s, APIBase=%s, ModelName=%s\n",
			cfg.OpenAI.APIKey, cfg.OpenAI.APIBase, cfg.OpenAI.ModelName)
	}

	// Initialize logger
	logger, err := zap.NewDevelopment()
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}
	sugar := logger.Sugar()

	// Create LLM config
	llmConfig := llm.LLMConfig{
		ID:        primitive.NewObjectID(),
		Name:      "Demo LLM",
		Type:      "openai_compatible",
		APIKey:    cfg.OpenAI.APIKey,
		APIBase:   cfg.OpenAI.APIBase,
		ModelName: cfg.OpenAI.ModelName,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Initialize LLM client
	llmClient, err := provider.NewOpenAICompatibleClient(provider.OpenAICompatibleConfig{})
	if err != nil {
		return fmt.Errorf("failed to create LLM client: %w", err)
	}

	// Test cases
	testCases := []struct {
		name        string
		userMessage string
		description string
	}{
		{
			name:        "Simple Question",
			userMessage: "What is Go programming language? Please give me a brief introduction.",
			description: "This should use single agent mode",
		},
		{
			name:        "Complex Analysis",
			userMessage: "Analyze the advantages and disadvantages of microservices architecture, compare it with monolithic architecture, and provide detailed recommendations for when to use each approach with specific examples",
			description: "This should trigger multi-agent processing",
		},
	}

	// Test task detection
	taskDetector := NewTaskDetector(sugar)

	for i, tc := range testCases {
		fmt.Printf("\n--- Test Case %d: %s ---\n", i+1, tc.name)
		fmt.Printf("User Message: %s\n", tc.userMessage)
		fmt.Printf("Description: %s\n", tc.description)

		// Test task detection
		shouldUseMulti := taskDetector.ShouldUseMultiAgent(tc.userMessage)
		complexity := taskDetector.GetTaskComplexity(tc.userMessage)

		fmt.Printf("Task Detection Result: %v\n", shouldUseMulti)
		fmt.Printf("Task Complexity: %s\n", complexity.String())

		if shouldUseMulti {
			agentCount := complexity.GetRecommendedAgentCount()
			fmt.Printf("Recommended Agent Count: %d\n", agentCount)
		}

		// Create a simple chat completion request
		messages := []agent.Message{
			{
				MsgID:     fmt.Sprintf("demo-msg-%d", time.Now().UnixNano()),
				Role:      agent.RoleUser,
				Content:   tc.userMessage,
				Timestamp: time.Now(),
			},
		}

		req := &agentDomain.ChatCompletionRequest{
			Model:    llmConfig.ModelName,
			Messages: messages,
			Stream:   true,
		}

		// Start streaming interaction
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
		defer cancel()

		fmt.Printf("Starting LLM interaction...%+v\n", llmConfig)
		streamChan, err := llmClient.ChatCompletionStream(ctx, llmConfig, req)
		if err != nil {
			sugar.Errorw("Failed to start streaming interaction", "error", err)
			continue
		}

		// Collect and display response
		var responseContent string
		var chunkCount int

		fmt.Println("Response:")
		fmt.Println("---")

		for chunk := range streamChan {
			chunkCount++

			if chunk.Error != nil {
				fmt.Printf("Error: %s\n", chunk.Error)
				break
			}

			if chunk.ContentDelta != "" {
				fmt.Print(chunk.ContentDelta)
				responseContent += chunk.ContentDelta
			}

			if chunk.IsFinal {
				fmt.Print(" [FINAL]")
			}
		}

		fmt.Println("\n---")
		fmt.Printf("Total chunks received: %d\n", chunkCount)
		fmt.Printf("Response length: %d characters\n", len(responseContent))

		if shouldUseMulti {
			fmt.Printf("This task would benefit from multi-agent processing\n")
		} else {
			fmt.Printf("This task is suitable for single agent processing\n")
		}

		// Add a delay between test cases
		if i < len(testCases)-1 {
			fmt.Println("\nWaiting 3 seconds before next test case...")
			time.Sleep(3 * time.Second)
		}
	}

	// Demo resource monitoring
	fmt.Println("\n--- Resource Monitoring Demo ---")
	monitor := NewResourceMonitor(sugar)

	agentID := "demo-agent-1"
	monitor.StartMonitoring(agentID)

	// Simulate usage
	monitor.UpdateTokenUsage(agentID, 150)
	monitor.UpdateToolCallCount(agentID)
	monitor.UpdateToolCallCount(agentID)

	usage := monitor.GetAgentUsage(agentID)
	fmt.Printf("Agent Usage: Tokens=%d, ToolCalls=%d\n", usage.TokensUsed, usage.ToolCalls)

	finalUsage := monitor.StopMonitoring(agentID)
	fmt.Printf("Final Usage: Tokens=%d, ToolCalls=%d, Duration=%v\n",
		finalUsage.TokensUsed, finalUsage.ToolCalls, finalUsage.ExecutionTime)

	globalUsage := monitor.GetGlobalUsage()
	fmt.Printf("Global Usage: TotalAgents=%d, TotalTokens=%d, TotalToolCalls=%d\n",
		globalUsage.TotalAgentsCreated, globalUsage.TotalTokensUsed, globalUsage.TotalToolCalls)

	// Demo permission validation
	fmt.Println("\n--- Permission Validation Demo ---")
	validator := NewPermissionValidator(sugar)

	context := &SubAgentContext{
		AgentID: "demo-agent",
		AllowedTools: []string{
			"mcp.projectfs.read_file",
			"mcp.projectfs.write_file",
		},
		ResourceLimits: &ResourceLimits{
			MaxToolCalls: 5,
			MaxTokens:    1000,
		},
		ToolCallCount: 2,
		TokenCount:    200,
	}

	// Test allowed tool
	allowedTool := &agent.ToolCall{
		MCPService: "mcp.projectfs",
		MCPMethod:  "read_file",
	}

	err = validator.ValidateToolCall(context, allowedTool)
	if err != nil {
		fmt.Printf("Allowed tool validation failed: %v\n", err)
	} else {
		fmt.Printf("Allowed tool validation passed\n")
	}

	// Test forbidden tool
	forbiddenTool := &agent.ToolCall{
		MCPService: "mcp.forbidden",
		MCPMethod:  "dangerous_operation",
	}

	err = validator.ValidateToolCall(context, forbiddenTool)
	if err != nil {
		fmt.Printf("Forbidden tool validation correctly failed: %v\n", err)
	} else {
		fmt.Printf("Forbidden tool validation incorrectly passed\n")
	}

	fmt.Println("\n=== Simple LLM Demo Complete ===")
	return nil
}
