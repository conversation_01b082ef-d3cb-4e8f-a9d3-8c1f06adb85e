package multiagent

import (
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// ChineseTaskDetectionDemo demonstrates Chinese language support in TaskDetector
func ChineseTaskDetectionDemo() {
	fmt.Println("=== 中文任务检测演示 ===")

	logger := zap.NewNop().Sugar()
	taskDetector := NewTaskDetector(logger)

	// 中文测试用例
	testCases := []struct {
		name        string
		userMessage string
		description string
		expected    bool
	}{
		{
			name:        "简单问题",
			userMessage: "Go语言是什么？",
			description: "简单问题应该使用单Agent模式",
			expected:    false,
		},
		{
			name:        "复杂分析任务",
			userMessage: "请分析微服务架构的优缺点，与单体架构进行详细对比，并提供全面的建议和最佳实践",
			description: "复杂分析任务应该触发多Agent处理",
			expected:    true,
		},
		{
			name:        "代码搜索任务",
			userMessage: "查找项目中所有处理数据库操作的函数，分析它们的性能，并提出优化建议",
			description: "代码搜索和分析应该使用多Agent",
			expected:    true,
		},
		{
			name:        "架构设计任务",
			userMessage: "设计一个高性能、可扩展的分布式系统架构，包含详细的组件设计和实现方案",
			description: "架构设计任务应该使用多Agent",
			expected:    true,
		},
		{
			name:        "重构任务",
			userMessage: "重构这个项目的代码结构，改进可维护性和可读性，提供具体的重构方案",
			description: "重构任务应该使用多Agent",
			expected:    true,
		},
		{
			name:        "实现任务",
			userMessage: "实现一个用户认证系统，包含注册、登录、权限管理等功能",
			description: "复杂实现任务应该使用多Agent",
			expected:    true,
		},
		{
			name:        "简单查询",
			userMessage: "如何使用Go连接数据库？",
			description: "简单查询应该使用单Agent",
			expected:    false,
		},
		{
			name:        "混合中英文",
			userMessage: "分析这个Go项目的architecture，找出所有的performance问题并优化",
			description: "混合语言的复杂任务应该使用多Agent",
			expected:    true,
		},
	}

	fmt.Printf("%-20s %-10s %-10s %-15s %s\n", "测试用例", "预期结果", "实际结果", "复杂度", "Agent数量")
	fmt.Println(strings.Repeat("-", 100))

	for _, tc := range testCases {
		// 测试任务检测
		shouldUseMulti := taskDetector.ShouldUseMultiAgent(tc.userMessage)
		complexity := taskDetector.GetTaskComplexity(tc.userMessage)
		agentCount := complexity.GetRecommendedAgentCount()

		// 结果验证
		result := "✅"
		if shouldUseMulti != tc.expected {
			result = "❌"
		}

		fmt.Printf("%-20s %-10v %-10v %-15s %d个Agent %s\n",
			tc.name, tc.expected, shouldUseMulti, complexity.String(), agentCount, result)

		// 显示详细分析（仅对复杂任务）
		if shouldUseMulti {
			taskDesc := taskDetector.GenerateTaskDescription(tc.userMessage)
			fmt.Printf("  任务描述增强: %s\n", taskDesc[:min(100, len(taskDesc))]+"...")
		}

		fmt.Println()
	}

	// 演示关键词检测
	fmt.Println("\n=== 关键词检测演示 ===")

	keywordTests := []struct {
		category string
		keywords []string
		examples []string
	}{
		{
			category: "多Agent关键词",
			keywords: []string{"分析", "比较", "全面", "架构", "优化"},
			examples: []string{"请分析这个系统", "比较不同方案", "提供全面的建议"},
		},
		{
			category: "复杂度指标",
			keywords: []string{"如何", "解释", "帮我", "一步步", "示例"},
			examples: []string{"如何实现", "请解释原理", "帮我设计"},
		},
		{
			category: "代码分析关键词",
			keywords: []string{"代码", "函数", "组件", "数据库", "接口"},
			examples: []string{"分析代码结构", "优化函数性能", "设计数据库"},
		},
	}

	for _, test := range keywordTests {
		fmt.Printf("\n%s:\n", test.category)
		for i, keyword := range test.keywords {
			if i < len(test.examples) {
				shouldUse := taskDetector.ShouldUseMultiAgent(test.examples[i])
				fmt.Printf("  '%s' -> '%s' -> %v\n", keyword, test.examples[i], shouldUse)
			}
		}
	}

	fmt.Println("\n=== 中文任务检测演示完成 ===")
}

// min helper function
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
