package multiagent

import (
	"strings"

	"go.uber.org/zap"

	"git.nevint.com/fota3/t-rex/model/agent"
)

// PermissionValidator validates tool permissions for SubAgents
type PermissionValidator struct {
	logger *zap.SugaredLogger
}

// NewPermissionValidator creates a new PermissionValidator instance
func NewPermissionValidator(logger *zap.SugaredLogger) *PermissionValidator {
	return &PermissionValidator{
		logger: logger.Named("PermissionValidator"),
	}
}

// ValidateToolCall validates if a SubAgent is allowed to call a specific tool
func (pv *PermissionValidator) ValidateToolCall(
	agentContext *SubAgentContext,
	toolCall *agent.ToolCall,
) error {
	// Check if the tool is in the allowed list
	if !pv.isToolAllowed(agentContext.AllowedTools, toolCall) {
		pv.logger.Warnw("Tool call denied - not in allowed list", 
			"agentID", agentContext.AgentID,
			"tool", toolCall.MCPService+"."+toolCall.MCPMethod)
		return &PermissionError{
			AgentID:   agentContext.AgentID,
			Tool:      toolCall.MCPService + "." + toolCall.MCPMethod,
			Reason:    "Tool not in allowed list",
			ErrorType: "permission_denied",
		}
	}

	// Check for recursive Task tool calls (prevent infinite loops)
	if pv.isTaskTool(toolCall) {
		pv.logger.Warnw("Tool call denied - recursive Task tool call", 
			"agentID", agentContext.AgentID,
			"tool", toolCall.MCPService+"."+toolCall.MCPMethod)
		return &PermissionError{
			AgentID:   agentContext.AgentID,
			Tool:      toolCall.MCPService + "." + toolCall.MCPMethod,
			Reason:    "Recursive Task tool calls are not allowed",
			ErrorType: "recursive_call_denied",
		}
	}

	// Check resource limits
	if err := pv.checkResourceLimits(agentContext, toolCall); err != nil {
		return err
	}

	pv.logger.Debugw("Tool call validated", 
		"agentID", agentContext.AgentID,
		"tool", toolCall.MCPService+"."+toolCall.MCPMethod)

	return nil
}

// isToolAllowed checks if a tool is in the allowed list
func (pv *PermissionValidator) isToolAllowed(allowedTools []string, toolCall *agent.ToolCall) bool {
	toolName := toolCall.MCPService + "." + toolCall.MCPMethod
	
	for _, allowed := range allowedTools {
		if allowed == toolName {
			return true
		}
		// Support wildcard matching (e.g., "mcp.projectfs.*")
		if strings.HasSuffix(allowed, "*") {
			prefix := strings.TrimSuffix(allowed, "*")
			if strings.HasPrefix(toolName, prefix) {
				return true
			}
		}
	}
	
	return false
}

// isTaskTool checks if the tool call is a Task tool (to prevent recursion)
func (pv *PermissionValidator) isTaskTool(toolCall *agent.ToolCall) bool {
	// Task tools are identified by specific patterns
	taskToolPatterns := []string{
		"task",
		"multi_agent",
		"multiagent",
		"parallel_task",
		"concurrent_task",
	}
	
	toolName := strings.ToLower(toolCall.MCPService + "." + toolCall.MCPMethod)
	
	for _, pattern := range taskToolPatterns {
		if strings.Contains(toolName, pattern) {
			return true
		}
	}
	
	return false
}

// checkResourceLimits validates resource usage against limits
func (pv *PermissionValidator) checkResourceLimits(
	agentContext *SubAgentContext,
	toolCall *agent.ToolCall,
) error {
	limits := agentContext.ResourceLimits
	
	// Check tool call count limit
	if agentContext.ToolCallCount >= limits.MaxToolCalls {
		pv.logger.Warnw("Tool call denied - max tool calls exceeded", 
			"agentID", agentContext.AgentID,
			"currentCount", agentContext.ToolCallCount,
			"maxAllowed", limits.MaxToolCalls)
		return &PermissionError{
			AgentID:   agentContext.AgentID,
			Tool:      toolCall.MCPService + "." + toolCall.MCPMethod,
			Reason:    "Maximum tool calls exceeded",
			ErrorType: "resource_limit_exceeded",
		}
	}

	// Check token count limit
	if agentContext.TokenCount >= limits.MaxTokens {
		pv.logger.Warnw("Tool call denied - max tokens exceeded", 
			"agentID", agentContext.AgentID,
			"currentTokens", agentContext.TokenCount,
			"maxAllowed", limits.MaxTokens)
		return &PermissionError{
			AgentID:   agentContext.AgentID,
			Tool:      toolCall.MCPService + "." + toolCall.MCPMethod,
			Reason:    "Maximum tokens exceeded",
			ErrorType: "resource_limit_exceeded",
		}
	}

	return nil
}

// UpdateResourceUsage updates the resource usage counters after a tool call
func (pv *PermissionValidator) UpdateResourceUsage(
	agentContext *SubAgentContext,
	toolCall *agent.ToolCall,
	result *agent.ToolResult,
) {
	// Increment tool call count
	agentContext.ToolCallCount++
	
	// Update token count if available in the result
	if result != nil && result.Content != "" {
		estimatedTokens := len(result.Content) / 4 // Rough estimate: 4 chars per token
		agentContext.TokenCount += estimatedTokens
	}
	
	pv.logger.Debugw("Updated resource usage", 
		"agentID", agentContext.AgentID,
		"toolCallCount", agentContext.ToolCallCount,
		"tokenCount", agentContext.TokenCount)
}

// PermissionError represents a permission validation error
type PermissionError struct {
	AgentID   string `json:"agent_id"`
	Tool      string `json:"tool"`
	Reason    string `json:"reason"`
	ErrorType string `json:"error_type"`
}

// Error implements the error interface
func (pe *PermissionError) Error() string {
	return pe.Reason
}
