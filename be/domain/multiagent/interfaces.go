package multiagent

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"

	"git.nevint.com/fota3/t-rex/model/agent"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"
)

// MultiAgentDomain defines the interface for multi-agent conversation handling
// This interface is compatible with the original AgentReActDomain interface
type MultiAgentDomain interface {
	// HandleStreamingInteraction handles a streaming conversation using multi-agent architecture
	// Compatible with the original agent domain interface
	HandleStreamingInteraction(
		ctx context.Context,
		conversation *agent.Conversation,
		userMessage agent.Message,
		systemPrompt string,
		isRetry bool,
	) (<-chan DomainStreamChunk, error)

	// HandleProjectStreamingInteraction handles project-level streaming interactions
	// Compatible with the original agent domain interface
	HandleProjectStreamingInteraction(
		ctx context.Context,
		projectID string,
		userID string,
		userMessage agent.Message,
	) (<-chan DomainStreamChunk, error)

	// Project-level conversation management methods
	GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error)
	GetProjectConversation(ctx context.Context, projectID string) (*agent.Conversation, error)
	ClearProjectConversation(ctx context.Context, projectID string) error
	DeleteProjectConversation(ctx context.Context, projectID string) error
	ExportProjectConversation(ctx context.Context, projectID string) ([]agent.Message, error)
}

// DomainStreamChunk carries data from the multi-agent core logic to the service layer
// Compatible with the original DomainStreamChunk structure
type DomainStreamChunk struct {
	Delta          *string            // Raw text delta from the LLM
	ConversationID string             // The ID of the current conversation
	MessageID      string             // The ID of the message being streamed
	Role           agent.Role         // The role of the message
	MsgType        string             // The type of the message
	Marker         *StreamEventMarker // Optional marker for specific stream events
	Error          *error             // Optional error that occurred during processing
}

// StreamEventMarker denotes specific points or events in the stream
type StreamEventMarker string

const (
	MarkerStart StreamEventMarker = "start"
	MarkerEnd   StreamEventMarker = "end"
	MarkerError StreamEventMarker = "error"
)

// ConversationRepository defines the persistence operations required for conversations
// Reusing the same interface as the original agent domain
type ConversationRepository interface {
	CreateConversation(ctx context.Context, conversation *agent.Conversation) error
	GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error)
	AppendMessage(ctx context.Context, conversationID string, message *agent.Message) error
	FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error)
	GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error)
	CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error)
	GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error)
	ClearConversationMessages(ctx context.Context, conversationID string) error
	DeleteProjectConversation(ctx context.Context, projectID string) error
}

// LLMClient defines the interface for interacting with a Large Language Model
// Reusing the same interface as the original agent domain
type LLMClient interface {
	ChatCompletionStream(
		ctx context.Context,
		llmConfig modelLLM.LLMConfig,
		req *ChatCompletionRequest,
	) (<-chan ChatCompletionStreamResponse, error)

	ChatCompletionNonStreaming(ctx context.Context, req *ChatCompletionRequest) (*ChatCompletionResponse, error)
}

// MCPToolExecutor defines the interface for executing MCP tool calls
// Reusing the same interface as the original agent domain
type MCPToolExecutor interface {
	ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error)
	ListMCPServers(ctx context.Context) map[string][]mcp.Tool
}

// HistoryManager defines the interface for managing conversation history
// Reusing the same interface as the original agent domain
type HistoryManager interface {
	Prune(ctx context.Context, conversation *agent.Conversation) ([]agent.Message, error)
}

// ChatCompletionRequest encapsulates the data needed for an LLM chat completion request
type ChatCompletionRequest struct {
	Model       string           `json:"model"`
	Messages    []agent.Message  `json:"messages"`
	Stream      bool             `json:"stream"`
	Tools       []ToolDefinition `json:"tools,omitempty"`
	Temperature *float32         `json:"temperature,omitempty"`
	MaxTokens   *int             `json:"max_tokens,omitempty"`
}

// ToolDefinition describes a tool that the LLM can call
type ToolDefinition struct {
	Type     string             `json:"type"`
	Function FunctionDefinition `json:"function"`
}

// FunctionDefinition describes the function signature for a tool
type FunctionDefinition struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	Parameters  interface{} `json:"parameters,omitempty"`
}

// ChatCompletionStreamResponse represents a single chunk received from the LLM stream
type ChatCompletionStreamResponse struct {
	ContentDelta  string          `json:"content_delta,omitempty"`
	ToolCallDelta []ToolCallDelta `json:"tool_call_delta,omitempty"`
	IsFinal       bool            `json:"is_final,omitempty"`
	Error         error           `json:"error,omitempty"`
}

// ChatCompletionResponse represents the complete response from a non-streaming LLM call
type ChatCompletionResponse struct {
	Content      string           `json:"content"`
	ToolCalls    []agent.ToolCall `json:"tool_calls,omitempty"`
	FinishReason string           `json:"finish_reason,omitempty"`
}

// ToolCallDelta represents partial information about a tool call received during streaming
type ToolCallDelta struct {
	Index      int    `json:"index"`
	ID         string `json:"id,omitempty"`
	MCPService string `json:"mcp_service,omitempty"`
	MCPMethod  string `json:"mcp_method,omitempty"`
	ArgChunk   string `json:"arg_chunk,omitempty"`
}
