package main

import (
	"fmt"
	"log"
	"os"

	"git.nevint.com/fota3/t-rex/domain/multiagent"
)

func main() {
	fmt.Println("Starting Multi-Agent Simple LLM Demo...")

	// Change to the be directory to find config files
	if err := os.Chdir(".."); err != nil {
		log.Fatalf("Failed to change to be directory: %v", err)
	}

	// Run the demo
	if err := multiagent.SimpleLLMDemo(); err != nil {
		log.Fatalf("Demo failed: %v", err)
	}

	fmt.Println("Demo completed successfully!")
}
