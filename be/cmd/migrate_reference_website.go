package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ReferenceWebsite represents the new structure
type ReferenceWebsite struct {
	URL        string             `bson:"url" json:"url"`
	Analysis   string             `bson:"analysis" json:"analysis"`
	AnalyzedAt *time.Time         `bson:"analyzed_at" json:"analyzed_at"`
	CreatedAt  time.Time          `bson:"created_at" json:"created_at"`
}

func main() {
	// Connect to MongoDB
	client, err := mongo.Connect(context.TODO(), options.Client().ApplyURI("mongodb://localhost:27017"))
	if err != nil {
		log.Fatal(err)
	}
	defer client.Disconnect(context.TODO())

	// Get the database and collection
	db := client.Database("trex")
	coll := db.Collection("requirements")

	// Find all documents with reference_website as string
	filter := bson.M{
		"reference_website": bson.M{"$type": "string"},
	}

	cursor, err := coll.Find(context.TODO(), filter)
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(context.TODO())

	var updatedCount int
	for cursor.Next(context.TODO()) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Printf("Error decoding document: %v", err)
			continue
		}

		// Get the string value
		urlStr, ok := doc["reference_website"].(string)
		if !ok {
			log.Printf("Skipping document %v: reference_website is not a string", doc["_id"])
			continue
		}

		// Create new ReferenceWebsite struct
		newRefWebsite := ReferenceWebsite{
			URL:       urlStr,
			Analysis:  "", // Empty analysis for migrated data
			CreatedAt: time.Now(),
		}

		// Update the document
		update := bson.M{
			"$set": bson.M{
				"reference_website": newRefWebsite,
			},
		}

		_, err := coll.UpdateOne(context.TODO(), bson.M{"_id": doc["_id"]}, update)
		if err != nil {
			log.Printf("Error updating document %v: %v", doc["_id"], err)
			continue
		}

		updatedCount++
		fmt.Printf("Updated document %v: %s -> ReferenceWebsite struct\n", doc["_id"], urlStr)
	}

	if err := cursor.Err(); err != nil {
		log.Fatal(err)
	}

	fmt.Printf("Migration completed. Updated %d documents.\n", updatedCount)
}