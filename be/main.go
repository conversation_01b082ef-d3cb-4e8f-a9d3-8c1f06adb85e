package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
	mcpServerLib "github.com/mark3labs/mcp-go/server"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"git.nevint.com/fota3/t-rex/config"
	agentDomain "git.nevint.com/fota3/t-rex/domain/agent"
	aimodelDomain "git.nevint.com/fota3/t-rex/domain/aimodel"
	llmDomain "git.nevint.com/fota3/t-rex/domain/llm"
	projectDomain "git.nevint.com/fota3/t-rex/domain/project"
	"git.nevint.com/fota3/t-rex/domain/projectfs"
	requirementDomain "git.nevint.com/fota3/t-rex/domain/requirement"
	runtimeDomain "git.nevint.com/fota3/t-rex/domain/runtime"
	userDomain "git.nevint.com/fota3/t-rex/domain/user"
	"git.nevint.com/fota3/t-rex/internal/agentstore"
	"git.nevint.com/fota3/t-rex/internal/aimodelstore"
	llmStore "git.nevint.com/fota3/t-rex/internal/llmstore"
	"git.nevint.com/fota3/t-rex/internal/projectstore"
	"git.nevint.com/fota3/t-rex/internal/requirementstore"
	dockerProviderImpl "git.nevint.com/fota3/t-rex/internal/runtime/docker"
	k8sProviderImpl "git.nevint.com/fota3/t-rex/internal/runtime/kubernetes"
	runtimeStoreImpl "git.nevint.com/fota3/t-rex/internal/runtimestore"
	"git.nevint.com/fota3/t-rex/internal/storage/localfs"
	"git.nevint.com/fota3/t-rex/internal/userstore"
	mcpProjectfs "git.nevint.com/fota3/t-rex/mcp/projectfs"
	runtimeMCP "git.nevint.com/fota3/t-rex/mcp/runtime"
	websearchMCP "git.nevint.com/fota3/t-rex/mcp/websearch"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm" // This line is correct, keeping it.
	"git.nevint.com/fota3/t-rex/pkg/auth"
	"git.nevint.com/fota3/t-rex/pkg/fileupload"
	"git.nevint.com/fota3/t-rex/pkg/imageanalyzer"
	llmFactory "git.nevint.com/fota3/t-rex/pkg/llm/factory"
	"git.nevint.com/fota3/t-rex/pkg/llm/provider"
	"git.nevint.com/fota3/t-rex/pkg/mcp_hub"
	"git.nevint.com/fota3/t-rex/pkg/middleware"
	"git.nevint.com/fota3/t-rex/pkg/tokenizer"
	"git.nevint.com/fota3/t-rex/pkg/websiteanalyzer"
	agentService "git.nevint.com/fota3/t-rex/service/agent"
	aimodelService "git.nevint.com/fota3/t-rex/service/aimodel"
	llmService "git.nevint.com/fota3/t-rex/service/llm"
	projectfsService "git.nevint.com/fota3/t-rex/service/projectfs"
	requirementService "git.nevint.com/fota3/t-rex/service/requirement"
	runtimeService "git.nevint.com/fota3/t-rex/service/runtime"
	userService "git.nevint.com/fota3/t-rex/service/user"
)

// createRuntimeProvider creates the appropriate runtime provider based on configuration
func createRuntimeProvider(cfg *config.Config, logger *zap.Logger) (runtimeDomain.Provider, error) {
	switch cfg.Runtime.Provider {
	case "docker":
		logger.Info("Creating Docker runtime provider...")
		return dockerProviderImpl.NewDockerProvider(logger, cfg.Runtime.DockerHost)
	case "kubernetes":
		logger.Info("Creating Kubernetes runtime provider...")
		return k8sProviderImpl.NewK8sProvider(logger, cfg.Runtime.Kubernetes)
	default:
		return nil, fmt.Errorf("unsupported runtime provider: %s (supported: docker, kubernetes)", cfg.Runtime.Provider)
	}
}

// migrateRuntimeProviderType sets provider_type to "docker" for existing runtime records that don't have it set
func migrateRuntimeProviderType(ctx context.Context, client *mongo.Client, dbName string, logger *zap.Logger) error {
	collection := client.Database(dbName).Collection("runtimes")

	// Find all documents that don't have provider_type field or have it empty
	filter := map[string]interface{}{
		"$or": []interface{}{
			map[string]interface{}{"provider_type": map[string]interface{}{"$exists": false}},
			map[string]interface{}{"provider_type": ""},
		},
	}

	// Update all matching documents to set provider_type to "docker" (backward compatibility)
	update := map[string]interface{}{
		"$set": map[string]interface{}{
			"provider_type": "docker",
		},
	}

	result, err := collection.UpdateMany(ctx, filter, update)
	if err != nil {
		logger.Error("Failed to migrate runtime provider_type field", zap.Error(err))
		return fmt.Errorf("failed to migrate runtime provider_type: %w", err)
	}

	if result.ModifiedCount > 0 {
		logger.Info("Migration completed",
			zap.Int64("documentsUpdated", result.ModifiedCount),
			zap.String("providerType", "docker"))
	} else {
		logger.Info("No runtime documents needed migration")
	}

	return nil
}

const ip = "localhost"
const proxyIp = "localhost"

func main() {
	fmt.Println("Starting backend server...")

	// --- Load Viper config First ---
	cfg, err := config.LoadConfig("./config")
	if err != nil {
		// Use a temporary basic logger for config loading errors
		log.Fatalf("Failed to load application configuration: %v", err)
	}

	// --- Initialize Logger based on Config ---
	logCfg := zap.NewDevelopmentConfig()
	if cfg.Logger.Path != "" {
		logCfg.OutputPaths = []string{cfg.Logger.Path}
		logCfg.ErrorOutputPaths = []string{cfg.Logger.Path}
	}
	// You can also set the level from config
	logLevel := zapcore.InfoLevel // Default level
	if err := logLevel.Set(cfg.Logger.Level); err == nil {
		logCfg.Level.SetLevel(logLevel)
	}

	// Only show stack trace for Error level and above (not for Warn)
	logger, err := logCfg.Build(zap.AddStacktrace(zapcore.ErrorLevel))
	if err != nil {
		log.Fatalf("can't initialize zap logger: %v", err)
	}
	defer logger.Sync()
	sugar := logger.Sugar()
	sugar.Info("Logger initialized")
	sugar.Info("Configuration loaded successfully")

	// --- Load MCP Server Configuration ---
	sugar.Infof("Loading MCP server configuration from %s", cfg.MCP.ServersConfigPath)
	mcpServersConfig, err := mcp_hub.LoadConfigFromFile(cfg.MCP.ServersConfigPath)
	if err != nil {
		sugar.Fatalf("Failed to load MCP server configuration: %v", err)
	}
	sugar.Infof("Loaded configuration for %d MCP server(s)", len(mcpServersConfig.MCPServers))
	sugar.Debugf("MCP server configuration: %v", mcpServersConfig)

	// --- Initialize MongoDB Connection ---
	sugar.Info("Connecting to MongoDB...")
	clientOptions := options.Client().ApplyURI(cfg.MongoDB.URI)
	mongoClient, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		sugar.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	err = mongoClient.Ping(context.TODO(), nil)
	if err != nil {
		sugar.Fatalf("Failed to ping MongoDB: %v", err)
	}
	sugar.Info("Connected to MongoDB!")
	defer func() {
		if err = mongoClient.Disconnect(context.TODO()); err != nil {
			sugar.Errorf("Error disconnecting from MongoDB: %v", err)
		}
		sugar.Info("Disconnected from MongoDB.")
	}()

	// --- Initialize Storage Layer Implementations ---

	// LocalFS Storage for ProjectFS
	basePath, err := filepath.Abs(cfg.Workspace.BasePath)
	if err != nil {
		sugar.Fatalf("Failed to get absolute path for workspace: %v", err)
	}
	if err := os.MkdirAll(basePath, 0750); err != nil {
		sugar.Fatalf("Failed to create workspace base directory '%s': %v", basePath, err)
	}
	sugar.Infof("Using workspace base path: %s", basePath)
	fsStorage, err := localfs.NewLocalFSStorage(basePath)
	if err != nil {
		sugar.Fatalf("Failed to initialize LocalFSStorage: %v", err)
	}

	// User Store (MongoDB)
	sugar.Info("Initializing User Store...")
	userStore, err := userstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize User Store: %v", err)
	}

	// Project Store (MongoDB)
	sugar.Info("Initializing Project Store...")
	projectStore, err := projectstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize Project Store: %v", err)
	}

	// Agent Store (MongoDB)
	sugar.Info("Initializing Agent Store...")
	agentStore, err := agentstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize Agent Store: %v", err)
	}

	// LLM Store (MongoDB) NEW
	sugar.Info("Initializing LLM Store...")
	llmStore, err := llmStore.NewStore(mongoClient, cfg.MongoDB.Database, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize LLM Store: %v", err)
	}
	// Requirement Store (MongoDB)
	sugar.Info("Initializing Requirement Store...")
	requirementStore, err := requirementstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize Requirement Store: %v", err)
	}

	// AI Model Store (MongoDB)
	sugar.Info("Initializing AI Model Store...")
	aiModelStore, err := aimodelstore.NewStore(mongoClient, cfg.MongoDB.Database)
	if err != nil {
		sugar.Fatalf("Failed to initialize AI Model Store: %v", err)
	}

	// --- Initialize Domain Layer in correct order ---
	// 1. Domains with no cross-domain dependencies
	sugar.Info("Initializing User Domain Dependencies...")
	passwordHasher := auth.NewBcryptHasher()
	sugar.Info("Initializing User Domain...")
	userDomain, err := userDomain.New(userStore, passwordHasher, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize User Domain: %v", err)
	}

	sugar.Info("Initializing ProjectFS Domain...")
	projectfsDomain := projectfs.NewDomain(fsStorage, logger)

	// LLM Domain (NEW)
	sugar.Info("Initializing LLM Domain...")
	llmDomainInstance := llmDomain.NewDomain(llmStore, logger)

	// Seed default LLM config if none exist
	if err := llmDomainInstance.InitialSeedDefaultLLMConfig(context.Background(), modelLLM.LLMConfig{
		Type:      "openai_compatible",
		APIKey:    cfg.OpenAI.APIKey, // Use API key from config for initial seed
		APIBase:   cfg.OpenAI.APIBase,
		ModelName: cfg.OpenAI.ModelName,
	}); err != nil {
		sugar.Fatalf("Failed to seed initial default LLM config: %v", err)
	}

	// 2. Runtime Domain, which depends on providers and repositories
	sugar.Info("Initializing Runtime Provider...")
	runtimeProvider, err := createRuntimeProvider(&cfg, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize Runtime Provider: %v", err)
	}
	sugar.Infof("Runtime Provider (%s) initialized.", cfg.Runtime.Provider)

	sugar.Info("Initializing Runtime Repository (MongoDB)...")
	runtimeRepo, err := runtimeStoreImpl.NewMongoRepository(mongoClient, cfg.MongoDB.Database, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize Runtime Repository: %v", err)
	}
	sugar.Info("Runtime Repository initialized.")

	sugar.Info("Initializing Runtime Domain...")
	runtimeDomain, err := runtimeDomain.New(runtimeRepo, runtimeProvider, logger, cfg.Runtime.DefaultImageName)
	if err != nil {
		sugar.Fatalf("Failed to initialize Runtime Domain: %v", err)
	}
	sugar.Info("Runtime Domain initialized.")

	// --- Runtime Database Migration ---
	sugar.Info("Running runtime database migration...")
	if err := migrateRuntimeProviderType(context.Background(), mongoClient, cfg.MongoDB.Database, logger); err != nil {
		sugar.Fatalf("Failed to migrate runtime database: %v", err)
	}
	// 3. Agent Domain, which is needed by project domain
	sugar.Info("Initializing LLM Client...")
	proxyUrl, _ := url.Parse(fmt.Sprintf("http://%s:7890/", proxyIp))
	// Create a custom HTTP client with no timeout for LLM requests
	httpClient := &http.Client{
		Timeout: 0, // No timeout for LLM requests
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyUrl),
		},
	}
	sugar.Info("Runtime database migration completed.")

	// Initialize Agent Domain, which depends on MCP Client Manager
	sugar.Info("Initializing LLM Client...")
	llmClient, err := provider.NewOpenAICompatibleClient(provider.OpenAICompatibleConfig{}) // Added error check
	if err != nil {
		sugar.Fatalf("Failed to initialize LLM Client: %v", err)
	}
	sugar.Info("LLM Client initialized.")

	sugar.Info("Initializing MCP Client Manager...")
	mcManager, err := mcp_hub.NewClientManager(mcpServersConfig, logger)
	if err != nil {
		// NewClientManager now only fails on catastrophic config errors
		sugar.Fatalf("Failed to initialize MCP Client Manager: %v", err)
	}
	defer mcManager.CloseAll()
	sugar.Info("MCP Client Manager initialized and polling MCP servers in the background.")

	sugar.Info("Initializing MCP Tool Executor...")
	mcpToolExecutor := mcp_hub.NewMCPToolExecutorImpl(mcManager)
	sugar.Info("MCP Tool Executor initialized.")

	sugar.Info("Initializing Tokenizer Service...")
	tokenizerService, err := tokenizer.NewService(cfg.Tokenizer.Encoding)
	if err != nil {
		sugar.Fatalf("Failed to initialize Tokenizer Service: %v", err)
	}
	sugar.Info("Tokenizer Service initialized.")

	sugar.Info("Initializing History Manager...")
	historyManager := agentDomain.NewSimpleHistoryManager(
		sugar,
		tokenizerService,
		cfg.History.MaxMessages,
		cfg.History.MaxTokens,
	)
	sugar.Info("History Manager initialized.")

	sugar.Info("Initializing Agent Domain...")
	agentReactService := agentDomain.NewAgentReActDomain(
		agentStore,
		llmClient,
		mcpToolExecutor,
		historyManager,
		sugar,
		llmDomainInstance, // Pass the new llmDomainInstance
	)
	sugar.Info("Agent Domain initialized.")

	// Initialize File Upload Service
	sugar.Info("Initializing File Upload Service...")
	uploadDir := filepath.Join(basePath, "uploads")
	if err := os.MkdirAll(uploadDir, 0750); err != nil {
		sugar.Fatalf("Failed to create upload directory '%s': %v", uploadDir, err)
	}
	fileUploadSrv, err := fileupload.NewService(uploadDir, 10*1024*1024, logger) // 10MB max
	if err != nil {
		sugar.Fatalf("Failed to initialize File Upload Service: %v", err)
	}
	sugar.Info("File Upload Service initialized.")

	sugar.Info("Initializing AI Model Domain...")
	aiModelDomain, err := aimodelDomain.New(aiModelStore, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize AI Model Domain: %v", err)
	}
	sugar.Info("AI Model Domain initialized.")

	sugar.Info("Initializing LLM Client Factory...")
	llmClientFactory := llmFactory.NewLLMClientFactory(aiModelDomain, httpClient, logger)
	sugar.Info("LLM Client Factory initialized.")

	// Initialize Image Analyzer Service
	sugar.Info("Initializing Image Analyzer Service...")
	imageAnalyzerSrv, err := imageanalyzer.NewService(llmClientFactory, uploadDir, cfg.OpenAI.ModelName, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize Image Analyzer Service: %v", err)
	}
	sugar.Info("Image Analyzer Service initialized.")

	// Initialize Website Analyzer Service
	sugar.Info("Initializing Website Analyzer Service...")
	websiteAnalyzerSrv, err := websiteanalyzer.NewService(llmClientFactory, cfg.OpenAI.ModelName, logger)
	if err != nil {
		sugar.Fatalf("Failed to initialize Website Analyzer Service: %v", err)
	}
	sugar.Info("Website Analyzer Service initialized.")

	// 4. Project Domain, which depends on Runtime Domain and Agent Domain
	sugar.Info("Initializing Project Domain...")
	projectDomain, err := projectDomain.New(projectStore, runtimeDomain, agentReactService, logger, projectfsDomain)
	if err != nil {
		sugar.Fatalf("Failed to initialize Project Domain: %v", err)
	}
	sugar.Info("Project Domain initialized.")

	sugar.Info("Initializing Requirement Domain...")
	requirementDomain, err := requirementDomain.New(requirementStore, llmClientFactory, imageAnalyzerSrv, websiteAnalyzerSrv, fileUploadSrv, projectDomain, uploadDir, logger, cfg.OpenAI.ModelName)
	if err != nil {
		sugar.Fatalf("Failed to initialize Requirement Domain: %v", err)
	}
	sugar.Info("Requirement Domain initialized.")

	// --- Initialize Service Layer ---
	sugar.Info("Initializing User Service...")
	userSrv := userService.NewService(userDomain, logger)
	sugar.Info("User Service initialized.")

	sugar.Info("Initializing ProjectFS Service...")
	projectfsSrv := projectfsService.NewService(projectfsDomain, projectDomain, logger)
	sugar.Info("ProjectFS Service initialized.")

	sugar.Info("Initializing Runtime Service...")
	runtimeSrv := runtimeService.NewService(logger, runtimeDomain, projectDomain, projectfsDomain)
	sugar.Info("Runtime Service initialized.")

	sugar.Info("Initializing Agent Service...")
	agentSrv := agentService.NewService(agentReactService, projectDomain, runtimeDomain, sugar, mcManager)
	sugar.Info("Agent Service initialized.")

	// Initialize LLM Service (NEW)
	sugar.Info("Initializing LLM Service...")
	llmSrv := llmService.NewService(llmDomainInstance, sugar) // Corrected: pass llmDomainInstance directly
	sugar.Info("LLM Service initialized.")

	sugar.Info("Initializing Requirement Service...")
	requirementSrv := requirementService.NewService(requirementDomain, fileUploadSrv, logger)
	sugar.Info("Requirement Service initialized.")

	sugar.Info("Initializing AI Model Service...")
	aiModelSrv := aimodelService.NewService(aiModelDomain, logger)
	sugar.Info("AI Model Service initialized.")

	// --- Initialize MCP Components ---
	sugar.Info("Initializing MCP Handler...")
	mcpHandler := mcpProjectfs.NewProjectFSHandler(projectfsDomain, logger)

	sugar.Info("Initializing MCP Server...")
	mcpServer := mcpServerLib.NewMCPServer("T-Rex ProjectFS MCP", "1.0.0")

	sugar.Info("Registering MCP Tools...")
	mcpHandler.RegisterTools(mcpServer)

	sugar.Info("Creating MCP SSE Handler...")
	httpPort := cfg.Server.Port
	sseHandler := mcpServerLib.NewSSEServer(
		mcpServer,
		mcpServerLib.WithBaseURL(fmt.Sprintf("http://%s:%s", ip, httpPort)),
		mcpServerLib.WithBasePath("/mcp/projectfs"),
		mcpServerLib.WithSSEEndpoint("/sse"),
		mcpServerLib.WithMessageEndpoint("/message"),
	)

	// --- Initialize Runtime MCP Components ---
	sugar.Info("Initializing Runtime MCP Handler...")
	runtimeMCPHandler := runtimeMCP.NewRuntimeHandler(runtimeDomain, logger)

	sugar.Info("Initializing Runtime MCP Server...")
	runtimeMCPServer := mcpServerLib.NewMCPServer("T-Rex Runtime MCP", "1.0.0")

	sugar.Info("Registering Runtime MCP Tools...")
	runtimeMCPHandler.RegisterTools(runtimeMCPServer)

	sugar.Info("Creating Runtime MCP SSE Handler...")
	runtimeSSEHandler := mcpServerLib.NewSSEServer(
		runtimeMCPServer,
		mcpServerLib.WithBaseURL(fmt.Sprintf("http://%s:%s", ip, httpPort)),
		mcpServerLib.WithBasePath("/mcp/runtime"),
		mcpServerLib.WithSSEEndpoint("/sse"),
		mcpServerLib.WithMessageEndpoint("/message"),
	)

	// --- Initialize WebSearch MCP Components ---
	sugar.Info("Initializing WebSearch MCP Handler...")
	webSearchMCPHandler := websearchMCP.NewWebSearchHandler(logger, cfg.GoogleSearch.APIKey, cfg.GoogleSearch.CX)

	sugar.Info("Initializing WebSearch MCP Server...")
	webSearchMCPServer := mcpServerLib.NewMCPServer("T-Rex WebSearch MCP", "1.0.0")

	sugar.Info("Registering WebSearch MCP Tools...")
	webSearchMCPHandler.RegisterTools(webSearchMCPServer)

	sugar.Info("Creating WebSearch MCP SSE Handler...")
	webSearchSSEHandler := mcpServerLib.NewSSEServer(
		webSearchMCPServer,
		mcpServerLib.WithBaseURL(fmt.Sprintf("http://localhost:%s", httpPort)),
		mcpServerLib.WithBasePath("/mcp/websearch"),
		mcpServerLib.WithSSEEndpoint("/sse"),
		mcpServerLib.WithMessageEndpoint("/message"),
	)

	// --- Initialize Gin Engine ---
	r := gin.Default()

	// NEW: Middleware to inject logger into Gin context
	r.Use(func(c *gin.Context) {
		c.Set("logger", sugar)
		c.Next()
	})
	// Note: Using default MaxMultipartMemory (32MB) for file uploads

	// --- Setup CORS Middleware ---
	sugar.Info("Setting up CORS middleware...")
	corsConfig := cors.Config{
		// AllowAllOrigins:  true, // Cannot use wildcard with credentials
		AllowOrigins:     []string{fmt.Sprintf("http://%s:5173", ip)}, // Specify allowed origin(s)
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	r.Use(cors.New(corsConfig))

	// --- Setup Session Middleware ---
	sugar.Info("Setting up Session middleware (Redis Store)...")
	redisStore, err := redis.NewStore(10, "tcp", cfg.Redis.Address, "", cfg.Redis.Password, []byte(cfg.Session.Secret))
	if err != nil {
		sugar.Fatalf("Failed to create Redis session store: %v", err)
	}
	redisStore.Options(sessions.Options{
		Path:     "/",
		HttpOnly: true,
		Secure:   false,
		MaxAge:   86400 * 7,
		SameSite: http.SameSiteLaxMode,
	})
	r.Use(sessions.Sessions("trex_session", redisStore))

	// --- Setup Routes ---
	sugar.Info("Setting up API routes...")

	// Static file serving for uploaded images
	sugar.Info("Setting up static file serving for uploads...")
	r.Static("/api/v1/uploads", uploadDir)

	apiV1 := r.Group("/api/v1")
	{
		// User Auth Routes
		authGroup := apiV1.Group("/auth")
		{
			// Public routes
			authGroup.POST("/register", userSrv.HandleRegister)
			authGroup.POST("/login", userSrv.HandleLogin)

			// Authenticated routes (require session)
			authGroup.Use(middleware.AuthRequired())
			authGroup.GET("/profile", userSrv.HandleGetProfile)
			authGroup.POST("/logout", userSrv.HandleLogout)
		}

		// Project Routes (require authentication)
		projectsGroup := apiV1.Group("/projects")
		projectsGroup.Use(middleware.AuthRequired())
		{
			projectsGroup.POST("", projectfsSrv.CreateProject)
			projectsGroup.GET("", projectfsSrv.ListProjects)
			projectsGroup.DELETE("/:projectID", projectfsSrv.DeleteProject)

			// Project Filesystem Routes (Grouped under /projects/:projectID)
			projectFSRoutes := projectsGroup.Group("/:projectID")
			{
				projectFSRoutes.GET("/files", projectfsSrv.ListDirectory)
				projectFSRoutes.POST("/files", projectfsSrv.CreateFSObject)
				projectFSRoutes.GET("/files/content", projectfsSrv.ReadFile)
				projectFSRoutes.PUT("/files/content", projectfsSrv.WriteFile)
				projectFSRoutes.DELETE("/files", projectfsSrv.DeleteFSObject)
				projectFSRoutes.PUT("/files/rename", projectfsSrv.RenameFSObject)
				projectFSRoutes.GET("/watch", projectfsSrv.WatchFileChanges) // File change watching via SSE
				projectFSRoutes.GET("/terminal", runtimeSrv.HandleTerminalConnection)
				projectFSRoutes.POST("/runtime/rebuild", runtimeSrv.HandleRebuildRuntime)
				projectFSRoutes.GET("/runtime/urls", runtimeSrv.HandleGetProjectURLs) // Get external access URLs
				// Test route to debug routing
				projectFSRoutes.GET("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"message": "test route works"})
				})

				// Agent Conversation Routes
				conversationRoutes := projectFSRoutes.Group("/conversation")
				{
					conversationRoutes.GET("", agentSrv.GetProjectConversation)
					conversationRoutes.POST("/messages", agentSrv.SendMessageToProject)
					conversationRoutes.DELETE("/messages", agentSrv.ClearProjectConversation)
					conversationRoutes.GET("/export", agentSrv.ExportProjectConversation)
				}
			}
		}

		// Admin Routes (require authentication and admin privileges) NEW
		adminGroup := apiV1.Group("/admin")
		adminGroup.Use(middleware.AuthRequired(), middleware.AdminRequired())
		{
			// LLM Configuration management
			adminGroup.POST("/llm_configs", llmSrv.HandleCreateLLMConfig)
			adminGroup.GET("/llm_configs", llmSrv.HandleListLLMConfigs)
			adminGroup.GET("/llm_configs/:id", llmSrv.HandleGetLLMConfig)
			adminGroup.PUT("/llm_configs/:id", llmSrv.HandleUpdateLLMConfig)
			adminGroup.DELETE("/llm_configs/:id", llmSrv.HandleDeleteLLMConfig)
			adminGroup.POST("/llm_configs/:id/set_active", llmSrv.HandleSetActiveLLMConfig)

			// Other admin-only routes can go here in the future
		}

		// Requirement Routes (require authentication)
		requirementsGroup := apiV1.Group("/requirements")
		requirementsGroup.Use(middleware.AuthRequired())
		{
			requirementsGroup.POST("", requirementSrv.HandleCreateRequirement)
			requirementsGroup.POST("/with-images", requirementSrv.HandleCreateRequirementWithImages)
			requirementsGroup.POST("/unified", requirementSrv.HandleCreateRequirement)
			requirementsGroup.GET("", requirementSrv.HandleGetRequirements)
			requirementsGroup.GET("/:id", requirementSrv.HandleGetRequirement)
			requirementsGroup.POST("/:id/questions", requirementSrv.HandleGenerateQuestions)
			requirementsGroup.POST("/:id/answers", requirementSrv.HandleSubmitAnswers)
			requirementsGroup.POST("/:id/wireframe", requirementSrv.HandleGenerateWireframe)
			requirementsGroup.POST("/:id/create-project", requirementSrv.HandleCreateProjectFromRequirement)
			requirementsGroup.PUT("/:id", requirementSrv.HandleUpdateRequirement)
			requirementsGroup.DELETE("/:id", requirementSrv.HandleDeleteRequirement)
		}
		// AI Model Routes (require authentication)
		aiModelGroup := apiV1.Group("/aimodels")
		aiModelGroup.Use(middleware.AuthRequired())
		{
			aiModelGroup.POST("", aiModelSrv.HandleCreateAIModel)
			aiModelGroup.GET("", aiModelSrv.HandleGetAIModels)
			aiModelGroup.GET("/:id", aiModelSrv.HandleGetAIModel)
			aiModelGroup.PUT("/:id", aiModelSrv.HandleUpdateAIModel)
			aiModelGroup.DELETE("/:id", aiModelSrv.HandleDeleteAIModel)
			aiModelGroup.POST("/:id/toggle", aiModelSrv.HandleToggleAIModelStatus)
		}
	}

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "pong"})
	})

	// Health check endpoint
	r.GET("/healthz", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// --- Register MCP SSE Routes with Gin ---
	// Use specific paths to avoid route conflicts

	// Register Runtime MCP SSE Routes
	runtimeMCPBasePath := "/mcp/runtime"
	r.Any(runtimeMCPBasePath+"/*path", gin.WrapH(runtimeSSEHandler))
	sugar.Infof("Registered Runtime MCP SSE Handler routes at %s", runtimeMCPBasePath)

	// Register ProjectFS MCP SSE Routes
	projectfsMCPBasePath := "/mcp/projectfs"
	r.Any(projectfsMCPBasePath+"/*path", gin.WrapH(sseHandler))
	sugar.Infof("Registered ProjectFS MCP SSE Handler routes at %s", projectfsMCPBasePath)

	// Register WebSearch MCP SSE Routes
	webSearchMCPBasePath := "/mcp/websearch"
	r.Any(webSearchMCPBasePath+"/*path", gin.WrapH(webSearchSSEHandler))
	sugar.Infof("Registered WebSearch MCP SSE Handler routes at %s", webSearchMCPBasePath)

	// --- Start Gin Server (in goroutine) ---
	go func() {
		sugar.Infof("HTTP Server (including MCP SSE) starting on port %s", httpPort)
		if err := r.Run(":" + httpPort); err != nil && err != http.ErrServerClosed {
			sugar.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// --- Wait for Gin server to be ready by polling health check ---
	sugar.Info("Waiting for Gin server to become healthy...")
	healthCheckURL := fmt.Sprintf("http://%s:%s/healthz", ip, httpPort)
	maxRetries := 30
	retryDelay := 500 * time.Millisecond
	for i := 0; i < maxRetries; i++ {
		resp, err := http.Get(healthCheckURL)
		if err == nil && resp.StatusCode == http.StatusOK {
			sugar.Info("Gin server is healthy.")
			resp.Body.Close()
			break
		}
		if resp != nil {
			resp.Body.Close()
		}
		if i == maxRetries-1 {
			sugar.Fatalf("Gin server failed to become healthy after %d retries.", maxRetries)
		}
		sugar.Debugf("Health check attempt %d failed, retrying in %v... (err: %v)", i+1, retryDelay, err)
		time.Sleep(retryDelay)
	}

	// --- Keep main thread alive ---
	sugar.Info("Application started. Waiting for connections or shutdown signal...")

	// Setup graceful shutdown
	defer func() {
		sugar.Info("Shutting down application...")

		// Stop all file watchers
		if projectfsDomain != nil {
			sugar.Info("Stopping file watchers...")
			watcherManager := projectfsDomain.GetFileWatcherManager()
			if watcherManager != nil {
				watcherManager.StopAll()
			}
		}

		sugar.Info("Application shutdown complete.")
	}()

	select {}
}
